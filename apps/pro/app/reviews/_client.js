'use client';
import React, { useEffect, useState } from 'react';
import {
  Button,
  ContactConcierge,
  FeaturedTalent,
  MainLayout,
  ModalInviteTalent,
  ModalProfile,
  ModalReview,
  PageLayout,
  Paginator,
  ReviewCard,
} from '@components';
import { useAuth } from '@contexts/AuthContext';
import { useViewport } from '@utils/useViewport';
import Api from '@services/api';
import { formatTalentProfile } from '@utils/formatTalentProfile';
import { ContactsProvider } from '@contexts/ContactContext';
import styles from '@styles/reviews.module.scss';
import Masonry from 'react-masonry-css';
import { isInViewPort } from '@utils/isInViewPort';
import { useRouter } from 'next/navigation';
import { Amp } from '@services/amp';

const ReviewsClient = ({
  initialReviews,
  reviewCount,
  profiles,
  initialReviewQuery,
  canContactTalent,
}) => {
  const [reviews, setReviews] = useState(initialReviews);
  const [showProfileModal, setShowProfileModal] = useState(false);
  const [page, setPage] = useState(initialReviewQuery.page);
  const [showReviewModal, setShowReviewModal] = useState(false);
  const [showInviteTalentModal, setShowInviteTalentModal] = useState(false);
  const [selectedTalentId, setSelectedTalentId] = useState(null);
  const [selectedTalent, setSelectedTalent] = useState(null);
  const { profileId } = useAuth();
  const { width } = useViewport();
  const router = useRouter();

  const loadReviews = async (event, value) => {
    event?.stopPropagation();
    event?.preventDefault();

    const query = { limit: 8, page: value };
    const newReviews = await fetchReviews(query.page, query.limit);

    setReviews(newReviews.items);
    setPage(value);

    if (value === 1) {
      router.push('/reviews');
    } else {
      router.push('/reviews/page-' + value);
    }
  };

  const toggleShowProfileModal = (id) => {
    setSelectedTalentId(id || null);
    setShowProfileModal(!showProfileModal);
  };

  const openReviewModal = async () => {
    const profile = await fetchProfile(profileId);

    setSelectedTalent(profile);
    setShowReviewModal(true);
  };

  const closeReviewModal = async (update) => {
    setShowReviewModal(false);
    setSelectedTalent(null);

    if (update === true) {
      await loadReviews(null, 1);
    }
  };

  const fetchProfile = async (id) => {
    return await Api.clientside(`/profiles/${id}`);
  };

  const fetchReviews = async (page, limit) => {
    return await Api.clientside(`/testimonials?page=${page}&limit=${limit}`);
  };

  const toggleShowInviteTalentModal = () => {
    if (showInviteTalentModal && selectedTalent) {
      setSelectedTalent(null);
    }

    setShowInviteTalentModal(!showInviteTalentModal);
  };

  const onInviteSingleTalent = (talent) => {
    setSelectedTalent(formatTalentProfile(talent));
    toggleShowInviteTalentModal();
  };

  useEffect(() => {
    Amp.track(Amp.events.viewReviews, {
      page: initialReviewQuery.page,
    });
  }, [initialReviewQuery.page]);

  return (
    <ContactsProvider>
      <MainLayout
        isDefaultHeaderVisible
        isMobileMenuVisible
        isUserMenuVisible
        isFooterVisible
      >
        <PageLayout>
          <section className={styles['reviews-section']}>
            <div className={styles['reviews-container']}>
              <div className={styles['reviews-heading']}>
                <div>
                  <span>{reviewCount} Reviews</span>
                </div>
                <div>
                  <Button
                    minWidth="200px"
                    kind="secondary"
                    label="Leave your review"
                    onClick={openReviewModal}
                  />
                </div>
              </div>
              <Masonry
                breakpointCols={isInViewPort(width, 'tablet', 'min') ? 2 : 1}
                className={styles['masonry-grid']}
                columnClassName={styles['masonry-grid-column']}
              >
                {!!reviews &&
                  reviews.map(
                    ({ id, rating, content, author, published_at }) => (
                      <ReviewCard
                        key={id}
                        title={`${author.firstname} ${author.lastname}`}
                        subTitle={
                          author.type === 'agent'
                            ? 'Casting Professional'
                            : 'Talent'
                        }
                        date={published_at}
                        description={content
                          .replace(/(<([^>]+)>)/gi, '')
                          .replace(' ￼', ' ')}
                        imageSrc={
                          author.title_photo_url ||
                          (author.type === 'agent'
                            ? '/assets/placeholders/circle-casting-director.svg'
                            : `/assets/placeholders/circle-${author.gender?.title?.toLowerCase() || 'male'}-close_up.svg`)
                        }
                        rating={rating}
                        openProfilePopup={() =>
                          toggleShowProfileModal(author.id)
                        }
                        isClickable
                      />
                    ),
                  )}
              </Masonry>
              <Paginator
                page={page}
                perPage={initialReviewQuery.limit}
                total={reviewCount}
                prefixUrl={'/reviews/'}
                prefixPage={'page-'}
                handleChange={loadReviews}
              />
            </div>
            <div className={styles['featured-container']}>
              <ContactConcierge />
              <div className={styles['featured-heading']}>
                <h3 className={styles['featured-title']}>Featured Talent</h3>
              </div>
              <div className={styles['featured-talent']}>
                {profiles &&
                  profiles.map(({ id, rating, title_photo_url }) => (
                    <FeaturedTalent
                      key={id}
                      rating={rating?.toLocaleString('en-EN')}
                      imageSrc={title_photo_url}
                      openProfilePopup={() => toggleShowProfileModal(id)}
                    />
                  ))}
              </div>
            </div>
            {showProfileModal && (
              <ModalProfile
                id={selectedTalentId}
                onClose={toggleShowProfileModal}
                onInvite={(id) =>
                  onInviteSingleTalent(
                    reviews.find((talent) => talent.author.id === id)?.author ||
                      profiles.find((talent) => talent.id === id),
                  )
                }
              />
            )}
            {showInviteTalentModal && (
              <ModalInviteTalent
                talents={[selectedTalent]}
                onClose={toggleShowInviteTalentModal}
                canContactTalent={canContactTalent}
              />
            )}
            {showReviewModal && (
              <ModalReview
                onClose={closeReviewModal}
                profileId={profileId}
                imgSrc={selectedTalent.title_photo_url}
              />
            )}
          </section>
        </PageLayout>
      </MainLayout>
    </ContactsProvider>
  );
};

export default ReviewsClient;
