@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.placeholder-image-title {
  color: $grey-80;
  font-size: 18px;
  font-weight: 300;
  visibility: hidden;

  @include tablet {
    color: $black;
    font-size: 14px;
  }
}

.placeholder-image-title-main {
  visibility: visible;

  @include tablet {
    visibility: hidden;
  }
}

.placeholder-image-link {
  position: relative;
  display: inline-block;
  font-size: 14px;
  color: $blue-100;
  font-weight: 700;
  text-decoration: none;
  cursor: pointer;

  &::after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 2px;
    background-color: $blue-100;
    opacity: 0;
  }

  &:hover {
    text-decoration: none;

    &::after {
      opacity: 1;
    }
  }
}

.placeholder-image-container {
  position: relative;
  height: 0;
  padding-top: 133.124%;
}

.placeholder-image-box {
  position: absolute;
  inset: 0;
  display: flex;
  flex-flow: column nowrap;
  align-items: center;
  justify-content: space-between;
  text-align: center;
  border: 1px dashed #bec7d6;
  padding: $space-10 0;
  border-radius: 5px;
  cursor: pointer;

  @include desktop {
    padding: 18px 0;
  }

  img {
    object-fit: contain;
    width: 100%;
    height: 100%;
  }
}

.default-placeholder-image-box {
  justify-content: center;

  .image {
    width: 25%;
    height: 25%;
  }
}

.default-placeholder-image-info {
  display: flex;
  flex-flow: column nowrap;
  align-items: center;
  font-size: 12px;
  line-height: 1.2;
  color: $grey-80;
  font-weight: 300;
}

.main-image {
  width: 50%;
  height: 50%;
}

.placeholder-status-image {
  background-color: $grey-100;

  @include tablet {
    background-color: $white;
    border-radius: 5px;
  }

  .main-image {
    width: 65%;
    height: 65%;
    order: 1;
    margin-top: $space-20;
    min-width: 210px;
    min-height: 210px;

    @include tablet {
      width: 50%;
      height: 50%;
      order: 0;
      margin: 0;
      min-width: unset;
      min-height: unset;
    }
  }

  .main-image-headshot {
    width: 65%;
    height: 65%;

    @include tablet {
      width: 80%;
      height: 80%;
    }
  }

  .default-placeholder-image-info {
    flex-flow: row nowrap;

    @include tablet {
      flex-flow: column nowrap;
    }
  }

  .placeholder-image-box {
    padding: 70px 0 200px;
    justify-content: flex-start;

    @include tablet {
      padding: $space-10;
      justify-content: space-between;
    }
  }

  .placeholder-image-link {
    display: none;

    @include tablet {
      display: inline-block;
    }
  }
}

.upload-button {
  position: absolute;
  top: 20px;
  right: 20px;
  background-color: $white;
  border-radius: 20px;
  border: none;
  padding: 7px $space-25;
  font-size: 14px;
  font-weight: 400;
  cursor: pointer;

  @include tablet {
    display: none;
  }
}
