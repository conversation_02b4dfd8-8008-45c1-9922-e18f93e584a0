'use client';
import React, { memo, useCallback } from 'react';
import styles from './ModalContentProtection.module.scss';
import { ContentProtectionForm, Modal } from '@components';
import { useRouter } from 'next/navigation';

const ModalContentProtection = ({ identifier, onClose }) => {
  const router = useRouter();

  const closeContentProtectionForm = useCallback(() => {
    onClose();
  }, [onClose]);

  const closeContentProtectionFormAndNavigate = useCallback(() => {
    onClose();
    router.push('/settings');
  }, [onClose, router]);

  return (
    <Modal
      backdropClose
      onClose={closeContentProtectionFormAndNavigate}
      classNameContainer={styles['content-protection-modal']}
      showMobileBorderRadius={false}
      showDefaultLayout={false}
      hideMobileCloseButton
    >
      <ContentProtectionForm
        identifier={identifier}
        closeContentProtectionForm={closeContentProtectionForm}
        closeContentProtectionFormAndNavigate={
          closeContentProtectionFormAndNavigate
        }
      />
    </Modal>
  );
};

export default memo(ModalContentProtection);
