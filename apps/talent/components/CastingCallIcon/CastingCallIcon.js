'use client';
import { memo } from 'react';
import cn from 'classnames';
import styles from './CastingCallIcon.module.scss';
import Image from 'next/image';

const CastingCallIcon = ({ title, slug }) => {
  return (
    <div className={cn(styles.icon, styles[`icon-${slug}`])}>
      <Image
        src={`/assets/icons/categories/icon-${slug}.svg`}
        width={20}
        height={20}
        alt={title}
      />
    </div>
  );
};

export default memo(CastingCallIcon);
