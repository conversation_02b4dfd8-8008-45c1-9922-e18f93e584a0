@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.sidebar {
  display: none;
  position: fixed;
  bottom: 0;
  left: 0;
  top: 70px;
  min-width: 50px;
  width: 50px;
  z-index: $z-index-sidebar;

  @include desktop {
    display: flex;
  }
}

.sidebar-sale {
  top: 114px;
}

.content {
  position: relative;
  display: flex;
  width: 100%;
  align-items: center;
  padding: $space-15 0;
  flex-flow: column nowrap;
  background: $violet-100;
  justify-content: space-between;
  z-index: 2;
}

.menu-section {
  display: flex;
  align-items: center;
  flex-direction: column;
}

.button {
  position: relative;
  border-radius: 50%;
  border: 1px solid rgb(255, 255, 255, 30%);
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: $white;
  margin-top: 7px;
  transition: all 0.2s ease-in-out;
  font-weight: 700;
  font-size: 18px;
  cursor: pointer;

  &:hover {
    border-color: rgb(255, 255, 255, 100%);
    transition: all 0.2s ease-in-out;
    text-decoration: none;
  }

  .profile-img {
    width: 28px;
    height: 28px;
    border-radius: 100%;
    object-fit: cover;
  }
}

.badge {
  position: absolute;
  top: 0;
  right: -6px;
}

.active {
  border-color: rgb(255, 255, 255, 100%);
  transition: all 0.2s ease-in-out;
  text-decoration: none;
}
