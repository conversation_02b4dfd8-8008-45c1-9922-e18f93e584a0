'use client';
import styles from './TalentProfile.module.scss';
import React, { memo, useEffect, useState } from 'react';
import {
  <PERSON>ccordion,
  Button,
  ModalAddContact,
  ModalAddNote,
  ModalInviteTalent,
  ModalPostCastingCall,
  ModalSocialMediaAccessRestriction,
} from '@components';
import Image from 'next/image';
import { PageLayout } from '../Layouts';
import { sortProfilePhotos } from '@utils/imageHelpers';
import MainImages from './MainImages/MainImages';
import VideosDesktop from './VideosDesktop/VideosDesktop';
import { useModalContext } from '@contexts/ModalContext';
import AudiosDesktop from './AudiosDesktop/AudiosDesktop';
import ImagesDesktop from './ImagesDesktop/ImagesDesktop';
import Credits from './Credits/Credits';
import BasicDetails from './BasicDetails/BasicDetails';
import Categories from './Categories/Categories';
import Appearance from './Appearance/Appearance';
import SocialNetworks from './SocialNetworks/SocialNetworks';
import Attributes from './Attributes/Attributes';
import VideosMobile from './VideosMobile/VideosMobile';
import AudiosMobile from './AudiosMobile/AudiosMobile';
import ImagesMobile from './ImagesMobile/ImagesMobile';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import cn from 'classnames';
import Notes from './Notes/Notes';
import Api from '@services/api';
import { refreshNotesInMenu } from '@utils/talentHelper';
import { useMessageWidget } from '@contexts/MessageWidgetContext';

const TalentProfile = ({
  sideMenuItems,
  isMobileFromUserAgent,
  profile,
  canContactTalent,
  menuItems,
}) => {
  const {
    id,
    firstName,
    lastName,
    fullName,
    images,
    additionalImages,
    rating,
    videos,
    credits,
    closeUpImage,
    sideViewImage,
    fullHeightImage,
    gender,
    audios,
    categories,
    ethnicity,
    age,
    location,
    height,
    weight,
    eyeColor,
    hairColor,
    hipSize,
    dressSize,
    bust,
    cupSize,
    socialNetworks,
    bodyType,
    specificCharacteristics,
    tattoos,
    piercings,
    unions,
    skills,
    languages,
    accents,
    sports,
    dances,
    professions,
    musicianship,
    familyPhotoShoot,
    driving,
    pets,
    notes,
    showAttributes,
    showAdvancedAttributes,
    showAllAttributesButton,
    titlePhotoUrl,
    resume,
  } = profile;

  const [sliderImages, setSliderImages] = useState([]);
  const [currentNotes, setCurrentNotes] = useState(notes || []);
  const [currentSideMenuItems, setCurrentSideMenuItems] = useState(
    sideMenuItems || [],
  );
  const [showAddNoteModal, setShowAddNoteModal] = useState(false);
  const [showInviteTalentModal, setShowInviteTalentModal] = useState(false);
  const [selectedTalent, setSelectedTalent] = useState(null);
  const [showModalAddContact, setShowModalAddContact] = useState(false);
  const [
    showSocialMediaAccessRestrictionModal,
    setShowSocialMediaAccessRestrictionModal,
  ] = useState(false);
  const [showPostCastingCallModal, setShowPostCastingCallModal] =
    useState(false);
  const { toggleShowContactTalentModal, toggleShowGalleryModal } =
    useModalContext();

  const router = useRouter();
  const { openConversation } = useMessageWidget();

  useEffect(() => {
    sortSliderImages();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [images]);

  const sortSliderImages = () => {
    let sortedImages = [...images];

    if (sortedImages?.length) {
      sortedImages = sortProfilePhotos(sortedImages);
    }
    setSliderImages(sortedImages);
  };

  const onImageClick = (imageId) => {
    const index = sliderImages.findIndex((image) => image.id === imageId);

    toggleShowGalleryModal(sliderImages, index);
  };

  const handleNavigateBack = () => {
    router.push('/talent');
  };

  const toggleShowAddNoteModal = () => {
    setShowAddNoteModal(!showAddNoteModal);
  };

  const refreshNotes = async () => {
    const response = await Api.clientside(`/profiles/${id}/notes`);

    setCurrentNotes(response.items || []);
    setCurrentSideMenuItems(
      refreshNotesInMenu(response.items, currentSideMenuItems),
    );
  };

  const toggleShowModalAddContact = () => {
    setShowModalAddContact(!showModalAddContact);
  };

  const toggleShowInviteTalentModal = () => {
    if (showInviteTalentModal && selectedTalent) {
      setSelectedTalent(null);
    }

    setShowInviteTalentModal(!showInviteTalentModal);
  };

  const onInviteSingleTalent = () => {
    setSelectedTalent(profile);
    toggleShowInviteTalentModal();
  };

  const onContact = () => {
    if (!canContactTalent) {
      toggleShowContactTalentModal();
    } else {
      openConversation({
        id: id,
        firstName: firstName,
        lastName: lastName,
        image: titlePhotoUrl,
        gender: gender,
      });
    }
  };

  const toggleShowSocialMediaAccessRestrictionModal = () => {
    setShowSocialMediaAccessRestrictionModal(
      !showSocialMediaAccessRestrictionModal,
    );
  };

  const toggleShowPostCastingCallModal = () => {
    setShowSocialMediaAccessRestrictionModal(false);
    setShowPostCastingCallModal(!showPostCastingCallModal);
  };

  return (
    <PageLayout>
      <section className={styles['profile-section']}>
        <div className={styles['profile-header']}>
          <div className={styles['back-icon-container']}>
            <Image
              className={styles['back-icon']}
              src={'/assets/icons/icon-angle-3.svg'}
              width={16}
              height={16}
              onClick={handleNavigateBack}
              alt=""
            />
          </div>
          <h1>{fullName}</h1>
        </div>
        <div className={styles['profile-container']}>
          <div id="main-photos" className={styles.profile}>
            <MainImages
              closeUpImage={closeUpImage}
              fullHeightImage={fullHeightImage}
              sideViewImage={sideViewImage}
              gender={gender}
              onImageClick={onImageClick}
            />
            <div className={styles['profile-overview']}>
              <div id="name" className={styles['profile-overview-header']}>
                <h1 className={styles['profile-name']}>{fullName}</h1>
              </div>
              <div className={styles['profile-overview-details']}>
                <div className={styles['profile-rating']}>
                  <Image
                    className={styles['star']}
                    src={'/assets/icons/icon-star-3.svg'}
                    width={17}
                    height={17}
                    alt="star icon"
                    priority
                  />
                  <span className={styles['profile-rating-count']}>
                    {rating}
                  </span>
                  <div
                    onClick={toggleShowModalAddContact}
                    className={styles['contact-container']}
                  >
                    <Image
                      src="/assets/icons/icon-add-1.svg"
                      alt="icon"
                      width={20}
                      height={20}
                    />
                    Add to contacts
                  </div>
                </div>

                <div className={styles['profile-id']}>
                  <span>Profile ID: </span>
                  <span>{id}</span>
                </div>
              </div>
              <hr className={styles['separator']} />
              <div className={styles.desktop}>
                <Categories categories={categories} />
                <BasicDetails
                  gender={gender}
                  ethnicity={ethnicity}
                  age={age}
                  location={location}
                />
                <Appearance
                  height={height}
                  weight={weight}
                  bust={bust}
                  dressSize={dressSize}
                  eyeColor={eyeColor}
                  hipSize={hipSize}
                  hairColor={hairColor}
                  cupSize={cupSize}
                  gender={gender}
                  bodyType={bodyType}
                  specificCharacteristics={specificCharacteristics}
                  tattoos={tattoos}
                  piercings={piercings}
                />
                <SocialNetworks
                  socialNetworks={socialNetworks}
                  canViewSocialNetworks={canContactTalent}
                  toggleShowSocialMediaAccessRestrictionModal={
                    toggleShowSocialMediaAccessRestrictionModal
                  }
                />
              </div>
            </div>
          </div>

          <div className={styles['profile-mobile']}>
            {closeUpImage ? (
              <div>
                <img
                  className={styles.image}
                  src={closeUpImage.proxy_url}
                  alt="image"
                />
              </div>
            ) : (
              <div className={styles['placeholder-image-container']}>
                <div
                  className={cn(
                    styles['placeholder-image'],
                    styles[gender ? gender.toLowerCase() : 'male'],
                  )}
                ></div>
              </div>
            )}

            <div className={styles['profile-contact']}>
              <span className={styles['profile-name']}>{fullName}</span>
              <div className={styles['profile-rating']}>
                <Image
                  className={styles['star']}
                  src={'/assets/icons/icon-star-3.svg'}
                  width={17}
                  height={17}
                  alt="star icon"
                />
                <span className={styles['profile-rating-count']}>{rating}</span>
                <div
                  onClick={toggleShowModalAddContact}
                  className={styles['contact-container']}
                >
                  <Image
                    src="/assets/icons/icon-add-1.svg"
                    alt="icon"
                    width={20}
                    height={20}
                  />
                  Add to contacts
                </div>
              </div>
            </div>
          </div>

          <div className={styles['menu-container']}>
            {menuItems.map(({ title, selector }) => (
              <Link
                key={title}
                href={selector}
                scroll={false}
                className={styles['menu-item']}
              >
                {title}
              </Link>
            ))}
          </div>

          <div id="details" className={styles.mobile}>
            <div
              className={cn(
                styles['action-container'],
                styles['action-container-mobile'],
              )}
            >
              <Button
                onClick={onContact}
                label="Contact talent"
                minWidth="100%"
              />
              <div className={styles['btn-container']}>
                <Button
                  className={styles.button}
                  label="Invite"
                  kind="secondary"
                  onClick={onInviteSingleTalent}
                />
                <Button
                  onClick={toggleShowAddNoteModal}
                  className={styles.button}
                  label="Add notes"
                  kind="secondary"
                />
              </div>
            </div>
            <Categories categories={categories} />
            <BasicDetails
              gender={gender}
              ethnicity={ethnicity}
              age={age}
              location={location}
            />
            <Appearance
              height={height}
              weight={weight}
              bust={bust}
              dressSize={dressSize}
              eyeColor={eyeColor}
              hipSize={hipSize}
              hairColor={hairColor}
              cupSize={cupSize}
              gender={gender}
              bodyType={bodyType}
              specificCharacteristics={specificCharacteristics}
              tattoos={tattoos}
              piercings={piercings}
            />
            <SocialNetworks
              socialNetworks={socialNetworks}
              canViewSocialNetworks={canContactTalent}
              toggleShowSocialMediaAccessRestrictionModal={
                toggleShowSocialMediaAccessRestrictionModal
              }
            />
          </div>

          {resume && (
            <div id="about" className={styles.about}>
              <div>
                <b>About:</b>
                <p>{resume}</p>
              </div>
            </div>
          )}

          <Attributes
            unions={unions}
            skills={skills}
            languages={languages}
            accents={accents}
            sports={sports}
            dances={dances}
            professions={professions}
            musicianship={musicianship}
            familyPhotoShoot={familyPhotoShoot}
            driving={driving}
            pets={pets}
            showAttributes={showAttributes}
            showAdvancedAttributes={showAdvancedAttributes}
            showAllAttributesButton={showAllAttributesButton}
          />

          <hr className={styles['separator']} />
          <Notes
            notes={currentNotes}
            toggleShowAddNoteModal={toggleShowAddNoteModal}
            refreshNotes={refreshNotes}
          />

          {(videos?.length > 0 || audios?.length > 0) && (
            <>
              <hr className={styles['separator']} />
              <h2 id="assets" className={styles['profile-title']}>
                Assets
              </h2>
            </>
          )}

          {videos.length > 0 && (
            <>
              <VideosDesktop videos={videos} />
              <VideosMobile videos={videos} />
            </>
          )}

          {audios.length > 0 && (
            <>
              <AudiosDesktop
                audios={audios}
                isMobileFromUserAgent={isMobileFromUserAgent}
              />
              <AudiosMobile
                audios={audios}
                isMobileFromUserAgent={isMobileFromUserAgent}
              />
            </>
          )}

          {additionalImages.length > 0 && (
            <>
              <ImagesDesktop
                images={additionalImages}
                onImageClick={onImageClick}
              />
              <ImagesMobile images={additionalImages} />
            </>
          )}

          {credits.length > 0 && <Credits credits={credits} />}
        </div>
        <div className={styles['profile-sidemenu']}>
          <Accordion accordionItems={currentSideMenuItems}>
            <div className={styles['action-container']}>
              <Button
                onClick={onContact}
                label="Contact talent"
                minWidth="100%"
              />
              <div className={styles['btn-container']}>
                <Button
                  className={styles.button}
                  label="Invite"
                  kind="secondary"
                  onClick={onInviteSingleTalent}
                />
                <Button
                  onClick={toggleShowAddNoteModal}
                  className={styles.button}
                  label="Add notes"
                  kind="secondary"
                />
              </div>
            </div>
          </Accordion>
        </div>
      </section>
      {showAddNoteModal && (
        <ModalAddNote
          onClose={toggleShowAddNoteModal}
          talentId={id}
          refreshNotes={refreshNotes}
        />
      )}
      {showInviteTalentModal && (
        <ModalInviteTalent
          talents={[selectedTalent]}
          onClose={toggleShowInviteTalentModal}
          canContactTalent={canContactTalent}
        />
      )}
      {showModalAddContact && (
        <ModalAddContact
          id={id}
          firstName={firstName}
          lastName={lastName}
          gender={gender}
          imageSrc={titlePhotoUrl}
          onClose={toggleShowModalAddContact}
        />
      )}
      {showSocialMediaAccessRestrictionModal && (
        <ModalSocialMediaAccessRestriction
          onClose={toggleShowSocialMediaAccessRestrictionModal}
          onShowPostCc={toggleShowPostCastingCallModal}
        />
      )}
      {showPostCastingCallModal && (
        <ModalPostCastingCall onClose={toggleShowPostCastingCallModal} />
      )}
    </PageLayout>
  );
};

export default memo(TalentProfile);
