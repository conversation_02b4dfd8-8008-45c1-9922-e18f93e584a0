import { Experiment } from '@amplitude/experiment-js-client';
import { OneTrustService } from '@services/onetrust';

// For exposure tracking required to initialize on the client side
export const AmplitudeExperimentClient = {
  experimentInstance: null,

  init() {
    if (!this.experimentInstance) {
      this.experimentInstance = Experiment.initializeWithAmplitudeAnalytics(
        process.env.amplitudeId,
      );
    }

    return this.experimentInstance;
  },

  async track(user, flagKey) {
    if (!user?.user_id || !flagKey) return;

    if (!OneTrustService.checkIsPerformanceCookiesEnabled()) return;

    const experiment = this.init();

    await experiment.fetch(user);
    experiment.variant(flagKey);
  },
};
