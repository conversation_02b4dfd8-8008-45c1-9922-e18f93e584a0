'use client';
import { memo } from 'react';

const AudioPlayerTrack = ({
  url,
  audioRef,
  onEnded,
  setDuration,
  progressBarRef,
}) => {
  const onLoadedMetadata = () => {
    const seconds = audioRef.current.duration;

    setDuration(seconds);
    progressBarRef.current.max = seconds;
  };

  return (
    <audio
      src={url}
      ref={audioRef}
      onEnded={onEnded}
      onLoadedMetadata={onLoadedMetadata}
    />
  );
};

export default memo(AudioPlayerTrack);
