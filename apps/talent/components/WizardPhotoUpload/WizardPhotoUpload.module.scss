@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.heading {
  text-align: center;
}

.description {
  margin-top: 1rem;
}

.upload-template {
  height: 367px;
}

.footer {
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  background-color: $blue-10;
  border-radius: 15px;
  gap: 2rem;
  padding: 2rem 1.25rem;

  @include tablet {
    padding: 2.5rem 1.25rem;
  }
}

.footer-text {
  display: flex;
  flex-direction: column;
  gap: 0.625rem;
}

.footer-title {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.625rem;
  transition: all 0.3s ease-in-out;
}
