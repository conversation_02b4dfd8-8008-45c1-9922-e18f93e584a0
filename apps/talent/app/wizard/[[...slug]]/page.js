import WizardClient from './_client';
import { cookies, headers } from 'next/headers';
import { redirect } from 'next/navigation';
import { CookieServiceServer } from '@services/cookieServiceServer';
import Api from '@services/api';
import { getResolvedUrl } from '@utils/apiHelper';
import { formatProfile } from '@utils/formatProfile';
import { checkIsPasswordSet } from '@utils/authHelper';
import { extractResult } from '@utils/extractPromiseResult';

export const metadata = {
  title: 'Wizard',
};

const getWeighted = ({ title, value }) => (title === 'Other' ? 999 : value);

export default async function WizardPage(req) {
  const searchParams = await req.searchParams;
  const step = Number(searchParams.step) || 1;
  const resolvedUrl = getResolvedUrl('/wizard', searchParams);
  const cookieStore = await cookies();
  const headerStore = await headers();
  const id = CookieServiceServer.getProfileCookie(cookieStore);
  const userProfiles = CookieServiceServer.getUserProfilesCookie(cookieStore);

  if (step === 3) {
    // backward compatibility
    redirect('/wizard?step=2');
  }

  if (step !== 1 && step !== 2) {
    redirect('/wizard');
  }

  const paths = [
    '/parameters',
    `/profiles/${id}?expand=location,touches,ethnicities,categories`,
    `/profiles/${id}/images?expand=profile_image_characteristics&width=600&height=800`,
  ];

  const results = await Promise.allSettled(
    paths.map((path) =>
      Api.serverside(path, cookieStore, headerStore, resolvedUrl),
    ),
  );

  const [parameters, profile, images] = results.map((result) =>
    extractResult(result, {}),
  );

  const formattedProfile = formatProfile(profile, images.items);

  return (
    <>
      <WizardClient
        step={Number(step)}
        profile={formattedProfile}
        isEmailValid={userProfiles[0].isEmailValid ?? true}
        genderOptions={
          parameters.items?.genders?.items
            ?.map((gender) => ({
              ...gender,
              title:
                gender.title.charAt(0).toUpperCase() + gender.title.slice(1),
            }))
            .filter((gender) => gender.value) || []
        }
        ethnicitiesOptions={
          parameters.items?.ethnicities?.items
            ?.filter((ethnicity) => ethnicity.value)
            .toSorted((a, b) => getWeighted(a) - getWeighted(b)) || []
        }
        // from social auth, have to fields that are in RegisterForm, but not it social auth
        isPasswordSet={checkIsPasswordSet(
          CookieServiceServer.getAuthenticationCookie(headerStore),
        )}
        redirectPath={CookieServiceServer.getRedirectCookie(headerStore) || ''}
      />
    </>
  );
}
