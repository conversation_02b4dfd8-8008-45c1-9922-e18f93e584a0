'use client';
import { memo, useState } from 'react';
import styles from './NotificationsForm.module.scss';
import { Switch } from '@components';
import Api from '@services/api';
import { useNotifications } from '@contexts/NotificationContext';
import Link from 'next/link';
import cn from 'classnames';
import { useAuth } from '@contexts/AuthContext';
import { ErrorMessage } from '@constants/form';

const NotificationsForm = ({ allowEmailNotifications, identifier }) => {
  const { setNotification } = useNotifications();
  const [notificationsEnabled, setNotificationsEnabled] = useState(
    allowEmailNotifications,
  );
  const { accountId } = useAuth();

  const onAllowNotificationsChange = async (e) => {
    const value = e.target.checked;

    setNotificationsEnabled(value);

    await updateNotifications(value);
  };

  const updateNotifications = async (isAllowed) => {
    const body = new FormData();

    if (isAllowed) {
      body.append('channels[]', '2');
    }

    const notificationsChangeResponse = await Api.clientside(
      `/accounts/${accountId}/arrangements`,
      {
        body,
        method: 'PUT',
      },
    );

    if (notificationsChangeResponse.status !== 'ok') {
      setNotification({
        type: 'error',
        timeout: '5000',
        message: notificationsChangeResponse.message || ErrorMessage.Unexpected,
      });
    } else {
      setNotification({
        type: 'success',
        timeout: '5000',
        message: 'New settings successfully updated',
      });
    }
  };

  return (
    <form className={styles['notifications-form']}>
      <div className={styles['notifications-form-header']}>
        <div className={styles['notifications-form-header-title']}>
          <span
            className={cn({
              [styles['notifications-allowed']]: notificationsEnabled,
            })}
          >
            Allow email notifications
          </span>
          <span className={styles['notifications-form-header-separator']}>
            |
          </span>
          <span className={styles['notifications-form-header-identifier']}>
            {identifier}
          </span>
        </div>
        <div>
          <Switch
            name="allowEmailNotifications"
            value={notificationsEnabled}
            onChange={onAllowNotificationsChange}
          />
        </div>
      </div>
      <div className={styles['notifications-form-email-settings-container']}>
        <div className={styles['notifications-form-email-settings']}>
          <span className={styles['notifications-form-email-settings-title']}>
            Email:
          </span>
          <Link href={'/settings/contacts'} className={styles.link}>
            Edit
          </Link>
        </div>
        <span>{identifier}</span>
      </div>
      {!notificationsEnabled && (
        <div className={styles['notifications-form-content']}>
          <div className={styles['notifications-form-disclaimer-container']}>
            <span className={styles['notifications-form-disclaimer']}>
              You have opted-out of receiving all email notifications.
            </span>
            <p>
              However, you will continue receiving service emails, e.g., payment
              notifications, etc.
            </p>
          </div>
        </div>
      )}
      {notificationsEnabled && (
        <div className={styles['notifications-form-content']}>
          <div className={styles['notifications-form-settings']}>
            <p className={styles['notifications-form-description']}>
              Here&apos;s where you can easily manage your email subscription to
              ensure you stay informed with our latest news and updates. Please
              note, you will continue to receive essential service emails, but
              don&apos;t worry &mdash; they&apos;re a rare breed, so your inbox
              will remain nice and tidy.
            </p>
          </div>
        </div>
      )}
      <div className={styles['notifications-form-actions']}>
        <Link
          href={'/settings/contacts'}
          className={styles['notifications-form-button']}
          type="submit"
        >
          Edit email
        </Link>
      </div>
    </form>
  );
};

export default memo(NotificationsForm);
