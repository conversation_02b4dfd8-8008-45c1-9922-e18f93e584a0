'use client';
import React, { memo } from 'react';
import styles from './Loading.module.scss';
import cn from 'classnames';

const Loading = ({
  padding = '0',
  minHeight = '40px',
  color = 'midnight',
  justifyContent = 'center',
}) => {
  return (
    <div
      className={styles.container}
      style={{
        minHeight: minHeight,
        padding: padding,
        justifyContent: justifyContent,
      }}
    >
      {[...Array(5)]
        .map((el, index) => index)
        .map((key) => (
          <div key={key} className={cn(styles.bouncer, styles[color])} />
        ))}
    </div>
  );
};

export default memo(Loading);
