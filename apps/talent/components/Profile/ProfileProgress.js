'use client';
import React, { memo } from 'react';
import styles from './ProfileProgress.module.scss';
import cn from 'classnames';
import StarIcon from '../../public/assets/icons/icon-star-filled-slim.svg';
import {
  maxProfileRating,
  profileProgressStatusEnum,
} from '@constants/profile';
import { Hint } from '@components';
import { getRatingProgressPercentage } from '@utils/profileProgressHelpers';

const ProfileProgress = ({
  close,
  rating = 0,
  imageSrc,
  status = profileProgressStatusEnum.bad,
  genderTitle,
  setActiveTab,
  photoProgress,
  videoProgress,
  infoProgress,
}) => {
  const ratingProgressPercentage = getRatingProgressPercentage(rating);
  const onProgressAction = (action) => {
    close();
    setActiveTab(action);
  };

  return (
    <div className={styles['progress-wrapper']}>
      <div onClick={close} className={styles['progress-close']} />
      <div className={styles['progress-content']}>
        <div className={styles['progress-rating']}>
          <div className={styles['progress-title']}>Rating</div>
          <div className={styles['user-image-block']}>
            <div className={styles['progress-percentage']}>
              <svg
                viewBox="0 0 36 36"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                preserveAspectRatio="none"
                className={cn(
                  styles['circular-chart'],
                  styles[`progress-${status}`],
                )}
              >
                <path
                  className={styles['circular-chart-bg']}
                  d="M18 2.0845
          a 15.9155 15.9155 0 0 1 0 31.831
          a 15.9155 15.9155 0 0 1 0 -31.831"
                />
                <path
                  stroke={`url(#gradient-${status})`}
                  className={styles['circular-chart-percentage']}
                  style={{
                    strokeDasharray: `${ratingProgressPercentage}, 100`,
                  }}
                  d="M18 2.0845
          a 15.9155 15.9155 0 0 1 0 31.831
          a 15.9155 15.9155 0 0 1 0 -31.831"
                />
                <linearGradient
                  id="gradient-excellent"
                  x1="0%"
                  y1="0%"
                  x2="0%"
                  y2="100%"
                >
                  <stop offset="0%" stopColor="#2dc2e3" />
                  <stop offset="100%" stopColor="#6fd084" />
                </linearGradient>
                <linearGradient
                  id="gradient-good"
                  x1="0%"
                  y1="0%"
                  x2="0%"
                  y2="100%"
                >
                  <stop offset="0%" stopColor="#a8e0ff" />
                  <stop offset="100%" stopColor="#43c3ec" />
                </linearGradient>
                <linearGradient
                  id="gradient-bad"
                  x1="0%"
                  y1="0%"
                  x2="0%"
                  y2="100%"
                >
                  <stop offset="0%" stopColor="#ed645b" />
                  <stop offset="100%" stopColor="#f88f44" />
                </linearGradient>
              </svg>
            </div>
            <div
              className={styles['user-profile-image']}
              style={{
                backgroundImage: `url('${
                  imageSrc ||
                  `/assets/placeholders/circle-${genderTitle}-close_up.svg`
                }')`,
              }}
            />
          </div>

          <div className={styles['user-rating']}>
            <StarIcon className={styles['user-rating-star']} />
            <span className={styles['user-rating-count']}>
              {rating?.toLocaleString('en-EN')}{' '}
              <span>/ {maxProfileRating.toLocaleString('en-EN')}</span>
            </span>
          </div>
        </div>

        <div className={styles['progress-statistic']}>
          <div className={styles['progress-title']}>Progress</div>
          <div className={styles['progress-statistic-content']}>
            <div>
              <div className={styles['progress-statistic-header']}>
                <div className={styles['progress-statistic-title']}>Info</div>
                <div className={styles['progress-statistic-filling']}>
                  <div style={{ width: `${infoProgress}%` }}>
                    {infoProgress || 0}%
                  </div>
                </div>
              </div>
              <div className={styles['progress-statistic-text']}>
                Introduce yourself to the community - complete all sections with
                information about you.
              </div>
              <span
                className={styles['progress-statistic-link']}
                onClick={() => onProgressAction('info')}
              >
                Complete Profile
              </span>
            </div>
            <div>
              <div className={styles['progress-statistic-header']}>
                <div className={styles['progress-statistic-title']}>Photos</div>
                <div className={styles['progress-statistic-filling']}>
                  <div style={{ width: `${photoProgress}%` }}>
                    {photoProgress || 0}%
                  </div>
                </div>
              </div>
              <div className={styles['progress-statistic-text']}>
                Add more photos so casting directors can see you. Remember that
                comp card photos will be evaluated by a photo analyzer.
              </div>
              <span
                className={styles['progress-statistic-link']}
                onClick={() => onProgressAction('photos')}
              >
                Add More Photos
              </span>
            </div>
            <div>
              <div className={styles['progress-statistic-header']}>
                <div className={styles['progress-statistic-title']}>Videos</div>
                <div className={styles['progress-statistic-filling']}>
                  <div style={{ width: `${videoProgress}%` }}>
                    {videoProgress || '0'}%
                  </div>
                </div>
              </div>
              <div className={styles['progress-statistic-text']}>
                Add videos to expose your talent and get more attention from the
                casting directors.
              </div>
              <span
                className={styles['progress-statistic-link']}
                onClick={() => onProgressAction('assets')}
              >
                Add Videos
              </span>
            </div>
          </div>
        </div>
        <Hint title="Remember:" classNameAdditional={'slim'}>
          Comp card photos will be evaluated by a photo analyzer.
          <br />
          <br />
          You can see photo rating options by clicking the arrow icon on the
          photo.
        </Hint>
      </div>
    </div>
  );
};

export default memo(ProfileProgress);
