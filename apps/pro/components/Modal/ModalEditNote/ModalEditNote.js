'use client';
import React, { memo, useState } from 'react';
import Modal from '../Modal';
import { HeaderMobile, Input } from '@components';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import styles from './ModalEditNote.module.scss';
import Api from '@services/api';
import { useNotifications } from '@contexts/NotificationContext';
import { ErrorMessage } from '@constants/form';

const ModalEditNote = ({ onClose, id, note, onDelete, refreshNotes }) => {
  const [loading, setLoading] = useState(false);
  const { setNotification } = useNotifications();

  const formik = useFormik({
    initialValues: {
      note: note || '',
    },
    onSubmit: async (values) => {
      setLoading(true);
      const body = new FormData();

      body.append('content', values.note);

      const response = await Api.clientside(`/notes/${id}`, {
        method: 'PUT',
        body,
      });

      if (response.status !== 'ok') {
        setNotification({
          type: 'error',
          timeout: '5000',
          message: response.message || ErrorMessage.Unexpected,
        });
      } else {
        await refreshNotes();
        setNotification({
          type: 'success',
          timeout: '5000',
          message: 'Note updated',
        });
      }

      onClose();
      setLoading(false);
    },
    validationSchema: Yup.object({
      note: Yup.string()
        .required(ErrorMessage.FieldRequired)
        .max(500, ErrorMessage.MaxCharacters.replace('{X}', '500')),
    }),
  });

  const onRemove = () => {
    onClose();
    onDelete({ id });
  };

  return (
    <Modal
      onClose={onClose}
      backdropClose
      classNameContainer={styles['modal-container']}
      classNameOverlay={styles['modal-overlay']}
      classNameContent={styles['modal-content']}
      showDefaultLayout={false}
      hideMobileCloseButton
      showMobileBorderRadius={false}
      showAnimation={false}
    >
      <HeaderMobile
        onClose={onClose}
        title="Edit note"
        showButton
        buttonLabel="Done"
        onClick={formik.submitForm}
        buttonDisabled={loading || !formik.isValid}
        hideOnTablet
      />
      <form className={styles.form}>
        <div className={styles['form-title']}>
          <span className={styles.title}>Edit note</span>
          <span onClick={onRemove} className={styles['btn-remove']}>
            Remove
          </span>
        </div>
        <Input
          name="note"
          placeholder="Your note"
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          value={formik.values.note}
          isTouched={formik.touched.note}
          error={formik.errors.note}
          charCounter
        />
      </form>
      <div className={styles.actions}>
        <button disabled={loading} onClick={onClose} className={styles.action}>
          Cancel
        </button>
        <button
          disabled={loading}
          onClick={formik.submitForm}
          className={styles.action}
        >
          Update
        </button>
      </div>
    </Modal>
  );
};

export default memo(ModalEditNote);
