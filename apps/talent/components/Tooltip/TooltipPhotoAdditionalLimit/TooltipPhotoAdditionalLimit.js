'use client';
import React, { memo } from 'react';
import styles from './TooltipPhotoAdditionalLimit.module.scss';
import Image from 'next/image';

const TooltipPhotoAdditionalLimit = () => {
  return (
    <div className={styles['tooltip-container']}>
      <div className={styles['profile-panel-info-title']}>
        <Image
          className={styles['profile-panel-info-icon']}
          src={'/assets/icons/icon-important.svg'}
          width={17}
          height={17}
          alt="important icon"
          priority
        />
        &nbsp; What you need to know
      </div>
      <div className={styles['profile-panel-info-text']}>
        You can upload up to 10 additional photos. Get creative and showcase
        your best shots!
      </div>
    </div>
  );
};

export default memo(TooltipPhotoAdditionalLimit);
