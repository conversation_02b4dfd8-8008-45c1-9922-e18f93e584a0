import MissingInfoClient from './_client';
import { CookieServiceServer } from '@services/cookieServiceServer';
import { getResolvedUrl } from '@utils/apiHelper';
import { cookies, headers } from 'next/headers';
import Api from '@services/api';

export const metadata = {
  title: 'Missing Info',
};

export default async function MissingInfoPage(req) {
  const searchParams = await req.searchParams;
  const resolvedUrl = getResolvedUrl('/missing-info', searchParams);
  const cookieStore = await cookies();
  const headerStore = await headers();
  const userProfiles = CookieServiceServer.getUserProfilesCookie(cookieStore);
  const id = userProfiles[0].id;

  const profile = await Api.serverside(
    `/profiles/${id}?expand=touches`,
    cookieStore,
    headerStore,
    resolvedUrl,
  );

  return (
    <MissingInfoClient
      profile={profile}
      isEmailValid={userProfiles[0]?.isEmailValid ?? true}
    />
  );
}
