'use client';
import { memo } from 'react';
import styles from './AudioAssets.module.scss';
import { AudioPlaceholder } from '@components';
import Audio from '../Audio/Audio';

const AudioAssets = ({
  audios = [],
  isPaidOrDelayed,
  isMobileFromUserAgent,
}) => {
  return (
    <div className={styles['audio-assets']}>
      {!!audios?.length &&
        audios.map(({ id, title, full_path, path }) => (
          <Audio
            key={id}
            audioId={id}
            title={title}
            url={full_path}
            path={path}
            isMobileFromUserAgent={isMobileFromUserAgent}
          />
        ))}
      <AudioPlaceholder
        isPaidOrDelayed={isPaidOrDelayed}
        isMobileFromUserAgent={isMobileFromUserAgent}
      />
    </div>
  );
};

export default memo(AudioAssets);
