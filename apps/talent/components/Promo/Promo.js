'use client';
import { memo } from 'react';
import styles from './Promo.module.scss';
import Image from 'next/image';
import cn from 'classnames';
import { useAnalytics } from 'use-analytics';
import { GTM_EVENTS, GTM_CATEGORIES, GTM_ACTIONS } from '@constants/analytics';
import Link from 'next/link';
import Carousel from '../Carousel/Carousel';
import { Amp } from '@services/amp';
import { getPromoImages, getReducedPromos } from '@utils/promoHelper';

const Promo = ({ promos = [] }) => {
  const { track } = useAnalytics();

  const fixLink = (link) => {
    let fixed = link
      .replace('castingcalls', 'castingcall')
      // ToDo: fix with new categories instead of audition-tips
      .replace('/lessons', `${process.env.publicUrl}/blog/audition-tips`);

    if (!fixed.startsWith('/')) {
      fixed = '/' + fixed;
    }

    return `${process.env.publicUrl}${fixed}`;
  };

  const formattedPromos = promos.map((promo) => ({
    ...promo,
    link: fixLink(promo.link),
    ...getPromoImages(promo.images),
  }));

  const trackBanner = (href) => {
    track(GTM_EVENTS.interaction, {
      target: GTM_CATEGORIES.banner,
      action: GTM_ACTIONS.click,
      label: href,
    });
    Amp.track(Amp.events.topSliderBannerClicked, {
      banner_href: href,
    });
  };

  const trackBannerHover = () => {
    Amp.track(Amp.events.elementHovered, {
      name: 'banner',
      scope: Amp.element.scope.upgrade,
      section: Amp.element.section.promotions,
      type: Amp.element.type.block,
    });
  };

  return (
    <>
      {promos.length > 0 && (
        <>
          <div onMouseEnter={trackBannerHover} className={styles.mobile}>
            <Carousel
              className="carousel-promos-mobile"
              loop
              slidesToScroll={1}
              enableArrowNavigation
            >
              {formattedPromos.map(({ link, name, mobileImageHref }, index) => (
                <Link
                  href={link}
                  key={index}
                  onClick={() => trackBanner(mobileImageHref)}
                  className={styles['slider-slide']}
                  prefetch={false}
                >
                  <div key={index} className={styles['slider-slide-item']}>
                    <Image
                      src={mobileImageHref}
                      priority
                      alt={name}
                      fill
                      unoptimized
                    />
                  </div>
                </Link>
              ))}
            </Carousel>
          </div>
          <div onMouseEnter={trackBannerHover} className={styles.desktop}>
            <Carousel
              className="carousel-promos"
              enableArrowNavigation
              slidesToScroll={1}
              delay={3000}
              loop
              playOnInit
            >
              {getReducedPromos(formattedPromos, 5).map((item, i) => (
                <div key={i} className={styles['slider-slide']}>
                  {item.map(({ link, name, desktopImageHref }, index) => (
                    <Link
                      key={index}
                      href={link}
                      onClick={() => trackBanner(desktopImageHref)}
                      className={cn(styles['slider-slide-item'], {
                        [styles['slider-slide-item-middle']]: index === 2,
                      })}
                      prefetch={false}
                    >
                      <Image
                        src={desktopImageHref}
                        priority
                        alt={name}
                        fill
                        unoptimized
                      />
                    </Link>
                  ))}
                </div>
              ))}
            </Carousel>
          </div>
        </>
      )}
    </>
  );
};

export default memo(Promo);
