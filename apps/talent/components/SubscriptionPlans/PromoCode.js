'use client';
import { Form, Formik } from 'formik';
import styles from './PromoCode.module.scss';
import Image from 'next/image';
import cn from 'classnames';
import Api from '@services/api';
import { useState } from 'react';
import { Amp } from '@services/amp';
import { InputFormik } from '@components';
import { formatPlans, promoHasBetterPrices } from '@utils/planSelectHelper';
import { useAuth } from '@contexts/AuthContext';

const STATES = {
  Idle: 'idle',
  Input: 'input',
  Success: 'success',
  Warning: 'warning',
};

export const PromoToggleButton = ({ label, toggleInput, isInline = false }) => (
  <button
    className={cn(styles['toggle-button'], isInline && styles.inline)}
    onClick={toggleInput}
  >
    {label}
  </button>
);

const PromoInput = ({ onSubmit }) => {
  return (
    <Formik initialValues={{ code: '' }} onSubmit={onSubmit}>
      {({ isSubmitting }) => (
        <Form className={styles['promo-form']}>
          <div className={styles.promo}>
            <InputFormik
              className="coupon"
              placeholder="Coupon Code"
              hint="Enter your coupon code here"
              name="code"
            />
            <button
              type="submit"
              disabled={isSubmitting}
              className={cn(styles['promo-button'], {
                [styles.disabled]: isSubmitting,
              })}
            >
              Apply
            </button>
          </div>
        </Form>
      )}
    </Formik>
  );
};

const Success = ({ code, remove }) => {
  return (
    <div className={cn(styles.promo, styles.success)}>
      <Image
        src="/assets/icons/icon-completed-thick.svg"
        alt="checkmark-icon"
        width={33}
        height={30}
      />
      <div className={cn(styles['promo-text'], styles.success)}>
        <span className={styles['promo-code']}>{code}</span>
        <span className={styles['promo-success']}>
          Your Coupon Code has been applied!
        </span>
      </div>
      <button
        className={cn(styles['promo-button'], styles.success)}
        onClick={remove}
      >
        Remove
      </button>
    </div>
  );
};

const Warning = ({ remove }) => {
  return (
    <div className={cn(styles.promo, styles.warning)}>
      <div className={cn(styles['promo-text'], styles.warning)}>
        <span className={styles['promo-code']}>Coupon code applied</span>
        <span className={styles['promo-success']}>
          But the current deals are better than coupon code offers.
        </span>
      </div>
      <button
        className={cn(styles['promo-button'], styles.warning)}
        onClick={remove}
      >
        Remove
      </button>
    </div>
  );
};

export const PromoCode = ({
  updateSettings = () => {},
  resetSettings = () => {},
  amplitudeScope = '',
  plans,
}) => {
  const [state, setState] = useState(STATES.Input);
  const [code, setCode] = useState('');

  const { accountId } = useAuth();

  const submitCode = async (values, { setFieldError }) => {
    const promoCode = values.code.trim().toUpperCase();
    const params = new URLSearchParams();

    if (!promoCode) {
      return;
    }

    params.set('promo_code', promoCode);

    const res = await Api.clientside(
      `/accounts/${accountId}/prices?${params.toString()}`,
    );

    const hasData = !!res.prices?.length;

    Amp.track(Amp.events.elementClicked, {
      name: 'apply promo code',
      scope: amplitudeScope,
      section: Amp.element.section.planSelect,
      type: Amp.element.type.button,
      result: hasData ? Amp.element.result.success : Amp.element.result.fail,
      promo_code: promoCode,
    });

    if (!hasData) {
      setFieldError('code', 'Coupon Code is invalid');

      return;
    }

    const promoPlans = res.prices.map((plan) => ({
      ...plan,
      isPromo: true,
      promoCode: promoCode,
    }));

    const showPromoPlans = promoHasBetterPrices(plans, promoPlans);

    if (showPromoPlans) {
      updateSettings({
        plans: formatPlans(promoPlans),
        discount: res.discount,
      });
    }

    setState(showPromoPlans ? STATES.Success : STATES.Warning);
    setCode(promoCode);
  };

  const removeCode = () => {
    setCode('');
    setState(STATES.Input);
    resetSettings();
  };

  return (
    <>
      {state === STATES.Input && <PromoInput onSubmit={submitCode} />}
      {state === STATES.Success && <Success code={code} remove={removeCode} />}
      {state === STATES.Warning && <Warning remove={removeCode} />}
    </>
  );
};
