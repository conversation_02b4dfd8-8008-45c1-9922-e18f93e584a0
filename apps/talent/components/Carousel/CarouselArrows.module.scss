@use '@styles/mixins' as *;
@use '@styles/variables' as *;

.carousel-arrow-container {
  position: absolute;
  top: 0;
  height: 100%;
  width: 44px;
  display: flex;
  align-items: center;
  z-index: 2;

  &.right {
    right: 0;
  }

  &.left {
    left: 0;
  }
}

.carousel-arrow-icon {
  transition: all ease-in-out 0.3s;
  opacity: 0.6;

  &.left {
    transform: rotate(180deg);
  }
}

.carousel-arrow {
  background-color: transparent;
  border: none;
  cursor: pointer;
  transition: all ease-in-out 0.3s;
  width: 44px;
  display: flex;
  align-items: center;
  height: 100%;
  position: absolute;
  z-index: 2;
  top: 0;
  background-repeat: repeat-y;
  padding: 0;

  &:disabled {
    opacity: 0.6;
  }

  &.left {
    padding-left: 6px;
  }

  &.right {
    justify-content: flex-end;
    padding-right: 6px;
  }
}

.carousel-promos-mobile {
  .carousel-arrow-icon {
    width: 15px;
  }

  .carousel-arrow {
    filter: invert(1);

    &.left {
      background-image: $gradient-metallic-green-left;
    }

    &.right {
      background-image: $gradient-metallic-green-right;
    }
  }
}

.carousel-profile-gallery {
  .carousel-arrow-icon {
    filter: invert(1);
  }
}

.carousel-promos {
  &.right {
    right: -40px;
  }

  &.left {
    left: -40px;
  }

  .carousel-arrow-icon {
    width: 15px;
  }

  .carousel-arrow {
    padding: 0;
    width: 40px;
    opacity: 0.6;
  }
}

.carousel-main-images {
  z-index: 0;

  .carousel-arrow-icon {
    filter: invert(1);
    opacity: 1;
    width: 13px;
    height: 26px;
    z-index: 0;
  }

  .carousel-arrow {
    &.left {
      padding-left: $space-20;
      background: $gradient-carousel-arrow-left;
    }

    &.right {
      padding-right: $space-20;
      background: $gradient-carousel-arrow-right;
    }
  }
}

.carousel-arrow:hover {
  .carousel-arrow-icon {
    opacity: 1;
  }
}
