import ProfileClient from './_client';
import { cookies, headers } from 'next/headers';
import { notFound } from 'next/navigation';
import { getResolvedUrl } from '@utils/apiHelper';
import Api from '@services/api';
import { extractResult } from '@utils/extractPromiseResult';
import { isMobile } from '@utils/isMobile';
import { formatTalentProfile } from '@utils/formatTalentProfile';
import {
  getTalentProfileMobileMenuItems,
  getTalentProfileSideMenuItems,
} from '@utils/talentHelper';

export const metadata = {
  title: 'Profile',
};

export default async function ProfilePage(req) {
  const params = await req.params;
  const searchParams = await req.searchParams;
  const resolvedUrl = getResolvedUrl(`/profile/${params.id}`, searchParams);
  const cookieStore = await cookies();
  const headerStore = await headers();

  let id = params.id;

  if (isNaN(Number(id))) {
    const profileByUsername = await Api.serverside(
      `/personal-urls/${id}`,
      cookieStore,
      headerStore,
      resolvedUrl,
    );

    id = profileByUsername.links?.profile?.id;
  }

  if (!id) {
    notFound();
  }

  const profile = await Api.serverside(
    `/profiles/${id}?expand=categories,location`,
    cookieStore,
    headerStore,
    resolvedUrl,
  );

  const isFullProfile =
    profile?.firstname &&
    profile?.lastname &&
    profile?.birthday &&
    profile?.gender;

  // Intentionally
  if (profile.success === false || profile.active === false || !isFullProfile) {
    notFound();
  }

  const paths = [
    `/profiles/${id}/images?expand=profile_image_characteristics&width=600&height=800`,
    `/profiles/${id}/ethnicities`,
    `/profiles/${id}/socialities`,
    `/profiles/${id}/skills`,
    `/profiles/${id}/videos/youtubes`,
    `/profiles/${id}/credits`,
    `/profiles/${id}/audios`,
    `/profiles/${id}/attributes`,
    `/profiles/${id}/notes`,
    '/clientstatus/info',
  ];

  const results = await Promise.allSettled(
    paths.map((path) =>
      Api.serverside(path, cookieStore, headerStore, resolvedUrl),
    ),
  );

  const [
    images,
    ethnicities,
    socialNetworks,
    skills,
    youtubes,
    credits,
    audios,
    attributes,
    notes,
    clientStatus,
  ] = results.map((result) => extractResult(result, { items: [] }));

  const formattedProfile = formatTalentProfile(
    profile,
    images.items,
    ethnicities.items[0],
    notes.items,
    socialNetworks.items,
    skills.items,
    youtubes.items,
    credits.items,
    audios.items,
    attributes.items,
  );

  const userAgent = headerStore.get('user-agent');
  const isMobileFromUserAgent = isMobile(userAgent);

  return (
    <ProfileClient
      profile={formattedProfile}
      sideMenuItems={getTalentProfileSideMenuItems(formattedProfile)}
      defaultIsMobile={isMobileFromUserAgent}
      canContactTalent={
        clientStatus.info?.allowed_to?.hasOwnProperty('contact_talent') || false
      }
      menuItems={getTalentProfileMobileMenuItems(formattedProfile)}
    />
  );
}
