@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.notifications-form {
  width: 100%;
  display: flex;
  flex-direction: column;
  position: relative;

  @include tablet {
    max-width: 814px;
    border: 1px solid $grey-60;
    border-radius: 10px;
  }
}

.notifications-form-header {
  display: flex;
  justify-content: space-between;
  padding: $space-20 $space-10;
  border-bottom: 1px solid $grey-60;
  align-items: center;

  @include desktop {
    padding: $space-20 $space-50;
  }
}

.notifications-form-header-title {
  display: flex;
  align-items: center;
  gap: $space-10;
}

.notifications-form-content {
  display: flex;
  flex-direction: column;
  padding: 0;
  border-bottom: 1px solid $grey-60;
  gap: $space-20;
  text-align: left;

  @include tablet {
    padding: $space-20 0;
  }

  @include desktop {
    padding: $space-20 $space-50;
  }
}

.notifications-form-disclaimer {
  color: $red-60;
  font-weight: 400;
}

.notifications-form-field-container {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  gap: $space-10;
  border-top: 1px solid $grey-60;
  margin: 0;
  padding: $space-20 $space-10;
  width: 100%;

  @include desktop {
    border: none;
    margin: 0;
    padding: 0;
    gap: $space-40;
    flex-direction: row;
  }
}

.notifications-form-field-column {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: $space-10;
}

.notifications-form-actions {
  display: none;

  @include tablet {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: $space-20;
  }
}

.notifications-form-button {
  color: $blue-100;
  font-weight: 400;
  border: none;
  background-color: $white;
  font-size: 16px;
  cursor: pointer;

  &:disabled {
    opacity: 0.3;
    cursor: default;
  }
}

.notifications-allowed {
  font-weight: 700;
}

.notifications-form-description {
  background-color: $grey-10;
  padding: $space-20 $space-10;

  @include tablet {
    padding: 0 $space-10;
    background-color: $white;
  }

  @include desktop {
    padding: 0;
  }
}

.notifications-form-settings {
  display: flex;
  flex-direction: column;
  gap: 0;

  @include tablet {
    gap: $space-20;
  }
}

.notifications-form-header-separator,
.notifications-form-header-identifier {
  display: none;

  @include tablet {
    display: initial;
  }
}

.notifications-form-disclaimer-container {
  background-color: $grey-10;
  display: flex;
  flex-direction: column;
  gap: $space-10;
  padding: $space-20 $space-10;

  @include tablet {
    padding: 0 $space-10;
    background-color: $white;
  }

  @include desktop {
    padding: 0;
  }
}

.notifications-form-email-settings-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: $space-20 $space-10;
  border-bottom: 1px solid $grey-60;

  @include tablet {
    display: none;
  }
}

.notifications-form-email-settings {
  display: flex;
  gap: $space-5;
  align-items: center;
}

.notifications-form-email-settings-title {
  font-size: 18px;
  font-weight: 700;
}

.link {
  color: $blue-100;
  font-size: 16px;
  font-weight: 400;
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
}
