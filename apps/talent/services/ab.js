// AB service is synced with Iterable and BO by account id.
// Use only account id for authenticated users.
import { CookieService } from '@services/cookieService';

export const Ab = {
  groupName: {
    show_wizard_last_step: 'show_wizard_last_step',
  },

  async group(experimentName, identifier, headers = {}) {
    const response = await fetch(
      `${process.env.baseUrl}/api/ab?experimentName=${experimentName}&identifier=${identifier}`,
      { headers, method: 'GET' },
    );

    const data = await response.json();

    if (typeof window !== 'undefined') {
      CookieService.setExperimentsCookie(data);
    }

    return {
      group: data?.[experimentName]?.group || null,
      value: data || {},
    };
  },
};
