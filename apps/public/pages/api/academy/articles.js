import { ARTICLE_PAGE_LIMIT } from '../../../constants/articles';
import Api from '../../../services/api';

const generateUrl = (category, page, limit) => {
  const bcCategory = category === 'academy' ? 'lessons' : category;

  let url = `/articles?limit=${limit}&page=${page}`;

  if (undefined !== bcCategory) {
    url += `&categories=${bcCategory}`;
  }

  return url;
};

// ToDo: remove
export default async function handler(req, res) {
  const { category, page, limit = ARTICLE_PAGE_LIMIT } = req.query;
  const data = await Api.serverGateway(
    generateUrl(category, page, limit),
    req.headers,
  );

  res.status(200).json(data);
}
