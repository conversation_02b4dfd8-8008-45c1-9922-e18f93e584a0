'use client';
import React, { memo, useState } from 'react';
import styles from './Profile.module.scss';
import { CreditCard } from '@components';
import AddCredit from './Credits/AddCredit';
import creditCardStyles from './Credits/CreditCard.module.scss';
import cn from 'classnames';
import AddNewIcon from '../../public/assets/icons/icon-add.svg';
import { Amp } from '@services/amp';

const ProfileCredits = ({ credits, refreshCredits, updateProfileRating }) => {
  const [isCreditEditing, setCreditEditing] = useState(false);

  const openCreditEditing = () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'add credit',
      scope: Amp.element.scope.profile,
      section: Amp.element.section.credits,
      type: Amp.element.type.button,
    });
    setCreditEditing(true);
  };

  const closeCreditEditing = () => {
    setCreditEditing(false);
  };

  const closeCreditEditingAndRefresh = async () => {
    await refreshCredits();
    setCreditEditing(false);
  };

  return (
    <>
      {credits?.length ? (
        <>
          {credits.map(({ id, title, description, month, year, company }) => (
            <CreditCard
              key={id}
              id={id}
              title={title}
              description={description}
              month={month}
              year={year}
              company={company?.title}
              refreshCredits={refreshCredits}
              updateProfileRating={updateProfileRating}
            />
          ))}
          {!isCreditEditing && (
            <>
              <div
                className={cn(
                  styles['profile-section-empty'],
                  creditCardStyles['empty-block'],
                )}
                onClick={openCreditEditing}
              >
                <span
                  className={cn(
                    styles['profile-section-empty-add-button'],
                    creditCardStyles['add-btn'],
                  )}
                >
                  <AddNewIcon className={creditCardStyles['icon-add-new']} />
                  Add More
                </span>
              </div>
            </>
          )}
        </>
      ) : (
        <>
          {!isCreditEditing && (
            <div
              className={styles['profile-section-empty']}
              onClick={openCreditEditing}
            >
              Please list any acting, modeling or entertainment experience you
              have.
              <span className={styles['profile-section-empty-add-button']}>
                Add
              </span>
            </div>
          )}
        </>
      )}
      {isCreditEditing && (
        <AddCredit
          onClose={closeCreditEditing}
          onCloseAndRefresh={closeCreditEditingAndRefresh}
          updateProfileRating={updateProfileRating}
        />
      )}
    </>
  );
};

export default memo(ProfileCredits);
