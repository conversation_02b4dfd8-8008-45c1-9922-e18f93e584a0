@use '@styles/variables' as *;

.container {
  display: flex;
  position: relative;
  padding-left: 24px;
  cursor: pointer;
  font-size: 22px;
  user-select: none;
  align-content: center;
  width: 100%;

  &:has(input:disabled) {
    color: $grey-80;
  }
}

.container input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkmark {
  position: absolute;
  top: 0;
  left: 0;
  background-color: $white;
  border: 1px solid $grey-80;
  width: 16px;
  height: 16px;
}

.checkmark::after {
  content: '';
  position: absolute;
  display: none;
}

.container .checkmark::after {
  left: 4px;
  top: 0;
  width: 3px;
  height: 8px;
  border: solid $white;
  border-top-left-radius: 5px;
  border-width: 0 2.5px 2.5px 0;
  transform: rotate(45deg);
}

.error {
  .checkmark {
    border: 1px solid $red-80;
  }
}

.container input:checked ~ .checkmark {
  background-color: $black;
  border: 1px solid transparent;
}

.container input:checked ~ .checkmark::after {
  display: block;
}
