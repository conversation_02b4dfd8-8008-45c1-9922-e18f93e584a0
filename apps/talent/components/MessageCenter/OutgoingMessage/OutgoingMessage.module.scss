@use '@styles/mixins' as *;
@use '@styles/variables' as *;

$message-interaction-width: $space-25;
$message-interaction-width-full: 150px;

.message-row {
  display: flex;
  max-width: 100%;
  margin-bottom: 6px;
}

.message-meta {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  font-size: 12px;
  line-height: 1;
  margin-bottom: 8px;
  color: $grey-80;
  font-weight: 300;
  gap: $space-5;
  padding: 0 $space-20;
}

.attachment-container {
  display: flex;
  flex-flow: row wrap;
  gap: 8px;
  justify-content: flex-end;
}

.message-wrapper {
  flex-direction: row;
  display: flex;
  align-items: center;
  width: 100%;

  .attachment-container,
  .message-meta {
    margin-left: $message-interaction-width;

    @include tablet {
      margin-left: $message-interaction-width-full;
    }
  }
}

.message-body {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.message-content {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  width: 100%;

  .message-interaction {
    .interaction-btn {
      align-items: center;
      gap: $space-5;
      white-space: nowrap;
      cursor: pointer;

      @include desktop {
        visibility: hidden;
      }

      &:hover {
        text-decoration: underline;
      }
    }
  }

  @include tablet {
    &:hover {
      .message-interaction {
        .interaction-btn {
          visibility: visible;
        }
      }
    }
  }
}

.message {
  flex: 1;
  max-width: 100%;
  padding: $space-20;
  overflow-wrap: word-break;
  word-break: word-break;
  hyphens: auto;
  margin-bottom: $space-10;
  border-radius: 10px 10px 0;
  background: $gradient-capri;
  color: $white;
  align-content: flex-end;
  margin-left: $message-interaction-width;

  &.is-reported {
    font-style: italic;
    color: $grey-60;
  }
}

.attachment {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  margin-bottom: 8px;
  border: 0.5px solid $grey-40;
  padding: 1px;
}

.interaction-btn-text {
  display: none;
  margin-left: $space-5;

  @include desktop {
    display: inline;
  }
}

.report-btn {
  color: $red-60;
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
}

.inline-link {
  text-decoration: underline;
}
