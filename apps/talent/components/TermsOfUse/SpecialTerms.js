'use client';
import styles from '../TermsOfUse/TermsOfUse.module.scss';
import { ScrollTopButton } from '@components';
import SupportEmailLink from './SupportEmailLink';

const SpecialTerms = ({ containerRef }) => {
  return (
    <>
      <div>
        <span className={styles['terms-title']}>24. SPECIAL TERMS.</span>
        {containerRef && (
          <ScrollTopButton containerRef={containerRef} type={'text'} />
        )}
      </div>
      <p>
        <strong>
          You, the buyer, may cancel this Agreement, without penalty or
          obligation, at any time prior to midnight of the original contract
          seller&apos;s tenth business day following the original date of this
          contract, excluding Saturdays, Sundays and holidays. To cancel this
          Agreement, email, mail or deliver a signed and dated notice, or send a
          telegram which states that you, the buyer, are canceling this
          Agreement, or words of similar effect. This notice shall be sent to:
          AllCasting.com, Attn: Cancellations, 300 Delaware Ave, Suite 210,
          Wilmington, DE 19801 or <SupportEmailLink />. Please include your
          AllCasting.com username and email address in any correspondence or
          your refund may be delayed. If you send or deliver the notice to
          cancel your subscription agreement within such ten day period, we will
          refund the full amount of your subscription.
        </strong>
      </p>
      <p>
        <strong>
          In the event that you die before the end of your subscription period,
          your estate shall be entitled to a refund of that portion of any
          payment you had made for your subscription which is allocable to the
          period after your death. In the event that you become disabled (such
          that you are unable to use the services of AllCasting.com) before the
          end of your subscription period, you shall be entitled to a refund of
          that portion of any payment you had made for your subscription which
          is allocable to the period after your disability.
        </strong>
      </p>
    </>
  );
};

export default SpecialTerms;
