@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.toggle-button {
  border: none;
  background: transparent;
  font-size: 14px;
  line-height: 19px;
  font-weight: 700;
  color: $blue-100;
  cursor: pointer;

  &.inline {
    font-size: inherit;
  }

  &:hover {
    text-decoration: underline;
  }
}

.promo {
  width: 100%;
  display: flex;
  gap: $space-40;
  height: 76px;
  background-color: $grey-10;
  border-radius: 16px;
  padding: 0 $space-30;
  margin-top: $space-25;

  &.success {
    background-color: $green-10;
    align-items: center;
    gap: $space-20;
  }

  &.warning {
    background-color: $yellow-10;
  }

  @include desktop {
    height: 96px;
    padding: 0 $space-50;
  }
}

.promo-form {
  width: 100%;
}

.promo-text {
  display: flex;
  flex-direction: column;
  justify-content: center;
  font-size: 12px;
  flex-grow: 1;

  &.success {
    color: $green-100;
  }

  &.warning {
    color: $yellow-100;
  }

  & .promo-code {
    font-size: 14px;
    font-weight: 700;

    @include desktop {
      font-size: 16px;
    }
  }
}

.promo-button {
  border: none;
  background-color: inherit;
  color: $blue-100;
  font-size: 14px;
  font-weight: 700;
  cursor: pointer;

  &.success {
    color: $red-60;
  }

  &.warning {
    color: $red-60;
  }

  &.disabled {
    opacity: 0.7;
    cursor: unset;
  }

  &:hover:not(:disabled) {
    text-decoration: underline;
  }

  @include desktop {
    font-size: 16px;
  }
}
