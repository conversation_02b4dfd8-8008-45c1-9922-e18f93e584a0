'use client';
import React, { memo } from 'react';
import styles from './MenuItem.module.scss';
import Link from 'next/link';
import cn from 'classnames';
import { usePathname } from 'next/navigation';

const MenuItem = ({ label, href }) => {
  const path = usePathname();

  return (
    <Link
      href={href}
      className={cn(styles['navbar-link'], {
        [styles.active]: path.startsWith(href),
      })}
    >
      {label}
    </Link>
  );
};

export default memo(MenuItem);
