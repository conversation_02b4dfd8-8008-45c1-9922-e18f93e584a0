@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.tooltip-container {
  padding: $space-20;
}

.profile-panel-info-title {
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  font-size: 12px;
  font-weight: 700;
  margin-bottom: 2px;
}

.profile-panel-info-text {
  font-size: 12px;
  font-weight: 500;
  line-height: 1.3;
  margin-bottom: $space-10;
}

.profile-panel-info-link {
  color: $blue-100;
  font-weight: 500;
  font-size: 12px;
  line-height: 1.3;
  align-items: baseline;

  &::after {
    content: '';
    display: inline-flex;
    width: 15px;
    height: 8px;
    margin-left: 3px;
    background: url('#{$assetUrl}/assets/icons/icon-arrow-right-blue.svg') 0 0
      no-repeat;
  }
}

.profile-panel-info-close {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 12px;
  height: 12px;
  cursor: pointer;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    right: 0;
    left: 0;
    margin: 0 auto;
    width: 100%;
    height: 1px;
    background-color: $grey-60;
    transform: rotate(45deg);
  }

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 0;
    left: 0;
    margin: 0 auto;
    width: 100%;
    height: 1px;
    background-color: $grey-60;
    transform: rotate(-45deg);
  }
}
