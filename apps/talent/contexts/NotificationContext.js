'use client';
import React, { createContext, useContext, useState } from 'react';

const NotificationContext = createContext({});

export const NotificationProvider = ({ children }) => {
  const [showNotification, setNotification] = useState({
    message: null,
  });

  const clearNotification = () => {
    setNotification({
      message: null,
    });
  };

  return (
    <NotificationContext.Provider
      value={{
        showNotification,
        setNotification,
        clearNotification,
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotifications = () => useContext(NotificationContext);
