'use client';
import React, { memo, useCallback, useEffect, useState } from 'react';
import useEmblaCarousel from 'embla-carousel-react';
import styles from './Carousel.module.scss';
import Autoplay from 'embla-carousel-autoplay';
import cn from 'classnames';
import { CarouselPaginatorDot } from './CarouselPaginatorDot';
import { CarouselArrow } from './CarouselArrows';

const Carousel = ({
  watchDrag = true, // enable drag
  delay = 5000, // autoplay next slide delay in milliseconds
  stopOnMouseEnter = true, // stop autoplay while slide hovered
  stopOnInteraction = false, // stop autoplay on any interaction completely
  playOnInit = false, // start autoplay on init
  loop = false, // infinite loop
  slidesToScroll = 'auto', // number of slides grouped together (auto | number)
  dragFree = false, // drag free
  containScroll = 'trimSnaps', // clear leading and trailing empty space (trimSnaps | keepSnaps)
  speed = 10, // scroll speed (1-20)
  startIndex = 0, // initial slide index
  enablePagination = false,
  enableArrowNavigation = false,
  className = '',
  children,
  onSlideChange,
  paginationClassName = '',
  paginationColor = 'violet', // violet | white
}) => {
  const [viewportRef, emblaApi] = useEmblaCarousel(
    {
      dragFree,
      loop,
      slidesToScroll,
      containScroll,
      speed,
      startIndex,
      watchDrag,
    },
    [
      Autoplay({
        rootNode: (emblaRoot) => emblaRoot.parentElement,
        delay,
        stopOnMouseEnter,
        stopOnInteraction,
        playOnInit,
      }),
    ],
  );

  const [selectedIndex, setSelectedIndex] = useState(0);
  const [scrollSnaps, setScrollSnaps] = useState([]);
  const [prevBtnEnabled, setPrevBtnEnabled] = useState(false);
  const [nextBtnEnabled, setNextBtnEnabled] = useState(false);

  const scrollTo = useCallback(
    (index) => emblaApi && emblaApi.scrollTo(index),
    [emblaApi],
  );

  const scrollPrev = useCallback(
    () => emblaApi && emblaApi.scrollPrev(),
    [emblaApi],
  );
  const scrollNext = useCallback(
    () => emblaApi && emblaApi.scrollNext(),
    [emblaApi],
  );

  const onSelect = useCallback(() => {
    if (!emblaApi) {
      return;
    }

    setScrollSnaps(emblaApi.scrollSnapList());
    setSelectedIndex(emblaApi.selectedScrollSnap());
    setPrevBtnEnabled(emblaApi.canScrollPrev());
    setNextBtnEnabled(emblaApi.canScrollNext());
  }, [emblaApi, setSelectedIndex]);

  useEffect(() => {
    if (!emblaApi) {
      return;
    }

    onSelect();
    setScrollSnaps(emblaApi.scrollSnapList());
    emblaApi.on('select', onSelect);
    emblaApi.on('reInit', onSelect);
  }, [emblaApi, setScrollSnaps, onSelect]);

  useEffect(() => {
    if (onSlideChange) {
      onSlideChange(selectedIndex);
    }
  }, [selectedIndex]);

  return (
    <>
      <div className={cn(styles.embla, styles[className])}>
        <div className={styles['embla-viewport']} ref={viewportRef}>
          <div className={styles['embla-container']}>
            {children.map((slide, index) => (
              <div
                className={cn(styles['embla-slide'], {
                  [styles['embla-slide-single']]: children.length === 1,
                })}
                key={index}
              >
                {slide}
              </div>
            ))}
          </div>
          {enableArrowNavigation && (
            <div className={styles['arrow-navigation']}>
              <CarouselArrow
                onClick={scrollPrev}
                disabled={!prevBtnEnabled}
                direction="left"
                className={className}
              />
              <CarouselArrow
                onClick={scrollNext}
                disabled={!nextBtnEnabled}
                direction="right"
                className={className}
              />
            </div>
          )}
        </div>
      </div>
      {enablePagination && (
        <div
          className={cn(
            styles['paginator-container'],
            styles[paginationClassName],
          )}
        >
          {scrollSnaps.map((_, index) => (
            <CarouselPaginatorDot
              key={index}
              selected={index === selectedIndex}
              onClick={() => scrollTo(index)}
              paginationColor={paginationColor}
            />
          ))}
        </div>
      )}
    </>
  );
};

export default memo(Carousel);
