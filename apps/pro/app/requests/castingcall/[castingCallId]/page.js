import RequestsClient from './_client';
import Api from '@services/api';
import { extractResult } from '@utils/extractPromiseResult';
import { getResolvedUrl } from '@utils/apiHelper';
import { cookies, headers } from 'next/headers';
import { CookieServiceServer } from '@services/cookieServiceServer';
import {
  getActiveCastingCall,
  getCastingCallGroups,
  prepareCastingCalls,
} from '@utils/submissionsHelper';

export const metadata = {
  title: 'Requests',
};

export default async function RequestsPage(req) {
  const params = await req.params;
  const searchParams = await req.searchParams;
  const resolvedUrl = getResolvedUrl(
    'castingcall',
    { castingcall: params.castingCallId },
    searchParams,
  );
  const cookieStore = await cookies();
  const headerStore = await headers();
  const userProfiles = CookieServiceServer.getUserProfilesCookie(cookieStore);
  const clientId = userProfiles.length ? userProfiles[0].clientId || 0 : 0;
  const castingCallId = params.castingCallId ? Number(params.castingCallId) : 0;

  const paths = [
    `/accounts/${clientId}/castingcalls?es=1&context[]=for_submissions&filter[state]=active&filter[status][]=approved`,
    `/accounts/${clientId}/castingcalls?es=1&context[]=for_submissions&filter[status][]=approved`,
    `/accounts/${clientId}/castingcalls?es=1&context[]=for_submissions&filter[status][]=new`,
    `/accounts/${clientId}/castingcalls?es=1&context[]=for_submissions&filter[status][]=hold`,
    `/accounts/${clientId}/castingcalls?es=1&context[]=for_submissions&filter[status][]=edited`,
    `/accounts/${clientId}/castingcalls?es=1&context[]=for_submissions&filter[status][]=declined`,
    `/accounts/${clientId}/castingcalls?es=1&context[]=for_submissions&filter[status][]=deactivated`,
  ];

  const results = await Promise.allSettled(
    paths.map((path) =>
      Api.serverside(path, cookieStore, headerStore, resolvedUrl),
    ),
  );

  const [
    runningCastingCallsResponse,
    approvedCastingCallsResponse,
    newCastingCallsResponse,
    holdCastingCallsResponse,
    editedCastingCallsResponse,
    declinedCastingCallsResponse,
    deactivatedCastingCallsResponse,
  ] = results.map((result) => extractResult(result, {}));

  const castingCalls = prepareCastingCalls(
    runningCastingCallsResponse,
    approvedCastingCallsResponse,
    newCastingCallsResponse,
    holdCastingCallsResponse,
    editedCastingCallsResponse,
    declinedCastingCallsResponse,
    deactivatedCastingCallsResponse,
  );

  const castingCallGroups = getCastingCallGroups(castingCalls);
  const activeCastingCall = getActiveCastingCall(
    castingCalls,
    castingCallGroups,
    castingCallId,
  );

  return (
    <RequestsClient
      castingCalls={castingCallGroups}
      activeCastingCall={activeCastingCall}
    />
  );
}
