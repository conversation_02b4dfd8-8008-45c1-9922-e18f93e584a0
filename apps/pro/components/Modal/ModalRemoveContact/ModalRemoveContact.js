'use client';
import React, { memo, useState } from 'react';
import { Loading, Modal } from '@components';
import styles from './ModalRemoveContact.module.scss';
import Api from '@services/api';
import { useNotifications } from '@contexts/NotificationContext';
import { Amp } from '@services/amp';
import { useAuth } from '@contexts/AuthContext';
import { useContacts } from '@contexts/ContactContext';
import { ErrorMessage } from '@constants/form';

const ModalRemoveContact = ({
  contactId,
  onClose = () => {},
  amplitudeScope,
  extendsContactsModal = false,
  onRemoveFromContactsModal = () => {},
}) => {
  const [loading, setLoading] = useState(false);
  const { setNotification } = useNotifications();
  const { profileId } = useAuth();
  const { removeContact } = useContacts();

  const onRemove = async () => {
    if (extendsContactsModal) {
      onRemoveFromContactsModal();
    } else {
      setLoading(true);

      Amp.track(Amp.events.elementClicked, {
        name: 'remove from contacts',
        scope: amplitudeScope,
        section:
          amplitudeScope === Amp.element.scope.global
            ? Amp.element.section.contactsPopup
            : Amp.element.section.head,
        type: Amp.element.type.button,
      });

      const response = await Api.clientside(
        `/profiles/${profileId}/contacts?profile=${contactId}`,
        {
          method: 'DELETE',
        },
      );

      if (response.status !== 'removed') {
        setNotification({
          type: 'error',
          timeout: '5000',
          message: response.message || ErrorMessage.Unexpected,
        });
      } else {
        await removeContact(contactId);
      }

      onClose();
      setLoading(false);
    }
  };

  return (
    <Modal
      onClose={onClose}
      backdropClose
      showDefaultLayout={false}
      classNameContainer={styles['modal-container']}
    >
      {loading ? (
        <div className={styles['loading-container']}>
          <div>Removing contact...</div>
          <Loading minHeight="40px" padding="0" />
        </div>
      ) : (
        <>
          <div className={styles.container}>
            <span>
              Are you sure you want to
              <br />
              <b>remove this talent</b> from your contacts?
            </span>
            <span>
              This will also remove them from
              <br />
              <b>any other group</b> you have.
            </span>
          </div>
          <div className={styles.actions}>
            <button onClick={onClose} className={styles.action}>
              Cancel
            </button>
            <button onClick={onRemove} className={styles.action}>
              Remove
            </button>
          </div>
        </>
      )}
    </Modal>
  );
};

export default memo(ModalRemoveContact);
