@use '@styles/mixins' as *;
@use '@styles/variables' as *;

.audio-upload-modal {
  width: 100%;

  @include tablet {
    max-width: 500px;
  }
}

.audio-upload-modal-overlay {
  padding: 0;
  align-items: flex-end;

  @include tablet {
    padding: 0 $space-5;
    align-items: center;
  }
}

.audio-upload-modal-content {
  display: flex;
  flex-direction: column;
  gap: $space-40;
  justify-content: center;
  text-align: center;

  @include tablet {
    padding: 0 $space-30;
  }
}

.audio-container {
  border-radius: 5px;
  background-color: $grey-10;
  padding: $space-20;
}

.replace-btn {
  color: $blue-100;
  font-size: 14px;
  font-weight: 700;
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
}
