@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.mobile-header {
  position: sticky;
  top: 0;
  width: 100%;
  padding: $space-15 $space-20;
  display: grid;
  grid-template-columns: 0.2fr 1fr 0.2fr;
  background-color: $white;
  z-index: $z-index-mobile-header;

  &.show-border {
    border-bottom: 1px solid $grey-60;
  }

  &.show-shadow {
    box-shadow: $shadow-mobile-header;
  }

  &.hide-on-tablet {
    @include tablet {
      display: none;
    }
  }

  &.show-only-title {
    grid-template-columns: 1fr;
  }

  &.show-action-icon {
    grid-template-columns: 0.2fr 1fr;
    grid-column-gap: $space-10;
  }

  @include xsmall {
    grid-template-columns: 0.5fr 1fr 0.5fr;
  }

  @include desktop {
    display: none;
  }
}

.title {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: center;
}

.button-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.button {
  color: $blue-100;
  font-weight: 400;
  border: none;
  background-color: $white;
  font-size: 16px;
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }

  &:disabled {
    opacity: 0.3;
    cursor: default;

    &:hover {
      text-decoration: none;
    }
  }

  @include xsmall {
    padding: 0;
  }
}

.icon-container {
  display: flex;
  align-items: center;
}

.icon {
  transform: rotate(180deg);
  cursor: pointer;
}

.action-icon-container {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: $space-10;
  overflow: hidden;
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
}
