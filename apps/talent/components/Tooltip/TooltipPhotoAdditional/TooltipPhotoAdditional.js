'use client';
import React, { memo } from 'react';
import styles from './TooltipPhotoAdditional.module.scss';
import { Button } from '@components';
import { useRouter } from 'next/navigation';
import { Amp } from '@services/amp';
import useTrackElementActions from '@utils/useTrackElementActions';
import imageAttributesMale from '../../../public/assets/tooltip/image-photo-additional-male.webp';
import imageAttributesFemale from '../../../public/assets/tooltip/image-photo-additional-female.webp';
import Image from 'next/image';

const TooltipPhotoAdditional = ({ genderTitle = 'male' }) => {
  const router = useRouter();

  const { onTrackClick } = useTrackElementActions({
    name: 'Unlock additional photos popup',
    type: Amp.element.type.popup,
    context: Amp.element.context.ctaPopup,
    autoTrackEnabled: true,
  });

  const onButtonClick = () => {
    onTrackClick();

    router.push('/upgrade');
  };

  return (
    <div className={styles['tooltip-container']}>
      <span className={styles.title}>Additional Photos Feature</span>
      <p className={styles['description']}>
        Maximize your chances of success as a talent. More photos mean more
        opportunities to impress casting directors. Showcase your versatility
        and uniqueness. Add more photos now to stand out and succeed!
      </p>
      <Image
        className={styles.image}
        src={
          genderTitle === 'male' ? imageAttributesMale : imageAttributesFemale
        }
        alt="additional image"
      />
      <div className={styles['button-container']}>
        <Button
          className={styles['button-unblock']}
          label="Subscribe Now"
          shadow={false}
          color="blue"
          minWidth="75px"
          onClick={onButtonClick}
        />
      </div>
    </div>
  );
};

export default memo(TooltipPhotoAdditional);
