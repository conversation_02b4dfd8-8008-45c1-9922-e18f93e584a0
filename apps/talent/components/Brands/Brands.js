'use client';
import Image from 'next/image';
import styles from './Brands.module.scss';
import MTV from '../../public/assets/brands/mtv.svg';
import NintendoBW from '../../public/assets/brands/nintendo-bw.svg';
import FoxBW from '../../public/assets/brands/fox-bw.svg';
import ParamountBW from '../../public/assets/brands/paramount-bw.svg';
import NYFW from '../../public/assets/brands/nyfw.svg';
import QueerEye from '../../public/assets/brands/queer-eye.webp';
import QueerEyeBw from '../../public/assets/brands/queer-eye-bw.webp';
import CatfishBw from '../../public/assets/brands/catfish-bw.webp';
import MMG from '../../public/assets/brands/mmg-bw.webp';

export const CatfishLogo = () => (
  <div className={styles.catfish}>
    <MTV />
    <Image
      src={CatfishBw}
      alt="catfish tv show logo"
      sizes="(max-width: 767px) 15vw, 62px"
    />
  </div>
);

export const NintendoLogo = ({ bw = false, width = 93 }) => (
  <NintendoBW width={width} fill={bw ? null : '#E60012'} />
);

export const FoxLogo = ({ bw = false, width = 37 }) => (
  <FoxBW width={width} fill={bw ? null : '#003998'} />
);

export const ParamountLogo = ({ bw = false, width = 36 }) => (
  <ParamountBW width={width} fill={bw ? null : '#016BB1'} />
);

export const NyfwLogo = ({ width = '53' }) => <NYFW width={width} />;

export const QueerEyeLogo = ({ bw = false, width }) => {
  const style = width
    ? {
        width,
        height: 'auto',
      }
    : {};

  return (
    <Image
      style={style}
      src={bw ? QueerEyeBw : QueerEye}
      alt="queer eye logo"
      sizes={`${width}px`}
    />
  );
};

export const MMGLogo = ({ width }) => {
  const style = width
    ? {
        width,
        height: 'auto',
      }
    : {};

  return <Image style={style} src={MMG} alt="mmg logo" sizes={`${width}px`} />;
};

export const Brands = ({}) => (
  <div className={styles.brands}>
    <FoxLogo bw />
    <CatfishLogo />
    <QueerEyeLogo width={44} bw />
    <NintendoLogo bw />
    <NyfwLogo />
    <ParamountLogo bw />
    <MMGLogo width={42} />
  </div>
);
