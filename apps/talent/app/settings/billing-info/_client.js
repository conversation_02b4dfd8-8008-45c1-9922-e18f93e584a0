'use client';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import styles from '@styles/billing-info.module.scss';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import dayjs from 'dayjs';
import cn from 'classnames';
import {
  Button,
  EditPaymentForm,
  Loading,
  MainLayout,
  Modal,
  Settings,
  SubscriptionPlanDescription,
} from '@components';
import ModalDeletePaymentInfo from '@components/Modal/ModalDeletePaymentInfo/ModalDeletePaymentInfo';
import { useAuth } from '@contexts/AuthContext';
import { Amp } from '@services/amp';
import Api from '@services/api';
import { formatBillingInfo } from '@utils/formatBillingInfo';
import { formatAccountLevelsResponse } from '@utils/formatAccountLevelsResponse';
import { profileFeatures } from '@constants/payment';
import IconEdit from '../../../public/assets/icons/icon-edit.svg';

const BillingInfoClient = ({
  initialSubscription,
  upgradeLevel,
  initialBillingInfo,
}) => {
  const [showEditPaymentInfoForm, setShowEditPaymentInfoForm] = useState(false);
  const [showDeletePaymentInfoModal, setShowDeletePaymentInfoModal] =
    useState(false);
  const [activeSubscription, setActiveSubscription] =
    useState(initialSubscription);
  const [billingPortal, setBillingPortal] = useState();
  const [billingInfo, setBillingInfo] = useState({ ...initialBillingInfo });
  const router = useRouter();
  const [saveButtonDisabled, setSaveButtonDisabled] = useState(false);
  const contentRef = useRef(null);
  const { accountLevel, accountId, getIsProfileOutsideUSA } = useAuth();
  const isProfileOutsideUSA = getIsProfileOutsideUSA();
  const isStripe = billingInfo?.method === 'stripe';

  const toggleSaveButtonDisabled = (disabled) => {
    setSaveButtonDisabled(disabled);
  };

  const triggerSubmit = async () => {
    await contentRef.current.triggerSubmit();
  };

  useEffect(() => {
    if (accountId && isStripe) {
      getBillingPortalSession().catch();
    }
  }, [accountId]);

  const closeEditPaymentInfoForm = () => {
    setShowEditPaymentInfoForm(false);
  };

  const openEditPaymentInfoForm = () => {
    Amp.track(Amp.events.elementClicked, {
      name: `edit payment info`,
      scope: Amp.element.scope.settingsBillingInfo,
      type: Amp.element.type.button,
      payment_method: isStripe ? 'stripe' : 'card',
    });

    if (!isStripe) {
      setShowEditPaymentInfoForm(true);
    } else {
      window.location = billingPortal;
    }
  };

  const getBillingInfo = async () => {
    const billingInfoResponse = await Api.clientside(`/payment/billing/`);

    setBillingInfo(formatBillingInfo(billingInfoResponse));
  };

  const getBillingPortalSession = async () => {
    const body = new FormData();

    body.append('account_id', accountId);

    const stripePortalResponse = await Api.clientside(
      `/stripe/billing-portal-session`,
      {
        body: body,
        method: 'POST',
      },
    );

    setBillingPortal(stripePortalResponse.session_url || null);
  };

  const refreshBillingAndSubscription = async () => {
    const [billingInfoResponse, accountLevelsResponse] = await Promise.all([
      Api.clientside(`/payment/billing/`),
      Api.clientside(`/accounts/${accountId}/levels/last`),
    ]);

    setBillingInfo(billingInfoResponse);
    setActiveSubscription(formatAccountLevelsResponse(accountLevelsResponse));
  };

  const updatePaymentInfo = async () => {
    await getBillingInfo();
    closeEditPaymentInfoForm();
  };

  const goToUpgradePage = () => {
    router.push('/upgrade');
  };

  const upgradeLevelData = useCallback(() => {
    return (
      <>
        You will be switched to{' '}
        {upgradeLevel.links.level.sale_interval_description} on{' '}
        <strong>{dayjs(upgradeLevel.start).format('M/D/YY')}</strong>.
      </>
    );
  }, [upgradeLevel]);

  useEffect(() => {
    Amp.track(Amp.events.viewSettings, {
      name: 'billing info',
    });
  }, []);

  return (
    <MainLayout
      isDefaultHeaderVisible
      isSaleHeaderVisible
      isMobileMenuVisible
      isUserMenuVisible
    >
      <Settings
        title={
          showEditPaymentInfoForm ? 'Edit payment method' : 'Your Subscription'
        }
        showButton={showEditPaymentInfoForm}
        buttonLabel={showEditPaymentInfoForm && 'Save'}
        buttonDisabled={showEditPaymentInfoForm && saveButtonDisabled}
        onClick={showEditPaymentInfoForm && triggerSubmit}
        onClose={closeEditPaymentInfoForm}
      >
        <div className={styles['billing-info-container']}>
          <h1 className={styles['billing-info-title']}>Your Subscription</h1>
          {billingInfo && (
            <>
              <div className={styles['billing-info-card-container']}>
                <div className={styles['billing-info-card']}>
                  <div
                    className={styles['billing-info-payment-method-container']}
                  >
                    <div className={styles['billing-info-payment-method']}>
                      <div className={styles['billing-info-row']}>
                        <span className={styles['billing-info-row-title']}>
                          Name on card:
                        </span>
                        <span>{billingInfo.billingName || 'n/a'}</span>
                      </div>
                      <div className={styles['billing-info-row']}>
                        <span className={styles['billing-info-row-title']}>
                          Card type:
                        </span>
                        <span>
                          {billingInfo.number
                            ? `${
                                billingInfo?.number?.startsWith('4')
                                  ? 'Visa'
                                  : 'Mastercard'
                              }`
                            : 'n/a'}
                        </span>
                      </div>
                      <div className={styles['billing-info-row']}>
                        <span className={styles['billing-info-row-title']}>
                          Card number:
                        </span>
                        <span>{billingInfo?.number || 'n/a'}</span>
                      </div>
                      <div className={styles['billing-info-row']}>
                        <span className={styles['billing-info-row-title']}>
                          Expiration date:
                        </span>
                        <span>
                          {billingInfo?.exp_month && billingInfo?.exp_year
                            ? `${billingInfo?.exp_month}/${billingInfo?.exp_year}`
                            : 'n/a'}
                        </span>
                      </div>
                    </div>
                    {(!isStripe || billingPortal) && (
                      <div className={cn(styles['billing-info-actions'])}>
                        <button
                          onClick={openEditPaymentInfoForm}
                          className={styles['billing-info-button']}
                        >
                          <IconEdit width={17} /> Edit payment method
                        </button>
                      </div>
                    )}
                  </div>
                  <div className={styles['billing-info-column-right']}>
                    {!accountLevel?.isPaidOrDelayed ? (
                      <div className={styles['billing-info-plan']}>
                        <div className={styles['billing-info-plan-header']}>
                          <Image
                            src={'/assets/icons/icon-subscription.svg'}
                            alt="icon"
                            width={85}
                            height={45}
                          />
                          <span
                            className={styles['billing-info-plan-header-title']}
                          >
                            Subscribe now
                          </span>
                        </div>
                        <div className={styles['billing-info-plan-content']}>
                          <p>
                            Get full access and start applying to amazing jobs
                            in show business!
                          </p>
                          <div className={styles['subscribe-button-container']}>
                            <Button
                              label="Subscribe"
                              color="green-gradient"
                              minWidth="200px"
                              onClick={goToUpgradePage}
                            />
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className={styles['billing-info-plan']}>
                        <div className={styles['billing-info-plan-header']}>
                          {activeSubscription?.status === 'discontinued' ? (
                            <div
                              className={
                                styles[
                                  'billing-info-plan-description-discontinued'
                                ]
                              }
                            >
                              Subscription is <span>active</span>
                            </div>
                          ) : (
                            <>
                              <span>Subscription status</span>
                              <span
                                className={
                                  styles['billing-info-plan-description']
                                }
                              >
                                {activeSubscription?.plan.toLowerCase() ===
                                '120 month plan'
                                  ? 'Lifetime Subscription'
                                  : activeSubscription?.plan}
                              </span>
                            </>
                          )}

                          {accountLevel?.canUpgradeExistingSubscription && (
                            <div className={styles['upgrade-button-container']}>
                              <Link
                                href="/upgrade-12"
                                className={styles['upgrade-button']}
                              >
                                Upgrade your plan
                              </Link>
                            </div>
                          )}
                        </div>

                        <div className={styles['billing-info-plan-content']}>
                          {activeSubscription?.status === 'discontinued' ? (
                            <>
                              <div
                                className={
                                  styles['billing-info-plan-container']
                                }
                              >
                                <span
                                  className={
                                    styles[
                                      'billing-info-plan-title-discontinued'
                                    ]
                                  }
                                >
                                  Status:
                                </span>
                                <span
                                  className={cn(
                                    styles['billing-info-plan-status'],
                                    styles[activeSubscription?.status],
                                  )}
                                >
                                  Cancelled
                                </span>
                              </div>
                              <div
                                className={
                                  styles['billing-info-plan-container']
                                }
                              >
                                <span
                                  className={
                                    styles[
                                      'billing-info-plan-title-discontinued'
                                    ]
                                  }
                                >
                                  Expires on:
                                </span>
                                <span>
                                  {dayjs(activeSubscription?.endDate).format(
                                    'M/D/YY',
                                  )}
                                </span>
                              </div>
                            </>
                          ) : (
                            <>
                              {activeSubscription?.plan.toLowerCase() !==
                                '120 month plan' && (
                                <div
                                  className={
                                    styles['billing-info-plan-container']
                                  }
                                >
                                  <span
                                    className={
                                      styles['billing-info-plan-title']
                                    }
                                  >
                                    Period:
                                  </span>
                                  <span>
                                    {dayjs(
                                      activeSubscription?.startDate,
                                    ).format('M/D/YY')}{' '}
                                    –{' '}
                                    {dayjs(activeSubscription?.endDate).format(
                                      'M/D/YY',
                                    )}
                                  </span>
                                </div>
                              )}
                              <div
                                className={
                                  styles['billing-info-plan-container']
                                }
                              >
                                <span
                                  className={styles['billing-info-plan-title']}
                                >
                                  Amount:
                                </span>
                                <span>
                                  ${activeSubscription?.amount}
                                  {isProfileOutsideUSA && <>USD</>}
                                </span>
                              </div>
                              <div
                                className={
                                  styles['billing-info-plan-container']
                                }
                              >
                                <span
                                  className={styles['billing-info-plan-title']}
                                >
                                  Status:
                                </span>
                                <span
                                  className={cn(
                                    styles['billing-info-plan-status'],
                                    styles[activeSubscription?.status],
                                  )}
                                >
                                  {activeSubscription?.status}
                                </span>
                              </div>
                            </>
                          )}
                        </div>
                        {upgradeLevel && (
                          <div
                            className={cn(
                              styles['upgrade-level-start'],
                              styles['mobile'],
                            )}
                          >
                            {upgradeLevelData()}
                          </div>
                        )}
                        {activeSubscription?.status !== 'discontinued' &&
                          !upgradeLevel && (
                            <div
                              className={
                                styles['billing-discontinue-button-container']
                              }
                            >
                              <Link
                                href={'/settings/billing-info/discontinue'}
                                className={styles['billing-discontinue-button']}
                              >
                                <Image
                                  src={'/assets/icons/icon-close-3.svg'}
                                  alt="icon close"
                                  width={28}
                                  height={28}
                                />
                                <span>Discontinue subscription</span>
                              </Link>
                            </div>
                          )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
              {upgradeLevel && (
                <div
                  className={cn(
                    styles['upgrade-level-start'],
                    styles['desktop'],
                  )}
                >
                  {upgradeLevelData()}
                </div>
              )}
            </>
          )}
          {!billingInfo && <Loading />}
          {profileFeatures && (
            <div className={styles['plan-description']}>
              <SubscriptionPlanDescription
                features={profileFeatures}
                fullWidth
              />
            </div>
          )}
        </div>
        {showEditPaymentInfoForm && (
          <Modal
            backdropClose
            onClose={closeEditPaymentInfoForm}
            hideMobileCloseButton
            classNameOverlay={styles['edit-payment-modal-overlay']}
            classNameContainer={styles['edit-payment-modal']}
            classNameContent={styles['edit-payment-modal-content']}
            showDefaultLayout={false}
            contentOverflowHidden={false}
          >
            <EditPaymentForm
              ref={contentRef}
              toggleSaveButtonDisabled={toggleSaveButtonDisabled}
              nameOnCard={billingInfo?.billingName}
              zip={billingInfo?.zip}
              updatePaymentInfo={updatePaymentInfo}
            />
          </Modal>
        )}
        {showDeletePaymentInfoModal && (
          <ModalDeletePaymentInfo
            accountId={accountId}
            onClose={() => setShowDeletePaymentInfoModal(false)}
            refreshBillingAndSubscription={refreshBillingAndSubscription}
          />
        )}
      </Settings>
    </MainLayout>
  );
};

export default BillingInfoClient;
