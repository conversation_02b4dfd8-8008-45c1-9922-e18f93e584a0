@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.change-password-form-container {
  text-align: center;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;

  @include tablet {
    gap: $space-30;
    padding: $space-40 $space-20 0;
  }
}

.change-password-form-title {
  display: none;
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  line-height: 1.2;

  @include tablet {
    display: initial;
  }
}

.change-password-form {
  max-width: 814px;
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: $space-30 $space-20;
  position: relative;

  @include tablet {
    padding: $space-40 $space-30 0;
    border: 1px solid $grey-60;
    border-radius: 10px;
  }

  @include desktop {
    gap: $space-20;
  }
}

.change-password-form-row {
  display: flex;
  flex-direction: column;

  @include desktop {
    flex-direction: row;
    justify-content: space-between;
  }
}

.change-password-form-field {
  padding: $space-10 0;

  @include desktop {
    flex: 0 0 50%;
    padding: 0 $space-20;
  }
}

.change-password-form-actions {
  display: none;

  @include tablet {
    border-top: 1px solid $grey-60;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: $space-20;
    margin: $space-40 (-$space-30) 0;
  }

  @include desktop {
    margin: $space-20 (-$space-30) 0;
  }
}

.change-password-form-button {
  color: $blue-100;
  font-weight: 400;
  border: none;
  background-color: $white;
  font-size: 16px;
  cursor: pointer;

  &:disabled {
    opacity: 0.3;
    cursor: default;
  }
}

.change-password-form-error {
  padding-top: $space-10;
  margin-bottom: -$space-20;
  color: $red-80;
}
