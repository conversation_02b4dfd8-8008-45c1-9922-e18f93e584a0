'use client';
import { useEffect, useState } from 'react';
import { Attachments, Carousel, Loading, Modal } from '@components';
import IconFile from '../../public/assets/icons/icon-attachment.svg';
import IconCross from '../../public/assets/icons/icon-close-6.svg';
import Api from '@services/api';
import { useNotifications } from '@contexts/NotificationContext';
import styles from './FileListUpload.module.scss';
import { ErrorMessage } from '@constants/form';

const FileListUpload = ({ onUploadEnd, savedFiles, isFileUploading }) => {
  const [isUploading, setIsUploading] = useState(false);
  const [files, setFiles] = useState(savedFiles || []);
  const { setNotification } = useNotifications();
  const [showAttachmentGallery, setShowAttachmentGallery] = useState(false);
  const [clickedImageIndex, setClickedImageIndex] = useState(null);

  const uploadAttachments = async (e) => {
    if (e.target.files) {
      setIsUploading(true);
      isFileUploading(true);
      for (let i = 0; i < e.target.files.length; i++) {
        await uploadAttachment(e.target.files[i]);
      }
      setIsUploading(false);
      isFileUploading(false);
    }
  };

  useEffect(() => {
    onUploadEnd(files);
  }, [files]);
  const uploadAttachment = async (uploadedFile) => {
    if (uploadedFile.size >= 20000000) {
      setNotification({
        type: 'error',
        message: ErrorMessage.UploadFileSize.replace(
          '{Y}',
          uploadedFile.name,
        ).replace('{X}', '20MB'),
        timeout: '5000',
      });

      return;
    }

    const body = new FormData();

    if (uploadedFile) {
      body.append('file', uploadedFile, uploadedFile.name);
    }

    const response = await Api.clientside(`/calls/file`, {
      body,
      method: 'POST',
    });

    if (response.status !== 'ok') {
      setNotification({
        type: 'error',
        message: response.message || ErrorMessage.UploadFileFailed,
        timeout: '5000',
      });

      return;
    }

    const result = await response;

    setFiles((current) => {
      return [...current, result.data];
    });
  };

  const toggleShowGallery = () => {
    setShowAttachmentGallery(!showAttachmentGallery);
  };

  const onImageClick = (imageId) => {
    if (imageId) {
      const index = files
        .filter(({ content_type }) => content_type.split('/')[0] === 'image')
        .findIndex(({ id }) => id === imageId);

      setClickedImageIndex(index);
    }

    toggleShowGallery();
  };

  const removeFile = (id) => {
    setFiles((current) => {
      return current.filter((file) => file.id !== id);
    });
  };

  return (
    <div className={styles['container']}>
      <div className={styles['file-uploader']}>
        <input
          type="file"
          className={styles['file-input']}
          multiple
          name="file"
          onChange={uploadAttachments}
          disabled={isUploading}
        />
        {isUploading && <Loading />}
        {!isUploading && (
          <div className={styles['upload-text']}>
            <IconFile className={styles['icon']} />
            <span>Attach a file (20MB max)</span>
          </div>
        )}
      </div>
      <div className={styles['files']}>
        {files.map((file, i) => (
          <div className={styles['file']} key={i}>
            <Attachments
              attachment={file}
              openImageAttachment={onImageClick}
              className={styles['attached-file']}
            />
            <IconCross
              onClick={() => {
                removeFile(file.id);
              }}
              className={styles['delete-btn']}
            />
          </div>
        ))}
      </div>
      {showAttachmentGallery && (
        <Modal
          backdropClose
          onClose={toggleShowGallery}
          showCloseButton={false}
          showDefaultLayout={false}
          classNameContainer={styles['gallery-modal']}
          classNameContent={styles['gallery-modal-content']}
          containerClose
        >
          <Carousel
            enableArrowNavigation
            startIndex={clickedImageIndex}
            className="carousel-attachment-gallery"
            draggable={false}
            loop
          >
            {files
              .filter(
                ({ content_type }) => content_type.split('/')[0] === 'image',
              )
              .map(({ path }, index) => (
                <img
                  key={index}
                  src={path}
                  style={{ width: '100%' }}
                  alt={index}
                />
              ))}
          </Carousel>
        </Modal>
      )}
    </div>
  );
};

export default FileListUpload;
