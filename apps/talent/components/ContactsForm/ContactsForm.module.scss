@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.contacts-form-container {
  text-align: center;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;

  @include tablet {
    padding: 0 $space-20;
  }
}

.contacts-form {
  max-width: 814px;
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: $space-30 $space-20;
  position: relative;
  gap: $space-20;

  @include tablet {
    gap: $space-30;
    padding: $space-40 $space-30 0;
    border: 1px solid $grey-60;
    border-radius: 10px;
  }
}

.contacts-form-row {
  display: flex;
  flex-direction: column;
  gap: $space-40;

  @include desktop {
    gap: $space-20;
    flex-direction: row;
    justify-content: space-between;
  }
}

.contacts-form-field {
  padding: 0;
  display: flex;

  @include desktop {
    flex: 0 0 50%;
    padding: 0 $space-20;
    align-items: flex-end;
  }
}

.contacts-form-actions {
  display: none;

  @include tablet {
    border-top: 1px solid $grey-60;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: $space-20;
    margin: $space-40 (-$space-30) 0;
  }

  @include desktop {
    margin: $space-20 (-$space-30) 0;
  }
}

.contacts-form-button {
  color: $blue-100;
  font-weight: 400;
  border: none;
  background-color: $white;
  font-size: 16px;
  cursor: pointer;

  &:disabled {
    opacity: 0.3;
    cursor: default;
  }
}

.contacts-form-error {
  padding-top: $space-10;
  margin-bottom: -$space-20;
  color: $red-80;
}

.contacts-form-field-label {
  font-weight: 700;
  font-size: 14px;
  line-height: 18px;
}

.contacts-form-warning {
  font-size: 14px;
  font-weight: 400;
  display: flex;
  align-items: flex-end;
  padding: 0;
  text-align: left;

  @include desktop {
    flex: 0 0 calc(50% - 40px);
    margin-left: $space-20;
  }
}
