import CastingCallsClient from './_client';
import { getRequestUrl, getStatusFilter } from '@utils/castingCallFilterHelper';
import Api from '@services/api';
import { extractResult } from '@utils/extractPromiseResult';
import { getResolvedUrl } from '@utils/apiHelper';
import { cookies, headers } from 'next/headers';
import { CookieServiceServer } from '@services/cookieServiceServer';

export const metadata = {
  title: 'Casting Calls',
};

export default async function CastingCallsPage(req) {
  const searchParams = await req.searchParams;
  const resolvedUrl = getResolvedUrl(`/castingcalls`, searchParams);
  const cookieStore = await cookies();
  const headerStore = await headers();
  const page = 1;
  const userProfiles = CookieServiceServer.getUserProfilesCookie(cookieStore);
  const clientId = userProfiles.length ? userProfiles[0].clientId || 0 : 0;

  const { status } = searchParams;

  const paths = [
    getRequestUrl(status || '', page, clientId),
    '/clientstatus/info',
  ];

  const results = await Promise.allSettled(
    paths.map((path) =>
      Api.serverside(path, cookieStore, headerStore, resolvedUrl),
    ),
  );

  const [castingCalls, clientStatus] = results.map((result) =>
    extractResult(result, {}),
  );

  const castingCallsTotal = castingCalls?.data?.total || 0;
  const statusFilter = getStatusFilter(status || '');

  return (
    <CastingCallsClient
      castingCalls={castingCalls?.data?.calls || []}
      activeCastingCallsTotal={castingCalls?.data?.cc_active_count || 0}
      castingCallsTotal={castingCallsTotal}
      canContactTalent={
        clientStatus.info?.allowed_to?.hasOwnProperty('contact_talent') || false
      }
      page={page}
      statusFilter={statusFilter}
      showReset={
        statusFilter.options.some((option) => option.checked) || page > 1
      }
    />
  );
}
