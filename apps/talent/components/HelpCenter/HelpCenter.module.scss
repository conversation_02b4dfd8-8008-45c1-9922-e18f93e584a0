@use '@styles/mixins' as *;
@use '@styles/variables' as *;

.sidebar-container {
  background: $grey-10;
  width: 300px;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 50px;
  overflow: auto;
  transition: all 0.3s;
  transform: translate(-100%);
}

.sidebar-open {
  transform: translate(0);
}

.sidebar {
  display: flex;
  flex-flow: column nowrap;
  min-height: 100%;
}

.content {
  padding: $space-20;
  display: flex;
  flex-flow: column nowrap;
  min-height: 100%;
}

.title {
  font-size: 18px;
  margin-bottom: $space-15;
  font-weight: 700;
}

.list-title {
  color: rgb(0, 0, 0, 40%);
  font-size: 12px;
  text-transform: uppercase;
  margin-bottom: $space-20;
}

.menu-item {
  list-style: none;
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  margin: 0 (-$space-20) 4px;
  padding: 3px $space-20;
  color: $black;
  font-size: 14px;
  cursor: pointer;
  font-weight: 300;

  &:hover {
    text-decoration: none;
    background: rgb(255, 255, 255, 50%);
  }
}

.item-list-wrap {
  margin-bottom: $space-20;
}

.item-list {
  list-style: none;
  padding-inline-start: 0;
  padding: 0;
  margin: 0;
}

.icon {
  display: block;
  width: 24px;
  height: 24px;
  margin-right: $space-10;

  &.terms {
    background: url('#{$assetUrl}/assets/icons/help-center/icon-terms.svg');
  }

  &.policy {
    background: url('#{$assetUrl}/assets/icons/help-center/icon-policy.svg');
  }

  &.dnsmpi {
    background: url('#{$assetUrl}/assets/icons/help-center/icon-dnsmpi.svg');
  }

  &.scams {
    background: url('#{$assetUrl}/assets/icons/help-center/icon-scams.svg');
  }

  &.about {
    background: url('#{$assetUrl}/assets/icons/help-center/icon-about.svg');
  }

  &.contact {
    background: url('#{$assetUrl}/assets/icons/help-center/icon-contact.svg');
  }

  &.faq {
    background: url('#{$assetUrl}/assets/icons/help-center/icon-faq.svg');
  }

  &.labor-laws {
    background: url('#{$assetUrl}/assets/icons/help-center/icon-laborlaws.svg');
  }
}
