'use client';
import React, { memo, useState } from 'react';
import styles from '../ProfileConnections.module.scss';
import EditIcon from '../../../public/assets/icons/icon-edit.svg';
import Image from 'next/image';
import { useFormik } from 'formik';
import Api from '@services/api';
import * as Yup from 'yup';
import { useNotifications } from '@contexts/NotificationContext';
import { Input } from '@components';
import classNames from 'classnames';
import { hasFormValueChanged } from '@utils/hasFormValueChanged';
import { Amp } from '@services/amp';
import {
  ErrorMessage,
  NETWORK_REGEX,
  ABSOLUTE_PATH_REGEX,
} from '@constants/form';

const SocialNetworks = ({
  socialNetworks,
  profileId,
  refreshSocialNetworks,
}) => {
  const { setNotification } = useNotifications();
  const [isEditing, setEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const socialNetworkList = [
    { name: 'fb', placeholder: 'Facebook link' },
    { name: 'instagram', placeholder: 'Instagram link' },
    { name: 'twit', placeholder: 'X link' },
    { name: 'youtube', placeholder: 'Youtube link' },
    { name: 'website', placeholder: 'Link to your website' },
    { name: 'imdb', placeholder: 'IMDb link' },
    { name: 'vimeo', placeholder: 'Vimeo link' },
    { name: 'linkedin', placeholder: 'Linkedin link' },
    { name: 'flickr', placeholder: 'Flickr link' },
    { name: 'sag', placeholder: 'Sag-Afrta link' },
    { name: 'tiktok', placeholder: 'TikTok link' },
  ];

  const edit = () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'edit social networks',
      scope: Amp.element.scope.profile,
      section: Amp.element.section.socialNetworks,
      type: Amp.element.type.button,
    });
    setEditing(true);
  };
  const triggerSubmit = async () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'save social networks',
      scope: Amp.element.scope.profile,
      section: Amp.element.section.socialNetworks,
      type: Amp.element.type.button,
    });
    await formik.submitForm();
  };

  const formik = useFormik({
    initialValues: Object.fromEntries([
      ...socialNetworkList.map((curr) => [curr.name, '']),
      ...socialNetworks.map((curr) => [curr.network, curr.path]),
    ]),
    onSubmit: async (values) => {
      if (!hasFormValueChanged(values, formik.initialValues)) {
        setEditing(false);

        return;
      }

      Amp.track(Amp.events.elementClicked, {
        name: 'save social networks',
        scope: Amp.element.scope.editProfile,
        section: Amp.element.section.socialNetworkForm,
        type: Amp.element.type.button,
      });

      setLoading(true);

      const body = {
        items: Object.keys(values)
          .filter((key) => values[key])
          .map((key) => ({ network: key, path: values[key] })),
      };

      const response = await Api.clientside(
        `/profiles/${profileId}/socialities/batch`,
        {
          body: JSON.stringify(body),
          method: 'PUT',
        },
      );

      if (response.status !== 'ok') {
        setNotification({
          type: 'error',
          message: response.message || ErrorMessage.Unexpected,
          timeout: '5000',
        });
      } else {
        formik.resetForm({ values });

        await refreshSocialNetworks();
      }

      setEditing(false);
      setLoading(false);
    },
    validationSchema: Yup.object({
      website: Yup.string().matches(NETWORK_REGEX, ErrorMessage.NetworkPattern),
    }),
  });

  const getAbsolutePath = (path) => {
    return !ABSOLUTE_PATH_REGEX.test(path) ? 'http://' + path : path;
  };

  return (
    <>
      <section className={styles['profile-info-block']}>
        <div className={styles['profile-section-title']}>
          Social media:
          {!isEditing ? (
            <span onClick={edit} className={styles['edit-link']}>
              <EditIcon />
            </span>
          ) : (
            <button
              onClick={triggerSubmit}
              className={styles['save-button']}
              disabled={loading}
            >
              Save
            </button>
          )}
        </div>

        {!isEditing ? (
          <>
            {socialNetworks?.length ? (
              <>
                {socialNetworks.map(({ network, path, full_url }, index) => (
                  <div
                    className={classNames(
                      styles['soc-icon-link'],
                      styles['profile-info-row'],
                    )}
                    key={index}
                  >
                    <Image
                      src={`/assets/icons/social-networks/icon-${network}-1.svg`}
                      width={26}
                      height={26}
                      alt="social icon"
                      priority
                    />
                    <a
                      href={
                        network === socialNetworkList[4].name
                          ? getAbsolutePath(full_url)
                          : full_url
                      }
                      target="_blank"
                      rel="noreferrer"
                      className={classNames(
                        styles['profile-soc-link'],
                        styles['text-ellipsis'],
                      )}
                    >
                      {network !== socialNetworkList[4].name &&
                        path.at(0) !== '@' &&
                        '@'}
                      {path}
                    </a>
                  </div>
                ))}
              </>
            ) : (
              <div className={styles['profile-section-empty']}>
                Add links to your social accounts.
                <span
                  onClick={edit}
                  className={styles['profile-section-empty-add-button']}
                >
                  Add
                </span>
              </div>
            )}
          </>
        ) : (
          <form className={styles['form']} onSubmit={formik.handleSubmit}>
            {socialNetworkList.map(({ name, placeholder }, index) => (
              <div className={styles['soc-icon-link']} key={index}>
                <Image
                  src={`/assets/icons/social-networks/icon-${name}-1.svg`}
                  width={26}
                  height={26}
                  alt="social icon"
                  priority
                />
                <Input
                  name={name}
                  placeholder={placeholder}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  value={formik.values[name]}
                  isTouched={formik.touched[name]}
                  error={formik.errors[name]}
                />
              </div>
            ))}
          </form>
        )}
      </section>
    </>
  );
};

export default memo(SocialNetworks);
