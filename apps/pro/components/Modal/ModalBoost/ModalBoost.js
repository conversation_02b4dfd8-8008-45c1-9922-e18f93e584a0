'use client';
import React, { memo, useEffect, useState } from 'react';
import styles from './ModalBoost.module.scss';
import { HeaderMobile, Loading, Modal } from '@components';
import { useAuth } from '@contexts/AuthContext';
import Api from '@services/api';
import BoostSuccess from './BoostSuccess/BoostSuccess';
import BoostForm from './BoostForm/BoostForm';
import { BOOST_STATUS } from '@constants/castingCalls';
import AllBoosted from './AllBoosted/AllBoosted';
import BoostDisabled from './BoostDisabled/BoostDisabled';
import { useNotifications } from '@contexts/NotificationContext';
import { ErrorMessage } from '@constants/form';

const ModalBoost = ({
  onClose,
  isInBoostPage,
  showWarningDefault,
  defaultSelectedOptionValue,
  onBoostSuccess = () => {},
}) => {
  const [options, setOptions] = useState([]);
  const [boostStatus, setBoostStatus] = useState(BOOST_STATUS.Loading);
  const { profileId, userProfiles } = useAuth();
  const { setNotification } = useNotifications();

  useEffect(() => {
    const getCastingCalls = async () => {
      const clientId = userProfiles.length ? userProfiles[0].clientId : 0;
      const response = await Api.clientside(
        `/calls/search?es=1&casting_director_id=${clientId}&filter=active&status[]=approved`,
      );

      if (response.status !== 'ok') {
        setNotification({
          type: 'error',
          timeout: '5000',
          message: response.message || ErrorMessage.Unexpected,
        });
        onClose();
      } else {
        const castingCalls = response.data?.calls || [];
        const filteredCastingCalls = castingCalls.filter(
          ({ is_boost_requested }) => !is_boost_requested,
        );

        switch (true) {
          case !castingCalls.length:
            setBoostStatus(BOOST_STATUS.Disabled);
            break;
          case !filteredCastingCalls.length:
            setBoostStatus(BOOST_STATUS.Boosted);
            break;
          default:
            const formattedOptions = filteredCastingCalls.map(
              ({ title, id, type }) => ({
                title,
                value: id,
                type,
              }),
            );

            setOptions(formattedOptions);
            setBoostStatus(BOOST_STATUS.Enabled);
        }
      }
    };

    getCastingCalls().catch();
  }, []);

  const onShowSuccessMessage = () => {
    setBoostStatus(BOOST_STATUS.Success);
    onBoostSuccess();
  };

  const getContent = () => {
    switch (boostStatus) {
      case BOOST_STATUS.Enabled:
        return (
          <BoostForm
            onShowSuccessMessage={onShowSuccessMessage}
            onClose={onClose}
            options={options}
            profileId={profileId}
            isInBoostPage={isInBoostPage}
            showWarningDefault={showWarningDefault}
            defaultSelectedOptionValue={defaultSelectedOptionValue}
          />
        );
      case BOOST_STATUS.Disabled:
        return <BoostDisabled onClose={onClose} />;
      case BOOST_STATUS.Boosted:
        return <AllBoosted onClose={onClose} />;
      case BOOST_STATUS.Success:
        return <BoostSuccess onClose={onClose} />;
      default:
        return <Loading minHeight="40px" padding="20px 0 40px" />;
    }
  };

  return (
    <Modal
      onClose={onClose}
      backdropClose
      showDefaultLayout={false}
      classNameContainer={styles['modal-container']}
      classNameOverlay={styles['modal-overlay']}
      classNameContent={styles['modal-content']}
      hideMobileCloseButton
      showMobileBorderRadius={false}
      showAnimation={false}
    >
      <HeaderMobile onClose={onClose} title="Casting Call" hideOnTablet />
      {getContent()}
    </Modal>
  );
};

export default memo(ModalBoost);
