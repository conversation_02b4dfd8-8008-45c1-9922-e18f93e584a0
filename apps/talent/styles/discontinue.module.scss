@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.discontinue-subscription-section {
  display: flex;
  justify-content: center;
  flex-direction: column;
  overflow-x: hidden;

  @include tablet {
    padding: $space-30 0;
    flex-direction: row;
    height: 100%;
  }
}

.discontinue-subscription-container {
  display: flex;
  flex-direction: column;
  text-align: center;
  gap: $space-30;

  @include tablet {
    max-width: 600px;
  }
}

.discontinue-subscription-container-success {
  display: flex;
  flex-direction: column;
  text-align: center;
  gap: $space-20;
  padding: $space-20;

  @include tablet {
    gap: $space-40;
    max-width: 600px;
  }
}

.discontinue-subscription-title {
  font-size: 30px;
  line-height: 1.2;
  font-weight: 700;
  margin: 0;
  display: none;

  @include tablet {
    display: initial;
  }
}

.discontinue-subscription-title-success {
  line-height: 1.2;
  font-weight: 700;
  margin: 0;
  font-size: 18px;

  @include tablet {
    font-size: 24px;
  }
}

.discontinue-subscription-description {
  background-color: $grey-10;
  padding: $space-15 $space-20;
  border-bottom: 1px solid $grey-60;
  text-align: left;
  font-weight: 300;
  width: 100%;

  @include tablet {
    background-color: $white;
    padding: 0;
    border-bottom: none;
    text-align: center;
  }
}

.discontinue-subscription-form {
  background-color: $white;
  gap: $space-25;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 $space-20;

  @include tablet {
    border-radius: 10px;
    padding: $space-40 $space-50;
    box-shadow: $shadow-card-container;
  }
}

.discontinue-subscription-form-label {
  font-weight: 300;
  font-size: 16px;
  line-height: 18px;
}

.link {
  display: block;
  color: $blue-100;
  font-size: 16px;
  font-weight: 400;
  margin-bottom: $space-20;
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
}
