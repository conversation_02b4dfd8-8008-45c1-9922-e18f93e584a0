'use client';
import React, { memo, useEffect, useState } from 'react';
import styles from './StarRating.module.scss';
import Image from 'next/image';
import cn from 'classnames';

const StarRating = ({
  initialRating = 0,
  fixed = true,
  width = 14,
  height = 14,
  onChange,
  isImageRating = false,
  showHint = false,
}) => {
  const [rating, setRating] = useState(initialRating);
  const [hoveredRating, setHoveredRating] = useState(0);

  useEffect(() => {
    setRating(initialRating);
  }, [initialRating]);

  const updateRating = (value) => {
    if (!fixed) {
      setRating(value);

      if (onChange) {
        onChange(value);
      }
    }
  };

  const updateHoveredRating = (value) => {
    if (!fixed) {
      setHoveredRating(value);
    }
  };

  return (
    <div
      className={cn(
        styles['star-container'],
        styles[`rating-${hoveredRating || rating}`],
        {
          [styles['star-container-image-rating']]: isImageRating,
        },
      )}
    >
      {[...Array(5)].map((id, index) => {
        index += 1;

        return (
          <Image
            title={
              fixed
                ? `${rating} star${rating > 1 ? 's' : ''}`
                : `${index} star${index > 1 ? 's' : ''}`
            }
            onClick={() => updateRating(index)}
            key={index}
            className={cn(styles.star, {
              [styles['selected']]: index <= (hoveredRating || rating),
              [styles['dynamic']]: !fixed,
            })}
            src={`/assets/icons/icon-star-filled${
              isImageRating ? '-slim' : ''
            }.svg`}
            width={width}
            height={height}
            alt="star"
            onMouseEnter={() => updateHoveredRating(index)}
            onMouseLeave={() => updateHoveredRating(rating)}
          />
        );
      })}
      {isImageRating && rating < 5 && showHint && (
        <Image
          className={styles.hint}
          src="/assets/icons/icon-hint-red.svg"
          alt="icon"
          width={18}
          height={18}
        />
      )}
    </div>
  );
};

export default memo(StarRating);
