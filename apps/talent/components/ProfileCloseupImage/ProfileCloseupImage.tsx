import styles from './ProfileCloseupImage.module.scss';
import {
  PlaceholderCircleFemaleCloseUp,
  PlaceholderCircleMaleCloseUp,
} from '../../public/assets/placeholders';
import React from 'react';
import classnames from 'classnames';

type ProfileCloseupImageProps = {
  src?: string;
  gender?: string;
  alt: string;
  className?: string;
  image?: 'head' | 'closeup';
};

const placeholders = {
  closeup: {
    male: PlaceholderCircleMaleCloseUp,
    female: PlaceholderCircleFemaleCloseUp,
  },
};

export const ProfileCloseupImage = ({
  src,
  gender = 'male',
  alt,
  image = 'closeup',
  className,
}: ProfileCloseupImageProps) => {
  const PlaceHolder = placeholders[image][gender.toLowerCase() ?? 'male'];

  return (
    <>
      {src && (
        <img
          src={src}
          className={classnames(styles.avatar, className)}
          alt={alt}
        />
      )}
      {!src && <PlaceHolder className={classnames(styles.avatar, className)} />}
    </>
  );
};
