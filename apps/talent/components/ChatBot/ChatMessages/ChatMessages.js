'use client';
import styles from './ChatMessages.module.scss';
import React, { useMemo } from 'react';
import { Loading } from '@components';

const ChatMessages = ({
  conversationData,
  messagesEndRef,
  submitQuestion,
  isTyping,
}) => {
  const renderQuestion = (text, key) => {
    return (
      <div className={styles['message-wrapper']} key={key}>
        <div className={styles.question}>
          <div className={styles.meta}>
            <span>You</span>
          </div>
          <div className={styles['message-container']}>
            <div className={styles['question-message']}>
              <span>{text}</span>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderAnswerPrompt = (item, key) => {
    return (
      <div
        className={styles['answer-prompt']}
        key={key}
        onClick={() => submitQuestion(item)}
      >
        {item.displayText}
      </div>
    );
  };

  const renderAnswer = (text, prompts, key, typing, disabledOptions) => {
    return (
      <div className={styles['message-wrapper']} key={key}>
        <div className={styles.answer}>
          <div className={styles.meta}>
            <span>Support Chatbot</span>
          </div>
          <div className={styles['message-container']}>
            {typing ? (
              <Loading />
            ) : (
              <>
                <div className={styles['answer-message']}>
                  <span dangerouslySetInnerHTML={{ __html: text }} />
                  {!disabledOptions && prompts?.length > 0 && (
                    <div className={styles['answer-prompts-container']}>
                      {prompts.map((item, promptKey) =>
                        renderAnswerPrompt(item, promptKey),
                      )}
                    </div>
                  )}
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    );
  };

  const noAnswerPrompts = [{ displayText: 'Restart' }];

  const conversation = useMemo(() => {
    return conversationData.map((item, key) =>
      item.type === 'answer'
        ? renderAnswer(
            item.id === -1 ? 'No answer found to your question' : item.message,
            item.id === -1 ? noAnswerPrompts : item.prompts,
            key,
            null,
            conversationData.length - 1 !== key,
          )
        : renderQuestion(item.message, key),
    );
  }, [conversationData, noAnswerPrompts]);

  return (
    <div className={styles['message-container']}>
      {conversation}
      {isTyping && renderAnswer(null, null, null, true)}
      <div ref={messagesEndRef} />
    </div>
  );
};

export default ChatMessages;
