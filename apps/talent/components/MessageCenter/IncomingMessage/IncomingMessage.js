'use client';
import React, { memo } from 'react';
import styles from './IncomingMessage.module.scss';
import cn from 'classnames';
import dayjs from 'dayjs';
import IconFlag from '../../../public/assets/icons/icon-flag.svg';
import { IncomingAttachment } from '../IncomingAttachment/IncomingAttachment';
import { MESSAGE_LINK_REGEX } from '@constants/form';
import { decodeHtml, sanitiseUrl } from '@utils/messageHelpers';
import Link from 'next/link';

const IncomingMessage = ({
  message = {},
  partner,
  reportMessage,
  openImageAttachment,
}) => {
  const { links = {}, created, content } = message;

  const files = links.files?.items || [];
  const castingCalls = links['casting-calls']?.items || [];
  const externalLinks = links.external_links?.items || [];
  const complaints = links['message-complaints']?.items || [];
  const isReported = complaints.length > 0 || message.isReported;
  const messageFragments = content
    .replaceAll(/:\/\/(pro|me)\./g, '://')
    .split(MESSAGE_LINK_REGEX);

  return (
    <div className={styles['message-row']}>
      <div className={cn(styles['message-wrapper'], styles['incoming'])}>
        <div className={styles['message-body']}>
          <div className={styles['message-meta']}>
            <span>
              {partner.firstName} {partner.lastName}
            </span>
            <span>{dayjs(created).format('h:mm A')}</span>
          </div>
          {isReported ? (
            <div className={styles['message-content']}>
              <div
                className={cn(
                  styles['message'],
                  styles['incoming-message'],
                  styles['is-reported'],
                )}
              >
                Message reported
              </div>
              <div className={styles['message-interaction']} />
            </div>
          ) : (
            <>
              <div className={styles['message-content']}>
                <div
                  className={cn(styles['message'], styles['incoming-message'])}
                >
                  {messageFragments.map((fragment, index) =>
                    MESSAGE_LINK_REGEX.test(fragment) ? (
                      <Link
                        key={index}
                        href={sanitiseUrl(fragment)}
                        target="_blank"
                        rel="noreferrer"
                        className={styles['inline-link']}
                      >
                        {fragment}
                      </Link>
                    ) : (
                      <span key={index}>{decodeHtml(fragment)}</span>
                    ),
                  )}
                </div>
                <div className={styles['message-interaction']}>
                  <div
                    className={cn(
                      styles['interaction-btn'],
                      styles['report-btn'],
                    )}
                    onClick={() => reportMessage(message)}
                  >
                    <IconFlag />
                    <span className={styles['interaction-btn-text']}>
                      Report message
                    </span>
                  </div>
                </div>
              </div>
              {castingCalls.map(({ id, title }) => (
                <div className={styles['link-container']} key={id}>
                  <Link
                    href={`${process.env.publicUrl}/castingcall/${id}`}
                    className={styles.link}
                    target="_blank"
                    rel="noreferrer"
                  >
                    {title}
                  </Link>
                </div>
              ))}
              {externalLinks.map(({ link, title }, index) => (
                <div className={styles['link-container']} key={index}>
                  <Link
                    target="_blank"
                    className={styles.link}
                    href={sanitiseUrl(
                      link.replace('://pro.', '://').replace('://me.', '://'),
                    )}
                    rel="noreferrer"
                  >
                    {title}
                  </Link>
                </div>
              ))}
              {files.length > 0 && (
                <div className={styles['attachment-container']}>
                  {files.map((attachment, index) => (
                    <div
                      className={styles.attachment}
                      key={`attachment-${index}`}
                    >
                      <IncomingAttachment
                        attachment={attachment}
                        openImageAttachment={openImageAttachment}
                      />
                    </div>
                  ))}
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default memo(IncomingMessage);
