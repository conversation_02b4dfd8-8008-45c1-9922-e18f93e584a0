'use client';
import React, { memo } from 'react';
import styles from './Profile.module.scss';
import Image from 'next/image';
import { ImageWrapper, MainImageMobileCarousel, Tooltip } from '@components';
import TooltipPhotoInfo from '../Tooltip/TooltipPhotoInfo/TooltipPhotoInfo';

const ProfileMainPhotos = ({
  onImageClick,
  closeUpImage,
  sideViewImage,
  fullHeightImage,
  titleImage,
  refreshUserProfiles,
  refreshProfileDetails,
  isPaidOrDelayed,
  genderTitle,
  openRatingProgress,
}) => {
  return (
    <div className={styles['profile-main-images']}>
      <div className={styles['profile-main-images-container-desktop']}>
        <div className={styles['profile-image-container']}>
          <div className={styles['profile-section-title']}>
            Headshot
            <Tooltip content={<TooltipPhotoInfo />} openOnHover clickable>
              <div className={styles['icon-hint-container']}>
                <Image
                  src="/assets/icons/icon-hint-blue.svg"
                  alt="icon"
                  width={18}
                  height={18}
                />
              </div>
            </Tooltip>
          </div>
          <div className={styles['profile-image']}>
            <ImageWrapper
              image={
                closeUpImage
                  ? { ...closeUpImage, type: 'close_up', title: 'Headshot' }
                  : null
              }
              onImageClick={() => onImageClick(closeUpImage.id)}
              titlePhotoSourceId={titleImage?.source_id}
              declineContentSize="large"
              showUploadIcon
              imageType="close_up"
              imageTitle="Headshot"
              genderTitle={genderTitle}
              zoomActionIconSize="small"
              isPaidOrDelayed={isPaidOrDelayed}
              ratingPosition="left"
              isMainImage
              declineAction="upload"
            />
          </div>
        </div>
        <div className={styles['profile-secondary-images']}>
          <div className={styles['profile-image-container']}>
            <div className={styles['profile-section-title']}>
              Side view
              <Tooltip content={<TooltipPhotoInfo />} openOnHover clickable>
                <div className={styles['icon-hint-container']}>
                  <Image
                    src="/assets/icons/icon-hint-blue.svg"
                    alt="icon"
                    width={18}
                    height={18}
                  />
                </div>
              </Tooltip>
            </div>
            <div className={styles['profile-image']}>
              <ImageWrapper
                image={
                  sideViewImage
                    ? { ...sideViewImage, type: '3_4', title: 'Side view' }
                    : null
                }
                onImageClick={() => onImageClick(sideViewImage.id)}
                titlePhotoSourceId={titleImage?.source_id}
                declineContentSize="small"
                showUploadIcon
                imageType="3_4"
                imageTitle="Side view"
                genderTitle={genderTitle}
                zoomActionIconSize="small"
                isPaidOrDelayed={isPaidOrDelayed}
                showTooltip
                zoomIconSize="small"
                showRatingHint
                isMainImage
                declineAction="upload"
              />
            </div>
          </div>
          <div className={styles['profile-image-container']}>
            <div className={styles['profile-section-title']}>
              Full height
              <Tooltip content={<TooltipPhotoInfo />} openOnHover clickable>
                <div className={styles['icon-hint-container']}>
                  <Image
                    src="/assets/icons/icon-hint-blue.svg"
                    alt="icon"
                    width={18}
                    height={18}
                  />
                </div>
              </Tooltip>
            </div>
            <div className={styles['profile-image']}>
              <ImageWrapper
                image={
                  fullHeightImage
                    ? {
                        ...fullHeightImage,
                        type: 'height',
                        title: 'Full height',
                      }
                    : null
                }
                onImageClick={() => onImageClick(fullHeightImage.id)}
                titlePhotoSourceId={titleImage?.source_id}
                declineContentSize="small"
                showUploadIcon
                imageType="height"
                imageTitle="Full height"
                genderTitle={genderTitle}
                zoomActionIconSize="small"
                isPaidOrDelayed={isPaidOrDelayed}
                showTooltip
                zoomIconSize="small"
                showRatingHint
                isMainImage
                declineAction="upload"
              />
            </div>
          </div>
        </div>
      </div>

      <div className={styles['profile-main-images-container-mobile']}>
        <MainImageMobileCarousel
          genderTitle={genderTitle}
          closeUpImage={
            closeUpImage
              ? { ...closeUpImage, type: 'close_up', title: 'Headshot' }
              : null
          }
          fullHeightImage={
            fullHeightImage
              ? {
                  ...fullHeightImage,
                  type: 'height',
                  title: 'Full height',
                }
              : null
          }
          titleImage={titleImage}
          isPaidOrDelayed={isPaidOrDelayed}
          sideViewImage={
            sideViewImage
              ? { ...sideViewImage, type: '3_4', title: 'Side view' }
              : null
          }
          refreshUserProfiles={refreshUserProfiles}
          refreshProfileDetails={refreshProfileDetails}
          openRatingProgress={openRatingProgress}
        />
      </div>
    </div>
  );
};

export default memo(ProfileMainPhotos);
