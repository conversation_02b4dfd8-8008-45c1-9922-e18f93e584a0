'use client';
import { memo, useEffect, useRef, useState } from 'react';
import styles from './PaymentForm.module.scss';
import Image from 'next/image';
import {
  lifetimePeriod,
  paidProfileFeatures,
  premiumFeatures,
} from '@constants/payment';
import CardMethodFormInputs from './PaymentMethods/CardMethodFormInputs';
import StripeForm from './PaymentMethods/Stripe/StripeForm';
import { Elements } from '@stripe/react-stripe-js';
import { useNotifications } from '@contexts/NotificationContext';
import cn from 'classnames';
import { CookieService } from '@services/cookieService';
import PaymentFormFooter from './PaymentFormFooter';
import PaymentFormDescription from './PaymentFormDescription';
import PaymentFormMethods from './PaymentFormMethods';
import getStripe from '../../utils/getStripe';
import StripeExpress from './PaymentMethods/Stripe/StripeExpress';

const PaymentForm = ({
  period,
  price,
  baseMonthPrice,
  promoCode,
  nameOnCard,
  zip,
  memberLevelId,
  expMonth,
  expYear,
  accountId,
  monthOptions,
  yearOptions,
  clientId,
  email,
  campaign,
  campaignGroup,
  tracking,
  stripePK,
  accountLevelVerify,
  isStripe,
  stripeClientSecret,
  stripeInvoiceHash,
  isMobile,
  isAndroidDevice,
  isUpgradeForm,
  isProfileOutsideUSA = false,
  isPremium,
  isLifetime,
}) => {
  const [loading, setLoading] = useState(false);
  const [isApplePaySupported, setIsApplePaySupported] = useState(false);

  const paymentFormRef = useRef(null);
  const { clearNotification } = useNotifications();

  useEffect(() => {
    clearStripeParams();
    if (window.ApplePaySession?.canMakePayments()) {
      setIsApplePaySupported(true);
    }
  }, []);

  useEffect(() => {
    if (isPremium) {
      CookieService.setShowPremiumCheckoutSuccess(true);
      CookieService.deleteShowLifetimeCheckoutSuccessCookie();
    } else if (isLifetime) {
      CookieService.setShowLifetimeCheckoutSuccess(true);
      CookieService.deleteShowPremiumCheckoutSuccessCookie();
    } else {
      CookieService.deleteShowPremiumCheckoutSuccessCookie();
      CookieService.deleteShowLifetimeCheckoutSuccessCookie();
    }
  }, [isPremium, isLifetime]);

  const triggerSubmit = async (e) => {
    e?.preventDefault();
    clearNotification();
    setLoading(true);
    await paymentFormRef.current.triggerSubmit();
    setLoading(false);
  };

  // returning from stripe without attempting payment
  const clearStripeParams = () => {
    window.sessionStorage.removeItem('invoiceHash');
    CookieService.deleteShowStripeCheckoutSuccessCookie();
  };

  const updatePaymentLoading = (current) => {
    setLoading((previous) => (previous === current ? previous : current));
  };

  const stripe = getStripe(stripePK);
  const options = {
    clientSecret: stripeClientSecret,
    fonts: [
      {
        cssSrc:
          'https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800;1,200..800&display=swap',
      },
    ],
    appearance: {
      labels: 'above',
      theme: 'stripe',
      variables: {
        fontFamily:
          '"Plus Jakarta Sans", -apple-system, "system-ui", Roboto, sans-serif',
        colorText: '#abafba',
      },
      rules: {
        '.Input': {
          color: '#1A1A1A',
          fontWeight: '600',
          borderColor: '#d3d6de',
        },
        '.Input::placeholder': {
          fontWeight: '400',
          color: '#abafba',
        },
      },
    },
  };

  return (
    <form className={styles['payment-form']} onSubmit={triggerSubmit}>
      <div
        className={cn(styles['payment-form-header'], {
          [styles.lifetime]: period >= lifetimePeriod,
        })}
      >
        <div className={styles['payment-plan']}>
          {period >= lifetimePeriod && (
            <p className={styles['payment-plan-title']}>
              Lifetime subscription
            </p>
          )}
          {period < lifetimePeriod && (
            <p className={styles['payment-plan-title']}>
              {`${period} month`}&nbsp;
              <br className={styles['payment-plan-title-break']} />
              <span>subscription</span>
            </p>
          )}
          <span className={styles['payment-plan-sub-title']}>
            Cancel any time
          </span>
        </div>
        <div className={styles['payment-price-container']}>
          <span className={styles['payment-price']}>
            {!isLifetime && `$${(baseMonthPrice * period).toFixed(2)}`}
          </span>
          <span className={styles['payment-price-with-discount']}>
            ${price.toFixed(2)}
            {isProfileOutsideUSA && (
              <span className={styles['payment-price-currency']}>USD</span>
            )}
          </span>
        </div>
      </div>
      <div className={styles['payment-form-content']}>
        <div className={styles['payment-plan-container']}>
          <h2 className={styles['payment-plan-features-title']}>
            Premium features
          </h2>
          <ul className={styles['payment-plan-features']}>
            {(isPremium ? premiumFeatures : paidProfileFeatures).map(
              (feature) => (
                <li className={styles['payment-plan-feature']} key={feature}>
                  <Image
                    className={styles['payment-plan-feature-icon']}
                    src="/assets/icons/icon-checkmark.svg"
                    width={19}
                    height={16}
                    alt="icon checkmark"
                  />
                  <span>{feature}</span>
                </li>
              ),
            )}
          </ul>
        </div>
        <div className={styles['payment-information-container']}>
          {!isStripe && (
            <PaymentFormMethods
              promoCode={promoCode}
              memberLevelId={memberLevelId}
              isAndroidDevice={isAndroidDevice}
              isApplePaySupported={isApplePaySupported}
            />
          )}
          <div className={styles['payment-information-title-container']}>
            <Image
              src="/assets/icons/icon-lock.svg"
              width={21}
              height={24}
              alt="icon lock"
            />
            <span className={styles['payment-information-title']}>
              Payment information:
            </span>
          </div>
          {isStripe ? (
            <>
              <Elements stripe={stripe} options={options}>
                <StripeExpress
                  clientSecret={stripeClientSecret}
                  invoiceHash={stripeInvoiceHash}
                  updatePaymentLoading={updatePaymentLoading}
                />
              </Elements>
              <Elements stripe={stripe} options={options}>
                <StripeForm
                  ref={paymentFormRef}
                  clientSecret={stripeClientSecret}
                  invoiceHash={stripeInvoiceHash}
                  updatePaymentLoading={updatePaymentLoading}
                  isLifetime={isLifetime}
                  price={price}
                  isProfileOutsideUSA={isProfileOutsideUSA}
                  isUpgradeForm={isUpgradeForm}
                  isPaymentLoading={loading}
                />
              </Elements>
            </>
          ) : (
            <CardMethodFormInputs
              ref={paymentFormRef}
              nameOnCard={nameOnCard}
              monthOptions={monthOptions}
              promoCode={promoCode}
              zip={zip}
              yearOptions={yearOptions}
              expYear={expYear}
              expMonth={expMonth}
              memberLevelId={memberLevelId}
              tracking={tracking}
              email={email}
              clientId={clientId}
              campaignGroup={campaignGroup}
              campaign={campaign}
              accountId={accountId}
              accountLevelVerify={accountLevelVerify}
              isUpgradeForm={isUpgradeForm}
            />
          )}
          {!isStripe && (
            <>
              <PaymentFormDescription
                isLifetime={isLifetime}
                price={price}
                isProfileOutsideUSA={isProfileOutsideUSA}
              />
              <PaymentFormFooter
                isStripe={isStripe}
                isUpgradeForm={isUpgradeForm}
                loading={loading}
              />
            </>
          )}
        </div>
      </div>
    </form>
  );
};

export default memo(PaymentForm);
