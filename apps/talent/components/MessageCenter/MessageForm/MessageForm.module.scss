@use '@styles/mixins' as *;
@use '@styles/variables' as *;

.body {
  border-radius: 10px 10px 0 0;
  box-shadow: $shadow-input-box;
  width: 100%;

  @include desktop {
    border: 1px solid $grey-40;
    border-radius: 10px;
    box-shadow: none;
    max-width: 700px;
  }
}

.form {
  position: relative;
  display: flex;
  align-items: flex-end;
  padding: $space-15 $space-25 0;
}

.form-wrap {
  border-radius: 10px;
  padding-bottom: $space-20;
}

.input-box {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: auto;

  .input-area {
    width: 100%;
    height: 50px;
  }

  .input-controls {
    display: flex;
    justify-content: space-between;
  }

  .input-submit {
    background: transparent;
    display: flex;
    align-items: center;
    gap: $space-15;
    flex-wrap: wrap;
  }

  .input-counter {
    font-size: 12px;
  }

  .message-input {
    background: transparent;
    border: 0;
    width: 100%;
    padding: $space-10 0;
    outline: none;
    font-size: 16px;
    font-weight: 600;
  }
}

.attachment-controls {
  display: flex;
  align-items: center;
}

.attach-btn {
  cursor: pointer;
}

.file-list {
  padding: 0 $space-25;
}

.inactive {
  width: 100%;
  border-radius: 10px 10px 0 0;
  padding: $space-20 $space-20 $space-40;
  text-align: center;
  font-weight: 700;
  font-size: 14px;
  box-shadow: $shadow-input-box;
  color: $grey-60;

  @include tablet {
    padding: $space-20 $space-20 $space-50;
  }

  @include desktop {
    border: 1px solid $grey-40;
    border-radius: 10px;
    box-shadow: none;
    max-width: 700px;
    padding: $space-20;
  }
}
