@use '@styles/mixins' as *;
@use '@styles/variables' as *;

.modal-overlay {
  @include tablet {
    padding: 0 $space-20;
  }
}

.modal-container {
  width: 100vw;
  min-height: 100%;
  min-height: 100dvh;
  padding-bottom: $space-30;

  @include tablet {
    min-height: unset;
    max-width: 415px;
    border-radius: 10px;
    padding-bottom: 0;
  }
}

.header {
  padding: $space-15 $space-20;

  @include tablet {
    padding: 0 $space-40;
  }
}

.title {
  margin-top: 0;
  margin-bottom: $space-25;
  display: none;

  @include tablet {
    display: block;
  }
}

.message-wrap {
  border-radius: 10px;
  background: rgb($black, 0.05);
  padding: $space-30 $space-20;
}

.message {
  max-height: 200px;
  overflow: auto;
  overflow-wrap: word-break;
  word-break: word-break;
}

.content {
  background-color: $white;
  border-radius: 10px;
  padding: $space-15 $space-20 $space-20;
  margin: 0 0 $space-20;

  @include tablet {
    padding: $space-20 $space-40 0;
    margin: 0;
    border-radius: unset;
  }
}

.form-field {
  padding: $space-30 0 $space-40;
  position: relative;

  @include tablet {
    padding-bottom: 70px;
  }
}

.error {
  padding-top: $space-40;
  color: $red-60;
  text-align: center;
  font-size: 14px;
  font-weight: 300;
  position: absolute;
  margin: 0 auto;
  width: 100%;
}

.mobile-submit-button {
  display: flex;
  justify-content: center;

  @include tablet {
    display: none;
  }
}

.buttons {
  display: none;

  @include tablet {
    display: flex;
  }

  .button {
    flex: 1 1 50%;
    border: none;
    background: none;
    padding: $space-15;
    border-top: 1px solid rgb($black, 0.15);
    font-size: 16px;
    font-weight: 300;
    cursor: pointer;

    &:first-child {
      border-right: 1px solid rgb($black, 0.15);
    }

    &.cancel {
      &:hover {
        text-decoration: underline;
      }
    }

    &.submit {
      font-weight: 400;
      color: $blue-100;
    }

    &:disabled {
      color: rgb($blue-100, 0.8);
      cursor: default;
    }
  }
}

.inline-link {
  text-decoration: underline;
  color: $blue-100;
}
