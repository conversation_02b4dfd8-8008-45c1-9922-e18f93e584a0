'use client';
import styles from './Audio.module.scss';
import React, { memo, useRef, useState } from 'react';
import Image from 'next/image';
import {
  AudioPlayer,
  AudioUploadTool,
  ModalAudioControls,
  ModalAudioUpload,
  ModalDeleteAudio,
  ModalShareAudioLink,
} from '@components';
import { useProfileContext } from '@contexts/ProfileContext';
import Api from '@services/api';
import { useNotifications } from '@contexts/NotificationContext';
import { Amp } from '@services/amp';
import { ErrorMessage } from '@constants/form';

const Audio = ({ audioId, title, url, path, isMobileFromUserAgent }) => {
  const [showEditAudioModal, setShowEditAudioModal] = useState(false);
  const [showDeleteAudioModal, setShowDeleteAudioModal] = useState(false);
  const [showShareAudioModal, setShowShareAudioModal] = useState(false);
  const [showAudioControlsModal, setShowAudioControlsModal] = useState(false);
  const [selectedAudio, setSelectedAudio] = useState(null);
  const [loading, setLoading] = useState(false);
  const audioUploadToolRef = useRef();
  const { deleteAudio, id: profileId } = useProfileContext();
  const { setNotification } = useNotifications();

  const onFileInputChange = (audio) => {
    setSelectedAudio(audio);
    setShowEditAudioModal(true);
  };

  const selectFile = () => {
    audioUploadToolRef.current.selectFile();
  };

  const closeDeleteAudioModalAndDelete = async () => {
    setLoading(true);
    await deleteAudio(audioId);
    setLoading(false);
    setShowDeleteAudioModal(false);
  };

  async function onDownload() {
    Amp.track(Amp.events.elementClicked, {
      name: 'download audio',
      scope: Amp.element.scope.profile,
      section: Amp.element.section.audio,
      type: Amp.element.type.button,
    });

    const blob = await Api.clientside(
      `/profiles/${profileId}/audios/${audioId}/content`,
      {},
      {},
      true,
    );

    if (blob.status === 'error') {
      setNotification({
        type: 'error',
        message: ErrorMessage.Unexpected,
        timeout: '5000',
      });
    } else {
      const a = document.createElement('a');

      document.body.appendChild(a);
      a.style = 'display: none';

      const url = window.URL.createObjectURL(blob);

      a.href = url;
      a.download = path;
      a.click();
      window.URL.revokeObjectURL(url);
    }
  }

  const onEdit = () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'edit audio',
      scope: Amp.element.scope.profile,
      section: Amp.element.section.audio,
      type: Amp.element.type.button,
    });
    if (showAudioControlsModal) {
      setShowAudioControlsModal(false);
    }
    setShowEditAudioModal(true);
  };

  const onRemove = () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'remove audio',
      scope: Amp.element.scope.profile,
      section: Amp.element.section.audio,
      type: Amp.element.type.button,
    });
    if (showAudioControlsModal) {
      setShowAudioControlsModal(false);
    }
    setShowDeleteAudioModal(true);
  };

  const onShare = () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'share audio',
      scope: Amp.element.scope.profile,
      section: Amp.element.section.audio,
      type: Amp.element.type.button,
    });
    if (showAudioControlsModal) {
      setShowAudioControlsModal(false);
    }
    setShowShareAudioModal(true);
  };

  return (
    <AudioUploadTool
      ref={audioUploadToolRef}
      onFileInputChange={onFileInputChange}
    >
      <div className={styles['audio-preview']}>
        <div className={styles['audio-preview-header']}>
          <span className={styles.title}>{title}</span>
          <Image
            className={styles.icon}
            src="/assets/icons/icon-dots-1.svg"
            alt="icon"
            width={4}
            height={18}
            title="More"
            onClick={() => setShowAudioControlsModal(true)}
          />
        </div>
        <AudioPlayer url={url} isMobileFromUserAgent={isMobileFromUserAgent} />
        <div className={styles['audio-preview-actions']}>
          <Image
            className={styles.icon}
            src="/assets/icons/icon-edit.svg"
            alt="icon"
            width={16}
            height={19}
            title="Edit"
            onClick={onEdit}
          />
          <Image
            className={styles.icon}
            src="/assets/icons/icon-trash-black.svg"
            alt="icon"
            width={15}
            height={15}
            title="Remove"
            onClick={onRemove}
          />
          <Image
            className={styles.icon}
            src="/assets/icons/icon-download.svg"
            alt="icon"
            width={22}
            height={22}
            title="Download"
            onClick={onDownload}
          />
          <Image
            className={styles.icon}
            src="/assets/icons/icon-share.svg"
            alt="icon"
            width={19}
            height={19}
            title="Share link"
            onClick={onShare}
          />
        </div>
      </div>
      {showEditAudioModal && (
        <ModalAudioUpload
          id={audioId}
          isEditing={!!selectedAudio}
          onClose={() => {
            setSelectedAudio(null);
            setShowEditAudioModal(false);
          }}
          audio={selectedAudio || url}
          onReplaceAudio={() => {
            setShowEditAudioModal(false);
            selectFile();
          }}
          defaultTitle={title}
          isMobileFromUserAgent={isMobileFromUserAgent}
        />
      )}
      {showDeleteAudioModal && (
        <ModalDeleteAudio
          onClose={() => setShowDeleteAudioModal(false)}
          onDeleteAudio={closeDeleteAudioModalAndDelete}
          loading={loading}
        />
      )}
      {showShareAudioModal && (
        <ModalShareAudioLink
          link={url}
          title={title}
          onClose={() => setShowShareAudioModal(false)}
        />
      )}
      {showAudioControlsModal && (
        <ModalAudioControls
          onClose={() => setShowAudioControlsModal(false)}
          onDownload={onDownload}
          onEdit={onEdit}
          onRemove={onRemove}
          onShare={onShare}
        />
      )}
    </AudioUploadTool>
  );
};

export default memo(Audio);
