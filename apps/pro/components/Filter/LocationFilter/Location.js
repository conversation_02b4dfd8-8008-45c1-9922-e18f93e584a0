'use client';
import { useState } from 'react';
import { Autocomplete, Box, TextField } from '@mui/material';
import Api from '@services/api';
import styles from './Location.module.scss';

const generateOptionFromZipcode = (payload) => {
  const options = {};

  if (payload && payload.items) {
    payload.items.map((item) => {
      options[`${item.links.city.title},${item.links.state.code}`] = {
        label: `${item.links.city.title}, ${item.links.state.code} ${item.links.zip.code}`,
        latitude: item.links.coordinates.latitude,
        longitude: item.links.coordinates.longitude,
        city: item.links.city.slug,
        state: item.links.state.code,
        zip: item.links.zip.code,
      };
    });
  }

  return Object.values(options);
};

const generateOptionFromCity = (payload) => {
  const options = {};

  if (payload && payload.items) {
    payload.items.map((item) => {
      options[`${item.title},${item.links.state.code}`] = {
        label: `${item.title}, ${item.links.state.code}`,
        latitude: item.latitude,
        longitude: item.longitude,
        city: item.slug,
        state: item.links.state.code,
        zip: 0,
      };
    });
  }

  return Object.values(options);
};

const Location = ({ widget, callback, cities, onClick }) => {
  const defaultOptions = generateOptionFromCity(cities);
  const [options, setOptions] = useState(defaultOptions);

  const onFocus = async (event) => {
    if (!event.target.value) {
      setOptions(defaultOptions);
    }
    event.target.select();
  };

  const onInputChange = async (e) => {
    const newWidget = JSON.parse(JSON.stringify(widget));
    const { value } = e.target;

    newWidget.filters.map((filter) => {
      filter.value = '';
    });

    callback({
      target: {
        name: widget.slug,
        value: {
          ...newWidget,
          value: value,
        },
      },
    });

    if (value.length === 0) {
      setOptions(defaultOptions);

      return;
    }

    if (
      value.match(
        /^((\d{5}-\d{4})|(\d{5})|([A-Z|a-z]\d[A-Z|a-z]\d[A-Z|a-z]\d))$/,
      )
    ) {
      setOptions(
        generateOptionFromZipcode(
          await Api.clientside(
            `/locations?limit=50&search_by=zipcode&query=${value}`,
          ),
        ),
      );
    } else {
      setOptions(
        generateOptionFromCity(
          await Api.clientside(
            `/cities/search?limit=50&search[title]=${value}`,
          ),
        ),
      );
    }
  };

  const onClear = async () => {
    await onInputChange({ target: { value: '' } });
  };

  const onOptionSelect = async (event, newValue) => {
    const newWidget = JSON.parse(JSON.stringify(widget));

    if (
      newValue &&
      newValue.city &&
      (!newValue.longitude || !newValue.latitude)
    ) {
      const data = Api.clientside(
        `/cities/search?filter[slug]=${newValue.city}`,
      );

      newValue.longitude = data.longitude;
      newValue.latitude = data.latitude;
    }

    if (newValue) {
      newWidget.value = newValue.label;
      newWidget.filters.map((filter) => {
        filter.value = newValue[filter.slug];
      });
    } else {
      newWidget.value = '';
      newWidget.filters.map((filter) => {
        filter.value = '';
      });
      setOptions(defaultOptions);
    }

    callback({
      target: {
        name: widget.slug,
        value: newWidget,
      },
    });
  };

  return (
    <div>
      <div className={styles.container}>
        <Autocomplete
          freeSolo
          autoHighlight
          disableClearable
          blurOnSelect
          options={options}
          inputValue={widget.value ?? ''}
          onChange={onOptionSelect}
          getOptionLabel={(option) => option.label}
          renderOption={(props, option) => <Box {...props}>{option.label}</Box>}
          renderInput={(params) => (
            <TextField
              {...params}
              label={'Zipcode or city'}
              variant="standard"
              onFocus={onFocus}
              onClick={onClick}
              onChange={onInputChange}
              value={widget.value}
            />
          )}
        />
        <div onClick={onClear} className={styles.close}>
          <svg
            className="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-ptiqhd-MuiSvgIcon-root"
            focusable="false"
            aria-hidden="true"
            viewBox="0 0 24 24"
            data-testid="CloseIcon"
          >
            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"></path>
          </svg>
        </div>
      </div>
    </div>
  );
};

export default Location;
