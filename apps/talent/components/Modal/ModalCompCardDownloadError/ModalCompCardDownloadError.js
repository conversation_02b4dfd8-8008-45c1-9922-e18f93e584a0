'use client';
import React, { memo } from 'react';
import { Modal } from '@components';
import styles from './ModalCompCardDownloadError.module.scss';

const ModalCompCardDownloadError = ({ onClose }) => {
  return (
    <Modal
      backdropClose
      classNameContainer={styles['photo-error-modal']}
      classNameOverlay={styles['photo-error-modal-container']}
      showDefaultLayout={false}
      onClose={onClose}
      showCloseButton={false}
    >
      <div className={styles['red-text']}>
        Please, upload all the required photos to download your comp card
      </div>
      <div className={styles['info-text']}>
        You will receive a PDF file that you can print out
      </div>

      <div className={styles['photo-error-modal-btn-container']}>
        <span className={styles['confirm-button']} onClick={onClose}>
          OK
        </span>
      </div>
    </Modal>
  );
};

export default memo(ModalCompCardDownloadError);
