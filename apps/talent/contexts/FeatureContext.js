import React, { createContext, useContext, useEffect, useState } from 'react';
import { FeatureManager } from '@services/featureManager';
import { useInterval } from '@utils/useInterval';
import { CookieService } from '@services/cookieService';
import { isPreviewPage } from '@utils/planSelectHelper';

const FeatureContext = createContext({});
const POOLING_INTERVAL = 300000;

export const FeatureProvider = ({ initialValues = {}, children }) => {
  const [state, setState] = useState({
    accountFeatures: [],
    paymentMaintenance: false,
    promoCodeEnabled: false,
    ...initialValues,
  });
  const setAccountFeatures = (accountFeatures) =>
    setState((prev) => ({ ...prev, accountFeatures }));

  const premiumSupportEnabled = !!state.accountFeatures.find(
    ({ name }) => name === FeatureManager.featureKeys.premiumSupport,
  );
  // ToDo Get separate Feature state for PremiumActionsButton
  const premiumActionsButtonEnabled = premiumSupportEnabled;
  const instagramBoostFeature = state.accountFeatures.find(
    ({ name }) => name === FeatureManager.featureKeys.premiumInstagramBoost,
  );
  const premiumInstagramBoostEnabled = !!instagramBoostFeature;
  const premiumInstagramBoostDate =
    instagramBoostFeature?.links?.requests?.items?.find(
      ({ request_status }) => request_status === 'pending',
    )?.created_on || null;

  const refreshFeatures = async () => {
    await FeatureManager.refresh();
    await refreshFeatureState();
  };

  const refreshFeatureState = async () => {
    const paymentMaintenance = await FeatureManager.enabled(
      FeatureManager.featureKeys.paymentMaintenance,
    );
    const promoCodeEnabled = await FeatureManager.enabled(
      FeatureManager.featureKeys.promoCode,
    );

    setState((prev) => ({
      ...prev,
      paymentMaintenance,
      promoCodeEnabled,
    }));
  };

  useEffect(() => {
    if (!isPreviewPage()) {
      const features = CookieService.getFeaturesCookie();

      if (!features) {
        refreshFeatures();
      } else {
        refreshFeatureState();
      }
    }
  }, []);

  useInterval(() => {
    if (!isPreviewPage()) {
      refreshFeatures();
    }
  }, POOLING_INTERVAL);

  return (
    <FeatureContext.Provider
      value={{
        ...state,
        premiumActionsButtonEnabled,
        premiumSupportEnabled,
        premiumInstagramBoostEnabled,
        premiumInstagramBoostDate,
        setAccountFeatures,
      }}
    >
      {children}
    </FeatureContext.Provider>
  );
};

export const useFeature = () => useContext(FeatureContext);
