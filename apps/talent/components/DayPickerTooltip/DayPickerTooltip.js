'use client';
import React, { memo, useRef, useState } from 'react';
import styles from './DayPickerTooltip.module.scss';
import Image from 'next/image';
import { Popover } from 'react-tiny-popover';
import cn from 'classnames';
import { useViewport } from '@utils/useViewport';
import { createPortal } from 'react-dom';
import { DayPicker, Modal } from '@components';

const DayPickerTooltip = ({
  selected,
  initialValue,
  onSelect,
  endMonth,
  startMonth,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const pickerRef = useRef(null);
  const { width } = useViewport();
  const isMobile = width <= 768;

  const closeTooltip = () => {
    if (isOpen) {
      setIsOpen(false);
    }
  };

  const toggleTooltip = () => {
    setIsOpen(!isOpen);
  };

  return (
    <>
      {!isMobile && (
        <>
          <Popover
            ref={pickerRef}
            containerClassName={`${styles['tooltip']}`}
            isOpen={isOpen}
            positions={'bottom'}
            align="center"
            content={() => (
              <div className={styles['tooltip-content']}>
                <DayPicker
                  selected={selected}
                  initialValue={initialValue}
                  onSelect={onSelect}
                  onClose={closeTooltip}
                  startMonth={startMonth}
                  endMonth={endMonth}
                />
              </div>
            )}
          >
            <div
              className={cn(styles['tooltip-trigger'], {
                [styles.active]: isOpen,
              })}
              onTouchEnd={toggleTooltip}
              onClick={toggleTooltip}
            >
              <Image
                src={'/assets/icons/icon-calendar.svg'}
                alt="icon"
                width={24}
                height={24}
              />
            </div>
          </Popover>
          {isOpen && (
            <div
              className={styles['tooltip-overlay']}
              onClick={closeTooltip}
              onTouchEnd={closeTooltip}
            />
          )}
        </>
      )}
      {isMobile && (
        <>
          <div
            className={cn(styles['tooltip-trigger'])}
            onClick={toggleTooltip}
          >
            <Image
              src={'/assets/icons/icon-calendar.svg'}
              alt="icon"
              width={24}
              height={24}
            />
          </div>
          {isOpen &&
            createPortal(
              <Modal
                onClose={closeTooltip}
                backdropClose
                classNameContainer={styles['modal-container']}
                classNameContent={styles['modal-content']}
                showCloseButton={false}
                contentOverflowHidden={false}
                showDefaultLayout={false}
                showAnimation={false}
              >
                <DayPicker
                  selected={selected}
                  initialValue={initialValue}
                  onSelect={onSelect}
                  onClose={closeTooltip}
                  startMonth={startMonth}
                  endMonth={endMonth}
                />
              </Modal>,
              document.body,
            )}
        </>
      )}
    </>
  );
};

export default memo(DayPickerTooltip);
