'use client';
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import styles from './EditPaymentForm.module.scss';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import cn from 'classnames';
import Image from 'next/image';
import Api from '@services/api';
import { maskCardNumber } from '@utils/maskCardNumber';
import { validateMasterCard, validateVisa } from '@utils/validatePaymentMethod';
import { maskCVVCode } from '@utils/maskCVVCode';
import { getMonthOptions, getYearOptions } from '@utils/getTimeOptions';
import { cacheTest, nodeListsEqualTest } from '@utils/formikHelpers';
import { ErrorMessage } from '@constants/form';
import { CardInput, Input, Select } from '@components';

const monthOptions = getMonthOptions();
const yearOptions = getYearOptions();

const EditPaymentForm = forwardRef(
  ({ nameOnCard, zip, updatePaymentInfo, toggleSaveButtonDisabled }, ref) => {
    const [location, setLocation] = useState(zip || '');
    const [isCardValid, setIsCardValid] = useState(false);
    const [error, setError] = useState('');
    const [autofilledFields, setAutofilledFields] = useState(null);

    const validateZip = async (value) => {
      const length = value?.length || 0;

      if (length >= 4 && length <= 10) {
        const response = await getLocation(value);
        const isZipValid = response.count > 0;
        const { city = null, state = null } = response?.items[0]?.links || {};

        setLocation(isZipValid ? `${city?.title}, ${state?.code}` : '');

        return isZipValid;
      } else {
        return !!location;
      }
    };

    const validateZipRef = useRef(cacheTest(validateZip));

    const triggerSubmit = async () => {
      await formik.submitForm();
    };

    useImperativeHandle(ref, () => ({
      async triggerSubmit() {
        await triggerSubmit();
      },
    }));

    const formik = useFormik({
      initialValues: {
        nameOnCard: nameOnCard || '',
        creditCard: '',
        cvvCode: '',
        expMonth: '',
        expYear: '',
        zipCode: zip || '',
      },
      onSubmit: async (values) => {
        const billingBody = JSON.stringify({
          name_on_card: values.nameOnCard,
          card: values.creditCard.replace(/\s-\s/g, ''),
          cvv: values.cvvCode,
          expiration_month: values.expMonth,
          expiration_year: values.expYear,
          zip: values.zipCode.replaceAll(' ', ''),
        });

        const billingResponse = await Api.clientside(`/payment/billing`, {
          body: billingBody,
          method: 'PUT',
        });

        if (billingResponse.status !== 'ok') {
          setError(billingResponse.message || ErrorMessage.UnexpectedBilling);
        } else {
          setError('');
          formik.resetForm({ values });
          updatePaymentInfo();
        }
      },
      validationSchema: Yup.object({
        nameOnCard: Yup.string().required(ErrorMessage.NameRequiredBilling),
        creditCard: Yup.string()
          .required(ErrorMessage.CardNumberRequired)
          .test(
            'cardIsValid',
            ErrorMessage.CardNumberPattern,
            async (value) => {
              if (document.activeElement.id === 'creditCard' && value?.length) {
                const formattedValue = value.replace(/\s-\s/g, '');
                const isValidMasterCard = validateMasterCard(formattedValue);
                const isValidVisa = validateVisa(formattedValue);

                setIsCardValid(isValidMasterCard || isValidVisa);

                return isValidMasterCard || isValidVisa;
              } else {
                return isCardValid;
              }
            },
          ),
        cvvCode: Yup.string()
          .required(ErrorMessage.CVVRequired)
          .min(3, ErrorMessage.CVVPattern),
        expMonth: Yup.string().required(ErrorMessage.ExpirationMonthRequired),
        expYear: Yup.string().required(ErrorMessage.ExpirationYearRequired),
        zipCode: Yup.string()
          .transform((value) => value?.replaceAll(' ', ''))
          .required(ErrorMessage.ZipPatternBilling)
          .min(5, ErrorMessage.ZipPatternBilling)
          .max(10, ErrorMessage.ZipPatternBilling)
          .test(
            'zipIsValid',
            ErrorMessage.ZipPatternBilling,
            validateZipRef.current,
          ),
      }),
    });

    useEffect(() => {
      formik.validateField('zipCode');
    }, [location]);

    useEffect(() => {
      if (formik.values.zipCode.length < 4) {
        setLocation('');
      }
    }, [formik.values.zipCode]);

    useEffect(() => {
      const autofilledFieldsQuery = document.querySelectorAll(
        'input:-webkit-autofill',
      );

      if (!nodeListsEqualTest(autofilledFieldsQuery, autofilledFields)) {
        setAutofilledFields(autofilledFieldsQuery);
      }
    }, [formik.values]);

    useEffect(() => {
      if (autofilledFields) {
        autofilledFields.forEach(({ id }) => {
          if (!formik.touched[id]) {
            setFieldTouched(id);
          }
        });
      }
    }, [autofilledFields]);

    useEffect(() => {
      if (toggleSaveButtonDisabled) {
        toggleSaveButtonDisabled(!(formik.isValid && formik.dirty));
      }
    }, [formik.dirty, formik.isValid, formik.values, toggleSaveButtonDisabled]);

    const formatCardNumber = (e) => {
      formik.setFieldValue('creditCard', maskCardNumber(e.target.value));
    };

    const formatCVVCode = (e) => {
      formik.setFieldValue('cvvCode', maskCVVCode(e.target.value));
    };

    const setMonthValue = (e) => {
      formik.setFieldValue('expMonth', e);
    };

    const setYearValue = (e) => {
      formik.setFieldValue('expYear', e);
    };

    const getLocation = async (zip) => {
      return await Api.clientside(`/locations?query=${zip}`);
    };

    const setFieldTouched = (field) => {
      formik.setFieldTouched(field);
    };

    return (
      <form className={styles['payment-form']} onSubmit={formik.handleSubmit}>
        <div
          className={cn(
            styles['payment-form-content'],
            styles['overflow-visible'],
          )}
        >
          <div className={styles['payment-information-container']}>
            <div className={styles['payment-information-title-container']}>
              <span className={styles['payment-information-title']}>
                Edit Payment method
              </span>
            </div>

            <div>
              <div className={styles['form-row']}>
                <Input
                  name="nameOnCard"
                  placeholder="Name on card"
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  value={formik.values.nameOnCard}
                  isTouched={formik.touched.nameOnCard}
                  error={formik.errors.nameOnCard}
                />
              </div>
            </div>

            <div>
              <span className={styles['form-row-title']}>Card</span>
              <div className={styles['form-row']}>
                <CardInput
                  name="creditCard"
                  placeholder="Card number"
                  onChange={formatCardNumber}
                  onBlur={formik.handleBlur}
                  value={formik.values.creditCard}
                  isTouched={formik.touched.creditCard}
                  error={formik.errors.creditCard}
                />

                <div className={styles['form-field']}>
                  <Input
                    name="cvvCode"
                    placeholder="CVV/CVC"
                    onChange={formatCVVCode}
                    onBlur={formik.handleBlur}
                    value={formik.values.cvvCode}
                    isTouched={formik.touched.cvvCode}
                    error={formik.errors.cvvCode}
                  />
                  <div className={styles['cvv-icon-container']}>
                    <Image
                      className={styles['cvv-icon']}
                      src={'/assets/icons/icon-cvv.svg'}
                      alt="icon cvv"
                      width={68}
                      height={20}
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className={styles['form-row']}>
              <div className={styles['form-row-column']}>
                <span
                  className={cn(styles['form-row-title'], [
                    styles['extra-padding'],
                  ])}
                >
                  Expiration date
                </span>
                <div className={styles['form-inner-row']}>
                  <Select
                    name="expMonth"
                    classNameOptions={styles['select-options']}
                    onChange={setMonthValue}
                    value={formik.values.expMonth}
                    isTouched={formik.touched.expMonth}
                    error={formik.errors.expMonth}
                    placeholder="Month"
                    options={monthOptions}
                    setFormFieldTouched={() => setFieldTouched('expMonth')}
                  />
                  <Select
                    name="expYear"
                    classNameOptions={styles['select-options']}
                    onChange={setYearValue}
                    value={formik.values.expYear}
                    isTouched={formik.touched.expYear}
                    error={formik.errors.expYear}
                    placeholder="Year"
                    options={yearOptions}
                    setFormFieldTouched={() => setFieldTouched('expYear')}
                  />
                </div>
              </div>

              <div className={styles['form-row-column']}>
                <span
                  className={cn(styles['form-row-title'], [
                    styles['extra-padding'],
                  ])}
                >
                  Billing Zip Code
                </span>
                <Input
                  name="zipCode"
                  placeholder="Zip Code"
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  value={formik.values.zipCode}
                  isTouched={formik.touched.zipCode}
                  error={formik.errors.zipCode}
                />
              </div>
            </div>

            {error && (
              <div className={styles['payment-form-error-container']}>
                <span className={styles['payment-form-error']}>{error}</span>
              </div>
            )}

            <div className={styles['payment-form-actions']}>
              <button
                type="submit"
                disabled={!(formik.isValid && formik.dirty)}
                className={styles['payment-form-button']}
              >
                Save
              </button>
            </div>
          </div>
        </div>
      </form>
    );
  },
);

EditPaymentForm.displayName = 'EditPaymentForm';

export default EditPaymentForm;
