import { Experiment } from '@amplitude/experiment-node-server';

// Prevent Cumulative Layout Shift and get the variant on the server.
// Amplitude has no problem of initializing the same experiment multiple times
// it will just return the previously assigned value.
export const AmplitudeExperimentServer = {
  experimentInstance: null,
  flagKey: {
    pricesOfferDrop: 'prices_offer_drop',
  },

  init() {
    if (!this.experimentInstance) {
      this.experimentInstance = Experiment.initializeRemote(
        process.env.amplitudeId,
        {
          fetchTimeoutMillis: 500,
          fetchRetries: 1,
          fetchRetryBackoffMinMillis: 0,
          fetchRetryTimeoutMillis: 500,
        },
      );
    }

    return this.experimentInstance;
  },

  async getVariant(user, flagKey) {
    if (!user?.user_id || !flagKey) return null;

    const experiment = this.init();

    const variants = await experiment.fetchV2(user);

    return variants[flagKey]?.value || null;
  },
};
