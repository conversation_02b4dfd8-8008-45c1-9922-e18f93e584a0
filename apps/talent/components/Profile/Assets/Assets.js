'use client';
import React, { memo } from 'react';
import styles from './Assets.module.scss';
import {
  AudioAssets,
  Tooltip,
  TooltipAudioAssets,
  VideoAssets,
} from '@components';
import Image from 'next/image';

const Assets = ({
  profileId,
  videos,
  audios,
  isPaidOrDelayed,
  demoReel,
  slate,
  ugcDemoReel,
  isMobileFromUserAgent,
}) => {
  return (
    <section className={styles['assets-section']}>
      <div className={styles['assets-container']}>
        <span className={styles.title}>Videos:</span>
        <VideoAssets
          profileId={profileId}
          videos={videos}
          isPaidOrDelayed={isPaidOrDelayed}
          demoReel={demoReel}
          slate={slate}
          ugcDemoReel={ugcDemoReel}
        />
      </div>
      <div className={styles['assets-container']}>
        <div className={styles['title-container']}>
          <span className={styles.title}>Audio:</span>
          <Tooltip content={<TooltipAudioAssets />} openOnHover clickable>
            <div className={styles['icon-hint-container']}>
              <Image
                src="/assets/icons/icon-hint-blue.svg"
                alt="icon"
                width={18}
                height={18}
              />
            </div>
          </Tooltip>
        </div>
        <AudioAssets
          audios={audios}
          isPaidOrDelayed={isPaidOrDelayed}
          isMobileFromUserAgent={isMobileFromUserAgent}
        />
      </div>
    </section>
  );
};

export default memo(Assets);
