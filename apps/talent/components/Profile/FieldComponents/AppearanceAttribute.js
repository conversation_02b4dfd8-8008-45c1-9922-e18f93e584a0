'use client';
import styles from '../ProfileInfo.module.scss';
import React from 'react';
import cn from 'classnames';

const AppearanceAttribute = ({ label, values }) => {
  return (
    <div className={cn(styles['profile-property'], styles['wide'])}>
      <div className={styles['profile-property-title']}>{label}</div>
      <div
        className={
          styles[
            values ? 'profile-property-value' : 'profile-property-value-empty'
          ]
        }
      >
        <div className={styles['profile-badge-container']}>
          {values?.map((item, index) => (
            <span
              key={index}
              className={cn(styles['profile-badge-outline'], styles['slim'])}
            >
              {item.value}
            </span>
          )) || 'n/a'}
        </div>
      </div>
    </div>
  );
};

export default AppearanceAttribute;
