/* stylelint-disable */
@use '@styles/variables' as *;

.rdp-root {
  font-weight: 500;

  --rdp-accent-color: #{$blue-100};
  --rdp-font-family:
    -apple-system, blinkmacsystemfont, var(--font-jakarta), roboto, sans-serif;
  --rdp-month_caption-font: 600 17px var(--rdp-font-family);
  --rdp-weekday-font: 600 smaller var(--rdp-font-family);
  --rdp-weekday-text-transform: uppercase;
  --rdp-weekday-opacity: 0.3;
  --rdp-day-font: 400 20px var(--rdp-font-family);
  --rdp-selected-font: 500 20px var(--rdp-font-family);
  --rdp-disabled-opacity: 0.2;
}

.dropdowns {
  padding-left: 10px;
}

.nav {
  gap: 10px;
}

.chevron {
  height: 30px;
  width: 30px;
}

.month-grid {
  --rdp-weekday-padding: 0 0rem;
  border-collapse: separate;
  border-spacing: 4px 8px;

  &:has(tbody > tr:nth-child(n + 6)) {
    --rdp-weekday-padding: 8px 0rem;
    border-spacing: 4px 0;
  }
}

.months {
  min-height: 350px;
}

.day {
  padding: 0;
}

.rdp-selected {
  color: var(--rdp-accent-color);
}

.date-disabled {
  color: $grey-40;
}

.footer {
  margin: 0 -10px;
  padding: 12px 15px;
  border-top: 0.5px solid $grey-60;
  display: flex;
  justify-content: flex-end;
}

.footer-button {
  border: none;
  background: none;
  font: 500 18px var(--rdp-font-family);
  color: var(--rdp-accent-color);
  padding: 0;
  cursor: pointer;
}
