'use client';
import React, { createContext, useContext, useEffect, useState } from 'react';
import Api from '@services/api';
import { useNotifications } from './NotificationContext';
import { extractResult } from '@utils/extractPromiseResult';
import {
  formatCandidates,
  getActiveCastingCall,
  getActiveRole,
  getCastingCallGroups,
  prepareCastingCalls,
} from '@utils/submissionsHelper';
import { TABS } from '@constants/submissions';
import { usePathname, useRouter } from 'next/navigation';
import { Amp } from '@services/amp';
import { SUBMISSIONS_LIMIT } from '@constants/pagination';
import { ErrorMessage } from '@constants/form';

const SubmissionsContext = createContext({});

export const SubmissionsProvider = ({
  children,
  initialCastingCallGroups,
  initialRole,
  initialCandidates,
  initialCastingCall,
  initialCandidatesTotal,
  clientId,
  page,
}) => {
  const [candidates, setCandidates] = useState(initialCandidates);
  const [castingCallGroups, setCastingCallGroups] = useState(
    initialCastingCallGroups,
  );
  const [role, setRole] = useState(initialRole);
  const [castingCall, setCastingCall] = useState(initialCastingCall);
  const [candidatesTotal, setCandidatesTotal] = useState(
    initialCandidatesTotal,
  );
  const [loading, setLoading] = useState(false);
  const { setNotification } = useNotifications();
  const router = useRouter();
  const path = usePathname();

  useEffect(() => {
    setCandidates(initialCandidates);
  }, [initialCandidates]);

  useEffect(() => {
    setCastingCallGroups(initialCastingCallGroups);
  }, [initialCastingCallGroups]);

  useEffect(() => {
    setRole(initialRole);
  }, [initialRole]);

  useEffect(() => {
    setCastingCall(initialCastingCall);
  }, [initialCastingCall]);

  useEffect(() => {
    setCandidatesTotal(initialCandidatesTotal);
  }, [initialCandidatesTotal]);

  const onApprove = async (candidate, tab) => {
    setLoading(true);
    const body = new FormData();

    body.append('status', 'accepted');

    const response = await Api.clientside(
      `/candidates/${candidate.candidateId}`,
      {
        method: 'PATCH',
        body,
      },
    );

    if (response.status !== 'accepted') {
      setNotification({
        type: 'error',
        message: response.message || ErrorMessage.Unexpected,
        timeout: '5000',
      });
    } else {
      Amp.track(Amp.events.elementClicked, {
        name: `candidate accepted`,
        scope: Amp.element.scope.submissionsPage,
        section:
          tab === TABS.Declined
            ? Amp.element.section.declinedTab
            : Amp.element.section.appliedTab,
        type: Amp.element.type.button,
        talent_id: candidate.id,
      });

      if (tab === TABS.Applied) {
        router.push(path);
      } else {
        await refresh(tab);
      }

      setLoading(false);
    }
  };

  const onDecline = async (candidate, tab) => {
    setLoading(true);
    const body = new FormData();

    body.append('status', 'declined');

    const response = await Api.clientside(
      `/candidates/${candidate.candidateId}`,
      {
        method: 'PATCH',
        body,
      },
    );

    if (response.status !== 'declined') {
      setNotification({
        type: 'error',
        message: response.message || ErrorMessage.Unexpected,
        timeout: '5000',
      });
    } else {
      Amp.track(Amp.events.elementClicked, {
        name: `candidate declined`,
        scope: Amp.element.scope.submissionsPage,
        section:
          tab === TABS.Shortlist
            ? Amp.element.section.shortlistTab
            : Amp.element.section.appliedTab,
        type: Amp.element.type.button,
        talent_id: candidate.id,
      });

      if (tab === TABS.Applied) {
        router.push(path);
      } else {
        await refresh(tab);
      }
    }

    setLoading(false);
  };

  const refresh = async (tab) => {
    const paths = [
      `/accounts/${clientId}/castingcalls?es=1&context[]=for_submissions&filter[state]=active&filter[status][]=approved`,
      `/accounts/${clientId}/castingcalls?es=1&context[]=for_submissions&filter[status][]=approved`,
      `/accounts/${clientId}/castingcalls?es=1&context[]=for_submissions&filter[status][]=new`,
      `/accounts/${clientId}/castingcalls?es=1&context[]=for_submissions&filter[status][]=hold`,
      `/accounts/${clientId}/castingcalls?es=1&context[]=for_submissions&filter[status][]=edited`,
      `/accounts/${clientId}/castingcalls?es=1&context[]=for_submissions&filter[status][]=declined`,
      `/accounts/${clientId}/castingcalls?es=1&context[]=for_submissions&filter[status][]=deactivated`,
    ];

    const results = await Promise.allSettled(
      paths.map((path) => Api.clientside(path)),
    );

    const [
      runningCastingCallsResponse,
      approvedCastingCallsResponse,
      newCastingCallsResponse,
      holdCastingCallsResponse,
      editedCastingCallsResponse,
      declinedCastingCallsResponse,
      deactivatedCastingCallsResponse,
    ] = results.map((result) => extractResult(result, {}));

    const castingCalls = prepareCastingCalls(
      runningCastingCallsResponse,
      approvedCastingCallsResponse,
      newCastingCallsResponse,
      holdCastingCallsResponse,
      editedCastingCallsResponse,
      declinedCastingCallsResponse,
      deactivatedCastingCallsResponse,
    );

    const newCastingCallGroups = getCastingCallGroups(castingCalls);
    const activeCastingCall = getActiveCastingCall(
      castingCalls,
      newCastingCallGroups,
      castingCall.id,
    );
    const activeRole = getActiveRole(activeCastingCall, role.id);

    let newCandidates = [];

    let newCandidatesTotal = 0;

    if (tab === TABS.Shortlist) {
      const candidatesResponse = await Api.clientside(
        `/roles/${activeRole?.id || 0}/candidates?limit=${SUBMISSIONS_LIMIT}&page=${page}&status=accepted`,
      );

      newCandidates = formatCandidates(candidatesResponse.items || []);
      newCandidatesTotal = candidatesResponse.count || 0;
    }

    if (tab === TABS.Declined) {
      const candidatesResponse = await Api.clientside(
        `/roles/${activeRole?.id || 0}/candidates?limit=${SUBMISSIONS_LIMIT}&page=${page}&status=declined`,
      );

      newCandidates = formatCandidates(candidatesResponse.items || []);
      newCandidatesTotal = candidatesResponse.count || 0;
    }

    if (tab === TABS.Invited) {
      const candidatesResponse = await Api.clientside(
        `/casting-calls/${activeCastingCall?.id || 0}/invites?limit=${SUBMISSIONS_LIMIT}&page=${page}&filter[role_id]=${activeRole?.id || 0}`,
      );

      newCandidates = candidatesResponse.items || [];
      newCandidatesTotal = candidatesResponse.count || 0;
    }

    setCastingCallGroups(newCastingCallGroups);
    setCastingCall(activeCastingCall);
    setRole(activeRole);
    setCandidates(newCandidates);
    setCandidatesTotal(newCandidatesTotal);
  };

  return (
    <SubmissionsContext.Provider
      value={{
        castingCallGroups,
        role,
        candidates,
        candidatesTotal,
        castingCall,
        loading,
        onDecline,
        onApprove,
        page,
      }}
    >
      {children}
    </SubmissionsContext.Provider>
  );
};

export const useSubmissions = () => useContext(SubmissionsContext);
