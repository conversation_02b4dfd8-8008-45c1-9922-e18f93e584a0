@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.container {
  background-image: $gradient-port-gore;
  display: flex;
  flex-flow: column nowrap;
  width: 100%;
  align-items: center;
  padding: 0 20px 60px;
  text-align: center;

  @include tablet {
    padding-top: 60px;
  }
}

.title {
  color: $white;
}

.actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  color: $white;
}

.link {
  color: $blue-100;
  font-weight: 700;
}
