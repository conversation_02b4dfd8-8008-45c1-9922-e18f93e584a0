@use '@styles/mixins' as *;
@use '@styles/variables' as *;

.profile-photo-container {
  display: grid;
  grid-template-columns: repeat(2, minmax(120px, 1fr));
  grid-gap: $space-20;
  padding-bottom: $space-30;
  padding-top: $space-10;
  grid-auto-rows: 1fr;

  @include desktop {
    grid-template-columns: repeat(3, 1fr);
  }

  @include desktopxl {
    grid-template-columns: repeat(4, 1fr);
  }
}

.profile-photo-overlay {
  position: relative;
  flex: 1 1;
}

.profile-section-title {
  text-transform: uppercase;
  color: $grey-80;
  line-height: 30px;
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 8px;
}

.title {
  color: $grey-80;
  font-size: 16px;
  font-weight: 700;
  text-transform: uppercase;
}

.profile-photo-inner-container {
  display: flex;
  flex-direction: column;
  gap: $space-10;
}

.locked-feature-container {
  display: flex;
  justify-content: space-evenly;
  flex-direction: column;
  align-items: center;
  height: calc(100% - 34px);
  padding: $space-15 $space-10;
  gap: $space-10;
  border-radius: 10px;
  background: $grey-10;
  margin-top: 34px;

  .button-unblock {
    padding: $space-5 $space-10;
    text-transform: capitalize;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 700;
    white-space: nowrap;
    min-width: 105px;
  }
}

.locked-feature-inner-container {
  display: flex;
  gap: $space-15;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.locked-feature-description-container {
  .locked-feature-title {
    font-size: 14px;
    font-weight: 600;
  }

  .locked-feature-description {
    font-size: 12px;
    color: $grey-100;
    font-weight: 400;
    margin: 0;
    line-height: 1.2;
  }
}

.icon-locked-container {
  box-shadow: $shadow-locked-icon;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 46px;
  cursor: pointer;
}

.icon-locked {
  display: flex;
}

.desktop {
  display: none;

  @include tablet {
    display: block;
  }
}

.mobile {
  @include tablet {
    display: none;
  }
}

.modal-overlay {
  padding: $space-20;
}

.modal-container {
  border-radius: 15px;
  max-width: 500px;
}

.title-container {
  display: flex;
  align-items: center;
  gap: $space-5;
}

.icon-hint-container {
  display: flex;
  align-items: center;
}
