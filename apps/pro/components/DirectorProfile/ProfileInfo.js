'use client';
import React, { memo, useState } from 'react';
import styles from './ProfileInfo.module.scss';
import Categories from './Categories';
import EditIcon from '../../public/assets/icons/icon-edit.svg';
import { useFormik } from 'formik';
import Api from '@services/api';
import { useNotifications } from '@contexts/NotificationContext';
import { Textarea } from '@components';
import * as Yup from 'yup';
import PersonalInfo from './PersonalInfo';
import { sanitizeString } from '@utils/htmlSpecialChars';
import { Amp } from '@services/amp';
import { ErrorMessage } from '@constants/form';

const ProfileInfo = ({
  profileId,
  accountId,
  categories,
  location,
  zipCode,
  resume,
  company,
  refreshUserProfiles,
  categoryOptions,
  refreshCategories,
  refreshProfileDetails,
}) => {
  const [isResumeEditing, setResumeEditing] = useState(false);
  const { setNotification } = useNotifications();

  const openResumeEditing = () => {
    setResumeEditing(true);
  };

  const closeResumeEditingAndRefresh = async () => {
    await refreshProfileDetails();
    setResumeEditing(false);
  };

  const submitResumeForm = async () => {
    await formikResume.submitForm();
  };

  const formikResume = useFormik({
    initialValues: {
      resume: resume,
    },
    onSubmit: async (values) => {
      Amp.track(Amp.events.elementClicked, {
        name: 'save about',
        scope: Amp.element.scope.editProfile,
        section: Amp.element.section.aboutForm,
        type: Amp.element.type.button,
      });

      const body = new FormData();

      values.resume = sanitizeString(values.resume);

      body.append('resume', values.resume);

      const addResumeResponse = await Api.clientside(`/profiles/${profileId}`, {
        body,
        method: 'PATCH',
      });

      if (addResumeResponse.status !== 'ok') {
        setNotification({
          type: 'error',
          message: addResumeResponse.message || ErrorMessage.Unexpected,
          timeout: '5000',
        });
      } else {
        await closeResumeEditingAndRefresh();
      }
    },
    validationSchema: Yup.object({
      resume: Yup.string().max(
        500,
        ErrorMessage.MaxCharacters.replace('{X}', '500'),
      ),
    }),
  });

  return (
    <>
      <div className={styles['profile-section-content']}>
        <section>
          <div className={styles['profile-section-title']}>
            About:
            {!isResumeEditing ? (
              <span className={styles['edit-link']} onClick={openResumeEditing}>
                <EditIcon />
              </span>
            ) : (
              <span
                className={styles['save-button']}
                onClick={submitResumeForm}
              >
                Save
              </span>
            )}
          </div>
          {!isResumeEditing && (
            <>
              {resume ? (
                <div className={styles['profile-section-text']}>{resume}</div>
              ) : (
                <div className={styles['profile-section-empty']}>
                  Introduce yourself to the community - write a short, catchy
                  description.
                  <span
                    className={styles['profile-section-empty-add-button']}
                    onClick={openResumeEditing}
                  >
                    Add
                  </span>
                </div>
              )}
            </>
          )}
          {isResumeEditing && (
            <>
              <Textarea
                name="resume"
                placeholder=""
                value={formikResume.values.resume}
                onChange={formikResume.handleChange}
                onBlur={formikResume.handleBlur}
                isTouched={formikResume.touched.resume}
                error={formikResume.errors.resume}
                hint="Max 500 characters"
                charCounter={500}
              />
            </>
          )}
        </section>
        <div className={styles['profile-section-row']}>
          <Categories
            profileId={profileId}
            categories={categories}
            categoryOptions={categoryOptions}
            refreshCategories={refreshCategories}
          />
        </div>
        <PersonalInfo
          profileId={profileId}
          accountId={accountId}
          initialLocation={location}
          zipCode={zipCode}
          company={company}
          refreshUserProfiles={refreshUserProfiles}
          refreshProfileDetails={refreshProfileDetails}
        />
      </div>
    </>
  );
};

export default memo(ProfileInfo);
