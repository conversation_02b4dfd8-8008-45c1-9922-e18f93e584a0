@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.overlay {
  @include tablet {
    padding: 0 $space-20;
  }
}

.container {
  position: absolute;
  bottom: 0;
  padding: 0 10px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  width: 100%;
  color: $black;

  @include tablet {
    position: relative;
    max-width: 500px;
    border-radius: 10px;
    padding: $space-20;
  }
}

.title {
  font-weight: 800;
  font-size: 20px;
  margin-bottom: $space-5;
  text-align: center;
}

.description {
  font-weight: 400;
  text-align: center;
  margin-bottom: $space-30;
}

.actions {
  display: grid;
  grid-template-columns: auto auto;
  grid-column-gap: $space-20;
  justify-content: center;
  align-items: center;
  padding-bottom: $space-20;

  button {
    width: 100%;
  }
}

.loading-container {
  padding: $space-30 0 $space-20;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: $space-5;
  text-align: center;
}
