import Upgrade12Client from './_client';
import { cookies, headers } from 'next/headers';
import Api from '@services/api';
import { CookieServiceServer } from '@services/cookieServiceServer';
import { getResolvedUrl } from '@utils/apiHelper';

export const metadata = {
  title: 'Upgrade Now',
};

export default async function Upgrade12Page(req) {
  const searchParams = await req.searchParams;
  const cookieStore = await cookies();
  const headerStore = await headers();
  const accountId = CookieServiceServer.getAccountCookie(cookieStore);
  const resolvedUrl = getResolvedUrl('/upgrade-12', searchParams);

  const upgradePlans = await Api.serverside(
    `/accounts/${accountId}/prices`,
    cookieStore,
    headerStore,
    resolvedUrl,
  );

  const upgradePlan = upgradePlans.prices[0];

  return (
    <Upgrade12Client
      memberLevelId={upgradePlan?.member_level_id || null}
      period={upgradePlan?.period || null}
      baseMonthPrice={upgradePlan?.base_month_price || null}
      price={upgradePlan?.price || null}
    />
  );
}
