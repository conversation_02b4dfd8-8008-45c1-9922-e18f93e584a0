'use client';
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import styles from './LocationForm.module.scss';
import { Input, Loading } from '@components';
import { useNotifications } from '@contexts/NotificationContext';
import { useAuth } from '@contexts/AuthContext';
import { useFormik } from 'formik';
import Api from '@services/api';
import * as Yup from 'yup';
import { ErrorMessage } from '@constants/form';

const LocationForm = forwardRef(
  ({ zip, toggleSaveButtonDisabled, initialLocation }, ref) => {
    const [locationLoading, setLocationLoading] = useState(false);
    const [location, setLocation] = useState(initialLocation || '');
    const { setNotification } = useNotifications();
    const { accountId } = useAuth();

    const triggerSubmit = async () => {
      await formik.submitForm();
    };

    useImperativeHandle(ref, () => ({
      async triggerSubmit() {
        await triggerSubmit();
      },
    }));

    const formik = useFormik({
      initialValues: {
        zip: zip || '',
      },
      onSubmit: async (values) => {
        const body = new FormData();

        body.append('zip', values.zip.replaceAll(' ', ''));

        const changeLocationResponse = await Api.clientside(
          `/accounts/${accountId}/location`,
          {
            body: body,
            method: 'PUT',
          },
        );

        if (changeLocationResponse.status !== 'ok') {
          setNotification({
            type: 'error',
            timeout: '5000',
            message: changeLocationResponse.message || ErrorMessage.Unexpected,
          });
        } else {
          setNotification({
            type: 'success',
            timeout: '5000',
            message: 'Data saved',
          });

          formik.resetForm({ values });
        }
      },
      validationSchema: Yup.object({
        zip: Yup.string()
          .transform((value) => value?.replaceAll(' ', ''))
          .required(ErrorMessage.ZipRequired)
          .min(5, ErrorMessage.ZipPattern)
          .max(6, ErrorMessage.ZipPattern)
          .test('zipIsValid', ErrorMessage.ZipPattern, async (value) => {
            const length = value?.length || 0;

            if (length >= 4 && length <= 6 && value !== formik.values.zip) {
              setLocationLoading(true);

              const response = await getLocation(value);

              setLocation(
                response.count > 0
                  ? `${response.items[0]?.links?.city?.title}, ${response.items[0]?.links?.state?.code}`
                  : '',
              );
              setLocationLoading(false);

              return response.count > 0;
            }

            return !!location;
          }),
      }),
    });

    useEffect(() => {
      formik.validateField('zip');

      if (!location) {
        formik.setFieldTouched('zip', true);
      }
    }, [location]);

    useEffect(() => {
      if (toggleSaveButtonDisabled) {
        toggleSaveButtonDisabled(!(formik.isValid && formik.dirty));
      }
    }, [formik.dirty, formik.isValid, formik.values, toggleSaveButtonDisabled]);

    const getLocation = async (zip) => {
      return await Api.clientside(`/locations?query=${zip}`);
    };

    return (
      <form className={styles['location-form']} onSubmit={formik.handleSubmit}>
        <div className={styles['location-form-row']}>
          <div className={styles['location-form-field']}>
            <Input
              name="zip"
              placeholder="Zip Code"
              value={formik.values.zip}
              error={formik.errors.zip}
              isTouched={formik.touched.zip}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
          </div>
          {(location || locationLoading) && (
            <div className={styles['location-form-field']}>
              {location && !locationLoading && (
                <div className={styles.location}>{location}</div>
              )}
              {locationLoading && <Loading minHeight="40px" padding="0" />}
            </div>
          )}
        </div>
        <div className={styles['location-form-actions']}>
          <button
            className={styles['location-form-button']}
            type="submit"
            disabled={!(formik.isValid && formik.dirty)}
          >
            Save changes
          </button>
        </div>
      </form>
    );
  },
);

LocationForm.displayName = 'LocationForm';

export default LocationForm;
