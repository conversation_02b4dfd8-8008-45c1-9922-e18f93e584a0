@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.navbar-link {
  position: relative;
  margin-left: $space-20;
  margin-right: $space-20;
  padding: 23px 12px;
  color: $black;
  font-weight: 300;

  h1 {
    margin: 0;
    font-weight: 300;
    font-size: 16px;
  }

  &::before {
    content: '';
    position: absolute;
    left: 50%;
    bottom: 0;
    width: 0;
    height: 4px;
    opacity: 0.5;
    background-color: $violet-100;
    background-image: $gradient-port-gore;
    transform: translate(-50%);
    transition: all 0.3s ease-in-out;
  }

  &:hover,
  &.active {
    text-decoration: none;

    &::before {
      width: calc(100% - 5px);
      opacity: 0.5;
    }
  }

  &.active::before {
    opacity: 1;
  }
}

.navbar-link:first-of-type {
  margin-left: 0;
}
