'use client';
import React, { memo, useEffect } from 'react';
import styles from './ProfileAllPhotos.module.scss';
import {
  <PERSON><PERSON>,
  Hint,
  ImageWrapper,
  Tooltip,
  TooltipPhotoAdditionalLimit,
} from '@components';
import { useState } from 'react';
import { declineActionList } from '@constants/images';
import Image from 'next/image';
import UnlockAdditionalPhotos from './UnlockFeature/UnlockAdditionalPhotos';

const ProfileAllPhotos = ({
  onImageClick,
  additionalImages,
  closeUpImage,
  sideViewImage,
  fullHeightImage,
  titleImage,
  genderTitle,
  isPaidOrDelayed,
}) => {
  const imageCountIncrementStep = 10;
  const [visibleImages, setVisibleImages] = useState([]);
  const [visibleImageCount, setVisibleImageCount] = useState(
    imageCountIncrementStep,
  );

  useEffect(() => {
    setVisibleImages(
      additionalImages?.length
        ? [...additionalImages.slice(0, visibleImageCount)]
        : [],
    );
  }, [additionalImages, visibleImageCount]);

  const loadImages = () => {
    setVisibleImageCount(visibleImageCount + imageCountIncrementStep);
  };

  return (
    <>
      <div className={styles['profile-photo-container']}>
        <div className={styles['profile-photo-inner-container']}>
          <span className={styles.title}>Headshot</span>
          <div className={styles['profile-photo-overlay']}>
            <ImageWrapper
              image={
                closeUpImage
                  ? { ...closeUpImage, type: 'close_up', title: 'Headshot' }
                  : null
              }
              onImageClick={onImageClick}
              titlePhotoSourceId={titleImage?.source_id}
              declineContentSize="medium"
              showUploadIcon
              imageType="close_up"
              imageTitle="Headshot"
              declineActionLabel={declineActionList.upload}
              genderTitle={genderTitle}
              declineAction="upload"
            />
          </div>
        </div>
        <div className={styles['profile-photo-inner-container']}>
          <span className={styles.title}>Side view</span>
          <div className={styles['profile-photo-overlay']}>
            <ImageWrapper
              image={
                sideViewImage
                  ? { ...sideViewImage, type: '3_4', title: 'Side view' }
                  : null
              }
              onImageClick={onImageClick}
              titlePhotoSourceId={titleImage?.source_id}
              declineContentSize="medium"
              showUploadIcon
              imageType="3_4"
              imageTitle="Side view"
              declineActionLabel={declineActionList.upload}
              genderTitle={genderTitle}
              declineAction="upload"
            />
          </div>
        </div>
        <div className={styles['profile-photo-inner-container']}>
          <span className={styles.title}>Full height</span>
          <div className={styles['profile-photo-overlay']}>
            <ImageWrapper
              image={
                fullHeightImage
                  ? { ...fullHeightImage, type: 'height', title: 'Full height' }
                  : null
              }
              onImageClick={onImageClick}
              titlePhotoSourceId={titleImage?.source_id}
              declineContentSize="medium"
              showUploadIcon
              imageType="height"
              imageTitle="Full height"
              declineActionLabel={declineActionList.upload}
              genderTitle={genderTitle}
              declineAction="upload"
            />
          </div>
        </div>
        {!isPaidOrDelayed && (
          <UnlockAdditionalPhotos genderTitle={genderTitle} />
        )}
      </div>
      {isPaidOrDelayed && (
        <>
          <div className={styles['title-container']}>
            <span className={styles.title}>Additional photos:</span>
            <Tooltip
              content={<TooltipPhotoAdditionalLimit />}
              openOnHover
              clickable
            >
              <div className={styles['icon-hint-container']}>
                <Image
                  src="/assets/icons/icon-hint-blue.svg"
                  alt="icon"
                  width={18}
                  height={18}
                />
              </div>
            </Tooltip>
          </div>
          <div className={styles['profile-photo-container']}>
            {additionalImages?.length < 10 ? (
              <ImageWrapper titlePhotoSourceId={titleImage?.source_id} />
            ) : (
              <Hint classNameAdditional={'slim'}>
                You have reached the maximum number of additional photos
              </Hint>
            )}
            {visibleImages.length > 0 &&
              visibleImages.map((image) => (
                <div className={styles['profile-photo-overlay']} key={image.id}>
                  <ImageWrapper
                    image={image}
                    onImageClick={onImageClick}
                    titlePhotoSourceId={titleImage?.source_id}
                    declineContentSize="medium"
                    imageType="additional"
                    declineActionLabel={declineActionList.remove}
                    genderTitle={genderTitle}
                  />
                </div>
              ))}
          </div>
          {visibleImages.length !== additionalImages.length && (
            <Button
              onClick={loadImages}
              label="Load more"
              kind="secondary"
              minWidth="320px"
            />
          )}
        </>
      )}
    </>
  );
};

export default memo(ProfileAllPhotos);
