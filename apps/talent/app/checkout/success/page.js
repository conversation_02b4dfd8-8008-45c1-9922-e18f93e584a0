import CheckoutSuccessClient from './_client';
import { cookies, headers } from 'next/headers';
import { CookieServiceServer } from '@services/cookieServiceServer';
import { getResolvedUrl } from '@utils/apiHelper';
import Api from '@services/api';
import { formatAccountLevelsResponse } from '@utils/formatAccountLevelsResponse';

export const metadata = {
  title: 'Success',
};

export default async function CheckoutSuccessPage(req) {
  const searchParams = await req.searchParams;
  const resolvedUrl = getResolvedUrl('/checkout/success', searchParams);
  const cookieStore = await cookies();
  const headerStore = await headers();

  const accountId = CookieServiceServer.getAccountCookie(cookieStore);
  const isPremium =
    CookieServiceServer.getShowPremiumCheckoutSuccess(cookieStore);
  const isLifetime =
    CookieServiceServer.getShowLifetimeCheckoutSuccess(cookieStore);
  const showStripeCheckoutSuccess =
    CookieServiceServer.getShowStripeCheckoutSuccess(cookieStore);
  const showUpgradeCheckoutSuccess =
    CookieServiceServer.getShowUpgradeCheckoutSuccess(cookieStore);

  await fetch(`${process.env.baseUrl}/api/checkout-cleanup`, {
    headers: headerStore,
    method: 'POST',
  });

  if (showStripeCheckoutSuccess) {
    const touchesResponse = await Api.serverside(
      `/accounts/${accountId}/touches`,
      cookieStore,
      headerStore,
      resolvedUrl,
    );

    const clientStatusResponse = await Api.serverside(
      '/clientstatus/info',
      cookieStore,
      headerStore,
      resolvedUrl,
    );

    const formattedTracking = {};
    // If no tracking, response is an empty array.
    // If it has tracking, response is an object
    const tracking = clientStatusResponse.info?.tracking || {};
    const trackingKeys = Object.keys(tracking);

    if (trackingKeys?.length) {
      trackingKeys.forEach((key) => {
        formattedTracking[`tracking.${key}`] = tracking[key];
      });
    }

    return (
      <CheckoutSuccessClient
        accountId={accountId}
        email={touchesResponse.links?.email?.value || ''}
        phone={touchesResponse.links?.phone?.value || ''}
        campaign={clientStatusResponse.info?.campaign || ''}
        campaignGroup={clientStatusResponse.info?.campaign_group || ''}
        tracking={formattedTracking}
        isPremium={isPremium}
        isLifetime={isLifetime}
        isStripePayment
      />
    );
  }

  const upgradeLevelResponse = showUpgradeCheckoutSuccess
    ? await Api.serverside(
        `/accounts/${accountId}/levels/upgrade`,
        cookieStore,
        headerStore,
        resolvedUrl,
      )
    : null;

  const isUpgradeLevel = upgradeLevelResponse?.start || false;

  if (isUpgradeLevel) {
    return (
      <CheckoutSuccessClient
        accountId={accountId}
        isUpgradeLevel={isUpgradeLevel}
        isPremium={isPremium}
        isLifetime={isLifetime}
        plan={upgradeLevelResponse.links.level.sale_interval_description}
        startDate={upgradeLevelResponse.start}
        endDate={upgradeLevelResponse.end}
        amount={upgradeLevelResponse.links.level.sale_amount}
        status={upgradeLevelResponse.links.level.status}
      />
    );
  } else {
    const accountLevelsResponse = await Api.serverside(
      `/accounts/${accountId}/levels/last`,
      cookieStore,
      headerStore,
      resolvedUrl,
    );
    const levels = formatAccountLevelsResponse(accountLevelsResponse);

    return (
      <CheckoutSuccessClient
        accountId={accountId}
        isUpgradeLevel={isUpgradeLevel}
        isPremium={isPremium}
        isLifetime={isLifetime}
        plan={levels.plan}
        startDate={levels.startDate}
        endDate={levels.endDate}
        amount={levels.amount}
        status={levels.status}
      />
    );
  }
}
