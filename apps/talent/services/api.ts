import { CookieService } from '@services/cookieService';
import { CookieServiceServer } from '@services/cookieServiceServer';
import { extractExpiresFromAuthenticationToken } from '@utils/authHelper';
import { ReadonlyRequestCookies } from 'next/dist/server/web/spec-extension/adapters/request-cookies';

type ApiResponse = {
  status?: string | 'error';
  messageKey?: string;
};

// Prepare required headers
const getRequiredServerHeaders = (
  cookies: ReadonlyRequestCookies,
  headers: Headers,
  resolvedUrl: string | null,
) => {
  // Authentication token. Lifespan = 2 weeks.
  const authenticationToken =
    CookieServiceServer.getAuthenticationCookie(cookies);
  // Short-term authentication token. Lifespan = 1 min.
  const volatileToken = CookieServiceServer.getVolatileCookie(cookies);
  // Marketing tracking data.
  const tracking = CookieServiceServer.getTrackingCookie(cookies);
  // User profile id.
  const profileId = CookieServiceServer.getProfileCookie(cookies);
  // Real IP address of user.
  const ip = headers.get('x-forwarded-for');
  // Performance monitoring tool.
  const blackFire = headers.get('x-blackfire-query');
  // Overriding the default referrer with the current path.
  // Use default when override not available.
  // https://en.wikipedia.org/wiki/HTTP_referer
  const referrer =
    (resolvedUrl
      ? `${process.env.baseUrl}${resolvedUrl}`
      : headers.get('referer')) || '';

  const requiredHeaders: Record<string, string> = {};

  if (authenticationToken) {
    requiredHeaders[CookieService.cookie.authentication] = authenticationToken;
  }

  if (volatileToken) {
    requiredHeaders[CookieService.cookie.volatile] = volatileToken;
  }

  if (tracking) {
    requiredHeaders[CookieService.cookie.tracking] = btoa(
      JSON.stringify(tracking),
    );
  }

  if (profileId) {
    requiredHeaders[CookieService.cookie.profile] = String(profileId);
  }

  if (ip) {
    requiredHeaders['x-forwarded-for'] = ip;
  }

  if (blackFire) {
    requiredHeaders['x-blackfire-query'] = blackFire;
  }

  if (referrer) {
    requiredHeaders['referer'] = referrer;
  }

  return requiredHeaders;
};

const serverside = async <T extends ApiResponse>(
  url: string = '',
  cookies: ReadonlyRequestCookies,
  headers: Headers,
  resolvedUrl: string | null = null,
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
  body: FormData | string | null = null,
) => {
  try {
    const requiredHeaders = getRequiredServerHeaders(
      cookies,
      headers,
      resolvedUrl,
    );
    const options: RequestInit = {
      method,
      headers: requiredHeaders,
    };

    if (body) {
      options.body = body;
    }

    const response = await fetch(
      `${process.env.internalGateway}${url}`,
      options,
    );

    return (await response.json()) as T;
  } catch (error) {
    // Will render the nearest error.js file.
    throw new Error(
      `Failed to fetch serverside. Url: ${url}. Error: ${error.message}`,
    );
  }
};

const clientside = async <T extends ApiResponse>(
  url: string,
  options: RequestInit = {},
  headers: Record<string, string> = {},
  isBlob: boolean = false,
): Promise<T | ApiResponse | Response | Blob> => {
  const currentAuthToken = CookieService.getAuthenticationCookie();
  const currentVolatile = CookieService.getVolatileCookie();
  const currentTracking = CookieService.getTrackingCookie();
  const currentProfile = CookieService.getProfileCookie();

  if (currentAuthToken) {
    headers[CookieService.cookie.authentication] = currentAuthToken;
  }

  if (currentVolatile) {
    headers[CookieService.cookie.volatile] = currentVolatile;
  }

  if (currentTracking) {
    headers[CookieService.cookie.tracking] = btoa(
      JSON.stringify(currentTracking),
    );
  }

  if (currentProfile) {
    headers[CookieService.cookie.profile] = String(currentProfile);
  }

  let response: Response | null = null;

  try {
    response = await fetch(`${process.env.publicGateway}${url}`, {
      ...options,
      headers,
    });
  } catch (error) {
    console.error('Failed to fetch clientside. Url: ', url, error);
  }

  if (
    !response ||
    response.statusText !== 'OK' ||
    (response.status !== 200 && response.status !== 403)
  ) {
    return { status: 'error' };
  }

  let data: T | ApiResponse | Response | Blob = {};

  if (response.headers.get('content-type') === 'application/pdf') {
    data = response;
  } else if (isBlob) {
    data = !response.ok ? { status: 'error' } : await response.blob();
  } else {
    data = (await response.json()) as T;
  }

  if (
    typeof window !== 'undefined' &&
    ((data as ApiResponse).messageKey === 'error.access_denied' ||
      response.status === 403)
  ) {
    CookieService.cleanAuthenticationCookies();
    window.location.href = process.env.publicUrl;
  }

  const nextAuthToken = response.headers.get(
    CookieService.cookie.authentication,
  );
  const nextVolatile = response.headers.get(CookieService.cookie.volatile);

  if (nextVolatile) {
    CookieService.setVolatileCookie(nextVolatile);
  }

  if (nextAuthToken && currentAuthToken !== nextAuthToken) {
    CookieService.setAuthenticationCookie(nextAuthToken);
    CookieService.setExpiresCookie(
      extractExpiresFromAuthenticationToken(nextAuthToken),
    );
  }

  return data;
};

const exports = {
  clientside,
  serverside,
};

export default exports;
