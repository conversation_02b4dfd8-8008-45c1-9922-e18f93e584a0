'use client';
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { Loading } from '@components';
import IconCross from '../../../public/assets/icons/icon-close-4.svg';
import { useNotifications } from '@contexts/NotificationContext';
import styles from './FileListUpload.module.scss';
import { useAuth } from '@contexts/AuthContext';
import cn from 'classnames';
import { OutgoingAttachment } from '../OutgoingAttachment/OutgoingAttachment';
import toBase64 from '@utils/base64encoder';
import Api from '@services/api';

const FileListUpload = forwardRef(
  ({ onUploadEnd, savedFiles, openImageAttachment }, ref) => {
    const [isUploading, setIsUploading] = useState(false);
    const [files, setFiles] = useState(savedFiles || []);
    const { setNotification } = useNotifications();
    const { profileId } = useAuth();
    const fileInput = useRef();

    const uploadAttachments = async (e) => {
      setIsUploading(true);
      const selectedFiles = e.target.files || [];
      const newFiles = [];

      for (const file of selectedFiles) {
        if (file.size >= 8387584) {
          setNotification({
            type: 'error',
            message: `"${file.name}" is too large! Maximum size: 8MB.`,
            timeout: '5000',
          });
        } else {
          const newFile = await uploadAttachment(file);

          if (newFile) {
            newFiles.push(newFile);
          }
        }
      }

      setFiles([...files, ...newFiles]);
      e.target.value = '';
      setIsUploading(false);
    };

    useImperativeHandle(ref, () => ({
      attach: () => {
        fileInput.current.click();
      },
      clear: () => {
        clear();
      },
    }));

    useEffect(() => {
      onUploadEnd(files);
    }, [files]);

    const uploadAttachment = async (uploadedFile) => {
      const fileBase64 = await toBase64(uploadedFile);
      const body = new FormData();

      body.append('contents', fileBase64);
      body.append('filename', uploadedFile.name);
      body.append('profile', profileId);

      const response = await Api.clientside(`/files`, {
        body,
        method: 'POST',
      });

      if (response.status !== 'ok') {
        setNotification({
          type: 'error',
          message: response.message || 'File upload failed',
          timeout: '5000',
        });

        return null;
      } else {
        return !files.find((file) => file.id === response.id)
          ? {
              id: response.id,
              content_type: response.mimetype,
              name: '',
              path: response.uri,
            }
          : null;
      }
    };

    const removeFile = (id) => {
      setFiles(files.filter((file) => file.id !== id));
    };

    const clear = () => {
      setFiles([]);
    };

    return (
      <div className={styles['container']}>
        <input
          ref={fileInput}
          type="file"
          className={styles['file-input']}
          multiple
          name="file"
          onChange={uploadAttachments}
          disabled={isUploading}
        />
        <div
          className={cn(styles.files, {
            [styles.attached]: files.length > 0,
          })}
        >
          {files.map((file, i) => (
            <div className={styles.file} key={i}>
              <OutgoingAttachment
                attachment={file}
                className={styles['attached-file']}
                displaySize={30}
                openImageAttachment={() => openImageAttachment(file.path)}
              />
              <IconCross
                onClick={() => {
                  removeFile(file.id);
                }}
                className={styles['delete-btn']}
              />
            </div>
          ))}
          {isUploading && (
            <div>
              <Loading padding="0" justifyContent="flex-start" />
            </div>
          )}
        </div>
      </div>
    );
  },
);

FileListUpload.displayName = 'FileListUpload';

export default FileListUpload;
