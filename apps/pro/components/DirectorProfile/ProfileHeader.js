'use client';
import React, { memo, useState, useEffect } from 'react';
import { useProfileContext } from '@contexts/ProfileContext';
import Api from '@services/api';
import styles from './Profile.module.scss';
import { TextField } from '@mui/material';
import { useAuth } from '@contexts/AuthContext';
import { useNotifications } from '@contexts/NotificationContext';
import cn from 'classnames';
import EditIcon from '../../public/assets/icons/icon-edit.svg';
import { isInViewPort } from '@utils/isInViewPort';
import { useViewport } from '@utils/useViewport';
import { Amp } from '@services/amp';
import { NAME_LONG_REGEX } from '@constants/form';

const ProfileHeader = ({ refreshUserProfiles, refreshProfileDetails }) => {
  const { firstName, lastName, fullName } = useProfileContext();
  const { setNotification } = useNotifications();
  const { profileId } = useAuth();
  const { width } = useViewport();

  const [isNameContainerEditing, setNameContainerEditing] = useState(false);
  const [currentFirstName, setCurrentFirstName] = useState(firstName);
  const [currentLastName, setCurrentLastName] = useState(lastName);

  useEffect(() => {
    if (isInViewPort(width, 'tablet', 'max')) {
      document.body.style.overflow = isNameContainerEditing
        ? 'hidden'
        : 'unset';
    }
  }, [isNameContainerEditing, width]);

  useEffect(() => {
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, []);

  const editName = () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'edit name',
      scope: Amp.element.scope.editProfile,
      section: Amp.element.section.nameForm,
      type: Amp.element.type.button,
    });
    setNameContainerEditing(true);
  };

  const closeNameContainerEditing = () => {
    setNameContainerEditing(false);
  };

  const saveName = async () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'save name',
      scope: Amp.element.scope.editProfile,
      section: Amp.element.section.nameForm,
      type: Amp.element.type.button,
    });

    if (isValidName(currentFirstName) && isValidName(currentLastName)) {
      const body = new FormData();

      body.append('firstname', currentFirstName);
      body.append('lastname', currentLastName);

      const response = await Api.clientside(`/profiles/${profileId}`, {
        method: 'PATCH',
        body: body,
      });

      if (response.status !== 'ok') {
        setNotification({
          type: 'error',
          message: response.message,
          timeout: '5000',
        });
      } else {
        await refreshUserProfiles();
        await refreshProfileDetails();
        closeNameContainerEditing();
      }
    }
  };

  const showErrorMessage = (text) => {
    if (!text.length) {
      return 'Please provide the name';
    } else {
      return isValidName(text)
        ? ''
        : "Name must be 1-35 characters, letters, spaces, - and/or '";
    }
  };

  const isValidName = (name) => {
    const reg = new RegExp(NAME_LONG_REGEX);

    return name?.length && reg.test(name);
  };

  return (
    <>
      <div id="name" className={styles['profile-overview-header']}>
        {!isNameContainerEditing ? (
          <>
            <h1
              className={cn(styles['profile-name'], {
                [styles.smaller]: lastName.length > 13 || firstName.length > 13,
              })}
            >
              {fullName}
              <div onClick={editName} className={styles['edit-link-header']}>
                <EditIcon />
              </div>
            </h1>
          </>
        ) : (
          <>
            <div
              onClick={closeNameContainerEditing}
              className={cn(styles['edit-name-layer'])}
            />
            <div className={styles['profile-section-title-edit']}>
              <div className={styles['profile-section-title']}>
                first AND LAST name:
                <button
                  className={cn(
                    styles['save-button'],
                    styles['save-button-name-desktop'],
                  )}
                  onClick={saveName}
                >
                  Save
                </button>
              </div>
              <div className={styles['name-field-change']}>
                <TextField
                  label="First name"
                  sx={{
                    '& .MuiFormLabel-root': {
                      display: 'none;',
                    },
                    '& legend': {
                      display: 'none;',
                    },
                    '& .MuiOutlinedInput-input': {
                      padding: '16px 26px',
                    },
                    '& .MuiOutlinedInput-root': {
                      color: '#000',
                      fontWeight: '600',
                      borderRadius: '11px',
                    },
                    '& .MuiOutlinedInput-notchedOutline': {
                      border: '1px dashed #bec7d6',
                    },
                    '& .MuiOutlinedInput-root:hover .MuiOutlinedInput-notchedOutline':
                      {
                        borderColor: '#979da7',
                      },
                    '& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline':
                      {
                        borderColor: '#979DA7',
                        borderWidth: '1px',
                      },
                    '& .MuiFormHelperText-root.Mui-error': {
                      marginTop: '7px',
                      marginRight: '0',
                      marginBottom: '0',
                      marginLeft: '0',
                      fontSize: '0.75rem',
                      lineHeight: '1.35',
                      color: '#ed0000',
                    },
                    '  @media screen and (max-width: 768px)': {
                      '& .MuiFormLabel-root': {
                        display: 'block',
                        color: '#353',
                        fontWeight: '300',
                        transform: 'translate(0px, 8px) scale(1)',
                      },
                      '& .MuiFormLabel-root.Mui-focused': {
                        transform: 'translate(0px, -9px) scale(0.75)',
                      },
                      '&  .MuiInputLabel-shrink': {
                        transform: 'translate(0px, -9px) scale(0.75)',
                      },
                      '& .MuiOutlinedInput-notchedOutline': {
                        border: 'none',
                        borderBottom: '1px solid #353535',
                        borderRadius: '0',
                      },
                      '& .MuiOutlinedInput-input': {
                        padding: '8px 0 0',
                      },
                    },
                  }}
                  value={currentFirstName}
                  onChange={(event) => {
                    setCurrentFirstName(event.target.value);
                  }}
                  error={!isValidName(currentFirstName)}
                  helperText={showErrorMessage(currentFirstName)}
                />
                <TextField
                  label="Last name"
                  sx={{
                    '& .MuiFormLabel-root': {
                      display: 'none;',
                    },
                    '& legend': {
                      display: 'none;',
                    },
                    '& .MuiOutlinedInput-input': {
                      padding: '16px 26px',
                    },
                    '& .MuiOutlinedInput-root': {
                      color: '#000',
                      fontWeight: '600',
                      borderRadius: '11px',
                    },
                    '& .MuiOutlinedInput-notchedOutline': {
                      border: '1px dashed #bec7d6',
                    },
                    '& .MuiOutlinedInput-root:hover .MuiOutlinedInput-notchedOutline':
                      {
                        borderColor: '#979da7',
                      },
                    '& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline':
                      {
                        borderColor: '#979DA7',
                        borderWidth: '1px',
                      },
                    '& .MuiFormHelperText-root.Mui-error': {
                      marginTop: '7px',
                      marginRight: '0',
                      marginBottom: '0',
                      marginLeft: '0',
                      fontSize: '0.75rem',
                      lineHeight: '1.35',
                      color: '#ed0000',
                    },
                    '@media screen and (max-width: 768px)': {
                      '& .MuiFormLabel-root': {
                        display: 'block',
                        color: '#353',
                        fontWeight: '300',
                        transform: 'translate(0px, 8px) scale(1)',
                      },
                      '& .MuiFormLabel-root.Mui-focused': {
                        transform: 'translate(0px, -9px) scale(0.75)',
                      },
                      '&  .MuiInputLabel-shrink': {
                        transform: 'translate(0px, -9px) scale(0.75)',
                      },
                      '& .MuiOutlinedInput-notchedOutline': {
                        border: 'none',
                        borderBottom: '1px solid #353535',
                        borderRadius: '0',
                      },
                      '& .MuiOutlinedInput-input': {
                        padding: '8px 0 0',
                      },
                    },
                  }}
                  value={currentLastName}
                  onChange={(event) => {
                    setCurrentLastName(event.target.value);
                  }}
                  error={!isValidName(currentLastName)}
                  helperText={showErrorMessage(currentLastName)}
                />
                <button
                  className={cn(
                    styles['save-button'],
                    styles['save-button-name-mobile'],
                  )}
                  onClick={saveName}
                >
                  Save
                </button>

                <div
                  onClick={closeNameContainerEditing}
                  className={cn(
                    styles['profile-panel-info-close'],
                    styles['edit-name-close'],
                  )}
                />
              </div>
            </div>
          </>
        )}
      </div>
    </>
  );
};

export default memo(ProfileHeader);
