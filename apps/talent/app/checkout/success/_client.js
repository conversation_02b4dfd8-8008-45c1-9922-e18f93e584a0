'use client';
import { useEffect, useState } from 'react';
import styles from '@styles/checkout.module.scss';
import { useAnalytics } from 'use-analytics';
import { sha256 } from 'js-sha256';
import cn from 'classnames';
import dayjs from 'dayjs';
import { useAuth } from '@contexts/AuthContext';
import { GTM_ACTIONS, GTM_CATEGORIES, GTM_EVENTS } from '@constants/analytics';
import { Amp } from '@services/amp';
import Api from '@services/api';
import { Button, Loading, MainLayout } from '@components';

const CheckoutSuccessClient = ({
  accountId,
  isStripePayment = false,
  isUpgradeLevel = false,
  plan = null,
  startDate = null,
  endDate = null,
  amount = null,
  status = null,
  email = null,
  campaign = null,
  campaignGroup = null,
  tracking = null,
  phone = null,
  isPremium,
  isLifetime,
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [subscriptionData, setSubscriptionData] = useState({
    plan,
    startDate,
    endDate,
    amount,
    status,
  });
  const { accountLevelVerify, userProfiles, getIsProfileOutsideUSA } =
    useAuth();
  const { track } = useAnalytics();
  const isProfileOutsideUSA = getIsProfileOutsideUSA();

  let invoiceHash;

  if (typeof window !== 'undefined' && (isStripePayment || isUpgradeLevel)) {
    invoiceHash = window.sessionStorage?.getItem('invoiceHash');
  }

  useEffect(() => {
    let interval;

    if (invoiceHash) {
      let timesRun = 0;

      interval = setInterval(async function () {
        timesRun += 1;
        let invoiceResponse = await getInvoice();

        if (invoiceResponse.status === 'paid') {
          clearInterval(interval);
          if (isStripePayment) {
            setSubscriptionDataState(invoiceResponse);
          }
          trackUpgrade(invoiceResponse);
          await accountLevelVerify();
          setIsLoading(false);
        } else if (timesRun >= 10 && invoiceResponse.status !== 'paid') {
          clearInterval(interval);
          invoiceResponse = await syncInvoice();
          if (isStripePayment) {
            setSubscriptionDataState(invoiceResponse);
          }
          trackUpgrade(invoiceResponse);
          await accountLevelVerify();
          setIsLoading(false);
        }
      }, 1000);
    } else {
      setIsLoading(false);
    }

    return () => {
      clearInterval(interval);
      window.sessionStorage?.removeItem('invoiceHash');
    };
  }, []);

  const setSubscriptionDataState = (data) => {
    setSubscriptionData({
      plan: data.description,
      startDate: data.deffered,
      endDate: data.end_time,
      amount: data.amount,
      status: data.status,
    });
  };

  const trackUpgrade = async (data) => {
    const generateRandomPhone = `+1555${Math.floor(Math.random() * ********)}`;

    track(GTM_EVENTS.interaction, {
      target: GTM_CATEGORIES.upgradeSuccess,
      action: GTM_ACTIONS.upgrade,
      label: data.id,
      items: [
        {
          item_id: data.id,
          item_name: data.description,
          price: data.amount,
          item_category: 'subscription',
          quantity: 1,
        },
      ],
      price: data.amount,
      value: data.amount,
      upgrade_invoice_id_hash: sha256(String(data.id)),
      upgrade_invoice_id: data.id,
      upgrade_plan_price: data.amount,
      user_id: userProfiles[0]?.clientId,
      client: {
        account_id_hash: sha256(String(accountId)),
        user_id: userProfiles[0]?.clientId,
        email_hash: sha256(String(email)),
        email: email,
        phone: phone || generateRandomPhone,
        campaign_id: campaign,
        campaign_group: campaignGroup,
      },
      ...tracking,
    });
  };

  const getInvoice = async () => {
    return await Api.clientside(`/invoices/${invoiceHash}`);
  };

  const syncInvoice = async () => {
    await Api.clientside(`/invoices/${invoiceHash}/sync`, {
      method: 'PUT',
    });

    return await getInvoice();
  };

  const navigate = () => {
    window.location.href = `${process.env.publicUrl}/castingcalls`;
  };

  useEffect(() => {
    Amp.track(Amp.events.viewCheckoutSuccess, {
      is_premium: isPremium,
      is_lifetime: isLifetime,
    });
  }, []);

  return (
    <MainLayout isDefaultHeaderVisible isUserMenuVisible>
      {!isLoading && (
        <section className={styles['checkout-success-section']}>
          <div className={styles['checkout-success-section-header']}>
            <h1 className={styles['checkout-success-section-title']}>
              {subscriptionData.status === 'pending'
                ? `You're all set.`
                : 'Congratulations!'}
            </h1>
            <p className={styles['checkout-success-section-description']}>
              {subscriptionData.status === 'pending'
                ? 'Thank you for choosing allcasting as your companion in the entertainment industry!'
                : 'Everything you need to find the next casting call.'}
            </p>
          </div>
          <div className={styles['checkout-success-card-container']}>
            <div className={styles['checkout-success-card']}>
              <div className={styles['checkout-success-card-header']}>
                <span>Purchase Details:</span>
              </div>
              <div className={styles['checkout-success-card-content']}>
                <div
                  className={styles['checkout-success-card-details-container']}
                >
                  <div className={styles['checkout-success-card-details']}>
                    <span
                      className={styles['checkout-success-card-details-title']}
                    >
                      Plan:
                    </span>
                    <span
                      className={styles['checkout-success-card-details-value']}
                    >
                      {subscriptionData.plan.toLowerCase() === '120 month plan'
                        ? 'Lifetime Subscription'
                        : subscriptionData.plan}
                    </span>
                  </div>
                  {subscriptionData.plan.toLowerCase() !== '120 month plan' && (
                    <div className={styles['checkout-success-card-details']}>
                      <span
                        className={
                          styles['checkout-success-card-details-title']
                        }
                      >
                        Period:
                      </span>
                      <span
                        className={
                          styles['checkout-success-card-details-value']
                        }
                      >
                        {dayjs(subscriptionData.startDate).format('M/D/YY')} –{' '}
                        {dayjs(subscriptionData.endDate).format('M/D/YY')}
                      </span>
                    </div>
                  )}
                  <div className={styles['checkout-success-card-details']}>
                    <span
                      className={styles['checkout-success-card-details-title']}
                    >
                      Amount:
                    </span>
                    <span
                      className={styles['checkout-success-card-details-value']}
                    >
                      ${subscriptionData.amount}
                      {isProfileOutsideUSA && <>USD</>}
                    </span>
                  </div>
                  <div className={styles['checkout-success-card-details']}>
                    <span
                      className={styles['checkout-success-card-details-title']}
                    >
                      Status:
                    </span>
                    <span
                      className={cn(
                        styles['checkout-success-card-details-value'],
                        styles[subscriptionData.status],
                      )}
                    >
                      {subscriptionData.status === 'paid' && isUpgradeLevel
                        ? 'Completed'
                        : subscriptionData.status}
                    </span>
                  </div>
                </div>
                <p className={styles['checkout-success-card-message']}>
                  {isUpgradeLevel
                    ? `You will be switched to ${
                        subscriptionData.plan
                      } on ${dayjs(subscriptionData.startDate).format(
                        'M/D/YY',
                      )}.`
                    : 'Hey! Now you can use the service in full - apply to casting calls and read messages!'}
                </p>
                <div
                  className={styles['checkout-success-card-button-container']}
                >
                  <Button
                    label="Continue"
                    color="blue"
                    minWidth="220px"
                    onClick={navigate}
                  />
                </div>
              </div>
            </div>
          </div>
          <div className={styles['bottom-text']}>
            <p className={styles['description']}>
              With allcasting subscription, you gain the tools, connections,
              knowledge, and support you&apos;ve been missing to help you get
              discovered and become a successful actor or model.
            </p>
            <br />
            {isPremium && (
              <>
                <strong>Your premium subscription features:</strong>
                <ul>
                  <li>
                    3000-point rating boost, increasing your profile&apos;s
                    visibility.
                  </li>
                  <li>
                    Get featured on the allcasting Instagram account, showcasing
                    your talent and profile.
                  </li>
                  <li>
                    Special targeted mailing list, sent out directly to casting
                    directors.
                  </li>
                  <li>
                    Instant 24/7 support for addressing any issues and queries.
                  </li>
                </ul>
                <br />
              </>
            )}
            <strong>Your allcasting subscription entitles you to:</strong>
            <ul>
              <li>Professional portfolio</li>
              <li>Unlimited submissions to casting calls</li>
              <li>Priority position in search</li>
              <li>Exchange messages with casting directors</li>
              <li>Photo Analyzer</li>
              <li>Personalized profile URL</li>
              <li>Training & education</li>
            </ul>
          </div>
        </section>
      )}
      {isLoading && (
        <div className={styles['loading-container']}>
          <Loading />
          <p>
            Please do not leave or refresh the page and wait while we are
            processing your payment. <br /> This can take a few minutes.
          </p>
        </div>
      )}
    </MainLayout>
  );
};

export default CheckoutSuccessClient;
