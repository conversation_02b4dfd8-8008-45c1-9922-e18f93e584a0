{"name": "next-allcasting-com", "version": "1.0.0", "private": true, "engines": {"node": "20.x", "npm": "10.x"}, "scripts": {"dev": "next dev", "build": "next build", "start": "NODE_OPTIONS='-r next-logger' next start", "lint": "next lint", "lint:fix": "next lint --fix", "prettier": "prettier --check .", "prettier:fix": "prettier --write .", "lint:all": "npm run prettier:fix & npm run lint:fix", "cypress:open": "cypress open", "cypress:run:dev:all": "cypress run --config-file cypress.dev.config.js", "cypress:run:dev:essential": "cypress run --config-file cypress.dev.config.js --browser chrome --spec 'cypress/e2e/level-1-essential/**/*'", "cypress:run:dev:vital": "cypress run --config-file cypress.dev.config.js --browser chrome --spec 'cypress/e2e/level-2-vital/**/*'", "cypress:run:test": "cypress run --config-file cypress.stage.config.js --browser firefox", "cypress:run:prod": "cypress run --config-file cypress.config.js --browser firefox", "generate:icons": "node scripts/runGenerate.js"}, "dependencies": {"@amplitude/analytics-browser": "^2.12.2", "@analytics/google-tag-manager": "^0.6.0", "@aws-sdk/client-s3": "^3.808.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@entertech/filter-service": "^1.1.6", "@livechat/widget-react": "^1.3.4", "@mui/material": "^6.4.9", "@sentry/nextjs": "^8.55.0", "analytics": "^0.8.16", "classnames": "^2.5.1", "cookies-next": "^4.3.0", "dayjs": "^1.11.13", "embla-carousel-autoplay": "^8.5.2", "embla-carousel-react": "^8.5.2", "formik": "^2.4.6", "ioredis": "^5.6.0", "is-reachable": "^5.2.1", "next": "^14.2.26", "next-logger": "^5.0.1", "next-sitemap": "^4.2.3", "nextjs-progressbar": "^0.0.16", "pino": "^9.6.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-lottie-player": "^2.1.0", "react-masonry-css": "^1.0.16", "react-scroll-parallax": "^3.4.5", "react-tiny-popover": "^8.1.6", "sharp": "^0.33.5", "use-analytics": "^1.1.0", "yup": "^1.6.1"}, "devDependencies": {"@svgr/webpack": "^8.1.0", "cypress": "^13.17.0", "eslint": "^8.57.1", "eslint-config-next": "^14.2.26", "eslint-config-prettier": "^9.1.0", "eslint-plugin-unused-imports": "^3.2.0", "prettier": "^3.5.3", "sass": "^1.86.0", "stylelint": "^16.17.0", "stylelint-config-standard": "^36.0.1", "stylelint-config-standard-scss": "^13.1.0", "stylelint-webpack-plugin": "^5.0.1"}}