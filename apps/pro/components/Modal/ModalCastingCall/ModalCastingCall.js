'use client';
import React, { memo, useEffect, useState } from 'react';
import { Loading, Modal } from '@components';
import styles from './ModalCastingCall.module.scss';
import Api from '@services/api';
import { useNotifications } from '@contexts/NotificationContext';
import dayjs from 'dayjs';
import Link from 'next/link';
import { ErrorMessage } from '@constants/form';

const ModalCastingCall = ({ onClose = () => {}, castingCallId }) => {
  const [loading, setLoading] = useState(true);
  const [castingCall, setCastingCall] = useState({});
  const { setNotification } = useNotifications();

  useEffect(() => {
    const getCastingCall = async () => {
      const response = await Api.clientside(`/calls/get?id=${castingCallId}`);

      if (response.status !== 'ok') {
        setNotification({
          type: 'error',
          message: response.message || ErrorMessage.Unexpected,
          timeout: '5000',
        });
        onClose();
      } else {
        setCastingCall(response.data);
      }

      setLoading(false);
    };

    getCastingCall().catch();
  }, []);

  return (
    <Modal
      onClose={onClose}
      backdropClose
      showDefaultLayout={false}
      classNameContainer={styles['modal-container']}
      floatCloseButton
    >
      {loading ? (
        <div className={styles['loading-container']}>
          <Loading minHeight="40px" padding="0" />
        </div>
      ) : (
        <div className={styles.container}>
          <span className={styles.title}>{castingCall.title}</span>
          <div className={styles.details}>
            <span>ID: {castingCall.id}</span>
            {castingCall.location && (
              <span className={styles['dot-divider']}>
                {castingCall.location}
              </span>
            )}
            {castingCall.expiration && (
              <span className={styles['dot-divider']}>
                {dayjs(castingCall.expiration).format('M/D/YYYY')}
              </span>
            )}
            <span className={styles['dot-divider']} />
            <Link
              className={styles.link}
              href={`/castingcall/${castingCallId}/edit`}
            >
              Edit casting call
            </Link>
            <span className={styles['dot-divider']} />
            <Link
              className={styles.link}
              href={`/castingcall/${castingCallId}`}
            >
              Visit page
            </Link>
          </div>
          <div className={styles.details}>
            <div className={styles.category}>{castingCall.category?.name}</div>
          </div>
          <p className={styles.description}>{castingCall.description}</p>
        </div>
      )}
    </Modal>
  );
};

export default memo(ModalCastingCall);
