@use '@styles/mixins' as *;
@use '@styles/variables' as *;

.embla {
  position: relative;
  margin-left: auto;
  margin-right: auto;
}

.embla-viewport {
  overflow: hidden;
  width: 100%;
}

.embla-container {
  display: flex;
  user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}

.embla-slide {
  position: relative;
  flex: 0 0 100%;
  cursor: grab;

  &:active {
    cursor: grabbing;
  }
}

.paginator-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: $space-30;
}

// Custom overrides

.carousel-testimonials {
  margin: 0 0 (-$space-40);

  .embla-slide {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.carousel-profile-gallery {
  position: unset;

  .embla-slide {
    cursor: initial;
  }
}

.carousel-promos {
  margin: 0 $space-40;
}

.carousel-main-images {
  margin: 0 0 (-$space-40);

  .embla-slide {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.carousel-main-images-pagination {
  padding-top: 0;
  padding-bottom: $space-10;
}
