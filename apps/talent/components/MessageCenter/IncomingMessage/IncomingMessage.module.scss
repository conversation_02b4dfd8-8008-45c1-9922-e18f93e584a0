@use '@styles/mixins' as *;
@use '@styles/variables' as *;

$message-interaction-width: $space-25;
$message-interaction-width-full: 150px;

.message-row {
  display: flex;
  max-width: 100%;
  margin-bottom: 6px;
}

.message-meta {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  font-size: 12px;
  line-height: 1;
  margin-bottom: 8px;
  color: $grey-80;
  font-weight: 300;
  gap: $space-5;
  margin-right: $message-interaction-width;
  padding: 0 $space-20;

  @include tablet {
    margin-right: $message-interaction-width-full;
  }
}

.message-wrapper {
  flex-direction: row;
  display: flex;
  align-items: center;
  width: 100%;

  .interaction-btn {
    padding-left: $space-10;
  }
}

.message-body {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.message-content {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  width: 100%;

  .message-interaction {
    flex: 0 0 $message-interaction-width;

    .interaction-btn {
      align-items: center;
      gap: $space-5;
      white-space: nowrap;
      cursor: pointer;

      &:hover {
        text-decoration: underline;
      }
    }

    @include desktop {
      flex: 0 0 $message-interaction-width-full;

      .interaction-btn {
        visibility: hidden;
      }
    }
  }

  @include tablet {
    &:hover {
      .message-interaction {
        .interaction-btn {
          visibility: visible;
        }
      }
    }
  }
}

.message {
  flex: 1;
  max-width: 100%;
  padding: $space-20;
  overflow-wrap: word-break;
  word-break: word-break;
  hyphens: auto;
  margin-bottom: $space-10;
  color: $black;
  background: $grey-10;
  border-radius: 10px 10px 10px 0;
  align-content: flex-start;

  &.is-reported {
    font-style: italic;
    color: $grey-60;
  }
}

.interaction-btn-text {
  display: none;
  margin-left: $space-5;

  @include desktop {
    display: inline;
  }
}

.report-btn {
  color: $red-60;
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
}

.attachment-container {
  display: flex;
  flex-flow: row wrap;
  gap: 8px;
}

.attachment {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  margin-bottom: 8px;
  border: 0.5px solid $grey-40;
  padding: 1px;
}

.link-container {
  margin: $space-10 0;

  &:not(:last-of-type) {
    margin-bottom: 0;
  }
}

.link {
  color: $blue-100;
  font-weight: 400;
  font-size: 15px;
}

.inline-link {
  text-decoration: underline;
  color: $blue-100;
}
