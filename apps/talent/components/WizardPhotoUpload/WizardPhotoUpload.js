'use client';
import React, { memo, useState } from 'react';
import styles from './WizardPhotoUpload.module.scss';
import formStyles from '../WizardForm/WizardForm.module.scss';
import {
  Button,
  CropToolOverlay,
  Loading,
  PhotoUploadTemplate,
} from '@components';
import { useRouter } from 'next/navigation';
import { useAuth } from '@contexts/AuthContext';
import { GTM_ACTIONS, GTM_CATEGORIES, GTM_EVENTS } from '@constants/analytics';
import { useAnalytics } from 'use-analytics';
import { CookieService } from '@services/cookieService';
import { Amp } from '@services/amp';
import IconCompleted from '../../public/assets/icons/icon-completed-thick.svg';
import cn from 'classnames';
import { useProfileContext } from '@contexts/ProfileContext';

const WizardPhotoUpload = ({ gender, redirectPath }) => {
  const [showCropTool, setShowCropTool] = useState(false);
  const [selectedImageUrl, setSelectedImageUrl] = useState(null);
  const [isFinishing, setIsFinishing] = useState(false);

  const { refreshUserProfiles, accountLevel } = useAuth();
  const router = useRouter();
  const { track } = useAnalytics();
  const { closeUpImage } = useProfileContext();

  const finishRegistration = async () => {
    setIsFinishing(true);
    await refreshUserProfiles();

    track(GTM_EVENTS.interaction, {
      target: GTM_CATEGORIES.wizard,
      action: GTM_ACTIONS.complete,
    });

    CookieService.setFirstTimeVisitor(true);

    Amp.track(Amp.events.completeWizardStepTwo, {
      type: 'talent',
      headshot_uploaded: !!selectedImageUrl,
    });

    if (accountLevel?.isPaidOrDelayed) {
      router.push('/');
    } else {
      router.push(redirectPath || `/upgrade`);
    }
  };

  const onFileInputChange = (imageUrl) => {
    setSelectedImageUrl(imageUrl);
    setShowCropTool(true);
  };

  const closeCropTool = () => {
    setShowCropTool(false);
  };

  const footerTitle = !closeUpImage ? (
    <h2 className={formStyles.title}>
      Don&apos;t have your set of headshots yet?
    </h2>
  ) : (
    <>
      <IconCompleted width={26} height={null} />
      <h2 className={formStyles.title}>You&apos;re All Set!</h2>
    </>
  );

  const footerDescription = !closeUpImage
    ? "It's okay, you can upload it later."
    : 'You can now proceed to your account';

  return (
    <div className={formStyles.container}>
      <section className={styles.heading}>
        <p className={formStyles.subtitle}>Please, upload your</p>
        <h2 className={formStyles.title}>Headshot</h2>
        <p className={cn(formStyles.subtitle, styles.description)}>
          It&apos;s a close-up photo of you from the chest up.{' '}
          <b>Bad headshot = no jobs</b>. So upload your very best photo
        </p>
      </section>
      <div className={styles['upload-template']}>
        <PhotoUploadTemplate
          title="Headshot"
          onFileInputChange={onFileInputChange}
          previewImage={closeUpImage}
          placeholderImageSrc={`/assets/placeholders/circle-${
            gender?.toLowerCase() || 'male'
          }-close_up.svg`}
        />
      </div>

      <div className={styles.footer}>
        <div className={styles['footer-text']}>
          <div className={styles['footer-title']}>{footerTitle}</div>
          <p className={formStyles.subtitle}>{footerDescription}</p>
        </div>

        {isFinishing ? (
          <Loading />
        ) : (
          <Button
            className={formStyles.button}
            onClick={finishRegistration}
            label="Finish registration"
            minWidth={'220px'}
          />
        )}
      </div>

      {showCropTool && (
        <CropToolOverlay
          onClose={closeCropTool}
          image={selectedImageUrl}
          type="close_up"
        />
      )}
    </div>
  );
};

export default memo(WizardPhotoUpload);
