#!/bin/bash
set -e

# === CONFIGURATION ===
# Format: dir_name repo_url branch_name
PROJECTS=(
  "public ssh://**********************:1022/entertech/frontend/product/next-allcasting-com.git master"
  "pro ssh://**********************:1022/entertech/frontend/product/pro-allcasting-com.git master"
  "talent ssh://**********************:1022/entertech/frontend/product/talent-allcasting-com.git master"
)

# === FUNCTION TO CHECK FOR LOCAL CHANGES ===
check_clean_git_status() {
  if ! git diff-index --quiet HEAD --; then
    echo "⚠️  You have uncommitted changes. Please commit or stash them before running this script."
    exit 1
  fi
}

# === FUNCTION TO SYNC A PROJECT USING GIT SUBTREE ===
sync_subtree() {
  local dir_name="$1"
  local repo_url="$2"
  local branch_name="$3"
  local target_dir="apps/$dir_name"

  echo ""
  echo "=== Syncing $dir_name into $target_dir from $repo_url ($branch_name) ==="

  if [ ! -d "$target_dir" ]; then
    echo "📦 Subdirectory does not exist. Performing initial import..."
    git subtree add --prefix="$target_dir" "$repo_url" "$branch_name"
  else
    echo "🔄 Subdirectory exists. Pulling latest changes..."
    git subtree pull --prefix="$target_dir" "$repo_url" "$branch_name"
  fi

  echo "✔ Done syncing $dir_name"
}

# === BUILD OPTIONS MENU ===
OPTIONS=()
for entry in "${PROJECTS[@]}"; do
  dir_name=$(echo "$entry" | awk '{print $1}')
  OPTIONS+=("$dir_name")
done
OPTIONS+=("All")
OPTIONS+=("Cancel")

# === PROMPT USER ===
echo "Select a project to sync:"
select opt in "${OPTIONS[@]}"; do
  if [[ -z "$opt" ]]; then
    echo "❌ Invalid selection. Try again."
    continue
  fi

  case "$opt" in
    "All")
      check_clean_git_status
      for entry in "${PROJECTS[@]}"; do
        sync_subtree $entry
      done
      break
      ;;
    "Cancel")
      echo "❌ Operation cancelled."
      exit 0
      ;;
    *)
      for entry in "${PROJECTS[@]}"; do
        if [[ $entry == "$opt "* ]]; then
          check_clean_git_status
          sync_subtree $entry
          break 2
        fi
      done
      echo "❌ Unexpected error matching selection."
      ;;
  esac
done

echo ""
echo "✅ Sync complete."
