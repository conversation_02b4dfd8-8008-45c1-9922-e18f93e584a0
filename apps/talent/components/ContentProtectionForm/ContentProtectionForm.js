'use client';
import styles from './ContentProtectionForm.module.scss';
import { memo, useEffect, useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import Api from '@services/api';
import { ErrorMessage } from '@constants/form';
import { Button, HeaderMobile, PasswordInput } from '@components';

const ContentProtectionForm = ({
  identifier,
  closeContentProtectionForm,
  closeContentProtectionFormAndNavigate,
}) => {
  const [isError, setIsError] = useState(false);
  const formik = useFormik({
    initialValues: {
      password: '',
    },
    onSubmit: async (values) => {
      const expandableResources = ['level', 'settings', 'profiles'];
      const body = new FormData();

      body.append('email', identifier);
      body.append('password', values.password);
      body.append('expand', expandableResources.join(','));

      const passwordValidationResponse = await Api.clientside(`/auth/login`, {
        method: 'POST',
        body: body,
      });

      if (passwordValidationResponse.status !== 'ok') {
        setIsError(true);
      } else {
        closeContentProtectionForm();
      }
    },
    validationSchema: Yup.object({
      password: Yup.string()
        .required(ErrorMessage.PasswordRequired)
        .test('passwordWrong', ErrorMessage.PasswordInvalid, async () => {
          return !isError;
        }),
    }),
  });

  useEffect(() => {
    if (isError) {
      setIsError(false);
    }
  }, [formik.values, isError]);

  useEffect(() => {
    if (isError) {
      formik.setFieldTouched('password');
    }
  }, [isError]);

  return (
    <form
      className={styles['content-protection-form']}
      onSubmit={formik.handleSubmit}
    >
      <HeaderMobile
        onClose={closeContentProtectionFormAndNavigate}
        title="Password required"
        hideOnTablet
      />
      <Image
        src={'/assets/icons/icon-shield.svg'}
        alt="icon shield"
        width={60}
        height={60}
      />
      <div className={styles['content-protection-form-header']}>
        <span>To view private information</span>
        <span className={styles['content-protection-form-title']}>
          Enter Your Password
        </span>
      </div>
      <div className={styles['password-input-container']}>
        <PasswordInput
          name="password"
          value={formik.values.password}
          error={formik.errors.password}
          isTouched={formik.touched.password}
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          placeholder={'Your Password'}
        />
        <Link
          className={styles.link}
          href={`${process.env.publicUrl}/auth/forgot-password`}
        >
          Forgot password?
        </Link>
      </div>
      <Button
        type="submit"
        label="Continue"
        kind="secondary"
        minWidth={'280px'}
        disabled={!formik.isValid || !formik.dirty}
      />
    </form>
  );
};

export default memo(ContentProtectionForm);
