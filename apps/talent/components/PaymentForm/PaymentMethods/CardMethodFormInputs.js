'use client';
import styles from '../PaymentForm.module.scss';
import Image from 'next/image';
import { CardInput, Input, Loading, Select } from '@components';
import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { maskCardNumber } from '@utils/maskCardNumber';
import { maskCVVCode } from '@utils/maskCVVCode';
import { useLiveChat } from '@contexts/LiveChatContext';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { validateMasterCard, validateVisa } from '@utils/validatePaymentMethod';
import { useNotifications } from '@contexts/NotificationContext';
import { useRouter } from 'next/navigation';
import Api from '@services/api';
import { GTM_ACTIONS, GTM_CATEGORIES, GTM_EVENTS } from '@constants/analytics';
import { useAnalytics } from 'use-analytics';
import { sha256 } from 'js-sha256';
import { CookieService } from '@services/cookieService';
import { LIVE_CHAT_GROUP } from '@constants/liveChat';
import { cacheTest, nodeListsEqualTest } from '@utils/formikHelpers';
import { Amp } from '@services/amp';
import { ErrorMessage, FULL_NAME_REGEX } from '@constants/form';

const CardMethodFormInputs = forwardRef(
  (
    {
      nameOnCard,
      promoCode,
      zip,
      memberLevelId,
      expMonth,
      expYear,
      accountId,
      monthOptions,
      yearOptions,
      campaignGroup,
      tracking,
      campaign,
      email,
      clientId,
      accountLevelVerify,
      isUpgradeForm,
    },
    ref,
  ) => {
    const [location, setLocation] = useState(zip || '');
    const [isCardValid, setIsCardValid] = useState(false);
    const { setNotification } = useNotifications();
    const { initiateLiveChat } = useLiveChat();
    const router = useRouter();
    const { track } = useAnalytics();
    const [locationLoading, setLocationLoading] = useState(false);
    const [autofilledFields, setAutofilledFields] = useState(null);
    const [isFormStarted, setIsFormStarted] = useState(false);

    const handleFormStart = () => {
      if (!isFormStarted) {
        Amp.track(Amp.events.formStarted, {
          name: 'card payment',
          scope: Amp.element.scope.checkout,
        });
        setIsFormStarted(true);
      }
    };
    const handleFieldChange = (e) => {
      handleFormStart();
      formik.handleChange(e);
    };

    const validateZip = async (value) => {
      const length = value?.length || 0;

      if (length >= 4 && length <= 10) {
        setLocationLoading(true);

        const response = await getLocation(value);
        const isZipValid = response.count > 0;
        const { city = null, state = null } = response?.items[0]?.links || {};

        setLocation(isZipValid ? `${city?.title}, ${state?.code}` : '');
        setLocationLoading(false);

        return isZipValid;
      } else {
        return !!location;
      }
    };

    const validateZipRef = useRef(cacheTest(validateZip));

    const formik = useFormik({
      initialValues: {
        nameOnCard: nameOnCard || '',
        creditCard: '',
        cvvCode: '',
        expMonth: expMonth ? Number(expMonth) : '',
        expYear: expYear ? Number(expYear) : '',
        zipCode: zip || '',
      },
      onSubmit: async () => {
        CookieService.deleteShowStripeCheckoutSuccessCookie();
        CookieService.deleteShowCheckoutSuccessCookie();

        const billingResponse = await updateBillingInfo();

        if (billingResponse.status !== 'ok') {
          const billingErrorMessage =
            billingResponse.message || ErrorMessage.UnexpectedBilling;

          setNotification({
            type: 'error',
            message: billingErrorMessage,
            timeout: '5000',
          });

          Amp.track(Amp.events.formSubmitted, {
            name: 'card payment',
            scope: Amp.element.scope.checkout,
            result: Amp.element.result.fail,
            message: billingErrorMessage,
          });
          initiateLiveChat(true, LIVE_CHAT_GROUP.AllcastingPayments);
        } else {
          const upgradeResponse = await upgradeSubscription();

          if (upgradeResponse.status !== 'ok') {
            const upgradeErrorMessage =
              upgradeResponse.message || ErrorMessage.Unexpected;

            track(GTM_EVENTS.interaction, {
              target: GTM_CATEGORIES.payment,
              action: GTM_ACTIONS.submit,
              label: upgradeErrorMessage,
            });
            setNotification({
              type: 'error',
              message: upgradeErrorMessage,
              timeout: '5000',
            });

            Amp.track(Amp.events.formSubmitted, {
              name: 'card payment',
              scope: Amp.element.scope.checkout,
              result: Amp.element.result.fail,
              message: upgradeErrorMessage,
            });
            initiateLiveChat(true, LIVE_CHAT_GROUP.AllcastingPayments);
          } else {
            if (!isUpgradeForm) {
              trackUpgrade(upgradeResponse.data);
            } else {
              window.sessionStorage.setItem(
                'invoiceHash',
                upgradeResponse.invoice_hash,
              );
              CookieService.setShowUpgradeCheckoutSuccess(true);
            }

            CookieService.setShowCheckoutSuccess(true);

            Amp.track(Amp.events.formSubmitted, {
              name: 'card payment',
              scope: Amp.element.scope.checkout,
              result: Amp.element.result.success,
            });
            await accountLevelVerify();
            router.push('/checkout/success');
          }
        }
      },
      validationSchema: Yup.object({
        nameOnCard: Yup.string()
          .required(ErrorMessage.NameRequiredBilling)
          .max(35, ErrorMessage.NamePattern)
          .matches(FULL_NAME_REGEX, ErrorMessage.NamePattern),
        creditCard: Yup.string()
          .required(ErrorMessage.CardNumberRequired)
          .test(
            'cardIsValid',
            ErrorMessage.CardNumberPattern,
            async (value) => {
              if (value?.length) {
                const formattedValue = value.replace(/\s-\s/g, '');
                const isValidMasterCard = validateMasterCard(formattedValue);
                const isValidVisa = validateVisa(formattedValue);

                setIsCardValid(isValidMasterCard || isValidVisa);

                return isValidMasterCard || isValidVisa;
              } else {
                return isCardValid;
              }
            },
          ),
        cvvCode: Yup.string()
          .required(ErrorMessage.CVVRequired)
          .min(3, ErrorMessage.CVVPattern),
        expMonth: Yup.string().required(ErrorMessage.ExpirationMonthRequired),
        expYear: Yup.string().required(ErrorMessage.ExpirationYearRequired),
        zipCode: Yup.string()
          .transform((value) => value?.replaceAll(' ', ''))
          .required(ErrorMessage.ZipRequired)
          .min(5, ErrorMessage.ZipPatternBilling)
          .max(10, ErrorMessage.ZipMax)
          .test(
            'zipIsValid',
            ErrorMessage.ZipPatternBilling,
            validateZipRef.current,
          ),
      }),
    });

    useEffect(() => {
      formik.validateField('zipCode');
    }, [location]);

    useEffect(() => {
      if (formik.values.zipCode.length < 4) {
        setLocation('');
      }
    }, [formik.values.zipCode]);

    useEffect(() => {
      const autofilledFieldsQuery = document.querySelectorAll(
        'input:-webkit-autofill',
      );

      if (!nodeListsEqualTest(autofilledFieldsQuery, autofilledFields)) {
        setAutofilledFields(autofilledFieldsQuery);
      }
    }, [formik.values, formik.isValidating]);

    useEffect(() => {
      if (autofilledFields) {
        autofilledFields.forEach(({ id }) => {
          if (!formik.touched[id]) {
            setFieldTouched(id);
          }
        });
      }
    }, [autofilledFields]);

    const triggerSubmit = async () => {
      await formik.submitForm();
    };

    useImperativeHandle(ref, () => ({
      async triggerSubmit() {
        await triggerSubmit();
      },
    }));

    const updateBillingInfo = async () => {
      const billingBody = JSON.stringify({
        name_on_card: formik.values.nameOnCard,
        card: formik.values.creditCard.replace(/\s-\s/g, ''),
        cvv: formik.values.cvvCode,
        expiration_month: formik.values.expMonth,
        expiration_year: formik.values.expYear,
        zip: formik.values.zipCode.replaceAll(' ', ''),
      });

      return await Api.clientside(`/payment/billing`, {
        body: billingBody,
        method: 'PUT',
      });
    };

    const upgradeSubscription = async () => {
      const campaign = CookieService.getSessionCookie();
      const upgradeBody = new FormData();

      if (campaign) {
        upgradeBody.append('payment_method', 'card');
      }

      if (promoCode) {
        upgradeBody.append('promo_code', promoCode);
      }

      if (isUpgradeForm) {
        upgradeBody.append('level_id', memberLevelId);

        return await Api.clientside(`/accounts/${accountId}/levels/upgrade`, {
          body: upgradeBody,
          method: 'POST',
        });
      } else {
        upgradeBody.append('member_level', memberLevelId);

        return await Api.clientside(`/payment/upgrade`, {
          body: upgradeBody,
          method: 'POST',
        });
      }
    };

    const trackUpgrade = (data) => {
      track(GTM_EVENTS.interaction, {
        target: GTM_CATEGORIES.upgradeSuccess,
        action: GTM_ACTIONS.upgrade,
        label: data.sale_price,
        items: [
          {
            item_id: data.invoice_id,
            item_name: data.period + data.period_length.toLowerCase(),
            price: data.sale_price,
            item_category: 'subscription',
            quantity: 1,
          },
        ],
        price: data.sale_price,
        value: data.sale_price,
        upgrade_invoice_id_hash: sha256(String(data.invoice_id)),
        upgrade_invoice_id: data.invoice_id,
        upgrade_plan_price: data.sale_price,
        user_id: clientId,
        'client.account_id_hash': sha256(String(accountId)),
        'client.user_id': clientId,
        'client.email_hash': sha256(String(email)),
        'client.campaign_id': campaign,
        'client.campaign_group': campaignGroup,
        ...tracking,
      });
    };

    const formatCardNumber = (e) => {
      handleFormStart();
      formik.setFieldValue('creditCard', maskCardNumber(e.target.value));
    };

    const formatCVVCode = (e) => {
      handleFormStart();
      formik.setFieldValue('cvvCode', maskCVVCode(e.target.value));
    };

    const setMonthValue = (e) => {
      handleFormStart();
      formik.setFieldValue('expMonth', e);
    };

    const setYearValue = (e) => {
      handleFormStart();
      formik.setFieldValue('expYear', e);
    };

    const getLocation = async (zip) => {
      return await Api.clientside(`/locations?query=${zip}`);
    };

    const setFieldTouched = (field) => {
      formik.setFieldTouched(field);
    };

    return (
      <>
        <div className={styles['form-row']}>
          <Input
            name="nameOnCard"
            placeholder="Name on card"
            onChange={handleFieldChange}
            onBlur={formik.handleBlur}
            value={formik.values.nameOnCard}
            isTouched={formik.touched.nameOnCard}
            error={formik.errors.nameOnCard}
          />
        </div>

        <div className={styles['form-row']}>
          <CardInput
            name="creditCard"
            placeholder="Card number"
            onChange={formatCardNumber}
            onBlur={formik.handleBlur}
            value={formik.values.creditCard}
            isTouched={formik.touched.creditCard}
            error={formik.errors.creditCard}
          />

          <div className={styles['form-field']}>
            <Input
              name="cvvCode"
              placeholder="CVV/CVC"
              onChange={formatCVVCode}
              onBlur={formik.handleBlur}
              value={formik.values.cvvCode}
              isTouched={formik.touched.cvvCode}
              error={formik.errors.cvvCode}
            />
            <div className={styles['cvv-icon-container']}>
              <Image
                className={styles['cvv-icon']}
                src={'/assets/icons/icon-cvv.svg'}
                alt="icon cvv"
                width={68}
                height={20}
              />
            </div>
          </div>
        </div>

        <div className={styles['form-row']}>
          <div className={styles['form-inner-row']}>
            <Select
              name="expMonth"
              onChange={setMonthValue}
              value={formik.values.expMonth}
              isTouched={formik.touched.expMonth}
              error={formik.errors.expMonth}
              placeholder="Month"
              options={monthOptions}
              setFormFieldTouched={() => setFieldTouched('expMonth')}
            />
            <Select
              name="expYear"
              onChange={setYearValue}
              value={formik.values.expYear}
              isTouched={formik.touched.expYear}
              error={formik.errors.expYear}
              placeholder="Year"
              options={yearOptions}
              setFormFieldTouched={() => setFieldTouched('expYear')}
            />
          </div>
          <div className={styles['form-field']}>
            <Input
              name="zipCode"
              placeholder="Zip Code"
              onChange={handleFieldChange}
              onBlur={formik.handleBlur}
              value={formik.values.zipCode}
              isTouched={formik.touched.zipCode}
              error={formik.errors.zipCode}
              maxLength={10}
            />
            {location && <div className={styles.location}>{location}</div>}
            {locationLoading && <Loading minHeight="40px" padding="0" />}
          </div>
        </div>
      </>
    );
  },
);

CardMethodFormInputs.displayName = 'CardMethodFormInputs';

export default CardMethodFormInputs;
