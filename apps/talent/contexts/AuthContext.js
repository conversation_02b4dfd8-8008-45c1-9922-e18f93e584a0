import React, { createContext, useState, useContext, useEffect } from 'react';
import Api from '../services/api';
import { useNotifications } from './NotificationContext';
import { useAnalytics } from 'use-analytics';
import { GTM_ACTIONS, GTM_CATEGORIES, GTM_EVENTS } from '@constants/analytics';
import { CookieService } from '@services/cookieService';
import dayjs from 'dayjs';
import { formatAccountLevel, getUpgradeValue } from '@utils/formatAccountLevel';
import { useFeature } from './FeatureContext';
import * as Sentry from '@sentry/nextjs';
import { isPreviewPage } from '@utils/planSelectHelper';
import { usePathname } from 'next/navigation';

const AuthContext = createContext({});

export const AuthProvider = ({ children }) => {
  const [userProfiles, setUserProfiles] = useState([]);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [userType, setUserType] = useState(null);
  const [accountId, setAccountId] = useState(null);
  const [profileId, setProfileId] = useState(null);
  const [accountLevel, setAccountLevel] = useState(null);

  const path = usePathname();
  const { setNotification } = useNotifications();
  const { track } = useAnalytics();
  const { setAccountFeatures } = useFeature();

  const redirectOnLogout = () => {
    window.location.href = process.env.publicUrl;
  };

  const authVerify = () => {
    if (
      CookieService.getAuthenticationCookie() &&
      CookieService.getVolatileCookie() &&
      CookieService.getExpiresCookie() &&
      CookieService.getAccountCookie()
    ) {
      const currentEpoch = dayjs().unix();
      const tokenExpires = CookieService.getExpiresCookie();

      if (tokenExpires > currentEpoch) {
        setIsAuthenticated(true);
        setAccountId(CookieService.getAccountCookie());
        setUserType(CookieService.getUserTypeCookie());
        setProfileId(CookieService.getProfileCookie());

        const accountLevelCookie = CookieService.getAccountLevelCookie();
        const canUpgradeExistingSubscription = CookieService.getUpgradeCookie();

        if (accountLevelCookie) {
          setAccountLevel({
            ...accountLevelCookie,
            canUpgradeExistingSubscription,
          });
        }

        setUserProfiles(CookieService.getUserProfilesCookie());

        accountLevelVerify().catch();
      } else {
        authLogout();
      }
    } else {
      authLogout();
    }
  };

  const accountLevelVerify = async () => {
    const level = await Api.clientside(
      `/accounts/${CookieService.getAccountCookie()}/levels/last?expand=level_features,feature_requests`,
    );

    if (level.status !== 'ok') {
      setNotification({
        type: 'error',
        message: 'Session expired',
        timeout: '5000',
      });
      authLogout();
    } else {
      const modifiedAccountLevel = formatAccountLevel(
        level?.links?.level.status,
      );
      const canUpgradeExistingSubscription = getUpgradeValue(level);

      CookieService.setAccountLevelCookie(modifiedAccountLevel);
      CookieService.setUpgradeCookie(canUpgradeExistingSubscription);

      setAccountLevel({
        ...modifiedAccountLevel,
        canUpgradeExistingSubscription,
      });

      setAccountFeatures(level.links?.level?.links?.features?.items || []);
    }
  };

  const refreshUserProfiles = async () => {
    const userProfilesResponse = await Api.clientside(
      `/accounts/${accountId}/profiles?expand=location,personal_url,touches`,
    );

    if (userProfilesResponse.status !== 'ok') {
      setNotification({
        type: 'error',
        message: 'Failed to fetch profiles',
        timeout: '5000',
      });
    } else {
      updateUserProfiles(userProfilesResponse.items);
    }
  };

  const updateUserProfiles = (userProfiles) => {
    const modifiedUserProfiles = userProfiles.map((el) => {
      return {
        titlePhotoUrl: el.title_photo_url,
        profileUrl: `/profile/${
          el.links?.personal_url?.path ? el.links.personal_url.path : el.id
        }`,
        id: el.id,
        firstName: el.firstname,
        lastName: el.lastname,
        fullName: `${el.firstname} ${el.lastname}`,
        birthday: el.birthday,
        gender: el.gender?.title?.toLowerCase() || '',
        href: el.href || '',
        rating: el.rating || 0,
        zipCode: el.links?.location?.links?.zip?.code || '',
        city: el.links?.location?.links?.city?.slug || '',
        country: el.links?.location?.links?.country?.code || '',
        phone: el.links?.touches?.links?.phone?.value || '',
        clientId: el.client_id || null,
        isEmailValid:
          !!el?.links?.touches?.links?.email?.value &&
          !el.links.touches.links.email.value.endsWith(
            '@privaterelay.appleid.com',
          ),
      };
    });

    if (modifiedUserProfiles.length) {
      CookieService.setUserProfilesCookie(modifiedUserProfiles);
    } else {
      CookieService.deleteUserProfilesCookie();
    }
    setUserProfiles(modifiedUserProfiles || []);
  };

  const getIsProfileOutsideUSA = () =>
    !!userProfiles?.length &&
    !!userProfiles[0]?.country &&
    userProfiles[0]?.country !== 'US';

  useEffect(() => {
    authVerify();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [path]);

  const clearAuthData = () => {
    CookieService.cleanAuthenticationCookies();
    setIsAuthenticated(false);
    setAccountId(null);
    setUserType(null);
    setProfileId(null);
    setUserProfiles([]);
    Sentry.setUser(null);
  };

  const authLogoutWithTracking = () => {
    track(GTM_EVENTS.interaction, {
      target: GTM_CATEGORIES.logout,
      action: GTM_ACTIONS.logout,
      label: path,
    });
    authLogout();
  };

  const authLogout = () => {
    if (isPreviewPage()) {
      clearAuthData();
    } else {
      clearAuthData();
      redirectOnLogout();
    }
  };

  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        accountId,
        userType,
        userProfiles,
        profileId,
        accountLevel,
        updateUserProfiles,
        getIsProfileOutsideUSA,
        authLogoutWithTracking,
        refreshUserProfiles,
        accountLevelVerify,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => useContext(AuthContext);
