'use client';
import { memo, useEffect, useRef } from 'react';
import useTrackElementActions from '@utils/useTrackElementActions';

const ElementViewed = ({
  children,
  name = '',
  type = '',
  context = '',
  version = '',
  position = '',
}) => {
  const ref = useRef(null);

  const { onTrackView } = useTrackElementActions({
    name,
    type,
    context,
    version,
    position,
    autoTrackEnabled: false,
  });

  useEffect(() => {
    const node = ref?.current;
    const hasIOSupport = !!window.IntersectionObserver;

    if (!hasIOSupport || !node) return;

    const observer = new IntersectionObserver(
      ([entry], observerInstance) => {
        if (entry.isIntersecting) {
          onTrackView();
          observerInstance.disconnect();
        }
      },
      { threshold: 0.5 },
    );

    if (node) {
      observer.observe(node);
    }

    return () => {
      if (node) {
        observer.unobserve(node);
      }
    };
  }, [onTrackView]);

  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
      ref={ref}
    >
      {children}
    </div>
  );
};

export default memo(ElementViewed);
