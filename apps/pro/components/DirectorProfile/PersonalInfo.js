'use client';
import React, { memo, useEffect, useState } from 'react';
import styles from './ProfileInfo.module.scss';
import Api from '@services/api';
import EditIcon from '../../public/assets/icons/icon-edit.svg';
import { useNotifications } from '@contexts/NotificationContext';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { Input } from '@components';
import { hasFormValueChanged } from '@utils/hasFormValueChanged';
import cn from 'classnames';
import { Amp } from '@services/amp';
import { ErrorMessage } from '@constants/form';

const PersonalInfo = ({
  profileId,
  accountId,
  initialLocation,
  zipCode,
  company,
  refreshUserProfiles,
  refreshProfileDetails,
}) => {
  const { setNotification } = useNotifications();
  const [isPersonalInfoContainerEditing, setPersonalInfoContainerEditing] =
    useState(false);
  const [isPersonalInfoNotEmpty, setIsPersonalInfoNotEmpty] = useState(false);
  const [location, setLocation] = useState(initialLocation || '');

  const formik = useFormik({
    initialValues: {
      company: company || '',
      zip: zipCode || '',
    },
    onSubmit: async (values) => {
      if (hasFormValueChanged(values, formik.initialValues)) {
        Amp.track(Amp.events.elementClicked, {
          name: 'save company',
          scope: Amp.element.scope.editProfile,
          section: Amp.element.section.companyForm,
          type: Amp.element.type.button,
        });

        const locationBody = new FormData();
        const profileBody = new FormData();

        locationBody.append('zip', values.zip);

        profileBody.append('company', values.company);

        const locationResponse = await Api.clientside(
          `/accounts/${accountId}/location`,
          {
            body: locationBody,
            method: 'PUT',
          },
        );

        const profileResponse = await Api.clientside(`/profiles/${profileId}`, {
          body: profileBody,
          method: 'PATCH',
        });

        const isError = [
          locationResponse.status !== 'ok',
          profileResponse.status !== 'ok',
        ].some((value) => value);

        if (isError) {
          setNotification({
            type: 'error',
            timeout: '5000',
            message:
              locationResponse.message ||
              profileResponse.message ||
              ErrorMessage.Unexpected,
          });
        } else {
          formik.resetForm({ values });

          await refreshUserProfiles();
          await refreshProfileDetails();

          setPersonalInfoContainerEditing(false);
        }
      } else {
        setPersonalInfoContainerEditing(false);
      }
    },
    validationSchema: Yup.object({
      company: Yup.string().max(
        130,
        ErrorMessage.MaxCharacters.replace('{X}', '130'),
      ),
      zip: Yup.string()
        .min(5, ErrorMessage.ZipPattern)
        .max(6, ErrorMessage.ZipPattern)
        .test('zipIsValid', ErrorMessage.ZipPattern, async (value) => {
          const length = value?.length || 0;

          if (length >= 4 && length <= 6 && value !== formik.values.zip) {
            const response = await getLocation(value);

            setLocation(
              response.count > 0 && response.items?.length
                ? `${response.items[0].links?.city?.title}, ${response.items[0].links?.state?.code}`
                : '',
            );

            return response.count > 0;
          } else if (!value) {
            setLocation('');
          }

          return true;
        }),
    }),
  });

  useEffect(() => {
    setIsPersonalInfoNotEmpty(
      Object.values(formik.initialValues).some((value) => value),
    );
  }, [formik.initialValues]);

  const editPersonalInfo = async () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'edit personal info',
      scope: Amp.element.scope.profile,
      section: Amp.element.section.personalInformation,
      type: Amp.element.type.button,
    });
    setPersonalInfoContainerEditing(true);
  };

  const triggerSubmit = async () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'save personal info',
      scope: Amp.element.scope.profile,
      section: Amp.element.section.personalInformation,
      type: Amp.element.type.button,
    });
    await formik.submitForm();
  };

  const getLocation = async (zip) => {
    return await Api.clientside(`/locations?query=${zip}`);
  };

  return (
    <>
      <section>
        <div className={styles['profile-section-title']}>
          Personal:
          {!isPersonalInfoContainerEditing ? (
            <span onClick={editPersonalInfo} className={styles['edit-link']}>
              <EditIcon />
            </span>
          ) : (
            <span className={styles['save-button']} onClick={triggerSubmit}>
              Save
            </span>
          )}
        </div>
        {!isPersonalInfoContainerEditing ? (
          <>
            {isPersonalInfoNotEmpty ? (
              <div className={styles['profile-properties-container']}>
                <div className={styles['profile-property']}>
                  <div className={styles['profile-property-title']}>
                    Company Name
                  </div>
                  <div
                    className={
                      styles[
                        formik.values.company
                          ? 'profile-property-value'
                          : 'profile-property-value-empty'
                      ]
                    }
                  >
                    {company || 'n/a'}
                  </div>
                </div>
                <div className={styles['profile-property']}>
                  <div className={styles['profile-property-title']}>
                    Location
                  </div>
                  <div
                    className={
                      styles[
                        formik.values.zip
                          ? 'profile-property-value'
                          : 'profile-property-value-empty'
                      ]
                    }
                  >
                    {location || 'n/a'}
                  </div>
                </div>
              </div>
            ) : (
              <div className={styles['profile-section-empty']}>
                To build credibility, add your company information and select a
                location for most of your casting calls in your profile.
                <span
                  onClick={editPersonalInfo}
                  className={styles['profile-section-empty-add-button']}
                >
                  Add
                </span>
              </div>
            )}
          </>
        ) : (
          <form
            className={cn(
              styles['profile-properties-container'],
              styles['profile-properties-container-form'],
            )}
            onSubmit={formik.handleSubmit}
          >
            <div className={styles['profile-property']}>
              <div className={styles['profile-property-value']}>
                <Input
                  name="company"
                  placeholder="Company name"
                  value={formik.values.company}
                  error={formik.errors.company}
                  isTouched={formik.touched.company}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                />
              </div>
            </div>

            <div className={styles['profile-property']}>
              <div className={styles['profile-property-value']}>
                <Input
                  name="zip"
                  placeholder="Location"
                  value={formik.values.zip}
                  error={formik.errors.zip}
                  isTouched={formik.touched.zip}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  hint="ZIP / Postal Code"
                />
                {location && (
                  <span className={styles['location-hint']}>{location}</span>
                )}
              </div>
            </div>
          </form>
        )}
      </section>
    </>
  );
};

export default memo(PersonalInfo);
