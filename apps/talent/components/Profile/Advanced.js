'use client';
import styles from './ProfileInfo.module.scss';
import AdvancedAttribute from './FieldComponents/AdvancedAttribute';
import { useState } from 'react';
import { ModalAddAttribute } from '@components';

const Advanced = ({
  attributes,
  hideAttributeLevelInfoBlock,
  refreshProfileAttributes,
  saveAttribute,
  profileAttributes,
}) => {
  const [showModal, setShowModal] = useState(null);

  return (
    <section>
      <div className={styles['profile-section-title']}>ADVANCED:</div>
      <div className={styles['profile-properties-container']}>
        <AdvancedAttribute
          label="Languages"
          onEdit={() => {
            setShowModal({
              attribute: attributes['languages'],
              label: 'Languages',
              fieldPlaceholder: 'Add Language...',
              initialValues: profileAttributes['languages']?.items || [],
            });
          }}
          values={profileAttributes['languages']?.items || []}
        />
        <AdvancedAttribute
          label="Accents"
          onEdit={() => {
            setShowModal({
              attribute: attributes['accents'],
              label: 'Accents',
              fieldPlaceholder: 'Add Accent...',
              initialValues: profileAttributes['accents']?.items || [],
            });
          }}
          values={profileAttributes['accents']?.items || []}
        />
        <AdvancedAttribute
          label="Sports"
          onEdit={() => {
            setShowModal({
              attribute: attributes['sports'],
              label: 'Sports',
              fieldPlaceholder: 'Add Sport...',
              initialValues: profileAttributes['sports']?.items || [],
            });
          }}
          values={profileAttributes['sports']?.items || []}
        />
        <AdvancedAttribute
          label="Dances"
          onEdit={() => {
            setShowModal({
              attribute: attributes['dances'],
              label: 'Dances',
              fieldPlaceholder: 'Add Dance...',
              initialValues: profileAttributes['dances']?.items || [],
            });
          }}
          values={profileAttributes['dances']?.items || []}
        />
        <AdvancedAttribute
          label="Professions"
          onEdit={() => {
            setShowModal({
              attribute: attributes['professions'],
              label: 'Professions',
              fieldPlaceholder: 'Add Profession...',
              initialValues: profileAttributes['professions']?.items || [],
            });
          }}
          values={profileAttributes['professions']?.items || []}
        />
        <AdvancedAttribute
          label="Musicianship"
          onEdit={() => {
            setShowModal({
              attribute: attributes['musicianship'],
              label: 'Musicianship',
              fieldPlaceholder: 'Add Musicianship...',
              initialValues: profileAttributes['musicianship']?.items || [],
            });
          }}
          values={profileAttributes['musicianship']?.items || []}
        />
        <AdvancedAttribute
          label="Family Photoshoot"
          onEdit={() => {
            setShowModal({
              attribute: attributes['familyPhotoshoot'],
              label: 'Family Photoshoot',
              fieldPlaceholder: 'Add Family Photoshoot...',
              initialValues: profileAttributes['familyPhotoshoot']?.items || [],
            });
          }}
          values={profileAttributes['familyPhotoshoot']?.items || []}
        />
        <AdvancedAttribute
          label="Driving"
          onEdit={() => {
            setShowModal({
              attribute: attributes['driving'],
              label: 'Driving',
              fieldPlaceholder: 'Add Driving...',
              initialValues: profileAttributes['driving']?.items || [],
            });
          }}
          values={profileAttributes['driving']?.items || []}
        />
        <AdvancedAttribute
          label="Pets"
          onEdit={() => {
            setShowModal({
              attribute: attributes['pets'],
              label: 'Pets',
              fieldPlaceholder: 'Add Pets...',
              initialValues: profileAttributes['pets']?.items || [],
            });
          }}
          values={profileAttributes['pets']?.items || []}
        />
      </div>
      {showModal && (
        <ModalAddAttribute
          {...showModal}
          hideAttributeLevelInfoBlock={hideAttributeLevelInfoBlock}
          onClose={() => {
            setShowModal('');
          }}
          refreshProfileAttributes={refreshProfileAttributes}
          saveAttribute={saveAttribute}
        />
      )}
    </section>
  );
};

export default Advanced;
