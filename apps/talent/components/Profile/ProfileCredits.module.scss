@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.credit-container {
  position: relative;
  display: grid;
  justify-content: stretch;
  padding: 33px $space-30 $space-55;
  background: $white;
  box-shadow: $shadow-credit-container;
  border-radius: 10px;
  margin-bottom: $space-30;

  @include tablet {
    padding: 27px $space-50 73px;
  }
}

.add-new {
  justify-content: stretch;
  border: 1px dashed #bec7d6;
  box-shadow: none;
}

.add-credit-header {
  display: flex;
}

.credit-header {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  grid-row-gap: $space-10;

  & > div {
    display: flex;
  }

  @include tablet {
    flex-direction: row;
    align-items: center;
    grid-row-gap: 0;

    & > div {
      display: inline-flex;
    }
  }
}

.credit-container-box {
  display: grid;
  grid-row-gap: 33px;
  margin-top: $space-10;
}

.credit-container-row {
  display: grid;
  grid-row-gap: 33px;

  @include tablet {
    grid-template-columns: 1fr 1fr;
    grid-gap: 0 $space-60;
  }
}

.credit-container-row-selects {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-column-gap: $space-20;
}

.credit-container-close {
  position: absolute;
  top: 24px;
  right: 22px;
  width: 24px;
  height: 24px;
  cursor: pointer;
  z-index: 2;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    right: 0;
    left: 0;
    margin: 0 auto;
    width: 100%;
    height: 1px;
    background-color: $grey-80;
    transform: rotate(45deg);
  }

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 0;
    left: 0;
    margin: 0 auto;
    width: 100%;
    height: 1px;
    background-color: $grey-80;
    transform: rotate(-45deg);
  }
}

.credit-save-btn {
  margin-left: 0;

  @include tablet {
    margin-left: $space-10;
  }
}
