import Api from '../services/api';
import FilterService from '@entertech/filter-service';
import { getHeaders } from './headerHelper';

export const formatFilters = async (isTalent, reqQuery, req, path) => {
  const filter = {
    data: await Api.serverAPIRoute(
      `/castingcalls/filters?isTalent=${isTalent}`,
      null,
      getHeaders(req, path),
    ),
    seo: await Api.serverAPIRoute(
      `/seo/castingcalls`,
      null,
      getHeaders(req, path),
    ),
    cities: await Api.serverAPIRoute(
      `/location/search/popular-cities`,
      null,
      getHeaders(req, path),
    ),
  };

  const queryParameters = !Object.keys(reqQuery.query.filter || {}).length
    ? {}
    : JSON.parse(reqQuery.query.filter);

  FilterService.parseStatic(queryParameters, filter.seo);

  /**
   * Dirty hack, sorry.
   */
  if (
    queryParameters.city &&
    queryParameters.city !== 'unknown' &&
    (!queryParameters.longitude ||
      !queryParameters.latitude ||
      !queryParameters.location)
  ) {
    const city = await Api.serverAPIRoute(
      `/location/resource/city?slug=${queryParameters.city}`,
      null,
      getHeaders(req, path),
    );

    queryParameters.longitude = `${city.longitude}`;
    queryParameters.latitude = `${city.latitude}`;
    queryParameters.location = `${city.title}, ${city.links.state.code}`;
  }

  if (!queryParameters.city || queryParameters.city === 'unknown') {
    delete queryParameters.longitude;
    delete queryParameters.latitude;
    delete queryParameters.location;
  }

  if (isTalent && !queryParameters.near) {
    queryParameters.near = '';
  }

  if (isTalent && !queryParameters.best) {
    queryParameters.best = '';
  }

  if (isTalent && !queryParameters.viewed) {
    queryParameters.viewed = '';
  }

  filter.data = FilterService.generateFilterFromUrl(
    filter.data,
    queryParameters,
    filter.seo,
  );

  const filterParameters = FilterService.generateBCUrlFromFilter(
    filter.data,
    filter.seo,
  );

  return {
    filter,
    filterParameters,
  };
};
