'use client';
import React, {
  createContext,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react';
import { useInterval } from '@utils/useInterval';
import Api from '../services/api';
import { useAlert } from './AlertContext';
import { Amp } from '@services/amp';
import useTabInactivity from '../utils/useTabInactivity';

const DEFAULT_CONVERSATION = {
  id: null,
};

const DEFAULT_CONVERSATION_SETTINGS = {
  status: 0,
  limit: 10,
};

const CONVERSATION_POOLING_INTERVAL = 35000;

const MessageContext = createContext({});

export const MessageProvider = ({
  children,
  initialConversations,
  canViewMessages,
  profileId,
}) => {
  const [showConversationSelect, setConversationSelect] = useState(false);
  const [conversations, setConversations] = useState(initialConversations);
  const [conversation, setConversation] = useState(
    initialConversations.length
      ? {
          ...initialConversations[0],
          id: initialConversations[0].links.profile.id,
        }
      : DEFAULT_CONVERSATION,
  );
  const [conversationSettings, setConversationSettings] = useState({
    ...DEFAULT_CONVERSATION_SETTINGS,
  });
  const [loadingConversations, setLoadingConversations] = useState(false);
  const [isChatbotActive, setChatbotActive] = useState(false);

  const { fetchAlerts } = useAlert();
  const isTabInactive = useTabInactivity();

  const fetchController = useRef(null);

  useInterval(() => {
    if (canViewMessages && !loadingConversations && !isTabInactive) {
      fetchConversations(conversationSettings, true);
    }
  }, CONVERSATION_POOLING_INTERVAL);

  const fetchConversations = async (
    settings = conversationSettings,
    isInterval = false,
  ) => {
    if (!isInterval) {
      setLoadingConversations(true);
    }

    fetchController.current?.abort();

    const params = new URLSearchParams({
      expand: 'profile',
      profile: `${profileId}`,
      limit: `${settings.limit}`,
      'filter[unread]': `${settings.status}`,
    });

    const controller = new AbortController();

    fetchController.current = controller;

    Api.clientside(`/conversations?${params.toString()}`, {
      signal: controller.signal,
    })
      .then(({ status, items }) => {
        fetchController.current = null;

        if (status === 'ok') {
          setConversations(items);
          setConversationSettings(settings);
        }

        setLoadingConversations(false);
      })
      .catch((error) => {
        if (error.name !== 'AbortError') {
          setLoadingConversations(false);
        }
      });
  };

  const loadMoreConversations = () => {
    if (!canViewMessages) return;

    fetchConversations({
      ...conversationSettings,
      limit: conversationSettings.limit + 10,
    });
  };

  const trackConversationView = (id) => {
    Amp.track(Amp.events.elementClicked, {
      name: `open conversation`,
      scope: Amp.element.scope.messages,
      section: Amp.element.section.sidemenu,
      type: Amp.element.type.button,
      recipient_id: id,
    });
  };

  const openConversation = async (id) => {
    setConversationSelect(false);
    setConversation({
      ...conversations.find(({ links }) => id === links.profile.id),
      id,
    });

    trackConversationView(id);
    await fetchAlerts();
  };

  const toggleConversationSelect = () => {
    setConversationSelect((prev) => !prev);
  };

  const onShowConversationSelect = () => {
    setConversationSelect((prev) => prev || true);
  };

  const showAllConversations = () => {
    if (!canViewMessages) return;

    onShowConversationSelect();

    fetchConversations(DEFAULT_CONVERSATION_SETTINGS);

    setConversation(DEFAULT_CONVERSATION);
  };

  const showUnreadConversations = () => {
    if (!canViewMessages) return;

    onShowConversationSelect();

    fetchConversations({ ...DEFAULT_CONVERSATION_SETTINGS, status: 1 });

    setConversation(DEFAULT_CONVERSATION);
  };

  const toggleChatbot = (value) => {
    if (value) {
      setConversation(DEFAULT_CONVERSATION);
    }

    setChatbotActive(value);
  };

  const onDeleteConversation = (id) => {
    if (conversation.id === id) {
      setConversation(DEFAULT_CONVERSATION);
    }

    setConversations(
      conversations.filter(({ links }) => links.profile.id !== id),
    );
  };

  useEffect(() => {
    Amp.track(Amp.events.viewMessages, {
      profile_id: profileId,
      conversation_profile_id: conversation?.id,
    });
  }, [conversation, profileId]);

  return (
    <MessageContext.Provider
      value={{
        conversations,
        conversation,
        conversationSettings,
        loadingConversations,
        showConversationSelect,
        isChatbotActive,
        loadMoreConversations,
        openConversation,
        toggleConversationSelect,
        showAllConversations,
        showUnreadConversations,
        toggleChatbot,
        onDeleteConversation,
      }}
    >
      {children}
    </MessageContext.Provider>
  );
};

export const useMessage = () => useContext(MessageContext);
