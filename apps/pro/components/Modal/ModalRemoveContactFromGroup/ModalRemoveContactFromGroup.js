'use client';
import React, { memo, useState } from 'react';
import { Loading, Modal } from '@components';
import styles from './ModalRemoveContactFromGroup.module.scss';
import { useNotifications } from '@contexts/NotificationContext';
import Api from '@services/api';
import { Amp } from '@services/amp';
import { useContacts } from '@contexts/ContactContext';
import { ErrorMessage } from '@constants/form';

const ModalRemoveContactFromGroup = ({
  contactId,
  groupId,
  onClose = () => {},
  onRemoveFromContactsModal = () => {},
  amplitudeScope,
  extendsContactsModal = false,
}) => {
  const [loading, setLoading] = useState(false);
  const { setNotification } = useNotifications();
  const { removeFromGroup } = useContacts();

  const onRemove = async () => {
    if (extendsContactsModal) {
      onRemoveFromContactsModal();
    } else {
      setLoading(true);

      Amp.track(Amp.events.elementClicked, {
        name: 'remove from contact group',
        scope: amplitudeScope,
        section:
          amplitudeScope === Amp.element.scope.global
            ? Amp.element.section.contactsPopup
            : Amp.element.section.head,
        type: Amp.element.type.button,
      });

      const response = await Api.clientside(
        `/contact-groups/${groupId}/contacts?profiles[]=${contactId}`,
        {
          method: 'DELETE',
        },
      );

      if (response.status !== 'ok') {
        setNotification({
          type: 'error',
          timeout: '5000',
          message: response.message || ErrorMessage.Unexpected,
        });
      } else {
        await removeFromGroup(groupId, contactId);
        onClose();
      }

      setLoading(false);
    }
  };

  return (
    <Modal
      onClose={onClose}
      backdropClose
      showDefaultLayout={false}
      classNameContainer={styles['modal-container']}
      floatCloseButton
      hideMobileCloseButton
    >
      {loading ? (
        <div className={styles['loading-container']}>
          <div>Removing contact from group...</div>
          <Loading minHeight="40px" padding="0" />
        </div>
      ) : (
        <>
          <div className={styles.container}>
            <span>
              Are you sure you want to
              <br />
              <b>remove this talent</b> ?
            </span>
          </div>
          <div className={styles.actions}>
            <button onClick={onClose} className={styles.action}>
              Cancel
            </button>
            <button onClick={onRemove} className={styles.action}>
              Remove
            </button>
          </div>
        </>
      )}
    </Modal>
  );
};

export default memo(ModalRemoveContactFromGroup);
