'use client';
import React, { memo, useEffect } from 'react';
import { useField } from 'formik';
import { Switch } from '@components';

const SwitchFormik = (props) => {
  const { name } = props;
  const [{ onChange, value }, { touched, initialValue }, { setTouched }] =
    useField(name);

  // Autofill validation trigger
  useEffect(() => {
    if (initialValue !== value && !touched) {
      setTouched(true);
    }
  }, [value, touched, setTouched, initialValue]);

  return <Switch onChange={onChange} value={value} {...props} />;
};

export default memo(SwitchFormik);
