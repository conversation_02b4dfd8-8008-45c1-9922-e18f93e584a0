# web-allcasting-com

## Description

This is the monorepo for allcasting front end projects.

## Local usage

Setup reverse-proxy to use Local Domains

- clone and setup https://gitlab.dyninno.net/entertech/dev-tools
- run reverse-proxy here only `docker compose up reverse-proxy`
- add allcasting.test and pro/talent subdomains to hosts

```bash
echo "127.0.0.1 allcasting.test pro.allcasting.test talent.allcasting.test cdn.allcasting.test" | sudo tee -a /etc/hosts
```

### Running all services

Run this command from the root of the repo to start all services:

```bash
docker compose watch
```

### Viewing logs

To see logs separately run:

```bash
docker compose logs -f
```

To see logs for specific services:
```bash
docker compose logs -f public cdn-allcasting
```

## Applications overview

This monorepo contains separate applications for different parts of allcasting.com

- Public (apps/public)
  - Purpose: Public-facing website, partially available to authenticated Talent and Pro users.
  - Tech: Next.js 14, optimized for SEO and performance.
  - Dev URLs: https://allcasting.test or http://localhost:3000
  - Notes: Supports Redis-based caching; in dev, Talent/Pro flows redirect to their respective apps.

- Talent (apps/talent)
  - Purpose: Talent profile, settings, payments, registration. Authenticated-only.
  - Tech: Next.js 14
  - Dev URLs: https://talent.allcasting.test or http://localhost:3100

- Pro (apps/pro)
  - Purpose: Casting professionals’ area (director profile). Authenticated-only.
  - Tech: Next.js 14
  - Dev URLs: http://pro.allcasting.test or http://localhost:3200

- CDN (apps/cdn)
  - Purpose: Node.js CDN that serves static assets for all apps and provides Next.js image optimization endpoint (/_next/image).
  - Notes: Required for asset serving across apps; ensure this service is running alongside app(s).

## App-specific setup

Below are the essential steps to install and run each app locally. You can either run everything with Docker Compose (recommended) or run individual apps on your host machine.

### Common prerequisites
- Node v20.11.0 and npm v10.2.4 (when running apps on the host)
  - Tip: use nvm to manage Node versions.
- Recommended IDE plugins: ESLint and Prettier.
- Local domains: ensure the reverse-proxy is running and hosts are set as described above.
- Two ways to run:
  - Docker Compose (recommended): from repo root run `docker compose watch` to start all services with file watching.
  - Host machine (per app): `cd apps/<app> && npm install && npm run dev` (see ports below). You can override the port with `PORT=4000 npm run dev`.

### Public (apps/public)
- Dev URLs: https://allcasting.test or http://localhost:3000
- Run on host:
  - `cd apps/public`
  - `npm install`
  - `npm run dev`
- Build/start (host):
  - Note: build prefers `.env.production`. Copy `.env` to `.env.local` to override production values when needed.
  - `npm run build && npm run start`
- Local BE over HTTPS (entertech.test):
  - Export CA once per shell: `export NODE_EXTRA_CA_CERTS="$HOME/src/entertech/dev-tools/certs/ca/rootCA.pem"`
  - To persist, add the same line to your `~/.bashrc` or shell profile.
- Caching (Redis):
  - Copy `.env` to `.env.local` and set `REDIS_ENABLED=enabled`.
  - From `apps/public`, you can start services required by the app via its docker-compose file if needed: `docker compose -f docker-compose.yaml up`.
- Dev auth flow notes:
  - In development, Casting Pros are redirected to Pro; Talents to Talent. Run all apps for full flows.

### Talent (apps/talent)
- Dev URLs: https://talent.allcasting.test or http://localhost:3100
- Run on host:
  - `cd apps/talent`
  - `npm install`
  - `npm run dev`
- Build/start (host): `npm run build && npm run start`
- Lint/format: `npm run lint`, `npm run prettier`

### Pro (apps/pro)
- Dev URLs: http://pro.allcasting.test or http://localhost:3200
- Run on host:
  - `cd apps/pro`
  - `npm install`
  - `npm run dev`
- Build/start (host): `npm run build && npm run start`
- Lint/format: `npm run lint`, `npm run prettier`

### CDN (apps/cdn)
- Purpose: serves static assets for all apps and provides Next.js image optimization at `/_next/image`.
- How to run: it is started automatically with `docker compose watch` from the repo root.
- Example image optimization request:
  - `https://cdn.allcasting.test/public/_next/image?url=%2Fassets%2Fhomepage%2Ftalent-spotlight.webp&w=640&q=75`
