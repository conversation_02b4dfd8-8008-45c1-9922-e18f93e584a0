import DeactivateAccountClient from './_client';
import { cookies, headers } from 'next/headers';
import { checkIsPasswordSet } from '@utils/authHelper';
import { CookieServiceServer } from '@services/cookieServiceServer';
import Api from '@services/api';
import { extractResult } from '@utils/extractPromiseResult';
import { getResolvedUrl } from '@utils/apiHelper';

export const metadata = {
  title: 'Deactivate Account',
};

export default async function DeactivateAccountPage(req) {
  const searchParams = await req.searchParams;
  const resolvedUrl = getResolvedUrl(
    '/settings/deactivate-account',
    searchParams,
  );
  const cookieStore = await cookies();
  const headerStore = await headers();
  const accountId = CookieServiceServer.getAccountCookie(cookieStore);

  const paths = [`/accounts/${accountId}/identities`];

  const results = await Promise.allSettled(
    paths.map((path) =>
      Api.serverside(path, cookieStore, headerStore, resolvedUrl),
    ),
  );

  const [identities] = results.map((result) => extractResult(result, {}));

  const identifier = identities.items?.length
    ? identities.items[0].identifier
    : '';

  const isPasswordSet = checkIsPasswordSet(
    CookieServiceServer.getAuthenticationCookie(cookieStore),
  );

  return (
    <DeactivateAccountClient
      identifier={identifier}
      isPasswordSet={isPasswordSet}
    />
  );
}
