'use client';
import styles from '../ProfileInfo.module.scss';
import React from 'react';

const AppearanceValue = ({ label, hasValue, children }) => {
  return (
    <div className={styles['profile-property']}>
      <div className={styles['profile-property-title']}>{label}</div>
      <div
        className={
          styles[
            hasValue ? 'profile-property-value' : 'profile-property-value-empty'
          ]
        }
      >
        {children}
      </div>
    </div>
  );
};

export default AppearanceValue;
