'use client';
import { memo } from 'react';
import { Modal, PremiumFeature } from '@components';
import styles from './ModalPremium.module.scss';

const ModalPremium = ({ onClose, premiumTrackingName }) => {
  return (
    <Modal
      classNameContainer={styles['premium-modal']}
      classNameOverlay={styles['premium-modal-overlay']}
      backdropClose
      onClose={onClose}
      contentOverflowHidden={false}
      showDefaultLayout={false}
    >
      <PremiumFeature
        isModal
        onClose={onClose}
        premiumTrackingName={premiumTrackingName}
      />
    </Modal>
  );
};

export default memo(ModalPremium);
