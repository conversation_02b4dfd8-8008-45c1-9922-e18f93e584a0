export const AMP_ELEMENTS = {
  type: {
    button: 'button',
    block: 'block',
    tooltip: 'tooltip',
    popup: 'popup',
  },
  context: {
    ctaBlock: 'cta_block',
    ctaPopup: 'cta_popup',
  },
  scope: {
    profile: 'talent_profile',
    messages: 'messages',
    global: 'global',
    burger: 'burger',
    wizard: 'wizard',
    checkout: 'checkout',
    upgrade: 'upgrade',
    onboarding: 'onboarding',
    settingsBillingInfo: 'settings_billing_info',
    modal: 'modal_window',
    settings: 'settings',
  },
  section: {
    interestedIn: 'interested_in',
    skills: 'skills',
    headshot: 'headshot',
    sideView: 'side_view',
    fullHeight: 'full_height',
    personalInformation: 'personal_info',
    unionAffiliation: 'union_affiliation',
    appearance: 'appearance',
    advancedSkills: 'advanced_skills',
    contacts: 'contacts',
    personalLink: 'personal_link',
    socialNetworks: 'social_networks',
    additionalPhotos: 'additional_photos',
    audio: 'audio',
    video: 'video',
    credits: 'credits',
    photoAnalyzer: 'photo_analyzer',
    premiumFeature: 'premium_feature',
    compCard: 'comp_card',
    name: 'name',
    conversation: 'conversation',
    messageForm: 'message_form',
    sidemenu: 'sidemenu',
    header: 'header',
    navigation: 'navigation',
    planSelect: 'plan_select',
    planSelectFeatures: 'plan_select_features',
    featuredProfile: 'featured_profile',
    about: 'about',
    reviews: 'reviews',
    promotions: 'promotions',
    footer: 'footer',
    premiumActions: 'premium_actions',
  },
  result: {
    success: 'success',
    fail: 'fail',
  },
};

export const AMP_EVENTS = {
  deactivateAccount: 'Deactivate Account',
  deletePaymentInfo: 'Delete Payment Info',
  modalViewed: 'modal_viewed',
  elementViewed: 'element_viewed',
  elementHovered: 'element_hovered',
  elementClicked: 'element_clicked',
  formStarted: 'form_started',
  formSubmitted: 'form_submitted',
  modalSubscribeClicked: 'modal_subscribe_clicked',
  viewPlanSelect: 'View Plan Select',
  viewCheckout: 'View Checkout',
  viewCheckoutSuccess: 'View Checkout Success',
  viewWizardStepOne: 'View Wizard Step 1',
  completeWizardStepOne: 'Complete Wizard Step 1',
  viewWizardStepTwo: 'View Wizard Step 2',
  completeWizardStepTwo: 'Complete Wizard Step 2',
  topSliderBannerClicked: 'top_slider_banner_clicked',
  invalidEventOccurred: 'invalid_event_occurred',
  phoneNotificationDisplayed: 'phone_notification_displayed',
  phoneNotificationDismissed: 'phone_notification_dismissed',
  submitPhoneNumber: 'Submit Phone Number',
  viewMessages: 'View Messages',
  viewProfile: 'View Profile',
  viewSettings: 'View Settings',
  viewError: 'View Error',
  view404: 'View 404',
  view500: 'View 500',
  viewOnboarding: 'View Onboarding',
};
