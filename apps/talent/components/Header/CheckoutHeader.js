'use client';
import React, { memo } from 'react';
import Logo from '/public/assets/logo/logo-4.svg';
import styles from './CheckoutHeader.module.scss';
import MenuItem from './MenuItem';
import Link from 'next/link';
import cn from 'classnames';

const CheckoutHeader = ({ isPremiumCheckout, isLifetimeCheckout }) => {
  const prevUrl =
    (isPremiumCheckout && `${process.env.publicUrl}/premium`) ||
    (isLifetimeCheckout && `${process.env.publicUrl}/lifetime`) ||
    '/upgrade';

  return (
    <div className={styles.header}>
      <Link
        href={`${process.env.publicUrl}/castingcalls`}
        passHref
        aria-label="allcasting"
        className={styles['logo-link']}
      >
        <Logo className={styles.logo} />
      </Link>
      <div className={cn(styles.navbar, styles.desktop)}>
        <MenuItem label="Back to plan selection" href={prevUrl} />
      </div>
      <div className={cn(styles.navbar, styles.mobile)}>
        <MenuItem label="Change plan" href={prevUrl} />
      </div>
    </div>
  );
};

export default memo(CheckoutHeader);
