'use client';
import styles from './MessageCenter.module.scss';
import { ChatBot, Loading, Messages } from '@components';
import Sidebar from './Sidebar/Sidebar';
import React from 'react';
import { useMessage } from '@contexts/MessageContext';

const MessageCenter = ({ canViewMessages, isBotAvailable, profileId }) => {
  const { loadingConversations, isChatbotActive } = useMessage();

  return (
    <div className={styles.wrapper}>
      {loadingConversations && (
        <div className={styles['loading-overlay']}>
          <Loading />
        </div>
      )}
      <Sidebar
        canViewMessages={canViewMessages}
        isChatbotAvailable={isBotAvailable}
      />
      <div className={styles.main}>
        {isChatbotActive ? (
          <ChatBot />
        ) : (
          <Messages profileId={profileId} canViewMessages={canViewMessages} />
        )}
      </div>
    </div>
  );
};

export default MessageCenter;
