'use client';
import React, { memo } from 'react';
import styles from './UserMenu.module.scss';
import IconLogout from '/public/assets/icons/icon-logout.svg';
import IconMessages from '/public/assets/icons/icon-messages.svg';
import IconSettings from '/public/assets/icons/icon-settings.svg';
import Link from 'next/link';
import { useAlert } from '@contexts/AlertContext';
import cn from 'classnames';
import { HelpCenter, NumberBadge } from '..';
import { ProfileCloseupImage } from '@components';

const UserMenu = ({
  showHelpCenter,
  toggleHelpCenter,
  showSale,
  stopSale,
  authLogout,
  userProfiles,
}) => {
  const { newConversationCount } = useAlert();

  const onLogout = () => {
    stopSale();
    authLogout();
  };

  return (
    <section
      className={cn(styles.sidebar, {
        [styles['sidebar-sale']]: showSale,
      })}
    >
      <div className={styles.content}>
        <div className={styles['menu-section']}>
          <Link href="/messages" className={styles.button}>
            <IconMessages />
            {newConversationCount > 0 && (
              <NumberBadge
                className={styles.badge}
                number={newConversationCount}
              />
            )}
          </Link>
        </div>
        <div className={styles['menu-section']}>
          {userProfiles?.map(({ profileUrl, titlePhotoUrl, gender }, key) => (
            <a href={`${profileUrl}/info`} key={key} className={styles.button}>
              <ProfileCloseupImage
                className={styles['profile-img']}
                gender={gender}
                src={titlePhotoUrl}
                alt="Talent"
                image="closeup"
              />
            </a>
          ))}
          <Link href="/settings" className={styles.button}>
            <IconSettings />
          </Link>
          <div
            className={cn(styles.button, { [styles.active]: showHelpCenter })}
            onClick={toggleHelpCenter}
          >
            ?
          </div>
          <span className={styles.button} onClick={onLogout}>
            <IconLogout />
          </span>
        </div>
      </div>
      <HelpCenter isOpen={showHelpCenter} onClose={toggleHelpCenter} />
    </section>
  );
};

export default memo(UserMenu);
