'use client';
import React, { memo } from 'react';
import Logo from '/public/assets/logo/logo-4.svg';
import styles from './PlanSelectHeader.module.scss';
import MenuItem from './MenuItem';
import Link from 'next/link';

const PlanSelectHeader = ({ profileUrl, referer }) => {
  const getLabel = () => {
    switch (true) {
      case referer.includes('/castingcalls'):
        return 'Back to casting calls';
      case referer.includes('/castingcall'):
        return 'Back to casting call';
      default:
        return 'Back to profile';
    }
  };

  return (
    <div className={styles.header}>
      <Link
        href={`${process.env.publicUrl}/castingcalls`}
        passHref
        aria-label="allcasting"
        className={styles['logo-link']}
      >
        <Logo className={styles.logo} />
      </Link>
      <div className={styles.navbar}>
        <MenuItem
          label={getLabel()}
          href={referer.includes('/castingcall') ? referer : profileUrl}
        />
      </div>
    </div>
  );
};

export default memo(PlanSelectHeader);
