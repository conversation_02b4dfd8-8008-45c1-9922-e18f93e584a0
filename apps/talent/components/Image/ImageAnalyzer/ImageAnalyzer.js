'use client';
import React, { memo, useEffect, useState } from 'react';
import styles from './ImageAnalyzer.module.scss';
import cn from 'classnames';
import {
  Button,
  ElementViewed,
  Modal,
  StarRating,
  Tooltip,
  TooltipPhotoInfo,
  TooltipPhotoPremium,
} from '@components';
import { Rating } from './components/Rating';
import Image from 'next/image';
import {
  getCharacteristicLevelName,
  getImageRatingLevel,
} from '@utils/imageHelpers';
import { useViewport } from '@utils/useViewport';
import { isInViewPort } from '@utils/isInViewPort';
import { imageLevelPercent } from '@constants/images';
import { Amp } from '@services/amp';
import { IconLockedFeature } from '@icons/index';

const ImageAnalyzer = ({
  characteristics = [],
  starRating = 0,
  isTooltip = false,
  isReviewPending,
  title = '',
  isPaidOrDelayed = false,
  genderTitle = 'male',
  isBlocked = false,
}) => {
  const [showPremiumModal, setShowPremiumModal] = useState(false);
  const { width } = useViewport();

  useEffect(() => {
    if (isInViewPort(width, 'tablet', 'min') && showPremiumModal) {
      toggleShowPremiumModal();
    }
  }, [width]);

  const toggleShowPremiumModal = () => {
    setShowPremiumModal(!showPremiumModal);
  };

  const getAnalyzerDescription = () => {
    switch (true) {
      case starRating === 5:
        return `You've achieved max photo score! Congratulations!`;
      case starRating === 0 && !isPaidOrDelayed:
        return 'High quality pictures help you succeed to get cast.';
      case starRating === 0 && isBlocked:
        return 'Upload a new photo to activate your photo score.';
      case starRating === 0:
        return isReviewPending
          ? 'Your photo Score will be displayed shortly!'
          : 'Upload a photo to view your photo score.';
      default:
        return 'Elevate your photo quality to increase your photo score.';
    }
  };

  const getAnalyzerTitle = () => {
    switch (true) {
      case isReviewPending && isPaidOrDelayed && !isBlocked:
        return 'Photo Under Review';
      case isBlocked && isPaidOrDelayed:
        return 'Blocked Photo';
      case !isPaidOrDelayed:
        return 'My Photo Score';
      default:
        return `${getImageRatingLevel(
          starRating,
          isPaidOrDelayed,
        )} photo score`;
    }
  };

  return (
    <>
      <div
        className={cn(styles['analyzer-container'], {
          [styles['analyzer-container-main']]: !isTooltip,
        })}
      >
        {!isTooltip && isPaidOrDelayed && (
          <div className={styles['analyzer-header-main']}>
            <div className={styles['analyzer-header-main-left']}>
              {title && (
                <span className={styles['analyzer-header-main-title']}>
                  {title}:
                </span>
              )}
              <StarRating
                isImageRating
                initialRating={starRating}
                width={25}
                height={25}
              />
            </div>
            <Tooltip content={<TooltipPhotoInfo />} clickable>
              <div className={styles['icon-hint-container']}>
                <Image
                  src="/assets/icons/icon-hint-blue.svg"
                  alt="icon"
                  width={25}
                  height={25}
                />
              </div>
            </Tooltip>
          </div>
        )}
        <div className={styles['analyzer-header']}>
          <div
            className={styles['analyzer-header-inner']}
            onClick={toggleShowPremiumModal}
          >
            {isPaidOrDelayed ? (
              <Rating rating={starRating} isTooltip={isTooltip} />
            ) : (
              <div className={styles['icon-locked-container']}>
                <div className={styles.desktop}>
                  <Tooltip
                    content={<TooltipPhotoPremium genderTitle={genderTitle} />}
                    clickable
                  >
                    <IconLockedFeature className={styles['icon-locked']} />
                  </Tooltip>
                </div>
                <div className={styles.mobile}>
                  <IconLockedFeature
                    className={styles['icon-locked']}
                    onClick={toggleShowPremiumModal}
                  />
                </div>
              </div>
            )}
            <div
              className={
                !isPaidOrDelayed
                  ? styles['analyzer-basic-description-container']
                  : ''
              }
            >
              <span className={styles['analyzer-title']}>
                {getAnalyzerTitle()}
              </span>
              <p className={styles['analyzer-description']}>
                {getAnalyzerDescription()}
              </p>
            </div>
          </div>
          {!isPaidOrDelayed && (
            <ElementViewed
              name="Unlock photo analyzer block"
              type={Amp.element.type.block}
              context={Amp.element.context.ctaBlock}
            >
              <Button
                className={styles['button-unblock']}
                label="Unlock"
                shadow={false}
                color="solid-blue"
                minWidth="75px"
                type="link"
                href="/upgrade"
              />
            </ElementViewed>
          )}
        </div>
        {!!characteristics.length && isPaidOrDelayed && (
          <div className={styles.analyzer}>
            {characteristics.map(({ level_percentage, name }) => (
              <div
                key={name}
                className={cn(
                  styles['filling-row'],
                  styles[`filling-row-${name}`],
                  styles[
                    `filling-row-status-${getCharacteristicLevelName(
                      level_percentage,
                    )}`
                  ],
                )}
              >
                <div className={styles['filling-row-inner']}>
                  <div
                    className={cn(
                      styles['image-status-filling-icon'],
                      styles[`icon-${name}`],
                    )}
                  />
                  <div className={styles['image-status-filling-text']}>
                    {name !== 'feed' ? name : 'Composition'}
                  </div>
                </div>
                <div className={styles['image-status-filling-status']}>
                  <div
                    className={cn(styles['image-status-line'], {
                      [styles.full]: level_percentage > imageLevelPercent.bad,
                    })}
                  />
                  <div
                    className={cn(styles['image-status-line'], {
                      [styles.full]: level_percentage > imageLevelPercent.good,
                    })}
                  />
                  <div
                    className={cn(styles['image-status-line'], {
                      [styles.full]:
                        level_percentage > imageLevelPercent.excellent,
                    })}
                  />
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
      {showPremiumModal && (
        <Modal
          onClose={toggleShowPremiumModal}
          backdropClose
          classNameOverlay={styles['modal-overlay']}
          classNameContainer={styles['modal-container']}
          showDefaultLayout={false}
          disableBackgroundScroll
        >
          <TooltipPhotoPremium genderTitle={genderTitle} />
        </Modal>
      )}
    </>
  );
};

export default memo(ImageAnalyzer);
