'use client';

import React, { memo, useEffect } from 'react';
import { useField } from 'formik';
import ChipSelect from './ChipSelect/ChipSelect';

const ChipSelectFormik = (props) => {
  const { name } = props;
  const [
    { onBlur, value },
    { error, touched, initialValue },
    { setTouched, setValue },
  ] = useField(name);
  const onChange = (option) => setValue(option.value);

  // Autofill validation trigger
  useEffect(() => {
    if (initialValue !== value && !touched) {
      setTouched(true);
    }
  }, [value, touched, setTouched, initialValue]);

  return (
    <ChipSelect
      onBlur={onBlur}
      onChange={onChange}
      value={value}
      error={error}
      touched={touched}
      {...props}
    />
  );
};

export default memo(ChipSelectFormik);
