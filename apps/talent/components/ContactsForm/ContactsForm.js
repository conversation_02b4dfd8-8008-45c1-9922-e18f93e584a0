'use client';
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import styles from './ContactsForm.module.scss';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { useNotifications } from '@contexts/NotificationContext';
import { useAuth } from '@contexts/AuthContext';
import { maskPhoneNumber } from '@utils/maskPhoneNumber';
import { checkTollFreeNumber } from '@utils/checkTollFreeNumber';
import { EMAIL_REGEX, ErrorMessage, PHONE_NUMBER_REGEX } from '@constants/form';
import { Checkbox, Input } from '@components';
import Api from '@services/api';
import { Amp } from '@services/amp';

const ContactsForm = forwardRef(
  ({ email, phone, allowNotifications, toggleSaveButtonDisabled }, ref) => {
    const [currentEmail, setCurrentEmail] = useState(email);
    const { setNotification } = useNotifications();
    const { accountId, userProfiles, refreshUserProfiles } = useAuth();

    const triggerSubmit = async () => {
      await formik.submitForm();
    };

    useImperativeHandle(ref, () => ({
      async triggerSubmit() {
        await triggerSubmit();
      },
    }));

    const formik = useFormik({
      initialValues: {
        email: email || '',
        phone: maskPhoneNumber(phone) || '',
        allowNotifications: allowNotifications || false,
      },
      onSubmit: async (values) => {
        const body = new FormData();

        const newPhone = values.phone?.replace(/[^A-Z0-9]+/gi, '');

        body.append('email', values.email);
        body.append('phone', newPhone);
        body.append('phone_opt_outed', values.allowNotifications ? '0' : '1');

        const changeContactsResponse = await Api.clientside(
          `/accounts/${accountId}/touches`,
          {
            body,
            method: 'PUT',
          },
        );

        const isError = changeContactsResponse.status !== 'ok';
        const errorMessage =
          changeContactsResponse?.message || ErrorMessage.Unexpected;

        if (values.phone) {
          const user = userProfiles[0] || {};

          Amp.track(Amp.events.submitPhoneNumber, {
            scope: Amp.element.scope.settings,
            phone_number_last_4_digits: newPhone.slice(-4),
            country_code: user.country,
            status: isError ? 'failure' : 'success',
            error_message: isError ? errorMessage : null,
          });
        }

        if (isError) {
          setNotification({
            type: 'error',
            timeout: '5000',
            message: errorMessage,
          });
        } else {
          setNotification({
            type: 'success',
            timeout: '5000',
            message:
              currentEmail !== values.email
                ? 'Contacts updated. Please use your new email to sign in.'
                : 'Contacts updated',
          });
          formik.resetForm({ values });
          setCurrentEmail(values.email);
          refreshUserProfiles();
        }
      },
      validationSchema: Yup.object({
        email: Yup.string()
          .required(ErrorMessage.EmailRequired)
          .matches(EMAIL_REGEX, ErrorMessage.EmailPattern),
        phone: Yup.string()
          .matches(PHONE_NUMBER_REGEX, ErrorMessage.PhonePattern)
          .test(
            'phoneIsValid',
            ErrorMessage.PhonePatternToll,
            async (value) => {
              return checkTollFreeNumber(value);
            },
          ),
      }),
    });

    useEffect(() => {
      if (toggleSaveButtonDisabled) {
        toggleSaveButtonDisabled(!(formik.isValid && formik.dirty));
      }
    }, [formik.dirty, formik.isValid, formik.values, toggleSaveButtonDisabled]);

    const formatPhoneNumber = (e) => {
      formik.setFieldValue('phone', maskPhoneNumber(e.target.value));
    };

    return (
      <form className={styles['contacts-form']} onSubmit={formik.handleSubmit}>
        <div className={styles['contacts-form-row']}>
          <div className={styles['contacts-form-field']}>
            <Input
              name="phone"
              placeholder="Phone number"
              onChange={formatPhoneNumber}
              onBlur={formik.handleBlur}
              value={formik.values.phone}
              isTouched={formik.touched.phone}
              error={formik.errors.phone}
              hint="Include area code"
            />
          </div>
          <div className={styles['contacts-form-field']}>
            <Checkbox
              disabled={!formik.values.phone}
              name="allowNotifications"
              onChange={formik.handleChange}
              value={formik.values.allowNotifications}
              error={formik.errors.allowNotifications}
              onBlur={formik.handleBlur}
              isTouched={formik.touched.allowNotifications}
            >
              <span className={styles['contacts-form-field-label']}>
                Allow SMS notifications
              </span>
            </Checkbox>
          </div>
        </div>
        <div className={styles['contacts-form-row']}>
          <div className={styles['contacts-form-field']}>
            <Input
              name="email"
              placeholder="Email"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.email}
              isTouched={formik.touched.email}
              error={formik.errors.email}
            />
          </div>
          <div className={styles['contacts-form-warning']}>
            <span>
              <b>Note:</b> Changing your email will update your login
              credentials. Please use your new email to sign in.
            </span>
          </div>
        </div>
        <div className={styles['contacts-form-actions']}>
          <button
            className={styles['contacts-form-button']}
            type="submit"
            disabled={!(formik.isValid && formik.dirty)}
          >
            Save changes
          </button>
        </div>
      </form>
    );
  },
);

ContactsForm.displayName = 'ContactsForm';

export default ContactsForm;
