'use client';
import styles from './IncomingAttachment.module.scss';
import IconVideo from '../../../public/assets/icons/icon-video.svg';
import IconAudio from '../../../public/assets/icons/icon-audio.svg';

export const IncomingAttachment = ({ attachment, openImageAttachment }) => {
  const type =
    attachment.mimetype.split('/')[1] === 'pdf'
      ? attachment.mimetype.split('/')[1]
      : attachment.mimetype.split('/')[0];

  switch (type) {
    case 'image':
      return (
        <button
          className={styles['image-opener']}
          onClick={() => openImageAttachment(attachment.uri)}
        >
          <img
            className={styles.image}
            src={attachment.uri}
            alt={attachment.mimetype}
          />
        </button>
      );
    case 'pdf':
      return (
        <a
          href={attachment.uri}
          target="_blank"
          rel="noreferrer"
          className={styles['attachment-document']}
        >
          PDF
        </a>
      );
    case 'video':
      return (
        <a
          href={attachment.uri}
          target="_blank"
          rel="noreferrer"
          className={styles['attachment-document']}
        >
          <IconVideo />
        </a>
      );
    case 'audio':
      return (
        <a
          href={attachment.uri}
          target="_blank"
          rel="noreferrer"
          className={styles['attachment-document']}
        >
          <IconAudio />
        </a>
      );
    default:
      return (
        <a
          href={attachment.uri}
          target="_blank"
          rel="noreferrer"
          className={styles['attachment-document']}
        >
          DOC
        </a>
      );
  }
};
