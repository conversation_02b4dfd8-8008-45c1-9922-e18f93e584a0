import CheckoutClient from './_client';
import { cookies, headers } from 'next/headers';
import { redirect } from 'next/navigation';
import * as Sentry from '@sentry/nextjs';
import Api from '@services/api';
import { CookieServiceServer } from '@services/cookieServiceServer';
import { getResolvedUrl } from '@utils/apiHelper';
import { isMobile } from '@utils/isMobile';
import { formatBillingInfo } from '@utils/formatBillingInfo';
import { getMonthOptions, getYearOptions } from '@utils/getTimeOptions';
import { extractResult } from '@utils/extractPromiseResult';

export const metadata = {
  title: 'Subscribe Now',
};

export default async function CheckoutPage(req) {
  const params = await req.params;
  const searchParams = await req.searchParams;
  const { action, promo_code, stripe } = searchParams;
  const { slug } = params;

  // ENTR-2977, if no longer need, remove and take component out of [[...page]]
  if (!action || (slug && /^best-plan-/.test(slug[0]))) {
    redirect('/upgrade');
  }

  const resolvedUrl = getResolvedUrl('/checkout', searchParams);
  const cookieStore = await cookies();
  const headerStore = await headers();

  const accountId = CookieServiceServer.getAccountCookie(cookieStore);

  const isPremium = action === '329';
  const isLifetime = action === '335' || action === '343';

  const query = promo_code ? `?promo_code=${promo_code}` : '';

  const data = await Api.serverside(
    `/accounts/${accountId}/prices${query}`,
    cookieStore,
    headerStore,
    resolvedUrl,
  );

  let pricePlan,
    isStripe,
    stripeClientSecret,
    stripeInvoiceHash,
    isStripeError = false;

  const stripePK =
    isPremium || isLifetime ? process.env.premiumStripePK : data.stripe_pk;

  if (action) {
    const methods = data?.prices?.[0]?.methods || [];

    pricePlan =
      (isLifetime && {
        member_level_id: 343,
        price: 198,
        base_month_price: 35.99,
        period: 120,
        period_length: 'MONTH',
        methods,
      }) ||
      (isPremium && {
        member_level_id: 329,
        price: 216,
        base_month_price: 35.99,
        period: 12,
        period_length: 'MONTH',
        methods,
      }) ||
      data?.prices?.find((price) => price.member_level_id === Number(action));
  }

  if (slug) {
    pricePlan = data?.prices.find(
      (price) => price.period === parseInt(slug[0].split('-').pop()),
    );
  }

  if (!pricePlan) {
    redirect('/upgrade');
  } else {
    isStripe =
      process.env.stripeEnabled === 'true' &&
      (pricePlan.methods.includes('stripe') || !!stripe);

    if (isStripe) {
      const stripeSubscriptionParams = new URLSearchParams();

      stripeSubscriptionParams.append(
        'member_level',
        pricePlan.member_level_id,
      );

      if (promo_code) {
        stripeSubscriptionParams.append('promo_code', promo_code);
      }

      const stripeSubscriptionResponse = await Api.serverside(
        '/stripe/subscription',
        cookieStore,
        headerStore,
        resolvedUrl,
        'PUT',
        stripeSubscriptionParams,
      );

      if (stripeSubscriptionResponse.status !== 'ok') {
        isStripe = false;
        isStripeError = true;

        Sentry.captureException(
          new Error('Failed to fetch stripe subscription'),
          {
            tags: {
              feature: 'stripe',
            },
            extra: {
              accountId,
              message: stripeSubscriptionResponse?.message,
            },
          },
        );
      } else {
        stripeClientSecret = stripeSubscriptionResponse.stripe_client_secret;
        stripeInvoiceHash = stripeSubscriptionResponse.invoice_hash;
        isStripe = !!(stripeClientSecret && stripeInvoiceHash && stripePK);

        if (!isStripe) {
          isStripeError = true;

          Sentry.captureException(new Error('Missing stripe params'), {
            tags: {
              feature: 'stripe',
            },
            extra: {
              accountId,
              stripePK: !!stripePK,
              stripeInvoiceHash: !!stripeInvoiceHash,
              stripeClientSecret: !!stripeClientSecret,
            },
          });
        }
      }
    }
  }

  const userAgent = headerStore.get('user-agent');
  const isMobileFromUserAgent = isMobile(userAgent);
  const isAndroidDevice = /android/i.test(userAgent);

  const paths = [
    '/payment/billing',
    `/accounts/${accountId}/touches`,
    '/clientstatus/info',
  ];

  const results = await Promise.allSettled(
    paths.map((path) =>
      Api.serverside(path, cookieStore, headerStore, resolvedUrl),
    ),
  );

  const [billingResponse, touchesResponse, clientStatusResponse] = results.map(
    (result) => extractResult(result, {}),
  );

  const formattedTracking = {};
  // If no tracking, response is an empty array.
  // If it has tracking, response is an object
  const tracking = clientStatusResponse.info?.tracking || {};
  const trackingKeys = Object.keys(tracking);

  if (trackingKeys?.length) {
    trackingKeys.forEach((key) => {
      formattedTracking[`tracking.${key}`] = tracking[key];
    });
  }

  return (
    <CheckoutClient
      pricePlan={pricePlan || null}
      promoCode={promo_code || null}
      billing={formatBillingInfo(billingResponse)}
      monthOptions={getMonthOptions()}
      yearOptions={getYearOptions()}
      email={touchesResponse.links?.email?.value || ''}
      campaign={clientStatusResponse.info?.campaign || ''}
      campaignGroup={clientStatusResponse.info?.campaign_group || ''}
      tracking={formattedTracking}
      isStripe={isStripe}
      stripePK={stripePK}
      stripeClientSecret={stripeClientSecret || ''}
      stripeInvoiceHash={stripeInvoiceHash || ''}
      isMobile={isMobileFromUserAgent}
      isAndroidDevice={isAndroidDevice}
      isPremium={isPremium}
      isLifetime={isLifetime}
      experiments={{}}
      referer={headerStore.get('referer') || ''}
      discountId={data?.discount?.id || null}
      discountAbExperiment={data?.discount?.ab_experiment || null}
      isStripeError={isStripeError}
    />
  );
}
