import { notFound } from 'next/navigation';
import Api from '@services/api';
import { cookies, headers } from 'next/headers';

export const metadata = {
  title: 'Test Error Server',
};

export default async function TestErrorClientPage(req) {
  const { type } = await req.searchParams;
  const headerStore = await headers();
  const cookieStore = await cookies();
  const resolvedUrl = '/test-error-server';
  const value = {};

  if (type === '404') {
    notFound();
  } else if (type === 'type') {
    const testError = value.items.find((item) => item.id);
  } else if (type === 'reference') {
    testFunction();
  } else if (type === 'request') {
    await Api.serverside('/test', cookieStore, headerStore, resolvedUrl);
  } else if (type === 'response-props') {
    const response = await Api.serverside(
      '/test',
      cookieStore,
      headerStore,
      resolvedUrl,
    );

    value.items = response.items;
  }

  return (
    <div>
      <h1>Test server-side errors</h1>
    </div>
  );
}
