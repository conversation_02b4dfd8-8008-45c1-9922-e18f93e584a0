export const companyLinks = [
  {
    id: 'about',
    title: 'About',
    routerLink: `${process.env.publicUrl}/about`,
  },
  {
    id: 'terms',
    title: 'Terms of use',
    routerLink: `${process.env.publicUrl}/terms-of-use`,
  },
  {
    id: 'privacy',
    title: 'Privacy policy',
    routerLink: `${process.env.publicUrl}/privacy-policy`,
  },
  {
    id: 'dnsmpi',
    title: 'DNSMPI',
    routerLink: `${process.env.publicUrl}/dnsmpi`,
  },
  {
    id: 'contact',
    title: 'Contact us',
    routerLink: `${process.env.publicUrl}/contact`,
  },
];

export const exploreLinks = [
  {
    id: 'castingcalls',
    title: 'Casting calls',
    routerLink: `${process.env.publicUrl}/castingcalls`,
  },
  {
    id: 'talent',
    title: 'Talent',
    routerLink: `${process.env.publicUrl}/talent`,
  },
  {
    id: 'blog',
    title: 'Blog',
    routerLink: `${process.env.publicUrl}/blog`,
  },
  {
    id: 'reviews',
    title: 'Reviews',
    routerLink: `${process.env.publicUrl}/reviews`,
  },
];
