'use client';
import React, { memo, useEffect, useState } from 'react';
import Modal from '../Modal';
import styles from './ModalInviteTalent.module.scss';
import cn from 'classnames';
import { Button, Loading, Select } from '@components';
import { useModalContext } from '@contexts/ModalContext';
import Api from '@services/api';
import { useAuth } from '@contexts/AuthContext';
import { useNotifications } from '@contexts/NotificationContext';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { INVITATION_STATUS } from '@constants/talent';
import Image from 'next/image';
import { GTM_CATEGORIES, GTM_EVENTS } from '@constants/analytics';
import { useAnalytics } from 'use-analytics';
import { Amp } from '@services/amp';
import { ErrorMessage } from '@constants/form';

const ModalInviteTalent = ({
  talents,
  onClose,
  canContactTalent,
  onInviteSuccess = () => {},
  castingCallId,
  roleId,
  onClearSelection,
}) => {
  const [castingCalls, setCastingCalls] = useState([]);
  const [roles, setRoles] = useState([]);
  const [invitationStatus, setInvitationStatus] = useState(
    INVITATION_STATUS.Loading,
  );
  const [error, setError] = useState('');
  const { toggleShowPostCastingCallModal } = useModalContext();
  const { userProfiles } = useAuth();
  const { setNotification } = useNotifications();
  const { track } = useAnalytics();

  useEffect(() => {
    const getCastingCalls = async () => {
      const clientId = userProfiles[0].clientId;
      const response = await Api.clientside(
        `/calls/search?es=1&casting_director_id=${clientId}&filter=active&status[]=approved&type[]=web&type[]=url&type[]=open&type[]=email&type[]=phone`,
      );

      if (response.status !== 'ok') {
        setNotification({
          type: 'error',
          message: response.message || ErrorMessage.Unexpected,
          timeout: '5000',
        });
        onClose();
      } else {
        const formattedCastingCalls = (response.data?.calls || []).map(
          (castingCall) => ({ ...castingCall, value: castingCall.id }),
        );

        setCastingCalls(formattedCastingCalls);
        setInvitationStatus(INVITATION_STATUS.Enabled);
      }
    };

    if (canContactTalent) {
      getCastingCalls().catch();
    } else {
      setInvitationStatus(INVITATION_STATUS.Disabled);
    }
  }, [canContactTalent]);

  const formik = useFormik({
    initialValues: {
      castingCall: castingCallId,
      role: roleId,
    },
    onSubmit: async (values) => {
      setInvitationStatus(INVITATION_STATUS.Loading);

      const clientId = userProfiles[0].clientId;
      const body = new FormData();

      body.append('cd_id', clientId);
      body.append('role_id', values.role);

      talents.forEach((talent) => {
        body.append('profile_ids[]', talent.id);
      });

      const response = await Api.clientside(
        `/casting-calls/${values.castingCall}/invites`,
        {
          method: 'POST',
          body,
        },
      );

      if (response.status !== 'ok') {
        setNotification({
          type: 'error',
          message: response.message || ErrorMessage.Unexpected,
          timeout: '5000',
        });
        onClose();
      } else {
        if (talents.length === 1) {
          const isAlreadyInvited =
            response.list?.already_invited_profile_ids?.includes(talents[0].id);

          if (isAlreadyInvited) {
            setError('Talent already invited to this role');
            setInvitationStatus(INVITATION_STATUS.Enabled);
          } else {
            trackInvitation();
            setInvitationStatus(INVITATION_STATUS.Success);
          }
        } else {
          trackInvitation();
          setInvitationStatus(INVITATION_STATUS.Success);
        }
      }
    },
    validationSchema: Yup.object({
      castingCall: Yup.string().required(ErrorMessage.CastingCallRequired),
      role: Yup.string().required(ErrorMessage.RoleRequired),
    }),
  });

  const trackInvitation = () => {
    track(GTM_EVENTS.interaction, {
      target: GTM_CATEGORIES.b2b,
      action: 'invite_basic',
    });

    const isBatchInvite = talents.length > 1;
    const amplitudeData = {
      name: `${isBatchInvite ? 'batch ' : ''}invite sent`,
      scope: Amp.element.scope.global,
      section: Amp.element.section.inviteForm,
      type: Amp.element.type.button,
    };

    if (!isBatchInvite) {
      amplitudeData.talent_id = talents[0].id;
    }

    Amp.track(Amp.events.elementClicked, amplitudeData);
  };

  const getImage = (talent, index) => {
    return (
      <div
        className={cn(
          styles['image-container'],
          styles[`image-container-${index}`],
        )}
      >
        <Image
          className={styles.image}
          src={
            talent.titlePhotoUrl ||
            `/assets/placeholders/${
              talent.gender?.title ? talent.gender.title.toLowerCase() : 'male'
            }-head.svg`
          }
          alt="image"
          width={64}
          height={64}
          priority
        />
        {index === 2 && (
          <div className={styles['image-overlay']}>+{talents.length - 2}</div>
        )}
      </div>
    );
  };

  const getImages = () => {
    switch (true) {
      case talents.length > 2:
        return (
          <>
            {getImage(talents[0], 0)}
            {getImage(talents[1], 1)}
            {getImage(talents[2], 2)}
          </>
        );
      case talents.length === 2:
        return (
          <>
            {getImage(talents[0], 0)}
            {getImage(talents[1], 1)}
          </>
        );
      default:
        return getImage(talents[0], 0);
    }
  };

  const onPostCastingCall = () => {
    toggleShowPostCastingCallModal();
    onClose();
  };

  const onCastingCallSelect = (value) => {
    const castingCall = castingCalls.find(({ id }) => id === value);

    formik.resetForm({ values: { castingCall: value, role: '' } });

    setRoles(
      castingCall.roles?.map((role) => ({
        title: `${role.title} - ${role.description}`,
        value: role.id,
      })) || [],
    );
  };

  const onRoleSelect = (value) => {
    formik.setFieldValue('role', value);
  };

  const handleClose = () => {
    onClose();
    if (
      onClearSelection &&
      invitationStatus === INVITATION_STATUS.Success &&
      talents.length !== 1
    ) {
      onClearSelection();
    }
  };

  const getContent = () => {
    switch (invitationStatus) {
      case INVITATION_STATUS.Disabled:
        return (
          <div className={styles['contact-locked']}>
            <p className={styles.description}>
              You need at least one approved casting call to invite talent
            </p>
            <Button
              kind="secondary"
              color="blue"
              label="Post a casting call"
              minWidth="220px"
              onClick={onPostCastingCall}
            />
          </div>
        );
      case INVITATION_STATUS.Enabled:
        return (
          <form className={styles.form}>
            <Select
              placeholder="Select a casting call"
              options={castingCalls}
              name="castingCall"
              value={formik.values.castingCall}
              isTouched={formik.touched.castingCall}
              setFormFieldTouched={() => formik.setFieldTouched('castingCall')}
              onChange={onCastingCallSelect}
              error={formik.errors.castingCall}
            />
            <Select
              disabled={!roles.length}
              placeholder="Select a role"
              options={roles}
              name="role"
              value={formik.values.role}
              isTouched={formik.touched.role}
              setFormFieldTouched={() => formik.setFieldTouched('role')}
              onChange={onRoleSelect}
              error={formik.errors.role}
            />
            {error && <div className={styles.error}>{error}</div>}
            <Button
              className={styles['invite-button']}
              label="Invite to apply"
              minWidth="200px"
              disabled={!formik.isValid || !formik.dirty}
              onClick={formik.submitForm}
            />
          </form>
        );
      case INVITATION_STATUS.Success:
        return (
          <div className={styles['success-container']}>
            <Image
              src="/assets/icons/icon-checkmark-3.svg"
              width={20}
              height={20}
              alt="icon"
            />
            Invitation successfully sent
          </div>
        );
      default:
        return <Loading minHeight="40px" padding="20px" />;
    }
  };

  useEffect(() => {
    if (invitationStatus === INVITATION_STATUS.Enabled && castingCallId) {
      onCastingCallSelect(castingCallId);
      onRoleSelect(roleId);
    }
  }, [invitationStatus]);

  useEffect(() => {
    return () => {
      if (invitationStatus === INVITATION_STATUS.Success) {
        onInviteSuccess();
      }
    };
  }, [invitationStatus]);

  return (
    <Modal
      classNameContainer={styles['modal-container']}
      classNameOverlay={styles['modal-overlay']}
      classNameContent={styles['modal-content']}
      onClose={handleClose}
      backdropClose
      showMobileBorderRadius={false}
      showAnimation={false}
    >
      <div>
        <div className={styles['images']}>
          {getImages()}
          <div className={styles['title-container']}>
            <span className={styles.title}>INVITE</span>
            <span className={styles['sub-title']}>
              {talents.length > 1
                ? `${talents.length} talent`
                : `${talents[0].firstName} ${talents[0].lastName}`}
            </span>
          </div>
        </div>
        {getContent()}
      </div>
    </Modal>
  );
};

export default memo(ModalInviteTalent);
