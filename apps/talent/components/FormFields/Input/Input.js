'use client';
import cn from 'classnames';
import React, { memo } from 'react';
import styles from './Input.module.scss';

function Input({
  type = 'text',
  onChange,
  value = '',
  name,
  onBlur,
  onFocus,
  error,
  hint,
  isTouched,
  placeholder,
  label,
  className = '',
  autoComplete,
  charCounter,
  onKeyDown,
  maxLength,
  prefix,
  icon,
  inputMode,
}) {
  const showError = isTouched && error;
  const labelAndPlaceholder = !!label && !!placeholder;

  return (
    <div className={cn(styles.container, styles[className])}>
      <div className={cn(styles.field, showError && styles['field-error'])}>
        {!!value.toString().length && prefix && (
          <div
            className={cn(styles.prefix, showError && styles['prefix-error'])}
          >
            {prefix}&nbsp;
          </div>
        )}
        <input
          autoComplete={autoComplete}
          className={cn(styles.input, showError && styles['input-error'])}
          name={name}
          id={name}
          type={type}
          onChange={onChange}
          value={value}
          onBlur={onBlur}
          onFocus={onFocus}
          onKeyDown={onKeyDown}
          maxLength={maxLength}
          inputMode={inputMode}
        />
        {icon && <div className={styles.icon}>{icon}</div>}
        <div className={styles['focus-border']}></div>
        <label
          htmlFor={name}
          className={cn(styles.label, {
            [styles['label-error']]: showError,
            [styles.float]: value.toString().length || labelAndPlaceholder,
          })}
        >
          {label || placeholder}
        </label>
        {labelAndPlaceholder && (
          <div
            className={cn(styles.label, {
              [styles['label-error']]: showError,
              [styles.hide]: value.toString().length,
            })}
          >
            {placeholder}
          </div>
        )}
      </div>

      <div className={styles.hint}>
        {hint && !showError && <span>{hint}</span>}
        {charCounter && (
          <span className={styles.counter}>{value.length} / 500</span>
        )}
      </div>

      {showError && typeof error !== 'boolean' && (
        <div className={styles['error-message']}>{error}</div>
      )}
    </div>
  );
}

export default memo(Input);
