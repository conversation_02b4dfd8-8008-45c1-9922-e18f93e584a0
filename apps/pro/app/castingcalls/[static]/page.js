import CastingCallsClient from '../_client';
import { getRequestUrl, getStatusFilter } from '@utils/castingCallFilterHelper';
import Api from '@services/api';
import { extractResult } from '@utils/extractPromiseResult';
import { getResolvedUrl } from '@utils/apiHelper';
import { cookies, headers } from 'next/headers';
import { CookieServiceServer } from '@services/cookieServiceServer';
import { CASTING_CALLS_LIMIT } from '@constants/pagination';
import { notFound, redirect } from 'next/navigation';

export const metadata = {
  title: 'Casting Calls',
};

export default async function CastingCallsQueryPage(req) {
  const params = await req.params;
  const searchParams = await req.searchParams;
  const resolvedUrl = getResolvedUrl(
    `/castingcalls${params.static}`,
    searchParams,
  );
  const cookieStore = await cookies();
  const headerStore = await headers();
  const page = params.static ? Number(params.static.replace('page-', '')) : 1;
  const userProfiles = CookieServiceServer.getUserProfilesCookie(cookieStore);
  const clientId = userProfiles.length ? userProfiles[0].clientId || 0 : 0;

  const { status } = searchParams;

  const paths = [
    getRequestUrl(status || '', page, clientId),
    '/clientstatus/info',
  ];

  const results = await Promise.allSettled(
    paths.map((path) =>
      Api.serverside(path, cookieStore, headerStore, resolvedUrl),
    ),
  );

  const [castingCalls, clientStatus] = results.map((result) =>
    extractResult(result, {}),
  );

  const castingCallsTotal = castingCalls?.data?.total || 0;
  const maximumPage = Math.ceil(castingCallsTotal / CASTING_CALLS_LIMIT);

  if (maximumPage && page > maximumPage) {
    redirect(
      resolvedUrl
        .replace(
          `-page-${page}`,
          maximumPage === 1 ? `` : `-page-${maximumPage}`,
        )
        .replace(
          `/page-${page}`,
          maximumPage === 1 ? `` : `/page-${maximumPage}`,
        ),
    );
  }

  if (page <= 0 || isNaN(page)) {
    notFound();
  }

  if (page === 1 && resolvedUrl.includes(`page-1`)) {
    redirect(
      resolvedUrl.replace(`-page-${page}`, ``).replace(`/page-${page}`, ``),
    );
  }

  const statusFilter = getStatusFilter(status || '');

  return (
    <CastingCallsClient
      castingCalls={castingCalls?.data?.calls || []}
      activeCastingCallsTotal={castingCalls?.data?.cc_active_count || 0}
      castingCallsTotal={castingCallsTotal}
      canContactTalent={
        clientStatus.info?.allowed_to?.hasOwnProperty('contact_talent') || false
      }
      page={page}
      statusFilter={statusFilter}
      showReset={
        statusFilter.options.some((option) => option.checked) || page > 1
      }
    />
  );
}
