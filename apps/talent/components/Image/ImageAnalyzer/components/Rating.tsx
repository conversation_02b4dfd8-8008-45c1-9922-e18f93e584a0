import styles from '../ImageAnalyzer.module.scss';
import cn from 'classnames';
import React, { useMemo } from 'react';
import {
  IconImageRating0,
  IconImageRating1,
  IconImageRating2,
  IconImageRating3,
  IconImageRating4,
  IconImageRating5,
} from '@icons/index';

const RATING_ICONS = {
  0: IconImageRating0,
  1: IconImageRating1,
  2: IconImageRating2,
  3: IconImageRating3,
  4: IconImageRating4,
  5: IconImageRating5,
} as const;

type RatingProps = {
  rating: keyof typeof RATING_ICONS;
  isTooltip: boolean;
};

export const Rating = ({ rating, isTooltip }: RatingProps) => {
  const { IconComponent, className } = useMemo(() => {
    const IconComponent = RATING_ICONS[rating] || IconImageRating0;

    const className = cn(
      styles['rating-icon'],
      styles[`rating-icon-${rating}`],
      {
        [styles['rating-icon-main']]: !isTooltip,
      },
    );

    return { IconComponent, className };
  }, [rating, isTooltip]);

  return <IconComponent className={className} />;
};
