'use client';
import React, { useEffect, useState } from 'react';
import {
  Button,
  CheckboxFormik,
  Input,
  Loading,
  MainLayout,
  Modal,
  PasswordInputFormik,
  PrivacyPolicy,
  TermsOfUse,
} from '@components';
import styles from '@styles/register.module.scss';
import { Form, Formik } from 'formik';
import { useAuth } from '@contexts/AuthContext';
import { useNotifications } from '@contexts/NotificationContext';
import { useRouter } from 'next/navigation';
import * as Yup from 'yup';
import { ErrorMessage, PASSWORD_REGEX } from '@constants/form';
import Api from '@services/api';
import cn from 'classnames';
import Image from 'next/image';
import { useAnalytics } from 'use-analytics';
import { Amp } from '@services/amp';
import { GTM_ACTIONS, GTM_CATEGORIES, GTM_EVENTS } from '@constants/analytics';
import { CookieService } from '@services/cookieService';
import * as Sentry from '@sentry/nextjs';

const RegisterClient = ({ email }) => {
  const [showTerms, setShowTerms] = useState(false);
  const [showPrivacyPolicy, setShowPrivacyPolicy] = useState(false);

  const { refreshUserProfiles, accountLevelVerify } = useAuth();
  const { setNotification } = useNotifications();
  const { track } = useAnalytics();
  const router = useRouter();

  const initialValues = {
    email: email,
    password: '',
    agree: false,
  };

  const validationSchema = Yup.object({
    password: Yup.string()
      .required(ErrorMessage.PasswordRequired)
      .matches(PASSWORD_REGEX, ErrorMessage.PasswordPattern)
      .min(8, ErrorMessage.PasswordPattern),
    agree: Yup.bool().isTrue(),
  });

  const handleSubmit = async ({ password }, { resetForm }) => {
    const body = new FormData();

    body.append('email', email);
    body.append('password', password);
    body.append('firstname', 'Casting');
    body.append('lastname', 'Professional');
    body.append('type', 'agent');
    body.append('optout_advertising', 0);

    const response = await Api.clientside('/auth/register', {
      body,
      method: 'POST',
    });

    const { status, message, identifier, key } = response || {};

    if (status !== 'ok') {
      setNotification({
        type: 'error',
        timeout: '5000',
        message: message || ErrorMessage.Unexpected,
      });
      Amp.track(Amp.events.formSubmitted, {
        name: 'registration errors',
        result: Amp.element.result.fail,
        message: message,
      });
    } else {
      resetForm();

      const loginBody = new FormData();

      loginBody.append('identifier', identifier);
      loginBody.append('key', key);
      loginBody.append('expand', 'level,settings,manager,profiles');

      const loginResponse = await Api.clientside('/auth/nonce', {
        body: loginBody,
        method: 'POST',
      });

      if (loginResponse.status !== 'error') {
        track(GTM_EVENTS.interaction, {
          target: GTM_CATEGORIES.successRegistration,
          action: GTM_ACTIONS.registration,
          label: 'agent',
          client: {
            first_name: 'Casting',
            last_name: 'Professional',
            email,
          },
        });

        const accountId = loginResponse.id;
        const profileId = loginResponse.links?.profiles?.items[0]?.id;

        CookieService.setAccountCookie(accountId);
        CookieService.setUserTypeCookie('agent');
        CookieService.setProfileCookie(profileId);
        Sentry.setUser({ id: accountId, type: 'agent' });

        await accountLevelVerify();
        await refreshUserProfiles();

        Amp.track(Amp.events.signUp, {
          type: 'agent',
          provider: 'native',
        });

        router.push('/welcome');
      } else {
        Amp.track(Amp.events.invalidEventOccurred, {
          name: `login failed`,
        });

        window.location.href = `${process.env.publicUrl}/login`;
      }
    }
  };

  const toggleShowTerms = (e) => {
    e?.preventDefault();
    setShowTerms(!showTerms);
  };

  const toggleShowPrivacyPolicy = (e) => {
    e?.preventDefault();
    setShowPrivacyPolicy(!showPrivacyPolicy);
  };

  useEffect(() => {
    Amp.track(Amp.events.viewRegister, {
      type: 'agent',
    });
  }, []);

  return (
    <MainLayout isLogoHeaderVisible>
      <div className={styles.container}>
        <span className={styles.title}>Welcome to allcasting!</span>
        <span className={styles.title}>Complete Your Account in Seconds</span>
        <span className={styles['title-mobile']}>
          Create your allcasting password
        </span>
        <p className={styles.description}>
          You&apos;re one step away from accessing powerful tools to find the
          perfect talent for your project.
        </p>
        <Formik
          initialValues={initialValues}
          onSubmit={handleSubmit}
          validationSchema={validationSchema}
        >
          {({ isSubmitting, touched, errors }) => (
            <Form className={styles.form}>
              <Input disabled value={email} />
              <p className={styles.hint}>
                This email is already linked to your account. You can update it
                anytime in your profile settings.
              </p>
              <PasswordInputFormik name="password" required />
              <div
                className={cn(styles['agreement-container'], {
                  [styles['agreement-error']]: touched.agree && errors.agree,
                })}
              >
                <CheckboxFormik name="agree">
                  <div className={styles['agreement-text']}>
                    <span>
                      By choosing to join, I certify I am at least 18 years old
                      and have read and agree to the allcasting.com
                    </span>
                    <span> </span>
                    <span
                      className={styles.link}
                      onClick={toggleShowPrivacyPolicy}
                    >
                      privacy policy
                    </span>
                    <span> and </span>
                    <span className={styles.link} onClick={toggleShowTerms}>
                      terms of use
                    </span>
                    <span>. </span>
                    <span>
                      I agree to receive welcome email, newsletter, SMS &amp;
                      occasional account updates from AllCasting.com
                    </span>
                  </div>
                </CheckboxFormik>
              </div>
              <div className={styles['form-footer']}>
                <div>
                  {isSubmitting ? (
                    <Loading minHeight="60px" />
                  ) : (
                    <Button
                      type="submit"
                      label="Complete my registration"
                      minWidth="240px"
                      shadow={false}
                    />
                  )}
                </div>
              </div>
            </Form>
          )}
        </Formik>
      </div>
      <div className={styles.footer}>
        <div className={styles.features}>
          <div className={styles.feature}>
            <Image
              src="/assets/icons/icon-checkmark-6.svg"
              alt="icon"
              width={23}
              height={22}
            />
            <span>Post casting calls for free</span>
          </div>
          <div className={styles.feature}>
            <Image
              src="/assets/icons/icon-checkmark-6.svg"
              alt="icon"
              width={23}
              height={22}
            />
            <span>Find and reach 2+ million diverse talents</span>
          </div>
          <div className={styles.feature}>
            <Image
              src="/assets/icons/icon-checkmark-6.svg"
              alt="icon"
              width={23}
              height={22}
            />
            <span>Simplify your talent search with easy-to-use tools</span>
          </div>
        </div>
      </div>
      {showTerms && (
        <Modal backdropClose onClose={toggleShowTerms}>
          <TermsOfUse />
        </Modal>
      )}
      {showPrivacyPolicy && (
        <Modal backdropClose onClose={toggleShowPrivacyPolicy}>
          <PrivacyPolicy onNavigation={toggleShowPrivacyPolicy} />
        </Modal>
      )}
    </MainLayout>
  );
};

export default RegisterClient;
