@use '@styles/variables' as *;
@use '@styles/mixins' as *;
@use '@styles/button-animations' as *;

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  align-self: auto;
  max-width: $content-max-width;
  margin-left: auto;
  margin-right: auto;
  padding-left: $space-20;

  @include desktop {
    padding: 0 $space-60;
  }

  .logo {
    width: 93px;
    height: 21px;
    color: $violet-100;
    cursor: pointer;
  }

  .logo-link {
    line-height: 1;
  }

  .navbar {
    display: flex;
    align-items: center;

    &.desktop {
      display: none;

      @include tablet {
        display: flex;
      }
    }

    &.mobile {
      display: flex;
      font-size: 12px;

      @include tablet {
        display: none;
      }
    }
  }
}
