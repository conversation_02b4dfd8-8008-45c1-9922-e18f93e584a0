'use client';
import { memo, useCallback, useEffect, useState } from 'react';
import <PERSON>ropper from 'react-easy-crop';
import styles from './CropTool.module.scss';
import cn from 'classnames';
import { getCroppedImage } from '@utils/cropToolHelpers';
import Image from 'next/image';
import { Loading } from '@components';

const CropTool = ({
  title = 'Adjust Your Photo',
  image,
  onCrop,
  cropShape = 'rect',
  onClose,
  initialZoom = 1,
  aspectRatio = 3 / 4,
  loading,
  initialCrop = { x: 0, y: 0 },
  initialRotation = 0,
  minZoom = 1,
  maxZoom = 2,
  zoomWithScroll = true,
  showGrid = true,
  zoomStep = 0.01,
}) => {
  const [crop, setCrop] = useState(initialCrop);
  const [zoom, setZoom] = useState(initialZoom);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState(null);
  const [rotation, setRotation] = useState(initialRotation);

  useEffect(() => {
    setRotation(initialRotation);
  }, [image]);

  const onCropComplete = useCallback((croppedArea, croppedAreaPixels) => {
    setCroppedAreaPixels(croppedAreaPixels);
  }, []);

  const cropImage = useCallback(async () => {
    const croppedImage = await getCroppedImage(
      image,
      croppedAreaPixels,
      rotation,
    );

    onCrop(croppedImage);
  }, [croppedAreaPixels, image, onCrop, rotation]);

  const rotateImage = () => {
    if (rotation === 360) {
      setRotation(90);
    } else {
      setRotation(rotation + 90);
    }
  };

  const updateZoomValue = (event) => {
    setZoom(Number(event.target.value));
  };

  return (
    <div className={styles['crop-tool-outer-container']}>
      <div className={styles['crop-tool-container']}>
        <div
          className={cn(styles['crop-tool-header'], {
            [styles.disabled]: loading,
          })}
        >
          <button
            disabled={loading}
            onClick={onClose}
            className={cn(
              styles['crop-tool-mobile-action-button'],
              styles['crop-tool-action-button-cancel'],
            )}
          >
            Cancel
          </button>
          <span>{title}</span>
          <button
            disabled={loading}
            onClick={cropImage}
            className={cn(
              styles['crop-tool-mobile-action-button'],
              styles['crop-tool-action-button-crop'],
            )}
          >
            Crop
          </button>
        </div>
        <div className={styles['crop-tool']}>
          {loading ? (
            <Loading minHeight="100%" color="white" />
          ) : (
            <>
              <Cropper
                rotation={rotation}
                image={image}
                crop={crop}
                zoom={zoom}
                aspect={aspectRatio}
                onCropChange={setCrop}
                onCropComplete={onCropComplete}
                onZoomChange={setZoom}
                minZoom={minZoom}
                maxZoom={maxZoom}
                zoomWithScroll={zoomWithScroll}
                showGrid={showGrid}
                cropShape={cropShape}
              />
              <div className={styles['zoom-slider-container']}>
                <div className={styles['zoom-slider-inner-container']}>
                  <input
                    type="range"
                    min={minZoom}
                    max={maxZoom}
                    step={zoomStep}
                    value={zoom}
                    onChange={updateZoomValue}
                    className={styles['zoom-slider']}
                  />
                </div>
                <span className={styles['zoom-slider-progress']}>
                  Zoom: {Math.floor((zoom - minZoom) * 100)}%
                </span>
              </div>
              <div
                onClick={rotateImage}
                className={styles['crop-tool-rotate-container']}
              >
                <Image
                  className={styles['crop-tool-rotate-icon']}
                  src={'/assets/icons/icon-rotate.svg'}
                  width={40}
                  height={40}
                  alt="icon"
                />
              </div>
            </>
          )}
        </div>

        <div className={styles['crop-tool-actions']}>
          <button
            disabled={loading}
            onClick={onClose}
            className={cn(
              styles['crop-tool-action-button'],
              styles['crop-tool-action-button-cancel'],
            )}
          >
            Cancel
          </button>
          <button
            disabled={loading}
            onClick={cropImage}
            className={cn(
              styles['crop-tool-action-button'],
              styles['crop-tool-action-button-crop'],
            )}
          >
            Crop
          </button>
        </div>
      </div>
    </div>
  );
};

export default memo(CropTool);
