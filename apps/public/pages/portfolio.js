import Api from '../services/api';
import { getGatewayHeaders, getHeaders } from '../utils/headerHelper';
import ApiNoCache from '../services/apiNoCache';

export const getServerSideProps = async ({ query, req, res }) => {
  const { view: profileId } = query;

  if (isNaN(Number(profileId))) {
    return {
      redirect: {
        permanent: true,
        destination: '/castingcalls',
      },
    };
  }

  const clientResponse = await ApiNoCache.serverGateway(
    `/clients/${profileId}?expand=profiles`,
    getGatewayHeaders(`/portfolio?view=${profileId}`),
    req,
    res,
  );

  let id = null;

  if (
    clientResponse.data?.status === 'ok' &&
    clientResponse.data?.links?.profiles &&
    Array.isArray(clientResponse.data.links.profiles.items)
  ) {
    id = clientResponse.data.links.profiles.items[0]?.id;
  }

  if (id) {
    return {
      redirect: {
        permanent: true,
        destination: `/profile/${id}`,
      },
    };
  }

  const profile = await Api.serverAPIRoute(
    `/profiles/${profileId}`,
    null,
    getHeaders(req, `/portfolio?view=${profileId}`),
  );

  if (profile.status === 'ok') {
    return {
      redirect: {
        permanent: true,
        destination: `/profile/${profileId}`,
      },
    };
  }

  return {
    redirect: {
      permanent: true,
      destination: '/castingcalls',
    },
  };
};
const Portfolio = () => {
  return <></>;
};

export default Portfolio;
