'use client';
import { memo, useRef, useState } from 'react';
import styles from './AudioPlaceholder.module.scss';
import Image from 'next/image';
import { AudioUploadTool, ModalAudioUpload } from '@components';
import { usePremium } from '@contexts/PremiumContext';
import { Amp } from '@services/amp';
import { allowedFileTypes, maxSizeMB } from '@constants/audio';

const AudioPlaceholder = ({ isPaidOrDelayed, isMobileFromUserAgent }) => {
  const [selectedAudio, setSelectedAudio] = useState(null);
  const [showAudioUploadModal, setShowAudioUploadModal] = useState(false);
  const audioUploadToolRef = useRef();
  const { openPremiumModal, setPremiumTrackingName } = usePremium();

  const onFileInputChange = (audio) => {
    setSelectedAudio(audio);
    setShowAudioUploadModal(true);
  };

  const selectFile = () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'upload audio',
      scope: Amp.element.scope.profile,
      section: Amp.element.section.audio,
      type: Amp.element.type.button,
    });

    if (isPaidOrDelayed) {
      audioUploadToolRef.current.selectFile();
    } else {
      setPremiumTrackingName('Unlock upload audio popup');
      openPremiumModal();
    }
  };

  return (
    <>
      <AudioUploadTool
        ref={audioUploadToolRef}
        onFileInputChange={onFileInputChange}
      >
        <div className={styles['audio-placeholder']} onClick={selectFile}>
          <Image
            src="/assets/icons/icon-add-audio.svg"
            alt="icon"
            width={58}
            height={40}
          />
          <div className={styles['audio-placeholder-content']}>
            <span className={styles['audio-placeholder-text']}>
              Upload your audio reel
            </span>
            <span>
              <b>Max size:</b> {maxSizeMB} MB ({allowedFileTypes.join(', ')})
            </span>
          </div>
        </div>
      </AudioUploadTool>
      {showAudioUploadModal && (
        <ModalAudioUpload
          onClose={() => {
            setSelectedAudio(null);
            setShowAudioUploadModal(false);
          }}
          audio={selectedAudio}
          onReplaceAudio={() => {
            setShowAudioUploadModal(false);
            selectFile();
          }}
          isMobileFromUserAgent={isMobileFromUserAgent}
        />
      )}
    </>
  );
};

export default memo(AudioPlaceholder);
