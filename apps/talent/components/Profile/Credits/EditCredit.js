'use client';
import React, { memo, useState } from 'react';
import styles from '../Profile.module.scss';
import creditStyles from '../ProfileCredits.module.scss';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { Input, Select } from '@components';
import cn from 'classnames';
import Api from '@services/api';
import { useNotifications } from '@contexts/NotificationContext';
import {
  getMonthOptionsWithFullTitles,
  getYearOptionsWithRange,
} from '@utils/getTimeOptions';
import { Amp } from '@services/amp';
import { ErrorMessage, JOB_TITLE_REGEX } from '@constants/form';

const EditCredit = ({
  id,
  title,
  month,
  year,
  description,
  company,
  onClose,
  onCloseAndRefresh,
  updateProfileRating,
}) => {
  const [isLoading, setLoading] = useState(false);
  const { setNotification } = useNotifications();
  const currentYear = new Date().getFullYear();
  const yearList = getYearOptionsWithRange();

  const setMonthValue = (e) => {
    formik.setFieldValue('month', e);
  };

  const setMonthTouched = () => {
    formik.setFieldTouched('month');
  };

  const setYearValue = (e) => {
    const isCurrentYear = e === currentYear;

    formik.setFieldValue('year', e);
    setMonthList(getMonthOptionsWithFullTitles(true, isCurrentYear));
  };

  const setYearTouched = () => {
    formik.setFieldTouched('year');
  };

  const onCreditDelete = async () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'remove credit',
      scope: Amp.element.scope.profile,
      section: Amp.element.section.credits,
      type: Amp.element.type.button,
    });

    setLoading(true);
    const deleteCreditResponse = await Api.clientside(
      `/credits/${id}?expand=profile`,
      {
        method: 'DELETE',
      },
    );

    if (deleteCreditResponse.status !== 'removed') {
      setLoading(false);
      setNotification({
        type: 'error',
        message: deleteCreditResponse.message || ErrorMessage.Unexpected,
        timeout: '5000',
      });
    } else {
      updateProfileRating(deleteCreditResponse.links?.profile?.rating);
      await onCloseAndRefresh();
      setLoading(false);
    }
  };

  const formik = useFormik({
    initialValues: {
      title: title || '',
      month: month || 1,
      year: year || currentYear,
      company: company || '',
      description: description || '',
    },
    onSubmit: async (values) => {
      Amp.track(Amp.events.elementClicked, {
        name: 'save credit',
        scope: Amp.element.scope.profile,
        section: Amp.element.section.credits,
        type: Amp.element.type.button,
      });

      const body = new FormData();

      body.append('title', values.title);
      body.append('company', values.company);
      body.append('month', String(values.month));
      body.append('year', String(values.year));
      body.append('description', values.description);

      const editCreditResponse = await Api.clientside(`/credits/${id}`, {
        body,
        method: 'PUT',
      });

      if (editCreditResponse.status !== 'ok') {
        setNotification({
          type: 'error',
          message: editCreditResponse.message || ErrorMessage.Unexpected,
          timeout: '5000',
        });
      } else {
        await onCloseAndRefresh();
      }
    },
    validationSchema: Yup.object({
      title: Yup.string()
        .required(ErrorMessage.JobTitleRequired)
        .min(3, ErrorMessage.MinCharactersLatin.replace('X', '3'))
        .max(130, ErrorMessage.MaxCharactersLatin.replace('X', '130'))
        .matches(
          JOB_TITLE_REGEX,
          ErrorMessage.MaxCharactersLatin.replace('X', '130'),
        ),
      company: Yup.string()
        .required(ErrorMessage.DescriptionsRequired)
        .max(130, ErrorMessage.MaxCharacters.replace('X', '130')),
      description: Yup.string()
        .required(ErrorMessage.DescriptionsRequired)
        .max(500, ErrorMessage.MaxCharacters.replace('X', '500'))
        .min(10, ErrorMessage.MinCharacters.replace('X', '10')),
    }),
  });

  const preselectedCurrenYear = currentYear === formik.values.year;
  const [monthList, setMonthList] = useState(
    getMonthOptionsWithFullTitles(true, preselectedCurrenYear),
  );

  return (
    <div className={creditStyles['credit-container']}>
      <form onSubmit={formik.handleSubmit}>
        <div
          className={cn(
            styles['profile-section-title'],
            creditStyles['credit-header'],
          )}
        >
          Edit credits:
          <div>
            <button
              className={cn(
                styles['save-button'],
                creditStyles['credit-save-btn'],
              )}
              disabled={
                !(formik.isValid && formik.dirty) ||
                formik.isSubmitting ||
                isLoading
              }
              type="submit"
            >
              Save
            </button>
            <button
              className={styles['delete-button']}
              onClick={onCreditDelete}
              disabled={formik.isSubmitting || isLoading}
              type="button"
            >
              Delete
            </button>
          </div>
        </div>

        <div className={creditStyles['credit-container-box']}>
          <div
            onClick={onClose}
            className={creditStyles['credit-container-close']}
          />
          <div className={creditStyles['credit-container-row']}>
            <Input
              name="title"
              placeholder="Job title"
              value={formik.values.title}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              isTouched={formik.touched.title}
              error={formik.errors.title}
              hint="From 3 to 130 characters"
            />
            <div className={creditStyles['credit-container-row-selects']}>
              <Select
                name="month"
                value={formik.values.month}
                onChange={setMonthValue}
                isTouched={formik.touched.month}
                setFormFieldTouched={setMonthTouched}
                options={monthList}
              />

              <Select
                name="year"
                value={formik.values.year}
                onChange={setYearValue}
                isTouched={formik.touched.month}
                setFormFieldTouched={setYearTouched}
                options={yearList}
              />
            </div>
          </div>

          <Input
            name="company"
            placeholder="Company"
            value={formik.values.company}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            isTouched={formik.touched.company}
            error={formik.errors.company}
            hint="Max 130 characters"
          />
          <Input
            name="description"
            placeholder="About"
            value={formik.values.description}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            isTouched={formik.touched.description}
            error={formik.errors.description}
            hint="Max 500 characters"
          />
        </div>
      </form>
    </div>
  );
};

export default memo(EditCredit);
