import DirectorClient from './_client';
import { cookies, headers } from 'next/headers';
import { notFound } from 'next/navigation';
import { getResolvedUrl } from '@utils/apiHelper';
import Api from '@services/api';
import { extractResult } from '@utils/extractPromiseResult';
import { isMobile } from '@utils/isMobile';
import { formatProProfile } from '@utils/formatProProfile';

export const metadata = {
  title: 'Profile',
};

export default async function DirectorPage(req) {
  const params = await req.params;
  const searchParams = await req.searchParams;
  const { slug } = params;
  const resolvedUrl = getResolvedUrl(
    `/director/${params.id}/${params.slug}`,
    searchParams,
  );
  const cookieStore = await cookies();
  const headerStore = await headers();

  let id = params.id;

  if (isNaN(Number(id))) {
    const profileByUsername = await Api.serverside(
      `/personal-urls/${id}`,
      cookieStore,
      headerStore,
      resolvedUrl,
    );

    id = profileByUsername.links?.profile?.id;
  }

  if (!id) {
    notFound();
  }

  const profile = await Api.serverside(
    `/profiles/${id}?expand=personal_url,socialities,categories,location,touches`,
    cookieStore,
    headerStore,
    resolvedUrl,
  );

  // Intentionally
  if (false === profile.success) {
    notFound();
  }

  const paths = [
    `/profiles/${id}/images?expand=profile_image_characteristics&width=600&height=800`,
    `/profiles/${id}/socialities`,
    `/parameters`,
  ];

  const results = await Promise.allSettled(
    paths.map((path) =>
      Api.serverside(path, cookieStore, headerStore, resolvedUrl),
    ),
  );

  const [profileImages, profileSocialNetworks, parameters] = results.map(
    (result) => extractResult(result, { items: [] }),
  );

  const formattedProfile = formatProProfile(
    profile,
    profileImages.items,
    profileSocialNetworks.items,
  );

  const userAgent = headerStore.get('user-agent');
  const isMobileFromUserAgent = isMobile(userAgent);

  return (
    <DirectorClient
      profile={formattedProfile}
      categoryOptions={
        parameters.items?.categories?.items?.map((option) => ({
          ...option,
          value: option.id,
        })) || []
      }
      tab={slug}
      isMobileFromUserAgent={isMobileFromUserAgent}
    />
  );
}
