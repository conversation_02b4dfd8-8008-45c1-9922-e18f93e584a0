@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.audio-preview {
  background-color: $grey-10;
  border-radius: 10px;
  display: flex;
  align-items: center;
  flex-direction: column;
  padding: $space-15;
  justify-content: space-between;
  gap: $space-5;
}

.title {
  font-size: 14px;
  color: $black;
  font-weight: 600;
  text-align: center;

  @include tablet {
    font-size: 16px;
  }
}

.audio-preview-actions {
  display: none;
  align-items: center;
  justify-content: center;
  gap: 32px;

  @include tablet {
    display: flex;
  }
}

.icon {
  cursor: pointer;
  opacity: 0.6;
  transition: all 0.2s ease-in-out;

  &:hover {
    opacity: 1;
    transition: all 0.2s ease-in-out;
  }
}

.audio-preview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;

  @include tablet {
    justify-content: center;

    .icon {
      display: none;
    }
  }
}

.audio-preview-action {
  display: flex;
  align-items: center;
  gap: $space-15;
  font-size: 14px;
  font-weight: 600;
}
