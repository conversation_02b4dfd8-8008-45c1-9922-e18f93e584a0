import Reviews from './[page]';
import Api from '../../services/api';
import seoPageProps from '../../utils/seoPageProps';
import ApiNoCache from '../../services/apiNoCache';
import { CookieService } from '../../services/cookieService';
import { formatAnnouncement } from '../../utils/formatAnnouncement';
import { getGatewayHeaders, getHeaders } from '../../utils/headerHelper';
import { sharedServerSideProps } from '../../utils/sharedServerSideProps';
import { fetchArticles } from '../../services/endpoints/articles';
import { ARTICLE_CATEGORY } from '../../constants/articles';

export const getServerSideProps = async ({ req, res, query, resolvedUrl }) => {
  const isTalent = CookieService.getUserTypeCookie(req, res) === 'talent';
  const page = 1;
  const limit = query.limit || 8;

  const reviewResponse = isTalent
    ? (
        await ApiNoCache.serverGateway(
          `/testimonials?page=${page}&limit=${limit}`,
          getGatewayHeaders(resolvedUrl),
          req,
          res,
        )
      ).data
    : await Api.serverAPIRoute(
        `/reviews/testimonials?page=${page}&limit=${limit}`,
        null,
        getHeaders(req, resolvedUrl),
      );
  const profileResponse = await Api.serverAPIRoute(
    `/reviews/profiles`,
    null,
    getHeaders(req, resolvedUrl),
  );
  const articlesResponse = await fetchArticles({
    limit: 5,
    category: ARTICLE_CATEGORY.lessons,
  });
  const accountLevel = CookieService.getAccountLevelCookie(req, res);

  let formattedAnnouncement = null;

  if (accountLevel?.isPaidOrDelayed) {
    const announcements = (
      await Api.serverAPIRoute(
        `/promotions?types=Announcements&targets=talent-is-paid&limit=1`,
        null,
        getHeaders(req, resolvedUrl),
      )
    ).data;
    const announcement = announcements ? announcements[0] : null;

    formattedAnnouncement = formatAnnouncement(announcement);
  }
  const sharedProps = await sharedServerSideProps(req, res);

  return {
    props: {
      ...sharedProps,
      initialReviews: reviewResponse.items,
      reviewCount: reviewResponse.count,
      profiles: profileResponse.items,
      articles: articlesResponse.items || [],
      initialReviewQuery: { page, limit },
      announcement: formattedAnnouncement,
      seoPage: {
        ...(await seoPageProps(resolvedUrl)),
        ogImageUrl: `${process.env.baseUrl}/assets/meta/reviews.webp`,
      },
    },
  };
};

export default function IndexReviews({
  initialReviews,
  reviewCount,
  articles,
  profiles,
  initialReviewQuery,
  announcement,
  seoPage,
}) {
  return (
    <Reviews
      seoPage={seoPage}
      initialReviews={initialReviews}
      reviewCount={reviewCount}
      articles={articles}
      profiles={profiles}
      announcement={announcement}
      initialReviewQuery={initialReviewQuery}
    />
  );
}
