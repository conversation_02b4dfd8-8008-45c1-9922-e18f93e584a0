@use '@styles/mixins' as *;
@use '@styles/variables' as *;

.container {
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
  width: 100%;
  height: 100%;
  align-items: center;
}

.conversation-wrapper {
  display: flex;
  flex-flow: column nowrap;
  height: 100%;
  width: 100%;
  position: relative;
  overflow: auto;
  align-items: center;
  flex: 1 1;
  max-width: 700px;
  padding-left: $space-10;
  padding-right: $space-10;
}
