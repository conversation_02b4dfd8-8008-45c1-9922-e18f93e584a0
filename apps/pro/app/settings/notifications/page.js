import NotificationsClient from './_client';
import { cookies, headers } from 'next/headers';
import { CookieServiceServer } from '@services/cookieServiceServer';
import Api from '@services/api';
import { getResolvedUrl } from '@utils/apiHelper';

export const metadata = {
  title: 'Email Notifications',
};

export default async function NotificationsPage(req) {
  const searchParams = await req.searchParams;
  const cookieStore = await cookies();
  const headerStore = await headers();
  const accountId = CookieServiceServer.getAccountCookie(cookieStore);
  const resolvedUrl = getResolvedUrl('/settings/notifications', searchParams);

  const touches = await Api.serverside(
    `/accounts/${accountId}/touches`,
    cookieStore,
    headerStore,
    resolvedUrl,
  );

  const identifier = touches.links?.email?.value || '';

  const body = new URLSearchParams();

  body.append('email', identifier);

  const notifications = await Api.serverside(
    '/email/is_unsubscribed',
    cookieStore,
    headerStore,
    resolvedUrl,
    'POST',
    body,
  );

  const allowEmailNotifications = notifications.data['opt-out'] === 0;

  return (
    <NotificationsClient
      identifier={identifier}
      allowEmailNotifications={allowEmailNotifications}
    />
  );
}
