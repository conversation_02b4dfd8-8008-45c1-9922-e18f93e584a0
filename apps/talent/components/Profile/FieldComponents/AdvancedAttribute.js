'use client';
import cn from 'classnames';
import styles from '../ProfileInfo.module.scss';
import React from 'react';
import IconPlus from '../../../public/assets/icons/icon-plus-circle.svg';
import Level from '../Level';
import { Amp } from '@services/amp';
import { EditButton } from '../EditToggle';

const AdvancedAttribute = ({ label, onEdit, values }) => {
  const onEditClick = () => {
    Amp.track(Amp.events.elementClicked, {
      name: `edit ${label}`,
      scope: Amp.element.scope.profile,
      section: Amp.element.section.advancedSkills,
      type: Amp.element.type.button,
    });
    onEdit();
  };

  return (
    <div className={cn(styles['profile-property'], styles['wide'])}>
      <div className={styles['profile-property-title']}>
        {label}
        <EditButton onClick={onEditClick} />
      </div>
      <div
        className={
          styles[
            values?.length
              ? 'profile-property-value'
              : 'profile-property-value-empty'
          ]
        }
      >
        <div className={styles['profile-badge-container']}>
          {values?.map((item, index) => (
            <span
              key={index}
              className={cn(
                styles['profile-badge-outline'],
                styles['slim'],
                styles['clickable'],
              )}
              onClick={onEditClick}
            >
              {item.value}
              {item.items?.length && (
                <div className={styles['attribute-level']}>
                  <Level max={4} value={item.items[0].value} />
                </div>
              )}
            </span>
          ))}

          <IconPlus
            onClick={onEditClick}
            className={styles['add-attribute-value-btn']}
          />
        </div>
      </div>
    </div>
  );
};

export default AdvancedAttribute;
