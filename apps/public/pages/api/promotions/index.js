import Api from '../../../services/api';

const generateUrl = (targets, types, page, limit) => {
  let url = `/promotions?limit=${limit}&page=${page}`;

  if (targets) {
    targets.split(',').forEach((target) => {
      url += `&targets[]=${target}`;
    });
  }
  if (types) {
    types.split(',').forEach((type) => {
      url += `&types[]=${type}`;
    });
  }

  return url;
};

export default async function handler(req, res) {
  const { targets, types, page = 1, limit = 20 } = req.query;

  const data = await Api.serverGateway(
    generateUrl(targets, types, page, limit),
    req.headers,
  );

  res.status(200).json(data);
}
