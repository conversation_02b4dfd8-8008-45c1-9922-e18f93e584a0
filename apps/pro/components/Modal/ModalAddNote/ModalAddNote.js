'use client';
import React, { memo, useState } from 'react';
import Modal from '../Modal';
import { HeaderMobile, Input } from '@components';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import styles from './ModalAddNote.module.scss';
import Api from '@services/api';
import { useNotifications } from '@contexts/NotificationContext';
import { GTM_ACTIONS, GTM_CATEGORIES, GTM_EVENTS } from '@constants/analytics';
import { useAnalytics } from 'use-analytics';
import { ErrorMessage } from '@constants/form';

const ModalAddNote = ({ onClose, talentId, refreshNotes }) => {
  const [loading, setLoading] = useState(false);
  const { setNotification } = useNotifications();
  const { track } = useAnalytics();

  const formik = useFormik({
    initialValues: {
      note: '',
    },
    onSubmit: async (values) => {
      setLoading(true);
      const body = new FormData();

      body.append('content', values.note);

      const response = await Api.clientside(`/profiles/${talentId}/notes`, {
        method: 'POST',
        body,
      });

      if (response.status !== 'ok') {
        setNotification({
          type: 'error',
          timeout: '5000',
          message: response.message || ErrorMessage.Unexpected,
        });
      } else {
        track(GTM_EVENTS.interaction, {
          target: GTM_CATEGORIES.b2b,
          action: GTM_ACTIONS.addNotes,
        });
        await refreshNotes();
        setNotification({
          type: 'success',
          timeout: '5000',
          message: 'Note added',
        });
      }

      onClose();
      setLoading(false);
    },
    validationSchema: Yup.object({
      note: Yup.string()
        .required(ErrorMessage.FieldRequired)
        .max(500, ErrorMessage.MaxCharacters.replace('{X}', '500')),
    }),
  });

  return (
    <Modal
      onClose={onClose}
      backdropClose
      classNameContainer={styles['modal-container']}
      classNameOverlay={styles['modal-overlay']}
      classNameContent={styles['modal-content']}
      showDefaultLayout={false}
      hideMobileCloseButton
      showMobileBorderRadius={false}
      showAnimation={false}
    >
      <HeaderMobile
        onClose={onClose}
        title="Add a new note"
        onClick={formik.submitForm}
        buttonLabel="Done"
        showButton
        buttonDisabled={loading || !formik.isValid}
        hideOnTablet
      />
      <form className={styles.form}>
        <span className={styles.title}>Add a new note</span>
        <Input
          name="note"
          placeholder="Your note"
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          value={formik.values.note}
          isTouched={formik.touched.note}
          error={formik.errors.note}
          charCounter
        />
      </form>
      <div className={styles.actions}>
        <button disabled={loading} onClick={onClose} className={styles.action}>
          Cancel
        </button>
        <button
          disabled={loading}
          onClick={formik.submitForm}
          className={styles.action}
        >
          Add note
        </button>
      </div>
    </Modal>
  );
};

export default memo(ModalAddNote);
