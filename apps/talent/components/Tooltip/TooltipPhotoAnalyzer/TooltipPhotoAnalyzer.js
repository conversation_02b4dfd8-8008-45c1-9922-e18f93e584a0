'use client';
import { memo } from 'react';
import styles from './TooltipPhotoAnalyzer.module.scss';
import ImageAnalyzer from '../../Image/ImageAnalyzer/ImageAnalyzer';
import { getImageStarRating } from '@utils/imageHelpers';

const TooltipPhotoAnalyzer = ({
  characteristics = [],
  isReviewPending,
  isPaidOrDelayed,
}) => {
  return (
    <div className={styles['tooltip-container']}>
      <ImageAnalyzer
        characteristics={characteristics}
        starRating={getImageStarRating(characteristics)}
        isReviewPending={isReviewPending}
        isPaidOrDelayed={isPaidOrDelayed}
      />
    </div>
  );
};

export default memo(TooltipPhotoAnalyzer);
