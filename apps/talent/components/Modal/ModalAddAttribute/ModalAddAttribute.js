'use client';
import { <PERSON><PERSON>, Hint, Modal, Select } from '@components';
import styles from './ModalAddAttribute.module.scss';
import React, { useCallback, useEffect, useState } from 'react';
import { CookieService } from '@services/cookieService';
import IconTrash from '../../../public/assets/icons/icon-trash-round-black.svg';
import Level from '../../Profile/Level';
import { attributeLevel } from '@constants/attributeLevel';
import cn from 'classnames';
import { Amp } from '@services/amp';
import { ErrorMessage } from '@constants/form';

const ModalAddAttribute = ({
  attribute,
  label,
  fieldPlaceholder,
  hideAttributeLevelInfoBlock,
  onClose,
  refreshProfileAttributes,
  saveAttribute,
  initialValues,
}) => {
  const [isAttributeLevelInfoBlockShow, setSsAttributeLevelInfoBlockShow] =
    useState(!hideAttributeLevelInfoBlock);
  const [selectedValues, setSelectedValues] = useState([...initialValues]);
  const [levels, setLevels] = useState(null);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const closeInfoBlock = useCallback(() => {
    CookieService.setAttributeLevelInfoBlockHidden(true);
    setSsAttributeLevelInfoBlockShow(false);
  }, []);

  useEffect(() => {
    setLevels(attribute.items[0].items || null);

    return () => {
      setLevels(null);
    };
  }, []);

  const selectValue = (selectedId) => {
    setSelectedValues((current) => {
      if (current.some((item) => item.id === selectedId)) {
        return current.slice();
      }

      const selectedValue = attribute.items.find(
        (item) => item.id === selectedId,
      );

      if (selectedValue.items !== undefined) {
        selectedValue.items = [];
      }

      return [selectedValue, ...current];
    });
  };

  const selectLevel = (valueId, levelId) => {
    setSelectedValues((current) => {
      return current.map((item) => {
        if (item.id === valueId) {
          const selectedLevel = levels.find((level) => level.id === levelId);

          return { ...item, items: [selectedLevel], hasError: false };
        }

        return item;
      });
    });
  };

  const deleteValue = (id) => {
    setSelectedValues((current) => current.filter((item) => item.id !== id));
  };

  const checkIfLevelsSelected = (selectedValues) => {
    let hasError = false;

    const updatedValues = selectedValues.map((item) => {
      const hasItems = item.items && item.items.length > 0;
      const updatedItem = { ...item, hasError: !hasItems };

      hasError = hasError || !hasItems;

      return updatedItem;
    });

    setSelectedValues(updatedValues);

    return hasError;
  };

  const submitAttribute = async () => {
    Amp.track(Amp.events.elementClicked, {
      name: `save ${label}`,
      scope: Amp.element.scope.profile,
      section: Amp.element.section.advancedSkills,
      type: Amp.element.type.button,
    });
    setError('');

    if (levels && checkIfLevelsSelected(selectedValues)) {
      return;
    }

    const data = {
      ...attribute,
      items: selectedValues.map((value) => {
        return {
          id: value.id,
          name: value.name,
          value: value.value,
          items: value.items?.length
            ? [
                {
                  id: value.items[0].id,
                  name: value.items[0].name,
                  value: value.items[0].value,
                },
              ]
            : null,
        };
      }),
    };

    setLoading(true);
    const saveAttributeResponse = await saveAttribute(data);

    if (saveAttributeResponse.status === 'ok') {
      await refreshProfileAttributes();
      setLoading(false);
      onClose();
    } else {
      setLoading(false);
      setError(saveAttributeResponse.message || ErrorMessage.Unexpected);
    }
  };

  return (
    <Modal
      onClose={onClose}
      showAnimation={false}
      showMobileBorderRadius={false}
      showDefaultLayout={false}
      classNameContainer={styles['add-attribute-modal']}
      classNameContent={styles['add-attribute-modal-content']}
    >
      <div className={styles['container']}>
        <div className={styles['title']}>{label}:</div>
        <div className={styles['attribute-field']}>
          <Select
            name="attribute"
            onChange={selectValue}
            options={attribute.items.map((item) => {
              const isSelected = selectedValues.some(
                (selected) => selected.id === item.id,
              );

              return {
                value: item.id,
                title: item.value,
                selected: isSelected,
              };
            })}
            setFormFieldTouched={() => {}}
            placeholder={fieldPlaceholder}
            style={'slim'}
          />
        </div>

        <div className={styles['selected-values-list-wrap']}>
          {isAttributeLevelInfoBlockShow && levels && (
            <div className={styles['hint-container']}>
              <Hint title="Your Skills Level" canClose onClose={closeInfoBlock}>
                <div className={styles['levels']}>
                  <div className={styles['level']}>
                    <Level max={4} value={'basic'} />
                    Basic/Beginner
                  </div>
                  <div className={styles['level']}>
                    <Level max={4} value={'advanced'} />
                    Fluent/Advanced
                  </div>
                  <div className={styles['level']}>
                    <Level max={4} value={'medium'} />
                    Medium
                  </div>
                  <div className={styles['level']}>
                    <Level max={4} value={'professional'} />
                    Native/Professional
                  </div>
                </div>
              </Hint>
            </div>
          )}
          <div className={styles['selected-values-list']}>
            {selectedValues.map((item) => (
              <div
                key={item.value}
                className={cn(styles['value'], {
                  [styles['no-level']]: !levels,
                })}
              >
                <div className={styles['value-title']}>{item.value}</div>
                <div className={styles['value-select']}>
                  {levels && (
                    <Select
                      name="level"
                      onChange={(selectedLevelId) => {
                        selectLevel(item.id, selectedLevelId);
                      }}
                      value={item.items[0]?.id}
                      isTouched={item.items[0]?.id || item.hasError}
                      options={levels.map((level) => {
                        return {
                          value: level.id,
                          title: attributeLevel[level.value].title,
                        };
                      })}
                      error={item.hasError}
                      setFormFieldTouched={() => {}}
                      placeholder={'Add Level...'}
                      style={'slim'}
                    />
                  )}
                </div>
                <IconTrash
                  className={styles['delete-btn']}
                  onClick={() => {
                    deleteValue(item.id);
                  }}
                />
              </div>
            ))}
          </div>
        </div>

        {error && <div className={styles['error']}>{error}</div>}

        <div className={styles['buttons']}>
          <Button
            type="cancel"
            label="Cancel"
            kind="primary"
            color="plain-red"
            minWidth={'160px'}
            className={styles['button']}
            onClick={onClose}
            disabled={loading}
          />
          <Button
            type="submit"
            label="Save"
            kind="primary"
            color="plain-blue"
            minWidth={'160px'}
            className={styles['button']}
            onClick={submitAttribute}
            disabled={loading}
          />
        </div>
      </div>
    </Modal>
  );
};

export default ModalAddAttribute;
