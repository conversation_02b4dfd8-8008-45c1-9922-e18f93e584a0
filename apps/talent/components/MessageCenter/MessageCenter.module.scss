@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.wrapper {
  background: $white;
  flex: 1;
  display: flex;
  flex-basis: 0;
  flex-flow: row nowrap;
  align-items: stretch;
  justify-content: center;
  min-height: 100%;
  position: relative;

  @include desktop {
    flex-direction: row;
  }
}

.loader {
  margin-top: 100px;
}

.main {
  display: flex;
  flex-grow: 1;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0;

  @include desktop {
    padding-bottom: $space-20;
    flex: 1;
  }
}

.loading-overlay {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  z-index: 3;
  background: $black-100-opacity-80;
  display: flex;
  align-items: center;
  justify-content: center;

  @include desktop {
    background: $white-100-opacity-60;
  }
}
