services:
  app:
    command: ['/bin/sh', '-c', 'npm run dev']
    build:
      context: .
      dockerfile: ./docker/node/Dockerfile
      target: build-local
    develop:
      watch:
        - action: sync
          path: .
          target: /app
          ignore:
            - node_modules/
            - .next
        - action: sync+restart
          path: .env
          target: /app.env
        - action: rebuild
          path: package.json
        - action: rebuild
          path: docker/node/
    container_name: next-talent-allcasting-com
    expose:
      - 3100
    networks:
      - proxy
    #.   To run with BE local environment.
    #    environment:
    #      - INTERNAL_GW=http://gateway/api/gta
    #      - PUBLIC_GW=https://api.entertech.test/api/gta
    #    command: npm run build && npm run start
    labels:
      - 'traefik.enable=true'
      - 'traefik.docker.network=proxy'
      - 'traefik.http.routers.next-ac-talent.rule=Host(`talent.allcasting.test`)'
      - 'traefik.http.routers.next-ac-talent.entrypoints=http,https'
      - 'traefik.http.routers.next-ac-talent.tls=true'
      - 'traefik.http.services.next-ac-talent.loadbalancer.server.port=3100'
    extra_hosts:
      - 'host.docker.internal:host-gateway'

networks:
  proxy:
    external: true
