'use client';
import styles from './Profile.module.scss';
import { memo, useEffect, useRef, useState } from 'react';
import {
  Assets,
  Carousel,
  Hint,
  Loading,
  Modal,
  ModalCompCardDownloadError,
  ProfileProgress,
} from '@components';
import { PageLayout } from '../Layouts';
import { useProfileContext } from '@contexts/ProfileContext';
import cn from 'classnames';
import ProfileInfo from './ProfileInfo';
import ProfileAllPhotos from './ProfileAllPhotos';
import ProfileConnections from './ProfileConnections';
import React from 'react';
import ProfileHeader from './ProfileHeader';
import ProfileCredits from './ProfileCredits';
import {
  maxProfileRating,
  profileTabs,
  profileTabSlugEnum,
} from '@constants/profile';
import ProfileMainPhotos from './ProfileMainPhotos';
import { useAuth } from '@contexts/AuthContext';
import { useNotifications } from '@contexts/NotificationContext';
import { usePremium } from '@contexts/PremiumContext';
import Image from 'next/image';
import Api from '@services/api';
import { useRouter } from 'next/navigation';
import { Amp } from '@services/amp';
import { useSale } from '@contexts/SaleContext';
import { isImageDeclined } from '@utils/imageHelpers';

const Profile = ({
  hideProfilePhotosInfoBlock,
  hideProfileProgressInfoBlock,
  hideAttributeLevelInfoBlock,
  genderOptions,
  ethnicitiesOptions,
  heightOptions,
  weightOptions,
  eyeColorOptions,
  hairColorOptions,
  hipSizeOptions,
  dressSizeOptions,
  bustOptions,
  cupSizeOptions,
  categoryOptions,
  accountLevel,
  attributes,
  isMobileFromUserAgent,
  tab,
}) => {
  const {
    images,
    rating,
    closeUpImage,
    sideViewImage,
    fullHeightImage,
    gender,
    videos,
    additionalImages,
    credits,
    phone,
    email,
    allowPhoneNotifications,
    socialNetworks,
    personalUsername,
    allowIndexingProfile,
    categories,
    skills,
    location,
    ethnicity,
    birthday,
    zipCode,
    height,
    weight,
    eyeColor,
    hairColor,
    hipSize,
    dressSize,
    bust,
    cupSize,
    attributes: profileAttributes,
    statusProgress,
    photoProgress,
    videoProgress,
    infoProgress,
    refreshSkills,
    refreshCategories,
    refreshCredits,
    refreshSocialNetworks,
    refreshPersonalUrl,
    clearPersonalUrl,
    refreshProfileDetails,
    refreshTouches,
    demoReel,
    slate,
    ugcDemoReel,
    titleImage,
    updateProfileRating,
    saveAttribute,
    refreshProfileAttributes,
    audios,
    multiples,
  } = useProfileContext();
  const { showSale } = useSale();

  const { profileId, accountId, refreshUserProfiles } = useAuth();
  const [showGallery, setShowGallery] = useState(false);
  const [clickedImageIndex, setClickedImageIndex] = useState(null);
  const [activeTab, setActiveTab] = useState(tab);
  const [showStatusBar, setShowStatusBar] = useState(false);
  const [compCardUrl, setCompCardUrl] = useState('');
  const downloadLinkRef = useRef(null);
  const [showCompCardErrorModal, setShowCompCardErrorModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { setNotification } = useNotifications();
  const { openPremiumModal, setPremiumTrackingName } = usePremium();
  const ratingProgressInPercentage = Math.round(
    (rating / maxProfileRating) * 100,
  );
  const router = useRouter();

  useEffect(() => {
    if (downloadLinkRef.current && compCardUrl) {
      downloadLinkRef.current.click();
    }
  }, [compCardUrl]);

  useEffect(() => {
    Amp.track(Amp.events.viewProfile, {
      profile_id: profileId,
    });
  }, [profileId]);

  const toggleProfileProgressModal = () => {
    if (!showStatusBar) {
      Amp.track(Amp.events.modalViewed, {
        name: 'profile progress',
      });
    }
    setShowStatusBar(!showStatusBar);
  };

  const fetchTalentCompCard = async () => {
    const raw = JSON.stringify({
      profile_ids: [parseInt(profileId)],
    });

    const response = await Api.clientside(
      `/accounts/${accountId}/compcards`,
      {
        method: 'POST',
        body: raw,
      },
      {
        'Content-Type': 'application/json',
      },
    );

    if (response.headers?.get('content-type') === 'application/pdf') {
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);

      setCompCardUrl(url);

      return;
    }

    if (response.status === 'error') {
      setNotification({
        type: 'error',
        message: response.message,
        timeout: '5000',
      });
    }
  };

  const toggleShowGallery = () => {
    setShowGallery(!showGallery);
  };

  const onImageClick = (imageId) => {
    if (imageId) {
      const index = images.findIndex(({ id }) => id === imageId);

      setClickedImageIndex(index);
    }

    toggleShowGallery();
  };

  const downloadCompCard = async () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'download comp card',
      scope: Amp.element.scope.profile,
      section: Amp.element.section.compCard,
      type: Amp.element.type.button,
    });

    if (accountLevel?.isPaidOrDelayed) {
      if (closeUpImage && sideViewImage && fullHeightImage) {
        setIsLoading(true);
        await fetchTalentCompCard();
        setIsLoading(false);
      } else {
        setShowCompCardErrorModal(true);
      }
    } else {
      setPremiumTrackingName('Unlock download comp card popup');
      openPremiumModal();
    }
  };

  const closeCompCardErrorModal = () => {
    setShowCompCardErrorModal(false);
  };

  const changeTab = (value) => {
    window.history.replaceState(
      null,
      '',
      `/profile/${personalUsername || profileId}/${value}`,
    );
    setActiveTab(value);
  };

  const renderTalentProfile = () => {
    return (
      <div className={styles['profile-container']}>
        {(!closeUpImage || isImageDeclined(closeUpImage)) && (
          <div className={styles['hidden-profile-block']}>
            <Hint
              title={'Your profile is hidden:'}
              canClose={false}
              classNameAdditional={'red'}
              iconFileName={'icon-hidden.svg'}
              inline
            >
              &nbsp; Upload a headshot photo
            </Hint>
          </div>
        )}
        <div className={styles.profile}>
          <ProfileMainPhotos
            isPaidOrDelayed={!!accountLevel?.isPaidOrDelayed}
            onImageClick={onImageClick}
            closeUpImage={closeUpImage}
            sideViewImage={sideViewImage}
            fullHeightImage={fullHeightImage}
            titleImage={titleImage}
            genderTitle={gender?.title}
            refreshUserProfiles={refreshUserProfiles}
            refreshProfileDetails={refreshProfileDetails}
            hideProfilePhotosInfoBlock={hideProfilePhotosInfoBlock}
            openRatingProgress={toggleProfileProgressModal}
          />

          <div className={styles['profile-overview']}>
            <ProfileHeader
              refreshUserProfiles={refreshUserProfiles}
              refreshProfileDetails={refreshProfileDetails}
              openRatingProgress={toggleProfileProgressModal}
            />
            <div
              className={cn(styles['profile-tabs-panel'], {
                [styles['profile-tabs-panel-with-sale']]: showSale,
              })}
            >
              <div className={styles['profile-tabs']}>
                {profileTabs.map(({ slug, title }, index) => (
                  <span
                    className={cn(styles['profile-tab'], {
                      [styles['active']]: activeTab === slug,
                    })}
                    key={index}
                    onClick={() => changeTab(slug)}
                  >
                    {title}
                  </span>
                ))}
              </div>

              <div className={styles['progress-panel-container']}>
                <div className={styles['progress-panel']}>
                  <div className={styles['progress-percentage']}>
                    <svg
                      viewBox="-2 -2 40 40"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      preserveAspectRatio="none"
                      className={cn(
                        styles['circular-chart'],
                        styles[`progress-${statusProgress}`],
                      )}
                    >
                      <path
                        className={styles['circular-chart-bg']}
                        d="M18 2.0845
          a 15.9155 15.9155 0 0 1 0 31.831
          a 15.9155 15.9155 0 0 1 0 -31.831"
                      />
                      <path
                        className={styles['circular-chart-percentage']}
                        style={{
                          strokeDasharray: `${ratingProgressInPercentage}, 100`,
                        }}
                        d="M18 2.0845
          a 15.9155 15.9155 0 0 1 0 31.831
          a 15.9155 15.9155 0 0 1 0 -31.831"
                      />
                    </svg>
                  </div>

                  <div className={styles['progress-section']}>
                    <div
                      onClick={toggleProfileProgressModal}
                      className={cn(
                        styles['progress-btn'],
                        styles[`progress-btn-${statusProgress}`],
                        { [styles['btn-is-active']]: showStatusBar },
                      )}
                    >
                      Your profile progress
                      <div className={styles['progress-btn-icon']} />
                    </div>
                    {showStatusBar && (
                      <>
                        <Modal
                          backdropClose
                          onClose={toggleProfileProgressModal}
                          showCloseButton={false}
                          showAnimation={false}
                          showMobileBorderRadius={false}
                          showDefaultLayout={false}
                          disableBackgroundScroll={false}
                        />
                        <ProfileProgress
                          status={statusProgress}
                          close={toggleProfileProgressModal}
                          genderTitle={gender?.title}
                          imageSrc={titleImage?.url || ''}
                          rating={rating}
                          setActiveTab={changeTab}
                          setShowStatusBar={setShowStatusBar}
                          photoProgress={photoProgress}
                          videoProgress={videoProgress}
                          infoProgress={infoProgress}
                        />
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>
            {activeTab === profileTabSlugEnum.info && (
              <ProfileInfo
                hideProfileProgressInfoBlock={hideProfileProgressInfoBlock}
                hideAttributeLevelInfoBlock={hideAttributeLevelInfoBlock}
                genderTitle={gender?.title}
                profileId={profileId}
                accountId={accountId}
                categories={categories}
                skills={skills}
                gender={gender}
                location={location}
                ethnicity={ethnicity}
                birthday={birthday}
                zipCode={zipCode}
                height={height}
                weight={weight}
                eyeColor={eyeColor}
                hairColor={hairColor}
                hipSize={hipSize}
                dressSize={dressSize}
                bust={bust}
                cupSize={cupSize}
                genderOptions={genderOptions}
                ethnicitiesOptions={ethnicitiesOptions}
                heightOptions={heightOptions}
                weightOptions={weightOptions}
                eyeColorOptions={eyeColorOptions}
                hairColorOptions={hairColorOptions}
                hipSizeOptions={hipSizeOptions}
                dressSizeOptions={dressSizeOptions}
                bustOptions={bustOptions}
                cupSizeOptions={cupSizeOptions}
                refreshUserProfiles={refreshUserProfiles}
                categoryOptions={categoryOptions}
                refreshSkills={refreshSkills}
                refreshCategories={refreshCategories}
                refreshProfileDetails={refreshProfileDetails}
                updateProfileRating={updateProfileRating}
                attributes={attributes}
                saveAttribute={saveAttribute}
                refreshProfileAttributes={refreshProfileAttributes}
                profileAttributes={profileAttributes}
                isPaidOrDelayed={!!accountLevel?.isPaidOrDelayed}
                multiples={multiples}
              />
            )}
            {activeTab === profileTabSlugEnum.photos && (
              <ProfileAllPhotos
                additionalImages={additionalImages}
                onImageClick={onImageClick}
                closeUpImage={closeUpImage}
                sideViewImage={sideViewImage}
                fullHeightImage={fullHeightImage}
                titleImage={titleImage}
                genderTitle={gender?.title}
                isPaidOrDelayed={!!accountLevel?.isPaidOrDelayed}
              />
            )}
            {activeTab === profileTabSlugEnum.assets && (
              <Assets
                profileId={profileId}
                videos={videos}
                isPaidOrDelayed={!!accountLevel?.isPaidOrDelayed}
                demoReel={demoReel}
                slate={slate}
                ugcDemoReel={ugcDemoReel}
                audios={audios}
                isMobileFromUserAgent={isMobileFromUserAgent}
              />
            )}
            {activeTab === profileTabSlugEnum.credits && (
              <ProfileCredits
                credits={credits}
                refreshCredits={refreshCredits}
                updateProfileRating={updateProfileRating}
              />
            )}
          </div>

          <div className={styles['profile-connections-container']}>
            <div
              className={cn(styles['profile-connections'], {
                [styles['display-none-on-mobile']]:
                  activeTab !== profileTabSlugEnum.info,
              })}
            >
              <ProfileConnections
                isPaidOrDelayed={!!accountLevel?.isPaidOrDelayed}
                accountId={accountId}
                phone={phone}
                profileId={profileId}
                allowPhoneNotifications={allowPhoneNotifications}
                email={email}
                socialNetworks={socialNetworks}
                personalUsername={personalUsername}
                allowIndexingProfile={allowIndexingProfile}
                refreshSocialNetworks={refreshSocialNetworks}
                refreshPersonalUrl={refreshPersonalUrl}
                clearPersonalUrl={clearPersonalUrl}
                refreshProfileDetails={refreshProfileDetails}
                refreshUserProfiles={refreshUserProfiles}
                refreshTouches={refreshTouches}
              />
              <div className={styles['profile-id-container']}>
                <b>Profile ID:</b> {profileId}
              </div>
            </div>
            <div className={styles['comp-card-download-container']}>
              {isLoading ? (
                <Loading minHeight="40px" padding="0" />
              ) : (
                <>
                  <a
                    onClick={downloadCompCard}
                    className={styles['profile-pdf-btn']}
                  >
                    <Image
                      src={'/assets/icons/icon-pdf.svg'}
                      width={20}
                      height={20}
                      alt="download pdf"
                      priority
                    />
                    Download PDF comp card
                  </a>
                  <a
                    href={compCardUrl}
                    download={'compcard.pdf'}
                    ref={downloadLinkRef}
                    style={{ display: 'none' }}
                  ></a>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <PageLayout>
      <section className={styles['profile-section']}>
        {renderTalentProfile()}
        {showGallery && (
          <Modal
            backdropClose
            onClose={toggleShowGallery}
            showCloseButton={false}
            showDefaultLayout={false}
            classNameOverlay={styles['gallery-modal-overlay']} // fixes specifficity issue
            classNameContainer={styles['gallery-modal']}
            classNameContent={styles['gallery-modal-content']}
            containerClose
          >
            <Carousel
              enableArrowNavigation
              startIndex={clickedImageIndex}
              className="carousel-profile-gallery"
              watchDrag={false}
              loop
            >
              {images.map(({ proxy_url }, index) => (
                <img
                  className={styles['carousel-image']}
                  key={index}
                  src={proxy_url}
                  alt={index}
                />
              ))}
            </Carousel>
          </Modal>
        )}
        {showCompCardErrorModal && (
          <ModalCompCardDownloadError onClose={closeCompCardErrorModal} />
        )}
      </section>
    </PageLayout>
  );
};

export default memo(Profile);
