@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.text-light {
  font-weight: 400;
}

.premium-actions {
  display: flex;
  flex-direction: column;
}

.premium-action {
  padding: 16px $space-30;
  font-size: 14px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;

  &:not(.disabled):hover {
    cursor: pointer;
    background-color: $grey-10;
    text-decoration: none;
  }

  &.disabled {
    cursor: not-allowed;
    opacity: 0.3;
  }

  @include desktop {
    padding: $space-10 16px;
  }
}
