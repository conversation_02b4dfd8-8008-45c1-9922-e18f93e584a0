import ContactsClient from './_client';
import { cookies, headers } from 'next/headers';
import { CookieServiceServer } from '@services/cookieServiceServer';
import Api from '@services/api';
import { getResolvedUrl } from '@utils/apiHelper';

export const metadata = {
  title: 'Contact Information',
};

export default async function ContactsPage(req) {
  const searchParams = await req.searchParams;
  const resolvedUrl = getResolvedUrl('/settings/contacts', searchParams);
  const cookieStore = await cookies();
  const headerStore = await headers();
  const accountId = CookieServiceServer.getAccountCookie(cookieStore);

  const data = await Api.serverside(
    `/accounts/${accountId}/touches`,
    cookieStore,
    headerStore,
    resolvedUrl,
  );

  const email = data.links?.email?.value || '';
  const phone = data.links?.phone?.value || '';
  const allowNotifications = data.links?.phone?.opt_outed === false;

  return (
    <ContactsClient
      email={email}
      phone={phone}
      allowNotifications={allowNotifications}
    />
  );
}
