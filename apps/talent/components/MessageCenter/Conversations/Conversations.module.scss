@use '@styles/mixins' as *;
@use '@styles/variables' as *;

.conversation-button {
  display: flex;
  color: $white;
  align-items: center;
  justify-content: space-between;
  background: transparent;
  border: 0;
  width: 100%;
  padding: $space-5 $space-15 $space-5 $space-20;
  cursor: pointer;
  font-size: 16px;
  font-weight: 300;

  &.client-support {
    background: $yellow-60;

    @include desktop {
      background: $yellow-20;
    }
  }

  &.active {
    @include desktop {
      background: $white;
      color: $black;
    }
  }

  @include desktop {
    color: $black;
    padding: $space-5 $space-5 $space-5 $space-20;

    &:hover {
      background: $white;
      color: $black;
    }
  }

  .badge {
    flex-shrink: 0;
    font-size: 14px;
    font-weight: 400;
    margin-left: $space-10;

    @include desktop {
      font-size: 12px;
      font-weight: 700;
    }
  }
}

.contact {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: row;
  text-align: left;
}

.frame {
  background: $white;
  border-radius: 50%;
  border: 1px solid $grey-60;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  min-width: 36px;
  margin-right: $space-15;
}

.avatar {
  border-radius: 50%;
  width: 30px;
  height: 30px;
}

.container {
  display: flex;
  align-items: center;
  gap: $space-5;
  justify-content: space-between;
  width: 100%;
  cursor: pointer;
}
