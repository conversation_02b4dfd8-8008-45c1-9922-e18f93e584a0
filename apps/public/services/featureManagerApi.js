import https from 'https';
import http from 'http';

const getConfiguration = async () => {
  const url = `${process.env.featureManagerApiUrl}/configuration`;

  try {
    const response = await fetchWithTimeout(url);

    return await response.json();
  } catch (error) {
    console.error(error);

    return {};
  }
};

const getConfigurationClientSide = async () => {
  const url = `${process.env.externalApiUrl}/feature`;
  const response = await fetch(url);

  if (response?.status !== 200) {
    return null;
  }

  return await response.json();
};

async function fetchWithTimeout(resource, options = {}) {
  const { timeout = 3000 } = options;

  let agent = new http.Agent();

  if (process.env.featureManagerApiUrl.includes(`https`)) {
    agent = new https.Agent({ rejectUnauthorized: false });
  }

  const controller = new AbortController();
  const id = setTimeout(() => controller.abort(), timeout);
  const response = await fetch(resource, {
    ...options,
    signal: controller.signal,
    agent: agent,
  });

  clearTimeout(id);

  return response;
}

export default { getConfiguration, getConfigurationClientSide };
