@use '@styles/mixins' as *;
@use '@styles/variables' as *;

.assets-section {
  display: flex;
  flex-direction: column;
  gap: $space-40;
}

.assets-container {
  display: flex;
  flex-direction: column;
  gap: $space-10;
}

.title {
  color: $grey-80;
  font-weight: 700;
  text-transform: uppercase;
  display: inline-block;
}

.title-container {
  display: flex;
  align-items: center;
  gap: $space-5;
}

.icon-hint-container {
  display: flex;
  align-items: center;
}
