import Api from '../../services/api';
import { ProfileDesktop, ProfileMobile } from '../../components';
import { formatProProfile } from '../../utils/formatProfile';
import Seo from '../../components/Seo/Seo';
import { useViewport } from '../../utils/useViewport';
import { ProfileProvider } from '../../contexts/ProfileContext';
import { useEffect, useState } from 'react';
import { isMobile } from '../../utils/isMobile';
import { getHeaders } from '../../utils/headerHelper';
import { MainLayout } from '../../components/Layouts';
import { sharedServerSideProps } from '../../utils/sharedServerSideProps';
import {
  getProProfileMobileMenuItems,
  getProProfileSideMenuItems,
} from '../../utils/getProfileMenuItems';
import { Amp } from '../../services/amp';

export const getServerSideProps = async ({ req, query, resolvedUrl, res }) => {
  const { slug } = query;

  let id = slug;

  if (isNaN(Number(slug))) {
    const profileByUsername = await Api.serverAPIRoute(
      `/profiles/profile-by-username/${slug}`,
      null,
      getHeaders(req, resolvedUrl),
    );

    id = profileByUsername.links?.profile?.id;
  }

  if (!id) {
    return {
      notFound: true,
    };
  }

  const profile = await Api.serverAPIRoute(
    `/profiles/${id}?expand=personal_url`,
    null,
    getHeaders(req, resolvedUrl),
  );

  if (false === profile.success) {
    return {
      notFound: true,
    };
  }

  const profileCategories = await Api.serverAPIRoute(
    `/profiles/categories/${id}`,
    null,
    getHeaders(req, resolvedUrl),
  );
  const profileImages = await Api.serverAPIRoute(
    `/profiles/images/${id}`,
    null,
    getHeaders(req, resolvedUrl),
  );
  const profileCastingCalls = await Api.serverAPIRoute(
    `/profiles/casting-calls/${profile.links.account.client_id}?limit=3&page=1`,
    null,
    getHeaders(req, resolvedUrl),
  );
  const formattedProfile = formatProProfile(
    profile,
    profileImages.items,
    profileCategories.items,
    profileCastingCalls?.data?.calls,
    profileCastingCalls?.data?.cc_active_count,
  );

  const isMobileFromUserAgent = isMobile(req.headers['user-agent']);

  const sharedProps = await sharedServerSideProps(req, res);

  return {
    props: {
      ...sharedProps,
      profile: formattedProfile,
      sideMenuItems: getProProfileSideMenuItems(formattedProfile),
      menuItems: getProProfileMobileMenuItems(formattedProfile),
      defaultIsMobile: isMobileFromUserAgent,
    },
  };
};

export default function Director({
  profile,
  sideMenuItems,
  defaultIsMobile,
  menuItems,
}) {
  const [isMobile, setIsMobile] = useState(defaultIsMobile);

  const { width } = useViewport();

  useEffect(() => {
    if (width) {
      setIsMobile(width < 1024);
    }
  }, [width]);

  useEffect(() => {
    Amp.track(Amp.events.viewDirector, {
      profile_id: profile.id,
    });
  }, [profile]);

  return (
    <MainLayout
      isDefaultHeaderVisible
      isMobileMenuVisible
      isUserMenuVisible
      isSaleHeaderVisible
      isMobileSubscribeHeaderVisible
    >
      <Seo
        seoPage={{
          title: `${profile.name} • Casting Director • allcasting`,
          description: `《 allcasting 》 ${profile.name} profile on ▶️ allcasting! Best Casting Directors. ★ Join us and get more!`,
          ogImageUrl: `${process.env.baseUrl}/assets/meta/main.webp`,
        }}
      />
      <ProfileProvider value={profile}>
        {!isMobile && <ProfileDesktop sideMenuItems={sideMenuItems} />}
        {isMobile && <ProfileMobile menuItems={menuItems} />}
      </ProfileProvider>
    </MainLayout>
  );
}
