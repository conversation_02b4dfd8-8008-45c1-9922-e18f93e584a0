import Api from '@services/api';
import { Amp } from '@services/amp';
import { CookieService } from '@services/cookieService';
import { targetRestrictedPaths } from '@constants/targetRestrictedPaths';

const TRACKING_PERFORMANCE_GROUP = 'C0002';
const TRACKING_TARGETING_GROUP = 'C0004';
const VISITED_PATHS = 'visitedPaths';

export const OneTrustService = {
  maxPathLimitToConsent: 0,
  authConsentUpdated: false,
  consentAutoUpdate: false,

  listener() {
    const checkOneTrustInterval = setInterval(() => {
      if (window.OneTrust) {
        OneTrustService.checkTracking();
        if (!OneTrustService.authConsentUpdated) {
          OneTrustService.checkConsent();
        }
        window.OneTrust.OnConsentChanged(() => {
          if (!OneTrustService.consentAutoUpdate) {
            OneTrustService.checkTracking(true);
          }
          OneTrustService.consentAutoUpdate = false;
        });
        clearInterval(checkOneTrustInterval);
      }
    }, 500);

    return () => clearInterval(checkOneTrustInterval);
  },

  checkIsPerformanceCookiesEnabled() {
    return OneTrustService.checkIsCookiesEnabled(TRACKING_PERFORMANCE_GROUP);
  },

  checkIsTargetingCookiesEnabled() {
    return OneTrustService.checkIsCookiesEnabled(TRACKING_TARGETING_GROUP);
  },

  checkIsCookiesEnabled(performanceGroupId) {
    if (`none` === process.env.amplitudeConsentProvider) {
      return true;
    }

    const consentCookie = CookieService.getOneTrustConsentCookie();

    if (!consentCookie) {
      switch (performanceGroupId) {
        case TRACKING_PERFORMANCE_GROUP:
          return true;
        case TRACKING_TARGETING_GROUP:
          return false;
        default:
          // do not collapse statement to have clarity
          return false;
      }
    }

    try {
      const consentGroups = consentCookie
        .split('&')
        .find((row) => row.startsWith('groups'))
        .split('=')[1];
      const isAllowed = consentGroups
        .split(',')
        .find((row) => row.startsWith(performanceGroupId))
        .split(':')[1];

      return isAllowed === '1';
    } catch (e) {
      // do nothing, later - log
    }

    return false;
  },

  checkTracking(hasConsentChanged = false) {
    OneTrustService.checkPerformanceTracking();
    OneTrustService.checkTargetingTracking(hasConsentChanged);

    if (hasConsentChanged && OneTrustService.authConsentUpdated) {
      OneTrustService.updateConsent();
    }
  },

  checkPerformanceTracking() {
    if (OneTrustService.checkIsPerformanceCookiesEnabled()) {
      Amp.init();
    }
  },

  checkTargetingTracking(hasConsentChanged = false) {
    // if consent has changed maxPathLimitToConsent
    // is no longer relevant
    if (hasConsentChanged) {
      CookieService.setTrackTargeting(
        OneTrustService.checkIsTargetingCookiesEnabled().toString(),
      );

      return;
    }

    const storedPaths = JSON.parse(sessionStorage.getItem(VISITED_PATHS)) || [];
    const cookie = CookieService.getTrackTargeting();

    if (
      storedPaths.length >= OneTrustService.maxPathLimitToConsent &&
      cookie === undefined
    ) {
      CookieService.setTrackTargeting(
        OneTrustService.checkIsTargetingCookiesEnabled().toString(),
      );
    }
  },

  saveVisitedPath(path) {
    const storedPaths = JSON.parse(sessionStorage.getItem(VISITED_PATHS)) || [];

    // no need in further paths collection
    if (storedPaths.length > OneTrustService.maxPathLimitToConsent) {
      return;
    }

    if (!targetRestrictedPaths.includes(path) && !storedPaths.includes(path)) {
      storedPaths.push(path);
      sessionStorage.setItem(VISITED_PATHS, JSON.stringify(storedPaths));
    }
  },

  async deleteConsent(accountId, consent) {
    await Api.clientside(`/accounts/${accountId}/optouts/${consent}`, {
      method: 'DELETE',
    });
  },

  async postConsent(accountId, consent) {
    const body = new FormData();

    consent.forEach((i) => {
      body.append('optouts[]', i);
    });

    await Api.clientside(`/accounts/${accountId}/optouts`, {
      method: 'POST',
      body,
    });
  },

  async updateConsent() {
    const accountId = CookieService.getAccountCookie() ?? 0;

    if (!accountId) {
      return;
    }
    const response = await Api.clientside(`/accounts/${accountId}/optouts`);
    const consents = response?.items?.map((i) => i.type) || [];
    const consentToPost = [];
    const consentToDeletePromises = [];

    const consentMappings = [
      {
        type: 'targeting_cookie',
        serviceCheck: () => CookieService.getTrackTargeting() === false,
        group: TRACKING_TARGETING_GROUP,
      },
      {
        type: 'performance_cookie',
        serviceCheck: () =>
          OneTrustService.checkIsPerformanceCookiesEnabled() === false,
        group: TRACKING_PERFORMANCE_GROUP,
      },
    ];

    consentMappings.forEach((consent) => {
      if (!consent.serviceCheck() && consents.includes(consent.type)) {
        consentToDeletePromises.push(
          OneTrustService.deleteConsent(accountId, consent.type),
        );
      } else if (consent.serviceCheck() && !consents.includes(consent.type)) {
        consentToPost.push(consent.type);
      }
    });

    if (consentToDeletePromises.length) {
      await Promise.all(consentToDeletePromises);
    }

    if (consentToPost.length) {
      await OneTrustService.postConsent(accountId, consentToPost);
    }
  },

  async checkConsent() {
    const accountId = CookieService.getAccountCookie() ?? 0;

    if (!accountId) {
      return;
    }

    OneTrustService.authConsentUpdated = true;

    const response = await Api.clientside(`/accounts/${accountId}/optouts`);
    const consents = response?.items?.map((i) => i.type) || [];

    const consentMappings = [
      {
        type: 'targeting_cookie',
        group: TRACKING_TARGETING_GROUP,
      },
      {
        type: 'performance_cookie',
        group: TRACKING_PERFORMANCE_GROUP,
      },
    ];

    for (const { type, group } of consentMappings) {
      if (consents.includes(type)) {
        if (type === 'targeting_cookie') {
          CookieService.setTrackTargeting(false);
        }

        if (window.OneTrust && window.OneTrust.UpdateConsent) {
          OneTrustService.consentAutoUpdate = true;
          window.OneTrust.UpdateConsent('Category', `${group}:0`);
        }
      }
    }
  },
};
