'use client';
import React, { memo, useState } from 'react';
import styles from '../Profile.module.scss';
import creditStyles from '../ProfileCredits.module.scss';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { Input, Select } from '@components';
import cn from 'classnames';
import Api from '@services/api';
import { useAuth } from '@contexts/AuthContext';
import { useNotifications } from '@contexts/NotificationContext';
import {
  getMonthOptionsWithFullTitles,
  getYearOptionsWithRange,
} from '@utils/getTimeOptions';
import { Amp } from '@services/amp';
import { ErrorMessage, JOB_TITLE_REGEX } from '@constants/form';

const AddCredit = ({ onClose, onCloseAndRefresh, updateProfileRating }) => {
  const { setNotification } = useNotifications();
  const { profileId } = useAuth();
  const currentMonth = new Date().getMonth();
  const currentYear = new Date().getFullYear();
  const yearList = getYearOptionsWithRange();

  const setMonthValue = (e) => {
    formik.setFieldValue('month', e);
  };

  const setMonthTouched = () => {
    formik.setFieldTouched('month');
  };

  const setYearValue = (e) => {
    const isCurrentYear = e === currentYear;

    formik.setFieldValue('year', e);
    setMonthList(getMonthOptionsWithFullTitles(true, isCurrentYear));
  };

  const setYearTouched = () => {
    formik.setFieldTouched('year');
  };

  const formik = useFormik({
    initialValues: {
      title: '',
      month: currentMonth + 1,
      year: currentYear,
      company: '',
      description: '',
    },
    onSubmit: async (values) => {
      Amp.track(Amp.events.elementClicked, {
        name: 'save credit',
        scope: Amp.element.scope.profile,
        section: Amp.element.section.credits,
        type: Amp.element.type.button,
      });

      const body = new FormData();

      body.append('title', values.title);
      body.append('company', values.company);
      body.append('month', String(values.month));
      body.append('year', String(values.year));
      body.append('description', values.description);

      const addCreditResponse = await Api.clientside(
        `/profiles/${profileId}/credits?expand=profile`,
        {
          body,
          method: 'POST',
        },
      );

      if (addCreditResponse.status !== 'ok') {
        setNotification({
          type: 'error',
          message: addCreditResponse.message || ErrorMessage.Unexpected,
          timeout: '5000',
        });
      } else {
        updateProfileRating(addCreditResponse.links.profile.rating);
        await onCloseAndRefresh();
      }
    },
    validationSchema: Yup.object({
      title: Yup.string()
        .required(ErrorMessage.JobTitleRequired)
        .min(3, ErrorMessage.MinCharactersLatin.replace('X', '3'))
        .max(130, ErrorMessage.MaxCharactersLatin.replace('X', '130'))
        .matches(
          JOB_TITLE_REGEX,
          ErrorMessage.MaxCharactersLatin.replace('X', '130'),
        ),
      company: Yup.string()
        .required(ErrorMessage.DescriptionsRequired)
        .max(130, ErrorMessage.MaxCharacters.replace('X', '130')),
      description: Yup.string()
        .required(ErrorMessage.DescriptionsRequired)
        .max(500, ErrorMessage.MaxCharacters.replace('X', '500'))
        .min(10, ErrorMessage.MinCharacters.replace('X', '10')),
    }),
  });

  const preselectedCurrenYear = currentYear === formik.values.year;
  const [monthList, setMonthList] = useState(
    getMonthOptionsWithFullTitles(true, preselectedCurrenYear),
  );

  return (
    <div
      className={cn(creditStyles['credit-container'], creditStyles['add-new'])}
    >
      <form onSubmit={formik.handleSubmit}>
        <div
          className={cn(
            styles['profile-section-title'],
            creditStyles['add-credit-header'],
          )}
        >
          Add credits:
          <button
            className={styles['save-button']}
            disabled={!(formik.isValid && formik.dirty) || formik.isSubmitting}
            type="submit"
          >
            Save
          </button>
        </div>

        <div className={creditStyles['credit-container-box']}>
          <div
            onClick={onClose}
            className={creditStyles['credit-container-close']}
          />
          <div className={creditStyles['credit-container-row']}>
            <Input
              name="title"
              placeholder="Job title"
              value={formik.values.title}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              isTouched={formik.touched.title}
              error={formik.errors.title}
              hint="From 3 to 130 characters"
            />
            <div className={creditStyles['credit-container-row-selects']}>
              <Select
                name="month"
                value={formik.values.month}
                onChange={setMonthValue}
                isTouched={formik.touched.month}
                setFormFieldTouched={setMonthTouched}
                options={monthList}
              />

              <Select
                name="year"
                value={formik.values.year}
                onChange={setYearValue}
                isTouched={formik.touched.month}
                setFormFieldTouched={setYearTouched}
                options={yearList}
              />
            </div>
          </div>

          <Input
            name="company"
            placeholder="Company"
            value={formik.values.company}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            isTouched={formik.touched.company}
            error={formik.errors.company}
            hint="Max 130 characters"
          />
          <Input
            name="description"
            placeholder="About"
            value={formik.values.description}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            isTouched={formik.touched.description}
            error={formik.errors.description}
            hint="Max 500 characters"
          />
        </div>
      </form>
    </div>
  );
};

export default memo(AddCredit);
