'use client';
import { memo } from 'react';
import styles from './HeaderMobile.module.scss';
import Image from 'next/image';
import cn from 'classnames';

const HeaderMobile = ({
  onClose,
  onClick,
  title,
  showButton,
  buttonLabel,
  buttonDisabled = false,
  hideOnTablet = false,
  showIcon = true,
  showBorder = true,
  showShadow = false,
  showActionIcon = false,
}) => {
  return (
    <div
      className={cn(styles['mobile-header'], {
        [styles['hide-on-tablet']]: hideOnTablet,
        [styles['show-only-title']]: !showIcon && !showButton,
        [styles['show-border']]: showBorder,
        [styles['show-shadow']]: showShadow,
        [styles['show-action-icon']]: showActionIcon,
      })}
    >
      {showIcon && (
        <div onClick={onClose} className={styles['icon-container']}>
          <Image
            className={styles.icon}
            src={'/assets/icons/icon-angle-right-blue.svg'}
            width={16}
            height={16}
            alt="icon"
          />
        </div>
      )}
      {!showActionIcon && <div className={styles.title}>{title}</div>}
      {showButton && (
        <div className={styles['button-container']}>
          <button
            onClick={onClick}
            className={styles.button}
            disabled={buttonDisabled}
          >
            {buttonLabel}
          </button>
        </div>
      )}
      {showActionIcon && (
        <div onClick={onClick} className={styles['action-icon-container']}>
          <div className={styles.title}>{title}</div>
          <Image
            src={'/assets/icons/icon-angle-right-blue.svg'}
            width={16}
            height={16}
            alt="icon"
          />
        </div>
      )}
    </div>
  );
};

export default memo(HeaderMobile);
