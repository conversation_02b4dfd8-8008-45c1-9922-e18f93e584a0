@use '@styles/mixins' as *;
@use '@styles/variables' as *;

.audio-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px dashed $grey-60;
  border-radius: 10px;
  padding: 44px $space-20;
  gap: $space-15;
  cursor: pointer;
}

.audio-placeholder-text {
  position: relative;
  font-size: 14px;
  color: $blue-100;
  font-weight: 700;
  text-decoration: none;

  &::after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 2px;
    background-color: $blue-100;
    opacity: 0;
  }

  &:hover {
    text-decoration: none;

    &::after {
      opacity: 1;
    }
  }
}

.audio-placeholder-content {
  display: flex;
  flex-direction: column;
  font-size: 14px;
  color: $grey-80;
  align-items: flex-start;
  justify-content: center;
}
