'use client';
import React, { useState, createRef, useEffect } from 'react';
import styles from './ChatBot.module.scss';
import dayjs from 'dayjs';
import ChatMessages from './ChatMessages/ChatMessages';
import { Footer } from '@components';

const FOLLOWUP_TIMEOUT = 5000;
const config = {
  qnaHost: 'https://allcastingbot.cognitiveservices.azure.com',
  knowledgebase:
    'language/:query-knowledgebases?projectName=allcastingBot&api-version=2021-10-01&deploymentName=production',
  authKey: '6cb2aa80a7b34189a84793349b1633de',
  confidenceThreshold: '0.4',
  storeHistoryInSessionStorage: false,
};

const ChatBot = () => {
  const [conversationData, setConversationData] = useState([
    {
      message: 'Hello! I am a chatbot and I am here to help you!',
      type: 'answer',
      prompts: [],
    },
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [lastQuestion, setLastQuestion] = useState('');

  const messagesEndRef = createRef();

  useEffect(() => {
    if (messagesEndRef.current) messagesEndRef.current.scrollIntoView(true);
  }, [conversationData, messagesEndRef]);

  const updateChatHistory = (newMessage) => {
    const { metadata: meta = {} } = newMessage;
    const anotherQuestion = meta.anotherQuestion === 'true';

    setConversationData((conversationData) => [
      ...conversationData,
      newMessage,
    ]);

    if (anotherQuestion) followupQuestion();
  };

  const handleInputChange = (e) => {
    setInputValue(e.target.value);
  };

  const chatBotMainMenu = () => {
    qnaRequest({ question: 'start conversation' });
  };

  const followupQuestion = () => {
    setTimeout(() => {
      updateChatHistory({
        message: 'End chat',
        type: 'answer',
        timestamp: dayjs().format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
        prompts: [
          { displayText: 'Continue chat' },
          { displayText: 'End chat' },
        ],
      });
    }, FOLLOWUP_TIMEOUT);
  };

  const qnaRequest = async (messageBody) => {
    try {
      const res = await fetch(`${config.qnaHost}/${config.knowledgebase}`, {
        method: 'POST',
        headers: {
          Host: config.qnaHost,
          Accept: 'application/json',
          'Content-Type': 'application/json',
          'Ocp-Apim-Subscription-Key': config.authKey,
        },
        body: JSON.stringify(messageBody),
      });
      const result = await res.json();

      setIsTyping(false);

      if (result?.answers) {
        const { answer, id, metadata = {} } = result.answers[0];
        const dialog = result.answers[0]?.dialog;

        updateChatHistory({
          message: answer,
          id,
          type: 'answer',
          timestamp: dayjs().format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
          prompts: dialog?.prompts || [],
          metadata,
        });
      }
    } catch (e) {
      console.log('Request error!', e);
    }
  };

  const submitQuestion = ({ displayText }) => {
    if (displayText === '') return;

    setInputValue('');
    setIsTyping(true);

    switch (displayText) {
      case 'Continue chat':
        chatBotMainMenu();

        break;
      case 'End chat':
        updateChatHistory({
          message: 'End chat',
          type: 'question',
          timestamp: dayjs().format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
          prompts: [],
        });

        updateChatHistory({
          message: 'End chat',
          type: 'answer',
          timestamp: dayjs().format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
          prompts: [{ displayText: 'Restart chat' }],
        });

        setIsTyping(false);
        break;
      default:
        updateChatHistory({
          message: displayText,
          type: 'question',
          timestamp: dayjs().format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
          prompts: [],
        });

        const lastAnswer =
          conversationData
            .slice()
            .reverse()
            .find((item) => item.type === 'answer') || -1;

        const context = {};

        if (lastAnswer) context.previousQnAId = lastAnswer.id;
        if (lastQuestion) context.previousUserQuery = lastQuestion;

        const messageBody = {
          question: displayText,
          confidenceScoreThreshold: config.confidenceThreshold,
          context,
        };

        setLastQuestion(displayText);

        qnaRequest(messageBody);
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles['conversation-wrapper']}>
        <ChatMessages
          conversationData={conversationData}
          messagesEndRef={messagesEndRef}
          submitQuestion={submitQuestion}
          isTyping={isTyping}
        />
      </div>
      <Footer
        value={inputValue}
        handleInputChange={handleInputChange}
        submitQuestion={submitQuestion}
      />
    </div>
  );
};

export default ChatBot;
