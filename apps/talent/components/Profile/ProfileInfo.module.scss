@use '@styles/mixins' as *;
@use '@styles/variables' as *;

@keyframes errorSlide {
  from {
    height: 0;
    opacity: 0;
    transform: translate3d(0, -15%, 0);
  }

  to {
    height: auto;
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

.profile-section-content {
  display: flex;
  flex-direction: column;
  gap: 36px;

  @include desktop {
    gap: 34px;
  }
}

.profile-section-title {
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  text-transform: uppercase;
  color: $grey-80;
  line-height: 1.875;
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 8px;
}

.profile-section-text {
  color: $grey-100;
  line-height: 1.75;
  font-weight: 600;
}

.profile-section-empty {
  display: flex;
  justify-content: space-between;
  padding: 23px $space-30;
  line-height: 1.6;
  align-items: center;
  border: 1px dashed $grey-60;
  border-radius: 10px;
  color: $black;
  cursor: pointer;

  .profile-section-empty-add-button {
    position: relative;
    margin-left: $space-10;
    color: $blue-100;
    font-weight: 700;
    text-decoration: none;

    &::after {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      height: 2px;
      background-color: $blue-100;
      opacity: 0;
    }

    &:hover::after {
      opacity: 1;
    }
  }
}

.profile-section-row {
  display: grid;
  flex-flow: row nowrap;
  grid-row-gap: $space-30;

  @include desktop {
    grid-template-columns: 1fr 1fr;
    grid-column-gap: $space-20;
  }
}

.profile-multiples-form {
  padding-top: 3px;
}

.profile-badge-container {
  display: flex;
  flex-wrap: wrap;
  gap: $space-10;

  &.profile-badge-container-filled {
    padding-top: $space-40;
  }
}

.profile-badge-outline {
  font-weight: 500;
  font-size: 16px;
  border-radius: 31px;
  line-height: 1.2;
  padding: 4px $space-10 $space-5;
  border: 1px solid $black;
  display: flex;
  gap: $space-5;
  align-items: center;

  &.slim {
    border-color: $grey-40;
    font-weight: 300;
  }
}

.profile-badge-filled {
  font-weight: 500;
  font-size: 16px;
  line-height: 30px;
  border-radius: 31px;
  padding: 0 $space-10;
  background-color: $grey-10;
  display: flex;
  gap: $space-5;
  align-items: center;
}

.clickable {
  cursor: pointer;
}

.profile-properties-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: $space-30 $space-50;

  @include desktop {
    grid-template-columns: repeat(4, 1fr);
  }
}

.profile-property.wide {
  grid-column: auto / span 2;
}

.profile-properties-container-form {
  position: relative;
  top: 6px;
  grid-gap: 33px $space-50;
  margin-bottom: 2px;
}

.profile-property-title {
  font-size: 14px;
  color: $black;
  font-weight: 600;
  margin-bottom: 8px;
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
}

.profile-property-value {
  position: relative;
  font-size: 16px;
  color: $black;
  font-weight: 700;
  text-transform: capitalize;

  &.age {
    text-transform: none;
  }
}

.profile-property-value-empty {
  font-size: 16px;
  color: $grey-40;
  font-weight: 600;
}

.location-hint {
  position: absolute;
  right: 0;
  bottom: 7px;
  max-width: calc(100% - 7ch);
  color: $black;
  font-size: 12px;
  font-weight: 300;
  line-height: 16px;
  letter-spacing: 0;
  text-align: right;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  pointer-events: none;
}

.save-button {
  background-color: $blue-20;
  border-radius: 10px;
  padding: 3px $space-10;
  color: $blue-100;
  font-weight: 600;
  line-height: 16px;
  margin-left: $space-10;
  text-transform: capitalize;
  cursor: pointer;
  border: none;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
}

.birthday-error {
  position: absolute;
  top: 35px;
  align-items: center;
  animation: errorSlide 200ms ease-in-out;
  color: $red-80;
  display: flex;
  font-size: 12px;
  font-weight: 300;
  text-align: initial;
  width: 100%;
  z-index: 1;
  line-height: 1;
}

.profile-form-property-title {
  color: $black;
  font-size: 14px;
  font-weight: 600;
  line-height: 18px;
  position: relative;
  top: -4.5px;

  &.error {
    color: $red-80;
  }
}

.hint {
  font-weight: 300;
  font-size: 12px;
  color: $grey-80;
  width: 100%;
  padding: $space-10 0 5px;
}

.delete-button {
  cursor: pointer;
}

.add-attribute-value-btn {
  cursor: pointer;
  width: 30px;
  height: 30px;
}

.attribute-level {
  margin-left: $space-5;
}

.locked-feature-container {
  gap: $space-10;
  align-items: center;
  background: $grey-10;
  margin-top: $space-20;
  border-radius: 10px;
  padding: $space-15 $space-10;
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  text-align: center;

  @include tablet {
    text-align: initial;
    flex-direction: row;
  }

  .button-unblock {
    padding: $space-5 $space-10;
    text-transform: capitalize;
    font-size: 12px;
    font-weight: 700;
    flex-shrink: 0;
  }
}

.locked-feature-description-container {
  flex-basis: 100%;

  .locked-feature-title {
    font-size: 14px;
    font-weight: 600;
  }

  .locked-feature-description {
    font-size: 12px;
    color: $grey-100;
    font-weight: 400;
    margin: 0;
  }
}

.icon-locked-container {
  box-shadow: $shadow-locked-icon;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 46px;

  &.mobile {
    @include tablet {
      display: none;
    }
  }

  &.desktop {
    display: none;

    @include tablet {
      display: initial;
    }
  }
}

.modal-overlay {
  padding: $space-20;
}

.modal-container {
  border-radius: 15px;
  max-width: 500px;
}

.icon-locked {
  display: flex;
}

.hint-container {
  margin-bottom: $space-20;
}
