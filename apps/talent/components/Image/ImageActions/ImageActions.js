'use client';
import React, { memo, useEffect, useState } from 'react';
import styles from './ImageActions.module.scss';
import cn from 'classnames';
import { getImageStarRating, isImageDeclined } from '@utils/imageHelpers';
import { StarRating, Tooltip, TooltipPhotoAnalyzer } from '@components';
import ImageAnalyzer from '../ImageAnalyzer/ImageAnalyzer';
import { IconPhoto, IconTitlePhoto, IconTrashRound } from '@icons/index';

const ImageActions = ({
  onClick = () => {},
  onMakeTitleClick,
  showUploadIcon = false,
  onDelete,
  selectFile,
  actionIconSize = 'small', // small | medium | large
  isCurrentTitlePhoto,
  image,
  isPaidOrDelayed,
  showTooltip = false,
  showRating,
  ratingPosition = 'center', // center | left
  iconSize = 'medium', // small | medium | large
  showRatingHint,
  genderTitle,
  showActions = true,
  openEditModal = () => {},
}) => {
  const [isFirstMobileClick, setIsFirstMobileClick] = useState(false);
  const [rating, setRating] = useState(0);

  useEffect(() => {
    setRating(getImageStarRating(image.links?.characteristics?.items));
  }, [image]);

  const onMobileClick = () => {
    if (!isFirstMobileClick) {
      setIsFirstMobileClick(true);
    } else {
      setIsFirstMobileClick(false);
      onClick();
    }
  };

  const clearIsFirstMobileClick = () => {
    if (isFirstMobileClick) {
      setIsFirstMobileClick(false);
    }
  };

  const IconTitle = IconTitlePhoto;

  // switch (talent)

  const getImageActions = () => {
    return (
      <>
        <div
          className={styles['actions-image-container']}
          onMouseLeave={clearIsFirstMobileClick}
        >
          {showActions && (
            <div
              className={cn(
                styles['profile-photo-actions'],
                styles[actionIconSize],
              )}
            >
              <div className={styles['profile-photo-actions-left']}>
                {showUploadIcon && (
                  <IconPhoto
                    className={styles.icon}
                    onClick={() => {
                      clearIsFirstMobileClick();
                      selectFile();
                    }}
                  />
                )}
                <img
                  className={
                    isCurrentTitlePhoto ? styles['icon-title'] : styles.icon
                  }
                  src={`/assets/icons/icon-title-photo${
                    isCurrentTitlePhoto ? '-current' : ''
                  }.svg`}
                  alt="icon"
                  onClick={() => {
                    clearIsFirstMobileClick();
                    onMakeTitleClick();
                  }}
                />
              </div>
              <IconTrashRound
                className={styles.icon}
                onClick={() => {
                  clearIsFirstMobileClick();
                  onDelete();
                }}
              />
            </div>
          )}
          <div
            onClick={onClick}
            className={cn(
              styles['actions-image-inner-container'],
              styles.desktop,
              styles[`actions-image-${iconSize}`],
            )}
          >
            <img className={styles['image']} src={image.url} alt="image" />
          </div>
          <div
            onClick={onMobileClick}
            className={cn(
              styles['actions-image-inner-container'],
              styles.mobile,
              styles[`actions-image-${iconSize}`],
              {
                [styles['actions-image-inner-container-main']]: !showActions,
              },
            )}
          >
            <img className={styles['image']} src={image.url} alt="image" />
          </div>
          {showRating && isPaidOrDelayed && (
            <div className={cn(styles['image-rating'], styles[ratingPosition])}>
              <StarRating
                initialRating={rating}
                height={16}
                width={16}
                isImageRating
                showHint={showRatingHint}
              />
            </div>
          )}

          {!showActions && (
            <button className={styles['edit-button']} onClick={openEditModal}>
              Edit photo
            </button>
          )}
        </div>
        {showRating && !showTooltip && isPaidOrDelayed && (
          <div className={styles['analyzer-container']}>
            <ImageAnalyzer
              characteristics={image.links?.characteristics?.items}
              starRating={rating}
              isTooltip={showTooltip}
              isReviewPending={!image.links?.characteristics?.items?.length}
              isPaidOrDelayed={isPaidOrDelayed}
              genderTitle={genderTitle}
              isBlocked={isImageDeclined(image)}
            />
          </div>
        )}
      </>
    );
  };

  return (
    <>
      {showTooltip && isPaidOrDelayed ? (
        <Tooltip
          clickable
          openOnHover
          content={
            <TooltipPhotoAnalyzer
              characteristics={image.links?.characteristics?.items}
              isReviewPending={!image.links?.characteristics?.items?.length}
              isPaidOrDelayed={isPaidOrDelayed}
              isBlocked={isImageDeclined(image)}
            />
          }
        >
          {getImageActions()}
        </Tooltip>
      ) : (
        getImageActions()
      )}
    </>
  );
};

export default memo(ImageActions);
