import React from 'react';
import { cookies, headers } from 'next/headers';
import { getResolvedUrl } from '@utils/apiHelper';
import { CookieServiceServer } from '@services/cookieServiceServer';
import Api from '@services/api';
import { MessageProvider } from '@contexts/MessageContext';
import { MainLayout, MessageCenter } from '@components';
import { ContactsProvider } from '@contexts/ContactContext';

export const metadata = {
  title: 'Messages',
};

export default async function MessagesPage(req) {
  const searchParams = await req.searchParams;
  const resolvedUrl = getResolvedUrl('/messages', searchParams);
  const cookieStore = await cookies();
  const headerStore = await headers();
  const profileId = CookieServiceServer.getProfileCookie(cookieStore);

  const conversations = await Api.serverside(
    `/conversations?expand=profile&profile=${profileId}&limit=10`,
    cookieStore,
    headerStore,
    resolvedUrl,
  );

  const clientStatus = await Api.serverside(
    '/clientstatus/info',
    cookieStore,
    headerStore,
    resolvedUrl,
  );

  const initialConversations = conversations.items;
  const canContactTalent =
    clientStatus.info?.allowed_to?.hasOwnProperty('contact_talent') || false;

  return (
    <MessageProvider
      initialConversations={initialConversations}
      canContactTalent={canContactTalent}
      profileId={profileId}
    >
      <ContactsProvider>
        <MainLayout
          isDefaultHeaderVisible
          isUserMenuVisible
          isMobileMenuVisible
        >
          <MessageCenter
            canContactTalent={canContactTalent}
            profileId={profileId}
          />
        </MainLayout>
      </ContactsProvider>
    </MessageProvider>
  );
}
