'use client';
import styles from '../ProfileConnections.module.scss';
import Image from 'next/image';
import React, { memo, useState } from 'react';
import { useFormik } from 'formik';
import Api from '@services/api';
import * as Yup from 'yup';
import { maskPhoneNumber } from '@utils/maskPhoneNumber';
import { Checkbox, Input } from '@components';
import { useNotifications } from '@contexts/NotificationContext';
import cn from 'classnames';
import { checkTollFreeNumber } from '@utils/checkTollFreeNumber';
import { hasFormValueChanged } from '@utils/hasFormValueChanged';
import { Amp } from '@services/amp';
import { EditToggle } from '../EditToggle';
import { EMAIL_REGEX, ErrorMessage, PHONE_NUMBER_REGEX } from '@constants/form';
import { useAuth } from '@contexts/AuthContext';

const PhoneAndEmail = ({
  accountId,
  phone,
  email,
  allowPhoneNotifications,
  refreshTouches,
  refreshUserProfiles,
}) => {
  const { setNotification } = useNotifications();
  const [isEditing, setEditing] = useState(false);
  const { userProfiles } = useAuth();

  const edit = (e) => {
    e?.preventDefault();
    Amp.track(Amp.events.elementClicked, {
      name: 'edit contacts',
      scope: Amp.element.scope.profile,
      section: Amp.element.section.contacts,
      type: Amp.element.type.button,
    });
    setEditing(true);
  };
  const triggerSubmit = async () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'save contacts',
      scope: Amp.element.scope.profile,
      section: Amp.element.section.contacts,
      type: Amp.element.type.button,
    });
    await formik.submitForm();
  };

  const formik = useFormik({
    enableReinitialize: true,
    initialValues: {
      email: email || '',
      phone: maskPhoneNumber(phone || ''),
      allowNotifications: allowPhoneNotifications || false,
    },
    onSubmit: async (values) => {
      if (!hasFormValueChanged(values, formik.initialValues)) {
        setEditing(false);

        return;
      }

      const body = new FormData();
      const newPhone = values.phone?.replace(/[^A-Z0-9]+/gi, '') || '';

      body.append('email', values.email);
      body.append('phone', newPhone);
      body.append('phone_opt_outed', values.allowNotifications ? '0' : '1');

      const changeContactsResponse = await Api.clientside(
        `/accounts/${accountId}/touches`,
        {
          body,
          method: 'PUT',
        },
      );

      const isError = changeContactsResponse.status !== 'ok';
      const errorMessage =
        changeContactsResponse?.message || ErrorMessage.Unexpected;

      if (newPhone) {
        const user = userProfiles[0] || {};

        Amp.track(Amp.events.submitPhoneNumber, {
          scope: Amp.element.scope.profile,
          phone_number_last_4_digits: newPhone.slice(-4),
          country_code: user.country,
          status: isError ? 'failure' : 'success',
          error_message: isError ? errorMessage : null,
        });
      }

      if (isError) {
        setNotification({
          type: 'error',
          message: errorMessage,
          timeout: '5000',
        });
      } else {
        await refreshTouches();
        refreshUserProfiles();
        setEditing(false);
      }
    },
    validationSchema: Yup.object({
      email: Yup.string()
        .required(ErrorMessage.EmailRequired)
        .matches(EMAIL_REGEX, ErrorMessage.EmailPattern),
      phone: Yup.string()
        .matches(PHONE_NUMBER_REGEX, ErrorMessage.PhonePattern)
        .test('phoneIsValid', ErrorMessage.PhonePatternToll, async (value) => {
          return checkTollFreeNumber(value);
        }),
    }),
  });

  const formatPhoneNumber = (e) => {
    formik.setFieldValue('phone', maskPhoneNumber(e.target.value));
  };

  return (
    <>
      <section className={styles['profile-info-block']}>
        <div className={styles['profile-section-title']}>
          Contacts:
          <EditToggle
            isEditing={isEditing}
            onEdit={edit}
            onSave={triggerSubmit}
            disabled={formik.isSubmitting || !formik.isValid}
          />
        </div>
        {!isEditing ? (
          <>
            {email || phone ? (
              <>
                {phone && (
                  <a
                    className={cn(
                      styles['profile-info-row'],
                      styles['profile-section-link'],
                    )}
                    href={`tel:${phone}`}
                    onClick={edit}
                  >
                    <Image
                      src={'/assets/icons/icon-phone.svg'}
                      width={27}
                      height={27}
                      alt="profile phone"
                      priority
                      className={styles['profile-info-icon']}
                    />

                    <span className={styles['text-ellipsis']}>
                      {maskPhoneNumber(phone)}
                    </span>
                  </a>
                )}
                {email && (
                  <a
                    className={cn(
                      styles['profile-info-row'],
                      styles['profile-section-link'],
                    )}
                    href={`mailto:${email}`}
                    onClick={edit}
                  >
                    <Image
                      src={'/assets/icons/icon-mail.svg'}
                      width={27}
                      height={27}
                      alt="profile email"
                      priority
                      className={styles['profile-info-icon']}
                      onClick={edit}
                    />
                    <span className={styles['text-ellipsis']}>{email}</span>
                  </a>
                )}
              </>
            ) : (
              <div className={styles['profile-section-empty']} onClick={edit}>
                Add contact information visible in applications but not in your
                public profile.
                <span className={styles['profile-section-empty-add-button']}>
                  Add
                </span>
              </div>
            )}
          </>
        ) : (
          <form className={styles['form']} onSubmit={formik.handleSubmit}>
            <Input
              name="phone"
              placeholder="Phone number"
              onChange={formatPhoneNumber}
              onBlur={formik.handleBlur}
              value={formik.values.phone}
              isTouched={formik.touched.phone}
              error={formik.errors.phone}
              hint="Include area code"
            />
            <div className={styles['form-notifications-container']}>
              <Checkbox
                disabled={!formik.values.phone}
                name="allowNotifications"
                onChange={formik.handleChange}
                value={formik.values.allowNotifications}
                error={formik.errors.allowNotifications}
                onBlur={formik.handleBlur}
                isTouched={formik.touched.allowNotifications}
              >
                <span className={styles['form-field-label']}>
                  Allow SMS notifications
                </span>
              </Checkbox>
            </div>
            <Input
              name="email"
              placeholder="Email"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.email}
              isTouched={formik.touched.email}
              error={formik.errors.email}
            />
          </form>
        )}
      </section>
    </>
  );
};

export default memo(PhoneAndEmail);
