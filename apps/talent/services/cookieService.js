import { getCookie, setCookie } from 'cookies-next/client';

export const CookieService = {
  cookie: {
    authentication: 'x-authentication',
    volatile: 'x-volatile',
    expires: 'x-expires',
    account: 'x-account',
    userType: 'x-user-type',
    profile: 'x-profile',
    accountLevel: 'x-account-level',
    userProfiles: 'x-user-profiles',
    sale: 'x-sale',
    tracking: 'x-tracking',
    profileProgressInfoBlockHidden: 'x-profile-progress-info-block-hidden',
    attributeLevelInfoBlockHidden: 'x-attribute-level-info-block-hidden',
    firstTimeVisitor: 'x-first-time-visitor',
    showCheckoutSuccess: 'x-show-checkout-success',
    showUpgradeCheckoutSuccess: 'x-show-upgrade-checkout-success',
    showStripeCheckoutSuccess: 'x-show-stripe-checkout-success',
    showPremiumCheckoutSuccess: 'x-show-premium-checkout-success',
    showLifetimeCheckoutSuccess: 'x-show-lifetime-checkout-success',
    guideSlotsData: 'x-guide-slots-data',
    trackTargeting: 'x-track-targeting',
    experiments: 'x-experiments',
    sessionId: 'x-session-id',
    features: 'x-features',
    redirect: 'x-redirect',
    upgrade: 'x-upgrade',
  },

  cookieLegacy: {
    session: '_ac',
    google: '_ga',
    reviewReminded: 'reviewReminded',
  },

  cookieThirdParty: {
    oneTrustConsent: 'OptanonConsent',
  },

  options: {
    domain: process.env.cookieDomain,
    path: '/',
  },

  isInternal(name) {
    return (
      Object.values(this.cookie).includes(name) ||
      Object.values(this.cookieLegacy).includes(name)
    );
  },
  //Setters
  setAuthenticationCookie(data) {
    const isDecoded = data.includes('Account');

    if (isDecoded) {
      setCookie(
        this.cookie.authentication,
        btoa(JSON.stringify(data)),
        options(),
      );
    } else {
      setCookie(this.cookie.authentication, data, options());
    }
  },
  setExpiresCookie(data) {
    setCookie(this.cookie.expires, data, options());
  },
  setVolatileCookie(data) {
    setCookie(this.cookie.volatile, data, options());
  },
  setAccountLevelCookie(data) {
    setCookie(this.cookie.accountLevel, JSON.stringify(data), options());
  },
  setUserProfilesCookie(data) {
    setCookie(this.cookie.userProfiles, JSON.stringify(data), options());
  },
  setTrackingCookie(data) {
    setCookie(
      this.cookie.tracking,
      btoa(JSON.stringify(data)),
      strictOptions(),
    );
  },
  setSessionCookie(data, expires) {
    setCookie(this.cookieLegacy.session, data, strictOptions(expires));
  },
  setProfileProgressInfoBlockHidden(data) {
    setCookie(
      this.cookie.profileProgressInfoBlockHidden,
      data,
      strictOptions(),
    );
  },
  setAttributeLevelInfoBlockHidden(data) {
    setCookie(this.cookie.attributeLevelInfoBlockHidden, data, strictOptions());
  },
  setFirstTimeVisitor(data) {
    setCookie(this.cookie.firstTimeVisitor, data, strictOptions());
  },
  setShowCheckoutSuccess(data) {
    setCookie(this.cookie.showCheckoutSuccess, data, options());
  },
  setShowUpgradeCheckoutSuccess(data) {
    setCookie(this.cookie.showUpgradeCheckoutSuccess, data, options());
  },
  setShowStripeCheckoutSuccess(data) {
    setCookie(this.cookie.showStripeCheckoutSuccess, data, options());
  },
  setShowPremiumCheckoutSuccess(data) {
    setCookie(this.cookie.showPremiumCheckoutSuccess, data, options());
  },
  setShowLifetimeCheckoutSuccess(data) {
    setCookie(this.cookie.showLifetimeCheckoutSuccess, data, options());
  },
  setTrackTargeting(data) {
    setCookie(this.cookie.trackTargeting, data, strictOptions());
  },
  setSessionIdCookie(data, expires) {
    setCookie(this.cookie.sessionId, data, strictOptions(expires));
  },
  setSaleCookie(data) {
    setCookie(this.cookie.sale, JSON.stringify(data), strictOptions());
  },
  setUpgradeCookie(data) {
    setCookie(this.cookie.upgrade, JSON.stringify(data), strictOptions());
  },
  setExperimentsCookie(data) {
    setCookie(this.cookie.experiments, JSON.stringify(data), strictOptions());
  },
  //Getters
  getAuthenticationCookie() {
    const value = this.exportString(getCookie(this.cookie.authentication));

    if (!value) return '';

    const isDecoded = value.includes('Account');

    if (isDecoded) {
      return value.replaceAll('"', '');
    } else {
      try {
        return this.exportString(JSON.parse(atob(value))).replaceAll('"', '');
      } catch (e) {
        return '';
      }
    }
  },
  getVolatileCookie() {
    return this.exportString(getCookie(this.cookie.volatile));
  },
  getExpiresCookie() {
    return this.exportInteger(getCookie(this.cookie.expires));
  },
  getAccountCookie() {
    return this.exportInteger(getCookie(this.cookie.account));
  },
  getUserTypeCookie() {
    return this.exportString(getCookie(this.cookie.userType));
  },
  getProfileCookie() {
    return this.exportInteger(getCookie(this.cookie.profile));
  },
  getAccountLevelCookie() {
    const value = this.exportString(getCookie(this.cookie.accountLevel));

    try {
      return JSON.parse(value);
    } catch (e) {
      return null;
    }
  },
  getUserProfilesCookie() {
    const value = this.exportString(getCookie(this.cookie.userProfiles));

    try {
      return JSON.parse(value);
    } catch (e) {
      return [];
    }
  },
  getSaleCookie() {
    const value = this.exportString(getCookie(this.cookie.sale));

    try {
      return JSON.parse(value) ?? null;
    } catch (e) {
      return null;
    }
  },
  getTrackingCookie() {
    const value = this.exportString(getCookie(this.cookie.tracking));

    try {
      return JSON.parse(atob(value));
    } catch (e) {
      return null;
    }
  },
  getExperimentsCookie() {
    const value = this.exportString(getCookie(this.cookie.experiments));

    try {
      return JSON.parse(value);
    } catch (e) {
      return null;
    }
  },
  getSessionCookie() {
    return this.exportString(getCookie(this.cookieLegacy.session));
  },
  getGoogleCookie() {
    return this.exportString(getCookie(this.cookieLegacy.google));
  },
  getFirstTimeVisitorCookie() {
    return this.exportBoolean(getCookie(this.cookie.firstTimeVisitor));
  },
  getOneTrustConsentCookie() {
    return this.exportString(getCookie(this.cookieThirdParty.oneTrustConsent));
  },
  getShowStripeCheckoutSuccess() {
    return this.exportBoolean(getCookie(this.cookie.showStripeCheckoutSuccess));
  },
  getTrackTargeting() {
    return getCookie(this.cookie.trackTargeting);
  },
  getSessionIdCookie() {
    return this.exportString(getCookie(this.cookie.sessionId));
  },
  getFeaturesCookie() {
    const value = this.exportString(getCookie(this.cookie.features));

    try {
      return JSON.parse(value);
    } catch (e) {
      return null;
    }
  },
  getRedirectCookie() {
    return this.exportString(getCookie(this.cookie.redirect));
  },
  getUpgradeCookie() {
    return this.exportBoolean(getCookie(this.cookie.upgrade));
  },
  // Removers
  deleteAuthenticationCookie() {
    setCookie(this.cookie.authentication, null, options(new Date(0)));
  },
  deleteVolatileCookie() {
    setCookie(this.cookie.volatile, null, options(new Date(0)));
  },
  deleteExpiresCookie() {
    setCookie(this.cookie.expires, null, options(new Date(0)));
  },
  deleteAccountCookie() {
    setCookie(this.cookie.account, null, options(new Date(0)));
  },
  deleteUserTypeCookie() {
    setCookie(this.cookie.userType, null, options(new Date(0)));
  },
  deleteProfileCookie() {
    setCookie(this.cookie.profile, null, options(new Date(0)));
  },
  deleteAccountLevelCookie() {
    setCookie(this.cookie.accountLevel, null, options(new Date(0)));
  },
  deleteUserProfilesCookie() {
    setCookie(this.cookie.userProfiles, null, options(new Date(0)));
  },
  deleteSaleCookie() {
    setCookie(this.cookie.sale, null, strictOptions(new Date(0)));
  },
  deleteFirstTimeVisitorCookie() {
    setCookie(this.cookie.firstTimeVisitor, null, strictOptions(new Date(0)));
  },
  deleteShowCheckoutSuccessCookie() {
    setCookie(this.cookie.showCheckoutSuccess, null, options(new Date(0)));
  },
  deleteShowStripeCheckoutSuccessCookie() {
    setCookie(
      this.cookie.showStripeCheckoutSuccess,
      null,
      options(new Date(0)),
    );
  },
  deleteShowPremiumCheckoutSuccessCookie() {
    setCookie(
      this.cookie.showPremiumCheckoutSuccess,
      null,
      options(new Date(0)),
    );
  },
  deleteShowLifetimeCheckoutSuccessCookie() {
    setCookie(
      this.cookie.showLifetimeCheckoutSuccess,
      null,
      options(new Date(0)),
    );
  },
  deleteRedirectCookie() {
    setCookie(this.cookie.redirect, null, strictOptions(new Date(0)));
  },
  deleteUpgradeCookie() {
    setCookie(this.cookie.upgrade, null, strictOptions(new Date(0)));
  },
  // Exporters
  exportInteger(value) {
    return value ? parseInt(String(value)) : 0;
  },
  exportString(value) {
    return value ? String(value) : '';
  },
  exportBoolean(value) {
    return value === 'true';
  },
  // Cleaners
  cleanAuthenticationCookies() {
    this.deleteAuthenticationCookie();
    this.deleteVolatileCookie();
    this.deleteExpiresCookie();
    this.deleteAccountCookie();
    this.deleteUserTypeCookie();
    this.deleteProfileCookie();
    this.deleteUserProfilesCookie();
    this.deleteAccountLevelCookie();
    this.deleteFirstTimeVisitorCookie();
    this.deleteShowCheckoutSuccessCookie();
    this.deleteUpgradeCookie();
  },
};

export const strictOptions = (expires) => {
  const options = { ...CookieService.options, sameSite: 'strict' };

  if (expires) {
    options.expires = expires;
  }

  return options;
};

export const options = (expires = null) => {
  const options = { ...CookieService.options };

  if (expires) {
    options.expires = expires;
  }

  return options;
};
