'use client';
import React, { memo, useState } from 'react';
import { Button, Loading, Modal } from '@components';
import styles from './ModalDeletePaymentInfo.module.scss';
import Api from '@services/api';
import { useNotifications } from '@contexts/NotificationContext';
import { ErrorMessage } from '@constants/form';
import { Amp } from '@services/amp';
import { useAuth } from '@contexts/AuthContext';

const ModalDeletePaymentInfo = ({
  onClose,
  accountId,
  refreshBillingAndSubscription,
}) => {
  const { setNotification } = useNotifications();
  const { accountLevel } = useAuth();
  const [loading, setLoading] = useState(false);

  const showErrorMessage = (message) => {
    setNotification({
      type: 'error',
      message: message || ErrorMessage.Unexpected,
      timeout: '5000',
    });
  };

  const deletePaymentInfo = async () => {
    if (accountLevel?.isPaidOrDelayed) {
      const reason = 'Payment info deletion';

      const discontinueSubscriptionResponse = await Api.clientside(
        `/accounts/${accountId}/subscription?reason=${reason}`,
        {
          method: 'DELETE',
        },
      );

      if (discontinueSubscriptionResponse.status !== 'ok') {
        showErrorMessage(discontinueSubscriptionResponse.message);

        return;
      }
    }

    const deleteResponse = await Api.clientside(
      `/accounts/${accountId}/billing-info`,
      { method: 'DELETE' },
    );

    if (deleteResponse.status !== 'ok') {
      showErrorMessage(deleteResponse.message);
    } else {
      Amp.track(Amp.events.deletePaymentInfo);
    }
  };

  const onDeleteClick = async () => {
    setLoading(true);
    try {
      await deletePaymentInfo();
      await refreshBillingAndSubscription();
    } catch {
      showErrorMessage();
    }
    setLoading(false);
    onClose();
  };

  return (
    <Modal
      backdropClose={!loading}
      onClose={onClose}
      classNameContainer={styles['modal']}
      classNameOverlay={styles['container']}
      showDefaultLayout={false}
    >
      {loading ? (
        <Loading padding="10px 0 30px 0" />
      ) : (
        <>
          <div className={styles['title']}>Are you sure?</div>
          <div className={styles['text']}>
            You have selected to delete your payment info.
          </div>
          <div className={styles['btn-container']}>
            <Button
              minWidth="130px"
              color="plain-blue"
              label="Cancel"
              onClick={onClose}
            />
            <Button
              minWidth="130px"
              color="plain-red"
              label="Delete"
              onClick={onDeleteClick}
            />
          </div>
        </>
      )}
    </Modal>
  );
};

export default memo(ModalDeletePaymentInfo);
