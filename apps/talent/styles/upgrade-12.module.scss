@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.upgrade-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: $space-40;

  @include desktop {
    gap: $space-60;
  }
}

.upgrade-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: $gradient-magnolia-white;
  width: 100%;
  color: $white;
  padding: 37px $space-10 $space-40;

  @include desktop {
    padding: 47px 0 $space-60;
  }
}

.upgrade-header-title {
  font-size: 24px;
  margin: 0 0 24px;
  transition: all 0.2s ease-in-out;
  text-align: center;
  line-height: 1;
  color: $violet-100;
  font-weight: 700;
  max-width: 300px;

  @include desktop {
    font-size: 40px;
    max-width: 100%;
  }
}

.upgrade-header-description {
  font-weight: 300;
  font-size: 16px;
  color: $black;
  max-width: 500px;
  text-align: center;
  margin-bottom: 39px;

  @include tablet {
    margin-bottom: 48px;
  }
}

.upgrade-plan-container {
  display: flex;
  width: 100%;
  justify-content: center;
  align-items: center;
  margin-top: -80px;
  padding: 0 $space-10;
  gap: $space-30;
  flex-direction: column;

  @include desktop {
    margin-top: -120px;
    gap: 44px;
  }
}

.upgrade-plan {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  background-color: $white;
  border-radius: 3px 10px 10px;
  transition: all 0.2s ease-in-out;
  height: 80px;
  position: relative;
  border: 3px solid $violet-60;
  box-shadow: $shadow-upgrade-12-plan;
  max-width: 600px;
  width: 100%;

  @include desktop {
    height: 114px;
    grid-template-columns: 1fr 0.8fr 1fr;
  }
}

.upgrade-plan-savings-container {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  line-height: 1;
  color: $green-100;
  font-weight: 700;

  @include desktop {
    font-size: 18px;
  }
}

.upgrade-plan-period-container {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  transition: all 0.2s ease-in-out;

  @include desktop {
    font-size: 18px;
  }
}

.upgrade-plan-price-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background-color: $violet-10;
}

.upgrade-plan-price-base {
  font-size: 14px;
  opacity: 0.7;
  text-decoration: line-through;
  line-height: 1;
  font-weight: 300;
}

.upgrade-plan-price {
  font-weight: 700;
  line-height: 1.2;
  font-size: 18px;
  color: $black;

  @include desktop {
    font-size: 30px;
  }

  .upgrade-plan-currency {
    font-size: 0.6em;
  }
}

.upgrade-plan-price-period {
  font-weight: 400;
  font-size: 14px;
  opacity: 0.7;
  line-height: 1;
  text-transform: lowercase;
}

.upgrade-plan-savings {
  font-size: 18px;

  @include desktop {
    font-size: 30px;
  }
}

.upgrade-action-container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.link {
  color: $blue-100;
  font-weight: 400;
  font-size: 14px;
  cursor: pointer;
  margin-top: 33px;

  &:hover {
    text-decoration: underline;
  }

  @include tablet {
    margin-top: 21px;
  }
}

.best-deal-tag {
  background-color: $violet-60;
  color: $white;
  position: absolute;
  padding: 0 6px 3px 3px;
  font-weight: 700;
  font-size: 12px;
  border-bottom-right-radius: 3px;
}

.upgrade-features {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: $space-10;
  text-align: center;
  margin-top: 33px;

  @include tablet {
    margin-top: 48px;
  }
}

.upgrade-feature {
  font-weight: 300;
  font-size: 14px;
  display: inline-block;
  color: $black;
}

.dot {
  min-width: 4px;
  height: 4px;
  background-color: $black;
  border-radius: 50%;
  display: inline-block;
  margin-bottom: 3px;
  margin-right: $space-5;
}

.divider {
  opacity: 0.1;
  border: 0.5px solid $black;
  width: 100%;
  max-width: 1240px;
  margin-top: $space-20;
  margin-bottom: 13px;
}

.disclaimer-container {
  display: flex;
  flex-direction: column;
  gap: $space-20;
  padding-bottom: $space-40;
  text-align: center;
  font-weight: 300;
  font-size: 14px;
  opacity: 0.4;
  max-width: $content-max-width;
}

.upgrade-feature-title {
  font-weight: 600;

  &::after {
    content: '';
    display: inline-block;
    margin: 0 8px;
    width: 12px;
    border: 1px solid $black;
    vertical-align: middle;
  }
}

.text-accent {
  font-weight: 600;
}

.error-message {
  background: $red-10;
  max-width: 600px;
  width: 100%;
  padding: $space-15 $space-20;
  border-radius: 8px;
  text-align: center;
  font-size: 14px;
  font-weight: 600;
  margin-top: $space-5;
  border: 1px solid $red-20;

  @include desktop {
    margin-top: -$space-10;
    margin-bottom: -$space-20;
    padding: $space-20 $space-60;
  }
}
