@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.premium-feature-container {
  text-align: center;
  background-color: $blue-10;
  max-width: 770px;
  font-weight: 400;
  border-radius: 10px;
  width: 100%;

  .premium-feature {
    position: relative;
    top: -40px;
  }

  p {
    margin: $space-15 0 0;
  }

  h1 {
    margin: 0;
    font-weight: 800;
  }

  svg {
    top: 0;
    margin-bottom: $space-15;
  }

  & .button {
    margin-top: $space-35;
    font-size: 14px;
  }

  &.modal-view {
    position: relative;
    background-color: transparent;
    margin: 0 0 (-$space-20);
    top: -30px;
    padding: 0 $space-40;
  }
}
