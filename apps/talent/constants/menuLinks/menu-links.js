export const mobileLinks = [
  {
    id: 'castingcalls',
    title: 'Casting calls',
    routerLink: `${process.env.publicUrl}/castingcalls`,
    icon: 'casting-calls',
  },
  {
    id: 'inbox',
    title: 'Inbox',
    routerLink: '/messages',
    icon: 'inbox',
  },
  {
    id: 'blog',
    title: 'Blog',
    routerLink: `${process.env.publicUrl}/blog`,
    icon: 'classroom',
  },
  {
    id: 'reviews',
    title: 'Reviews',
    routerLink: `${process.env.publicUrl}/reviews`,
    icon: 'reviews',
  },
];

export const desktopLinks = [
  {
    label: 'casting calls',
    href: `${process.env.publicUrl}/castingcalls`,
  },
  {
    label: 'talent',
    href: `${process.env.publicUrl}/talent`,
  },
  {
    label: 'blog',
    href: `${process.env.publicUrl}/blog`,
  },
  {
    label: 'reviews',
    href: `${process.env.publicUrl}/reviews`,
  },
];

export const mobileTalentLinks = [
  {
    id: 'blog',
    title: 'Blog',
    routerLink: `${process.env.publicUrl}/blog`,
    icon: 'classroom',
    isMainAction: false,
  },
  {
    id: 'inbox',
    title: 'Inbox',
    routerLink: '/messages',
    icon: 'inbox',
    isMainAction: false,
  },
  {
    id: 'castingcalls',
    title: 'Casting calls',
    routerLink: `${process.env.publicUrl}/castingcalls`,
    icon: 'loupe',
    isMainAction: true,
  },
  {
    id: 'profile',
    title: 'Profile',
    routerLink: false,
    icon: 'author',
    isMainAction: false,
  },
];
