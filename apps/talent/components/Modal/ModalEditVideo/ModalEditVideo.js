'use client';
import { Button, InputFormik, Modal } from '@components';
import styles from './ModalEditVideo.module.scss';
import { Form, Formik } from 'formik';
import React, { useState } from 'react';
import * as Yup from 'yup';
import { Amp } from '@services/amp';
import Api from '@services/api';
import { useProfileContext } from '@contexts/ProfileContext';
import { ErrorMessage } from '@constants/form';
import cn from 'classnames';

const ModalEditVideo = ({ profileId, onClose, video = {} }) => {
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);

  const { refreshCategories, categories, updateProfileRating, refreshVideos } =
    useProfileContext();

  const { id, type, link } = video || {};
  const isNewVideo = !id;
  const initialValues = { link: link || '' };
  const validationSchema = Yup.object().shape({
    link: Yup.string().required(ErrorMessage.YoutubeLinkRequired),
  });

  const onRemove = async () => {
    setLoading(true);

    Amp.track(Amp.events.elementClicked, {
      name: `remove video`,
      scope: Amp.element.scope.profile,
      section: Amp.element.section.video,
      type: Amp.element.type.button,
    });

    const { status, message, links } = await Api.clientside(
      `/profiles/${profileId}/videos/${id}?expand=profile`,
      {
        method: 'DELETE',
      },
    );

    if (status !== 'removed') {
      setError(message || ErrorMessage.Unexpected);
      setLoading(false);
    } else {
      await refreshVideos();
      updateProfileRating(links?.profile?.rating);
      onClose();
    }
  };

  const onSubmit = async (values) => {
    if (isNewVideo) {
      await onAdd(values);
    } else {
      await onUpdate(values);
    }
  };

  const onAdd = async (values) => {
    const body = new FormData();

    body.append('link', values.link);
    body.append('type', type);

    const { status, message, items } = await Api.clientside(
      `/profiles/${profileId}/videos/youtubes?expand=profile`,
      {
        method: 'POST',
        body: body,
      },
    );

    if (status !== 'ok') {
      setError(message || ErrorMessage.Unexpected);
    } else {
      updateProfileRating(items?.[0]?.links?.profile?.rating);
      await refreshVideos();

      // Once talent uploads the UGC demo reel to his profile,
      // talent profile needs to be added to Content Creators and Influencers Category
      if (type === 'ugc_demo_reel') {
        await addUgcDemoReelCategories();
      } else {
        onClose();
      }
    }
  };

  const onUpdate = async (values) => {
    const body = new FormData();

    body.append('link', values.link);

    const response = await Api.clientside(
      `/profiles/${profileId}/videos/${id}`,
      {
        method: 'PUT',
        body: body,
      },
    );

    if (response.status !== 'ok') {
      setError(response.message || ErrorMessage.Unexpected);
    } else {
      await refreshVideos();
      onClose();
    }
  };

  const addUgcDemoReelCategories = async () => {
    const ugc_categories = [24, 26];

    const userCategories = categories.map((c) => c.id);
    const userCategoriesWithUGC = [
      ...new Set([...userCategories, ...ugc_categories]),
    ];

    if (userCategories.length === userCategoriesWithUGC.length) {
      return;
    }

    const body = new FormData();

    userCategoriesWithUGC.forEach((id) => {
      body.append('items[]', id);
    });

    const response = await Api.clientside(`/profiles/${profileId}/categories`, {
      method: 'PUT',
      body: body,
    });

    if (response.status === 'ok') {
      await refreshCategories();
      onClose();
    }
  };

  const onChange = () => {
    if (error) {
      setError(null);
    }
  };

  return (
    <Modal
      backdropClose
      onClose={onClose}
      classNameContainer={styles['modal-container']}
    >
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={onSubmit}
      >
        {({ isSubmitting, errors }) => (
          <Form onChange={onChange}>
            <InputFormik
              error={errors.link || error}
              name="link"
              placeholder="Youtube link"
            />
            <div className={styles.footer}>
              <Button
                type="submit"
                minWidth="200px"
                color="blue"
                label="Save"
                disabled={isSubmitting || loading}
              />
              {!isNewVideo && (
                <span
                  className={cn(styles['remove-btn'], {
                    [styles.disabled]: loading,
                  })}
                  onClick={onRemove}
                >
                  Remove video
                </span>
              )}
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default ModalEditVideo;
