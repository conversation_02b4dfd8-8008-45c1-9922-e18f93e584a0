'use client';
import React, { createContext, useContext, useEffect, useState } from 'react';
import Api from '@services/api';
import { useAuth } from './AuthContext';
import { formatGroups } from '@utils/contactHelpers';

const ContactsContext = createContext({});

export const ContactsProvider = ({
  children,
  defaultGroups = [],
  defaultContacts = [],
  defaultContactsTotal = 0,
  defaultGroup = null,
}) => {
  const [groups, setGroups] = useState([]);
  const [group, setGroup] = useState({});
  const [contactsTotal, setContactsTotal] = useState(0);
  const [contacts, setContacts] = useState([]);
  const { profileId } = useAuth();

  useEffect(() => {
    if (defaultGroups?.length) {
      setGroups(defaultGroups);
    }
  }, [defaultGroups]);

  useEffect(() => {
    if (defaultContacts?.length || contacts.length) {
      setContacts(defaultContacts);
    }
  }, [defaultContacts]);

  useEffect(() => {
    setContactsTotal(defaultContactsTotal);
  }, [defaultContactsTotal]);

  useEffect(() => {
    if (defaultGroup) {
      setGroup(defaultGroup);
    }
  }, [defaultGroup]);

  const addGroup = (group) => {
    setGroups([...groups, group]);
  };

  const addToGroup = (groupId, id) => {
    setGroups(
      groups.map((group) =>
        groupId === group.id ? { ...group, total: group.total + 1 } : group,
      ),
    );

    if (contacts.some((contact) => contact.id === id && contact.isRemoved)) {
      setContactsTotal(contactsTotal + 1);
    }

    setContacts(
      contacts.map((contact) =>
        contact.id === id
          ? { ...contact, isRemovedFromGroup: false, isRemoved: false }
          : contact,
      ),
    );
  };

  const addContact = (id) => {
    setContactsTotal(contactsTotal + 1);
    setContacts(
      contacts.map((contact) =>
        contact.id === id
          ? { ...contact, isRemovedFromGroup: false, isRemoved: false }
          : contact,
      ),
    );
  };

  const removeFromGroup = (groupId, contactId) => {
    setGroups(
      groups.map((group) =>
        groupId === group.id ? { ...group, total: group.total - 1 } : group,
      ),
    );

    if (groupId === group.id) {
      setContacts(
        contacts.map((contact) =>
          contact.id === contactId
            ? { ...contact, isRemovedFromGroup: true }
            : contact,
        ),
      );
    }
  };

  const removeContact = async (contactId) => {
    setContactsTotal(contactsTotal - 1);
    setContacts(
      contacts.map((contact) =>
        contact.id === contactId ? { ...contact, isRemoved: true } : contact,
      ),
    );

    await refreshGroups();
  };

  const refreshGroups = async () => {
    const response = await Api.clientside(
      `/profiles/${profileId}/contact-groups`,
    );

    setGroups(formatGroups(response.items || []));
  };

  const renameGroup = (id, title) => {
    setGroups(
      groups.map((group) => (id === group.id ? { ...group, title } : group)),
    );

    setGroup({ ...group, title: title });
  };

  return (
    <ContactsContext.Provider
      value={{
        group,
        groups,
        refreshGroups,
        contactsTotal,
        contacts,
        addGroup,
        addToGroup,
        removeFromGroup,
        addContact,
        removeContact,
        renameGroup,
      }}
    >
      {children}
    </ContactsContext.Provider>
  );
};

export const useContacts = () => useContext(ContactsContext);
