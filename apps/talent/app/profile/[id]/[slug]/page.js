import ProfileClient from './_client';
import { cookies, headers } from 'next/headers';
import { notFound } from 'next/navigation';
import { getResolvedUrl } from '@utils/apiHelper';
import { CookieServiceServer } from '@services/cookieServiceServer';
import Api from '@services/api';
import { extractResult } from '@utils/extractPromiseResult';
import { kebabToCamelCase } from '@utils/kebabToCamelCase';
import { formatProfile } from '@utils/formatProfile';
import { isMobile } from '@utils/isMobile';

// Add shallow routing
export const metadata = {
  title: 'Profile',
};

export default async function ProfilePage(req) {
  const params = await req.params;
  const searchParams = await req.searchParams;
  const { slug } = params;
  const resolvedUrl = getResolvedUrl(
    `/profile/${params.id}/${slug}`,
    searchParams,
  );
  const cookieStore = await cookies();
  const headerStore = await headers();
  const hideProfileProgressInfoBlock =
    !!CookieServiceServer.getProfileProgressInfoBlockHidden(cookieStore);
  const hideAttributeLevelInfoBlock =
    !!CookieServiceServer.getAttributeLevelInfoBlockHidden(cookieStore);
  const accountLevel = CookieServiceServer.getAccountLevelCookie(cookieStore);

  let id = params.id;

  if (isNaN(Number(id))) {
    const profileByUsername = await Api.serverside(
      `/personal-urls/${id}`,
      cookieStore,
      headerStore,
      resolvedUrl,
    );

    id = profileByUsername.links?.profile?.id;
  }

  if (!id) {
    notFound();
  }

  // ToDo: skills and credits expand is return only empty array
  const profile = await Api.serverside(
    `/profiles/${id}?expand=personal_url,ethnicities,socialities,categories,location,touches`,
    cookieStore,
    headerStore,
    resolvedUrl,
  );

  // Intentionally
  if (false === profile.success) {
    notFound();
  }

  const paths = [
    'unions',
    'body-type',
    'piercings',
    'tattoos',
    'specific-characteristics',
  ];

  if (accountLevel?.isPaidOrDelayed) {
    paths.push(
      'languages',
      'accents',
      'sports',
      'dances',
      'musicianship',
      'professions',
      'family-photoshoot',
      'driving',
      'pets',
    );
  }

  const attributeRequests = paths.map((path) => `/profiles/attributes/${path}`);

  const resultPaths = [
    `/profiles/${id}/images?expand=profile_image_characteristics&width=600&height=800`,
    `/profiles/${id}/videos/youtubes`,
    `/profiles/${id}/skills`,
    `/profiles/${id}/socialities`,
    `/profiles/${id}/credits`,
    accountLevel?.isPaidOrDelayed ? `/profiles/${id}/attributes` : null,
    `/profiles/${id}/audios`,
    `/parameters`,
    ...attributeRequests,
  ];

  const requests = resultPaths.map((path) => {
    if (!path) {
      return Promise.resolve({ status: 'skipped' });
    }

    return Api.serverside(path, cookieStore, headerStore, resolvedUrl);
  });

  const results = await Promise.allSettled(requests);

  const [
    profileImages,
    profileYoutubes,
    profileSkills,
    profileSocialNetworks,
    profileCredits,
    profileAttributes,
    profileAudios,
    parametersResponse,
    ...attributesResponses
  ] = results.map((result) => extractResult(result, { items: [] }));

  const attributes = paths.reduce((acc, path, index) => {
    acc[kebabToCamelCase(path)] = attributesResponses[index];

    return acc;
  }, {});

  const formattedProfile = formatProfile(
    profile,
    profileImages.items,
    profileSocialNetworks.items,
    profileYoutubes.items,
    profileSkills.items,
    profileCredits.items,
    profileAttributes.items,
    profileAudios.items,
  );

  const userAgent = headerStore.get('user-agent');
  const isMobileFromUserAgent = isMobile(userAgent);

  return (
    <ProfileClient
      isMobileFromUserAgent={isMobileFromUserAgent}
      profile={formattedProfile}
      hideProfileProgressInfoBlock={hideProfileProgressInfoBlock}
      hideAttributeLevelInfoBlock={hideAttributeLevelInfoBlock}
      accountLevel={accountLevel}
      genderOptions={
        parametersResponse.items?.genders?.items?.filter(
          (option) => option.value,
        ) || []
      }
      ethnicitiesOptions={
        parametersResponse.items?.ethnicities?.items?.filter(
          (option) => option.value,
        ) || []
      }
      heightOptions={
        parametersResponse.items?.heights?.items?.map((option) =>
          option.value ? option : { value: 'n/a', title: 'n/a' },
        ) || []
      }
      weightOptions={
        parametersResponse.items?.weights?.items?.map((option) =>
          option.value ? option : { value: 'n/a', title: 'n/a' },
        ) || []
      }
      eyeColorOptions={
        parametersResponse.items['eye-colors']?.items?.map((option) =>
          option.value ? option : { value: 'n/a', title: 'n/a' },
        ) || []
      }
      hairColorOptions={
        parametersResponse.items['hair-colors']?.items?.map((option) =>
          option.value ? option : { value: 'n/a', title: 'n/a' },
        ) || []
      }
      hipSizeOptions={
        parametersResponse.items['hip-sizes']?.items?.map((option) =>
          option.value ? option : { value: 'n/a', title: 'n/a' },
        ) || []
      }
      dressSizeOptions={
        parametersResponse.items['dress-sizes']?.items?.map((option) =>
          option.value !== null
            ? { ...option, value: String(option.value) }
            : { value: 'n/a', title: 'n/a' },
        ) || []
      }
      bustOptions={
        parametersResponse.items['bust-sizes']?.items?.map((option) =>
          option.value ? option : { value: 'n/a', title: 'n/a' },
        ) || []
      }
      cupSizeOptions={
        parametersResponse.items['cup-size']?.items?.map((option) =>
          option.value ? option : { value: 'n/a', title: 'n/a' },
        ) || []
      }
      categoryOptions={
        parametersResponse.items?.categories?.items?.map((option) => ({
          ...option,
          value: option.id,
        })) || []
      }
      attributes={attributes}
      tab={slug}
    />
  );
}
