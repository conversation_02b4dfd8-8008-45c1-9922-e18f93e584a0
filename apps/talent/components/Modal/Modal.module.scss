@use '@styles/mixins' as *;
@use '@styles/variables' as *;

@include keyframes(bottomFadeIn) {
  from {
    opacity: 0;
    transform: translateY(30%);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.overlay {
  align-items: center;
  backdrop-filter: blur(3px);
  background-color: $black-100-opacity-30;
  display: flex;
  justify-content: center;
  overflow: auto;
  position: fixed;
  inset: 0;
  z-index: $z-index-modal;
}

.default-overlay {
  padding: 0 $space-20;
}

.animation {
  @include animation(bottomFadeIn, 0.5s, ease-in-out, 1);
}

.close-button-container {
  padding: $space-15;
  width: 100%;
}

.close-button {
  background-color: transparent;
  border-style: none;
  cursor: pointer;
  outline: none;
  position: absolute;
  right: 14px;
  top: 14px;
  height: 28px;
  opacity: 0.5;
  transition: opacity 0.2s ease-in-out;
  width: 28px;
  z-index: 2;

  &:hover {
    opacity: 1;
    transition: opacity 0.2s ease-in-out;
  }

  &::before {
    background-color: $black;
    transform: rotate(45deg);
    content: '';
    height: 1px;
    left: 50%;
    margin-left: -$space-10;
    position: absolute;
    top: 50%;
    width: 20px;
  }

  &::after {
    background-color: $black;
    transform: rotate(-45deg);
    content: '';
    height: 1px;
    left: 50%;
    margin-left: -$space-10;
    position: absolute;
    top: 50%;
    width: 20px;
  }
}

.hide-mobile-close-button {
  display: none;

  @include tablet {
    display: flex;
  }
}

.float-close-button {
  position: absolute;
  top: 0;
  left: 0;
}

.container {
  background-color: $white;
  position: relative;
}

.default-container {
  max-width: $breakpoint-tablet;
  background-color: $white;
  border-radius: 5px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  padding: $space-20 $space-15;
  position: relative;

  @include tablet {
    border-radius: 10px;
    max-height: 80vh;
  }
}

.content {
  box-sizing: border-box;
  width: 100%;
  height: 100%;

  @include tablet {
    border-radius: 10px;
  }

  &.show-mobile-border-radius {
    border-radius: 10px;
  }

  &.content-overflow-hidden {
    overflow: hidden auto;
  }
}

.default-content {
  padding: 0 $space-15 $space-25;
  border-radius: 10px;
}
