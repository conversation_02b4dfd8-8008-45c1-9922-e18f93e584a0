import Api from '../../services/api';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  CastingCallList,
  FilterMobileHeader,
  FilterPaginator,
  ItemDisplayMode,
  Loading,
  Seo,
  SeoLinking,
  AnnouncementBanner,
  ArticleBanner,
  Promo,
} from '../../components';
import IconClose from '../../public/assets/icons/icon-close-1.svg';
import React, { memo, useEffect, useState } from 'react';
import seoPageProps from '../../utils/seoPageProps';
import seoCrossLinkingProps from '../../utils/seoCrossLinkingProps';
import { formatFilters } from '../../utils/formatFilters';
import FilterService from '@entertech/filter-service';
import Router, { useRouter } from 'next/router';
import { useAuth } from '../../contexts/AuthContext';
import {
  applyDisableNearMe,
  applyEmptyLocation,
  applyFilter,
  applyFilterAge,
  applyFilterCategories,
  applyFilterEthnicities,
  applyFilterGenders,
  applyFilterLocation,
  valuesDiffer,
  getFilterCity,
  isValidStaticPart,
  applyBestMatch,
} from '../../utils/filterHelper';
import { MainLayout, PageLayout } from '../../components/Layouts';
import styles from '../../styles/castingcalls.module.scss';
import cn from 'classnames';
import Filter from '../../components/Filter/Filter';
import ApiNoCache from '../../services/apiNoCache';
import { CookieService } from '../../services/cookieService';
import { formatAnnouncement } from '../../utils/formatAnnouncement';
import { useLiveChat } from '../../contexts/LiveChatContext';
import {
  LIVE_CHAT_GROUP,
  LIVE_CHAT_IDLE_TIMEOUT,
} from '../../constants/liveChat';
import CallToActionBlock from '../../components/CallToActionBlock/CallToActionBlock';
import { Amp } from '../../services/amp';
import { VIEW_MODE } from '../../constants/castingCalls';
import { getGatewayHeaders, getHeaders } from '../../utils/headerHelper';
import { sharedServerSideProps } from '../../utils/sharedServerSideProps';
import { getDaysFromDate } from '../../utils/getDaysFromDate';
import { useCTA } from '../../contexts/CTAContext';
import { useSale } from '../../contexts/SaleContext';

export const generateTitle = (filters, seo) => {
  let title = `Casting Calls`;
  const items = FilterService.generateKeyValuePairsFilter(filters, seo);
  const strings = [];

  for (const [key, value] of Object.entries(items)) {
    strings.push(
      `${key.charAt(0).toUpperCase() + key.slice(1)}: ${value.join(', ')}`,
    );
  }

  if (strings.length) {
    title += ' - ' + strings.join(', ');
  }

  return title;
};

export async function getServerSideProps({ req, query, resolvedUrl, res }) {
  return provideProps(req, query, resolvedUrl, res);
}

export async function provideProps(req, query, resolvedUrl, res) {
  if (query?.static && !isNaN(parseInt(query.static))) {
    return {
      redirect: {
        permanent: true,
        destination: req.url.replace('calls', 'call'),
      },
    };
  }

  const isTalent = CookieService.getUserTypeCookie(req, res) === 'talent';
  const account = {};

  let content,
    formattedAnnouncement = null;

  if (isTalent) {
    const accountId = CookieService.getAccountCookie(req, res) ?? 0;

    if (accountId) {
      const accountLevel = CookieService.getAccountLevelCookie(req, res);

      if (accountLevel?.isPaidOrDelayed) {
        const announcements = (
          await Api.serverAPIRoute(
            `/promotions?types=Announcements&targets=talent-is-paid&limit=1`,
            null,
            getHeaders(req, resolvedUrl),
          )
        ).data;
        const announcement = announcements ? announcements[0] : null;

        formattedAnnouncement = formatAnnouncement(announcement);
      }

      const location = (
        await ApiNoCache.serverGateway(
          `/accounts/${accountId}/location`,
          getGatewayHeaders(resolvedUrl),
          req,
          res,
        )
      ).data;

      if (location.messageKey === 'error.access_denied') {
        CookieService.deleteExpiresCookie(req, res);

        return {
          redirect: {
            destination: `/`,
            permanent: false,
          },
        };
      }

      if (location) {
        account.location = {
          city: {
            slug: location.links?.city?.slug ?? '',
            longitude: location.links?.city?.longitude ?? 0,
            latitude: location.links?.city?.latitude ?? 0,
            title: location.links?.city?.title ?? '',
          },
          state: {
            code: location.links?.state?.code ?? '',
          },
          country: {
            code: location.links?.country?.code ?? '',
          },
        };
      }

      account.info = {
        ethnicities: [],
        genders: [],
        categories: [],
        age: [],
      };

      const data = (
        await ApiNoCache.serverGateway(
          `/accounts/${accountId}/profiles?expand=categories,ethnicities,gender,age`,
          getGatewayHeaders(resolvedUrl),
          req,
          res,
        )
      ).data;

      if (data && data.status !== 'error') {
        const profiles = Object.values(data.items);

        profiles.forEach((profile) => {
          account.info.age.push(profile.age);
          account.info.genders.push(profile.gender.id);
          profile.links.categories.items.forEach((category) => {
            account.info.categories.push(category.id);
          });
          profile.links.ethnicities.items.forEach((ethnicity) => {
            account.info.ethnicities.push(ethnicity.id);
          });
        });
      }
    }

    const formattedFilters = await formatFilters(
      true,
      {
        query: { filter: JSON.stringify(query) },
      },
      req,
    );

    const castingCallsResponse = (
      await ApiNoCache.serverGateway(
        `/calls/search${formattedFilters.filterParameters}&limit=24`,
        getGatewayHeaders(resolvedUrl),
        req,
        res,
      )
    ).data;

    content = {
      list: castingCallsResponse,
      filter: formattedFilters.filter,
    };
  }

  if (!content) {
    content = await Api.serverAPIRoute(
      `/castingcalls/list?filter=${JSON.stringify(query)}`,
      null,
      getHeaders(req, resolvedUrl),
    );
  }

  if (content.success === false) {
    // intentionally
    return {
      notFound: true,
    };
  }

  if (!isValidStaticPart(query, content.filter.seo)) {
    return {
      notFound: true,
    };
  }

  const page = parseInt(FilterService.getPage(content.filter.data));
  const count = content?.list?.data?.cc_active_count || 0;
  const limit = 24;
  const maximumPage = Math.ceil(count / limit);

  if (maximumPage && page > maximumPage) {
    return {
      redirect: {
        destination: req.url
          .replace(
            `-page-${page}`,
            maximumPage === 1 ? `` : `-page-${maximumPage}`,
          )
          .replace(
            `/page-${page}`,
            maximumPage === 1 ? `` : `/page-${maximumPage}`,
          ),
        permanent: false,
      },
    };
  }

  if (page <= 0 || isNaN(page)) {
    return {
      notFound: true,
    };
  }

  if (page === 1 && req.url.includes(`page-1`)) {
    return {
      redirect: {
        destination: req.url
          .replace(`-page-${page}`, ``)
          .replace(`/page-${page}`, ``),
        permanent: true,
      },
    };
  }

  const isUserGuest = !CookieService.getAccountCookie(req, res);

  let defaultFilters = await Api.serverAPIRoute(
    `/castingcalls/filters?isTalent=${isTalent}`,
    null,
    getHeaders(req, resolvedUrl),
  );
  const promoResponse = await Api.serverAPIRoute(
    `/promotions?targets=guest`,
    null,
    getHeaders(req, resolvedUrl),
  );

  if (isTalent && account.location) {
    defaultFilters = applyFilterLocation(
      defaultFilters,
      account?.location?.city?.longitude,
      account?.location?.city?.latitude,
      account?.location?.city?.slug,
      account?.location?.city?.title,
      account?.location?.state?.code,
    );
  }

  let registrationDate = null;

  if (isTalent) {
    const status = (
      await ApiNoCache.serverGateway(
        `/clientstatus/info`,
        getGatewayHeaders(resolvedUrl),
        req,
        res,
      )
    ).data;

    registrationDate = status?.info?.registration_date || null;
  }

  const sharedProps = await sharedServerSideProps(req, res);

  return {
    props: {
      ...sharedProps,
      defaultFilters: defaultFilters,
      selectedFilters: content.filter.data,
      castingCalls: content.list.data.calls.map((call) => ({
        ...call,
        applied: !!call.roles.filter((role) => role.attended).length,
      })),
      activeCastingCallsTotal: content?.list?.data?.cc_active_count || 0,
      activeRolesTotal: content?.list?.data?.roles_active_count || 0,
      promos: promoResponse.data,
      announcement: formattedAnnouncement,
      title: generateTitle(content.filter.data, content.filter.seo),
      page,
      seo: content.filter.seo,
      seoPage: {
        ...(await seoPageProps(resolvedUrl)),
        ogImageUrl: `${process.env.baseUrl}/assets/meta/casting-calls.webp`,
      },
      seoCrossLinking: await seoCrossLinkingProps(resolvedUrl),
      cities: content.filter.cities,
      account: account,
      isTalent: isTalent,
      isUserGuest: isUserGuest,
      registrationDate,
    },
  };
}

const CastingCallsSearchPage = ({
  defaultFilters,
  selectedFilters,
  title,
  seo,
  seoPage,
  seoSuffix,
  seoCrossLinking,
  cities,
  castingCalls,
  activeCastingCallsTotal,
  activeRolesTotal,
  account,
  isTalent,
  promos,
  announcement,
  isUserGuest,
  showSeoLinking = true,
  registrationDate,
  page,
}) => {
  const shouldActivateNearMe = (filters) => {
    return (
      isTalent &&
      account.location &&
      account.location.city?.slug === getFilterCity(filters)
    );
  };

  Router.events.on('routeChangeComplete', () => setLoading(false));

  const [loading, setLoading] = useState(false);
  const [mode, setMode] = useState(VIEW_MODE.List);
  const [filters, setFilters] = useState(selectedFilters);
  const [isFilterFormOpen, setIsFilterFormOpen] = useState(false);
  const [breadcrumbs, setBreadcrumbs] = useState(null);
  const [isActiveNearMe, setActiveNearMe] = useState(
    shouldActivateNearMe(filters),
  );
  const [hasFilterChanged, setHasFilterChanged] = useState(
    valuesDiffer(defaultFilters, filters),
  );

  const router = useRouter();

  const { accountLevel, isAuthenticated, isAuthenticating } = useAuth();
  const { isDiscountRefreshed, isDiscountBannerEnabled } = useCTA();
  const { isSaleRefreshed, showSale } = useSale();

  useEffect(() => {
    setFilters(
      FilterService.generateFilterFromUrl(selectedFilters, router.query, seo),
    );
    checkDynamics(selectedFilters);
  }, [selectedFilters]); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    checkDynamics(filters);
  }, [filters]); // eslint-disable-line react-hooks/exhaustive-deps

  const checkDynamics = (filtersToCheck) => {
    setHasFilterChanged(valuesDiffer(defaultFilters, filtersToCheck));
    setActiveNearMe(shouldActivateNearMe(filtersToCheck));
    checkBest(filtersToCheck);
  };

  const toggleMode = (val) => {
    setMode(val);
  };

  const toggleFilterForm = () => {
    setIsFilterFormOpen(!isFilterFormOpen);
  };

  const handleFilterElementChangeEvent = async (e) => {
    if (e.target.name === 'best') {
      applyBest(e.target.checked);
    }

    const nextFilter = FilterService.applyElementDataChange(
      e.target.name,
      e.target.value,
      filters,
    );

    checkDynamics(nextFilter);
    setFilters(nextFilter);
  };

  const checkBest = (nextFilter) => {
    if (!isTalent) {
      return;
    }

    let upcoming = JSON.parse(JSON.stringify(nextFilter));

    upcoming = applyFilterEthnicities(upcoming, account.info.ethnicities);
    upcoming = applyFilterCategories(upcoming, account.info.categories);
    upcoming = applyFilterGenders(upcoming, account.info.genders);
    upcoming = applyFilterAge(upcoming, account.info.age);
    upcoming = applyFilterLocation(
      upcoming,
      account?.location?.city?.longitude,
      account?.location?.city?.latitude,
      account?.location?.city?.slug,
      account?.location?.city?.title,
      account?.location?.state?.code,
    );

    applyBestMatch(nextFilter, !valuesDiffer(upcoming, nextFilter));
    setFilters(nextFilter);
  };

  const applyBest = (value) => {
    let upcoming = JSON.parse(JSON.stringify(filters));

    if (isActiveNearMe || value) {
      upcoming = applyFilterLocation(
        filters,
        account?.location?.city?.longitude,
        account?.location?.city?.latitude,
        account?.location?.city?.slug,
        account?.location?.city?.title,
        account?.location?.state?.code,
      );
    }

    upcoming = applyFilterEthnicities(
      upcoming,
      value ? account.info.ethnicities : [],
    );
    upcoming = applyFilterCategories(
      upcoming,
      value ? account.info.categories : [],
    );
    upcoming = applyFilterGenders(upcoming, value ? account.info.genders : []);
    upcoming = applyFilterAge(upcoming, value ? account.info.age : []);

    setFilters(upcoming);
  };

  const applyPagination = (event, value) => {
    event.preventDefault();
    applyFilter(
      router,
      setFilters,
      setLoading,
      checkDynamics,
      FilterService.applyPage(filters, value),
      seo,
      value,
    );
  };

  const applySubmit = () => {
    applyFilter(
      router,
      setFilters,
      setLoading,
      checkDynamics,
      FilterService.applyPage(filters, 1),
      seo,
    );
    toggleFilterForm();
  };

  const applyReset = () => {
    setIsFilterFormOpen(false);
    applyFilter(
      router,
      setFilters,
      setLoading,
      checkDynamics,
      defaultFilters,
      seo,
    );
    setActiveNearMe(shouldActivateNearMe(defaultFilters));
  };

  const toggleNearMe = () => {
    let upcoming = applyEmptyLocation(filters);

    upcoming = applyDisableNearMe(upcoming);

    checkBest(upcoming);
    applyFilter(router, setFilters, setLoading, checkDynamics, upcoming, seo);
  };

  useEffect(() => {
    Amp.track(Amp.events.viewCastingCallList, {
      page,
    });
  }, [page]);

  useEffect(() => {
    let crumbs = [
      {
        text: 'Casting Calls',
      },
    ];

    if (router.query.static && !/^page-/.test(router.query.static)) {
      crumbs = [
        {
          text: 'Casting Calls',
          href: '/castingcalls',
        },
        {
          text:
            seoPage && seoPage.h1
              ? seoPage.h1
              : title.replace('Casting Calls - ', ''),
        },
      ];
    }

    setBreadcrumbs(crumbs);
  }, [router.query.static]);

  // Browser back button click after login
  useEffect(() => {
    router.beforePopState(({ as }) => {
      const currentPath = router.asPath;

      if (as !== currentPath && isAuthenticated && as === '/') {
        router.replace(currentPath);

        return false;
      }

      return true;
    });

    return () => {
      router.beforePopState(() => true);
    };
  }, [router, isAuthenticated]);

  const { initiateLiveChat } = useLiveChat();

  useEffect(() => {
    const isFirstTimeVisitor = CookieService.getFirstTimeVisitorCookie();

    let liveChatGroup = LIVE_CHAT_GROUP.AllcastingCalls;

    let liveChatWait = isFirstTimeVisitor
      ? LIVE_CHAT_IDLE_TIMEOUT.Medium
      : LIVE_CHAT_IDLE_TIMEOUT.Long;

    if (
      accountLevel?.isBasic &&
      getDaysFromDate(registrationDate) > 2 &&
      !isFirstTimeVisitor
    ) {
      liveChatGroup = LIVE_CHAT_GROUP.AllcastingSales;
      liveChatWait = LIVE_CHAT_IDLE_TIMEOUT.Medium;
    }

    const liveChatTimeout = setTimeout(
      () => initiateLiveChat(true, liveChatGroup),
      liveChatWait,
    );

    return () => clearTimeout(liveChatTimeout);
  }, [isAuthenticated]);

  const onToggleFilterForm = () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'filter results',
      scope: Amp.element.scope.castingCallListPage,
      type: Amp.element.type.button,
    });
    toggleFilterForm();
  };

  return (
    <MainLayout
      isDefaultHeaderVisible
      isMobileMenuVisible
      isUserMenuVisible
      isSaleHeaderVisible
      isMobileSubscribeHeaderVisible
    >
      <PageLayout>
        <FilterMobileHeader
          onMobileFilterToggle={onToggleFilterForm}
          isFilterFormOpen={isFilterFormOpen}
          showReset={hasFilterChanged}
          resetCallback={applyReset}
        />
        {isUserGuest && <Promo promos={promos} />}
        {breadcrumbs && (
          <div
            className={cn(styles['breadcrumbs'], styles['breadcrumbs-mobile'])}
          >
            <Breadcrumbs crumbs={breadcrumbs} />
          </div>
        )}
        {announcement && <AnnouncementBanner announcement={announcement} />}
        <ArticleBanner />
        {breadcrumbs && (
          <div
            className={cn(styles['breadcrumbs'], styles['breadcrumbs-desktop'])}
          >
            <Breadcrumbs crumbs={breadcrumbs} />
          </div>
        )}
        {!isAuthenticating && !isAuthenticated && (
          <CallToActionBlock position={Amp.element.position.top} />
        )}
        {!isAuthenticating &&
          isAuthenticated &&
          isDiscountRefreshed &&
          !isDiscountBannerEnabled &&
          !accountLevel?.isPaidOrDelayed &&
          isSaleRefreshed &&
          !showSale && (
            <CallToActionBlock position={Amp.element.position.top} />
          )}
        <div className={styles.heading}>
          <Seo
            seoPage={seoPage}
            defaultTitle={`${title}`}
            defaultDescription={`《 allcasting 》 ${title} ✪ here! Auditions for everyone: we need talented people. Apply for castings and get hired. ★ Start To Be a Star Today!`}
            suffix={seoSuffix ?? ``}
          />
          {seoPage?.text_top && (
            <div
              className={styles.text}
              dangerouslySetInnerHTML={{ __html: seoPage.text_top }}
            />
          )}
          <div className={styles.title}>
            {activeRolesTotal?.toLocaleString('en-US')} roles found
            <span className={styles.subtitle}>
              in {activeCastingCallsTotal?.toLocaleString('en-US')} active
              casting calls
            </span>
            {isActiveNearMe && (
              <span className={styles.tag} onClick={toggleNearMe}>
                Near Me
                <IconClose className={styles.cancel} />
              </span>
            )}
          </div>
          <div className={styles.control}>
            {hasFilterChanged && (
              <div onClick={applyReset} className={styles.reset}>
                reset filters
              </div>
            )}
            <div>
              <ItemDisplayMode mode={mode} onClick={toggleMode} />
            </div>
          </div>
        </div>
        <div className={styles.content}>
          <div className={styles.left}>
            {loading && (
              <div className={styles.loading}>
                <Loading />
              </div>
            )}
            <CastingCallList mode={mode} items={castingCalls} />
            <FilterPaginator
              total={activeCastingCallsTotal}
              handleChange={applyPagination}
              filters={filters}
              perPage={24}
              seo={seo}
              urlPrefix={'castingcalls'}
            />
            {showSeoLinking && <SeoLinking seoCrossLinking={seoCrossLinking} />}
            {seoPage?.text_bottom && (
              <div
                className={styles['seo-bottom-block']}
                dangerouslySetInnerHTML={{ __html: seoPage.text_bottom }}
              />
            )}
          </div>
          <div
            className={cn(styles.right, {
              [styles.open]: isFilterFormOpen,
              [styles.sale]: showSale,
              [styles['upgrade-header-visible']]:
                !showSale &&
                accountLevel &&
                (!accountLevel?.isPaidOrDelayed ||
                  !!accountLevel?.canUpgradeExistingSubscription),
            })}
          >
            <Filter
              type="castingcalls"
              data={filters}
              cities={cities}
              onFilterChange={handleFilterElementChangeEvent}
              onFilterSubmit={applySubmit}
            />
          </div>
        </div>
      </PageLayout>
    </MainLayout>
  );
};

export default memo(CastingCallsSearchPage);
