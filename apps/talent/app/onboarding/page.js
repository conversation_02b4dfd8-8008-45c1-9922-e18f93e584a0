import OnboardingClient from './_client';
import { cookies, headers } from 'next/headers';
import { CookieServiceServer } from '@services/cookieServiceServer';
import Api from '@services/api';

export const metadata = {
  title: 'Onboarding',
};

export default async function OnboardingPage() {
  const cookieStore = await cookies();
  const headerStore = await headers();
  const accountId = CookieServiceServer.getAccountCookie(cookieStore);

  const data = await Api.serverside(
    `/accounts/${accountId}/prices`,
    cookieStore,
    headerStore,
    '/onboarding',
  );

  return (
    <OnboardingClient
      plans={data?.prices?.filter((price) => price.visible) || []}
    />
  );
}
