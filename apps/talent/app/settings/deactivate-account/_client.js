'use client';
import React, { useCallback, useEffect, useState } from 'react';
import styles from '@styles/deactivate-account.module.scss';
import { useRouter } from 'next/navigation';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import dayjs from 'dayjs';
import {
  Button,
  Checkbox,
  HeaderMobile,
  Input,
  Loading,
  MainLayout,
  ModalContentProtection,
} from '@components';
import { useNotifications } from '@contexts/NotificationContext';
import { useAuth } from '@contexts/AuthContext';
import { useSale } from '@contexts/SaleContext';
import { ErrorMessage } from '@constants/form';
import Api from '@services/api';
import { Amp } from '@services/amp';

const DeactivateAccountClient = ({ identifier, endDate, isPasswordSet }) => {
  const [showContentProtectionForm, setContentProtectionForm] = useState(false);
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const { setNotification } = useNotifications();
  const { accountId, authLogoutWithTracking } = useAuth();
  const { stopSale } = useSale();

  useEffect(() => {
    setContentProtectionForm(isPasswordSet);
  }, [isPasswordSet]);

  const formik = useFormik({
    initialValues: {
      reason: '',
      discontinue: !endDate,
      agree: false,
    },
    onSubmit: async (values) => {
      setLoading(true);

      const discontinueSubscriptionResponse = await Api.clientside(
        `/accounts/${accountId}?reason=${values.reason}`,
        {
          method: 'DELETE',
        },
      );

      if (discontinueSubscriptionResponse.status !== 'ok') {
        setNotification({
          type: 'error',
          timeout: '5000',
          message:
            discontinueSubscriptionResponse.message || ErrorMessage.Unexpected,
        });
      } else {
        Amp.trackAsync(Amp.events.deactivateAccount)
          .catch((error) => console.error('Tracking error:', error))
          .finally(() => {
            stopSale();
            authLogoutWithTracking();
          });
      }

      setLoading(false);
    },
    validationSchema: Yup.object({
      agree: Yup.bool().oneOf([true]),
      discontinue: Yup.bool().oneOf([true]),
      reason: Yup.string().required(ErrorMessage.ReasonRequired),
    }),
  });

  const navigateBack = useCallback(() => {
    router.back();
  }, [router]);

  const navigateBackToSettings = useCallback(() => {
    router.push('/settings');
  }, [router]);

  const closeContentProtectionForm = useCallback(() => {
    setContentProtectionForm(false);
  }, []);

  useEffect(() => {
    Amp.track(Amp.events.viewSettings, {
      name: 'deactivate account',
    });
  }, []);

  return (
    <MainLayout
      isDefaultHeaderVisible
      isSaleHeaderVisible
      isMobileMenuVisible
      isUserMenuVisible
    >
      <HeaderMobile
        onClose={navigateBack}
        title="Deactivate your account"
        hideOnTablet
      />
      <section className={styles['deactivate-account-section']}>
        <div className={styles['deactivate-account-container']}>
          <h1 className={styles['deactivate-account-title']}>
            Deactivate your account
          </h1>
          <div className={styles['deactivate-account-description']}>
            <p>
              Keep in mind that this <b>action is irreversible</b> and
              information, including your photo, video and audio files will be
              lost.
            </p>
            {endDate && (
              <p>
                Your current subscription expires on{' '}
                {dayjs(endDate).format('M/D/YY')}. Upon deactivation, you will
                no longer be able to access your account.
              </p>
            )}
          </div>
          <form
            className={styles['deactivate-account-form']}
            onSubmit={formik.handleSubmit}
          >
            <Input
              name="reason"
              placeholder="Please, provide the reason for deactivation"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.reason}
              isTouched={formik.touched.reason}
              error={formik.errors.reason}
            />
            <div className={styles['deactivate-account-form-row']}>
              {endDate && (
                <Checkbox
                  name="discontinue"
                  onChange={formik.handleChange}
                  value={formik.values.discontinue}
                  error={formik.errors.discontinue}
                  onBlur={formik.handleBlur}
                  isTouched={formik.touched.discontinue}
                >
                  <span className={styles['deactivate-account-form-label']}>
                    Discontinue my subscription
                  </span>
                </Checkbox>
              )}
              <Checkbox
                name="agree"
                onChange={formik.handleChange}
                value={formik.values.agree}
                error={formik.errors.agree}
                onBlur={formik.handleBlur}
                isTouched={formik.touched.agree}
              >
                <span className={styles['deactivate-account-form-label']}>
                  I understand the consequences
                </span>
              </Checkbox>
            </div>
            {loading ? (
              <Loading minHeight="40px" padding="0" />
            ) : (
              <>
                <Button
                  onClick={navigateBackToSettings}
                  label="Give another try"
                  color="green-gradient"
                  minWidth="280px"
                />
                <button
                  disabled={!(formik.isValid && formik.dirty)}
                  type="submit"
                  className={styles['deactivate-button']}
                >
                  Deactivate my account
                </button>
              </>
            )}
          </form>
        </div>
        {showContentProtectionForm && (
          <ModalContentProtection
            identifier={identifier}
            onClose={closeContentProtectionForm}
          />
        )}
      </section>
    </MainLayout>
  );
};

export default DeactivateAccountClient;
