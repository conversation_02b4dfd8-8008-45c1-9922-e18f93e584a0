'use client';
import React, { memo, useState } from 'react';
import { Button, Loading, Modal } from '@components';
import styles from './ModalDeleteConversation.module.scss';
import Api from '@services/api';
import { useNotifications } from '@contexts/NotificationContext';
import { ErrorMessage } from '@constants/form';
import { useAuth } from '@contexts/AuthContext';
import { useMessage } from '@contexts/MessageContext';

const ModalDeleteConversation = ({ onClose, id, partnerProfileId }) => {
  const [loading, setLoading] = useState(false);

  const { profileId } = useAuth();
  const { setNotification } = useNotifications();
  const { onDeleteConversation } = useMessage();

  const onDelete = async () => {
    setLoading(true);
    const body = new FormData();

    body.append('status', '0');
    body.append('profile', profileId);

    const { message, status } = await Api.clientside(`/conversations/${id}`, {
      method: 'PATCH',
      body,
    });

    if (status !== 'ok') {
      setNotification({
        type: 'error',
        timeout: '5000',
        message: message || ErrorMessage.Unexpected,
      });
    } else {
      onDeleteConversation(partnerProfileId);
    }

    setLoading(false);
    onClose();
  };

  return (
    <Modal
      backdropClose={!loading}
      onClose={onClose}
      classNameContainer={styles.container}
      classNameOverlay={styles.overlay}
      showDefaultLayout={false}
      showCloseButton={!loading}
    >
      {loading ? (
        <div className={styles['loading-container']}>
          <div>Removing conversation...</div>
          <Loading minHeight="40px" padding="0" />
        </div>
      ) : (
        <>
          <div className={styles.title}>Are you sure?</div>
          <div className={styles.description}>
            You will no longer be able to view this conversation.
            <br />
            <b>Note:</b> It will remain accessible to the other participant.
          </div>
          <div className={styles.actions}>
            <Button
              minWidth="130px"
              color="plain-blue"
              label="Cancel"
              onClick={onClose}
            />
            <Button
              minWidth="130px"
              color="plain-red"
              label="Delete"
              onClick={onDelete}
            />
          </div>
        </>
      )}
    </Modal>
  );
};

export default memo(ModalDeleteConversation);
