const { S3Client, PutObjectCommand } = require('@aws-sdk/client-s3');
const fs = require('fs');
const path = require('path');
const mime = require('mime-types');
const { promisify } = require('util');
const readdir = promisify(fs.readdir);
const stat = promisify(fs.stat);

// Configuration
const BUCKET_NAME = process.env.S3_BUCKET;
const PROJECT_NAME = 'talent'; // e.g., 'public', 'talent', or 'pro'
const VERSION = process.env.NEXT_PUBLIC_VERSION;

if (!PROJECT_NAME) {
  console.error('PROJECT_NAME environment variable must be set');
  process.exit(1);
}

if (!VERSION) {
  console.error('VERSION environment variable must be set');
  process.exit(1);
}

// Initialize S3 client
const isEks = process.env.EKS === 'true';

let s3Client;
if (isEks) {
  console.log('EKS mode enabled: using default IAM-based authentication');
  s3Client = new S3Client({
    region: process.env.AWS_REGION || 'eu-central-1',
  });
} else if (process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY) {
  console.log('Using explicit AWS credentials from environment variables');
  s3Client = new S3Client({
    region: process.env.AWS_REGION || 'eu-central-1',
    credentials: {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    },
  });
} else {
  console.error(
    'Missing AWS credentials. Set EKS=true or provide AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY.',
  );
  process.exit(1);
}

// Function to upload a file to S3
async function uploadFile(filePath, s3Key) {
  const fileContent = fs.readFileSync(filePath);
  const contentType = mime.lookup(filePath) || 'application/octet-stream';

  const params = {
    Bucket: BUCKET_NAME,
    Key: s3Key,
    Body: fileContent,
    ContentType: contentType,
    CacheControl: 'public, max-age=31536000', // Cache for 1 year
  };

  await s3Client.send(new PutObjectCommand(params));
  console.log(`Successfully uploaded ${filePath} to ${s3Key}`);
}

// Function to recursively get all files in a directory
async function getAllFiles(dirPath, arrayOfFiles = []) {
  const files = await readdir(dirPath);

  for (const file of files) {
    const filePath = path.join(dirPath, file);
    const fileStat = await stat(filePath);

    if (fileStat.isDirectory()) {
      arrayOfFiles = await getAllFiles(filePath, arrayOfFiles);
    } else {
      arrayOfFiles.push(filePath);
    }
  }

  return arrayOfFiles;
}

// Main function to upload assets
async function uploadAssets() {
  try {
    // In Dockerfile, we're at the app root (/app)
    // Upload static files
    const staticDir = path.join(process.cwd(), '.next/static');

    if (fs.existsSync(staticDir)) {
      const staticFiles = await getAllFiles(staticDir);

      console.log(
        `Found ${staticFiles.length} static files to upload for ${PROJECT_NAME}`,
      );

      for (const file of staticFiles) {
        const relativePath = path.relative(staticDir, file).replace(/\\/g, '/');
        const s3Key = `${PROJECT_NAME}/_next/static/${relativePath}`;
        await uploadFile(file, s3Key);
      }
    } else {
      console.warn(`Static directory not found: ${staticDir}`);
    }

    // Upload public assets with versioning
    const assetsDir = path.join(process.cwd(), 'public/assets');

    if (fs.existsSync(assetsDir)) {
      const assetFiles = await getAllFiles(assetsDir);

      console.log(
        `Found ${assetFiles.length} public asset files to upload for ${PROJECT_NAME}`,
      );

      for (const file of assetFiles) {
        const relativePath = path.relative(assetsDir, file).replace(/\\/g, '/');
        const s3Key = `${PROJECT_NAME}/assets/${VERSION}/${relativePath}`;
        await uploadFile(file, s3Key);
      }
    } else {
      console.warn(`Assets directory not found: ${assetsDir}`);
    }

    console.log(`Upload completed successfully for ${PROJECT_NAME}`);
  } catch (error) {
    console.error('Error uploading assets:', error);
    process.exit(1);
  }
}

// Run the upload process
uploadAssets();
