'use client';
import React, { useState } from 'react';
import styles from '@styles/upgrade-12.module.scss';
import Link from 'next/link';
import { Button, Loading, MainLayout, Modal, SpecialTerms } from '@components';
import { useAuth } from '@contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { CookieService } from '@services/cookieService';
import Api from '@services/api';
import { calculateSavings } from '@utils/calculateSavings';
import { upgradeFeatures } from '@constants/payment';

const Upgrade12Client = ({ memberLevelId, period, baseMonthPrice, price }) => {
  const [loading, setLoading] = useState(false);
  const [showSpecialTerms, setShowSpecialTerms] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const { accountId, getIsProfileOutsideUSA } = useAuth();
  const router = useRouter();
  const isProfileOutsideUSA = getIsProfileOutsideUSA();

  const upgradeAndSave = async () => {
    setLoading(true);

    const response = await upgrade();

    if (response.status === 'ok') {
      window.sessionStorage.setItem('invoiceHash', response.invoice_hash);
      CookieService.setShowUpgradeCheckoutSuccess(true);
      router.push('/checkout/success');
    } else {
      setErrorMessage(response.message);
    }

    setLoading(false);
  };

  const upgrade = async () => {
    const body = new FormData();

    body.append('level_id', memberLevelId);
    body.append('payment_method', 'card');

    return await Api.clientside(`/accounts/${accountId}/levels/upgrade`, {
      body,
      method: 'POST',
    });
  };

  const toggleShowSpecialTerms = (e) => {
    e?.preventDefault();
    setShowSpecialTerms(!showSpecialTerms);
  };

  return (
    <MainLayout isLogoHeaderVisible>
      <section className={styles['upgrade-section']}>
        <div className={styles['upgrade-header']}>
          <h1 className={styles['upgrade-header-title']}>
            Make the switch and save big!
          </h1>
          <p className={styles['upgrade-header-description']}>
            Upgrade to our annual plan for only{' '}
            <span className={styles['text-accent']}>
              ${price.toFixed(2)}
              {isProfileOutsideUSA && <>USD</>}
            </span>{' '}
            and unlock unlimited access to premium features.
          </p>
        </div>
        <div className={styles['upgrade-plan-container']}>
          <div className={styles['upgrade-plan']}>
            <div className={styles['best-deal-tag']}>Best deal</div>
            <div className={styles['upgrade-plan-period-container']}>
              <span>{`${period} month${period === 1 ? '' : 's'}`}</span>
            </div>
            <div className={styles['upgrade-plan-price-container']}>
              <span className={styles['upgrade-plan-price-base']}>
                ${baseMonthPrice}
              </span>
              <span className={styles['upgrade-plan-price']}>
                ${(price / period).toFixed(2)}
                {isProfileOutsideUSA && (
                  <span className={styles['upgrade-plan-currency']}>USD</span>
                )}
              </span>
              <span className={styles['upgrade-plan-price-period']}>
                per month
              </span>
            </div>
            <div className={styles['upgrade-plan-savings-container']}>
              <div>
                <span>Save</span>
                <span className={styles['upgrade-plan-savings']}>
                  {' '}
                  {calculateSavings(baseMonthPrice, period, price)}%
                </span>
              </div>
            </div>
          </div>
          {errorMessage && (
            <div className={styles['error-message']}>
              {errorMessage} Please{' '}
              <Link href={'/checkout-12'} className={styles['link']}>
                update your payment method
              </Link>
              .
            </div>
          )}
          <div className={styles['upgrade-action-container']}>
            {loading ? (
              <Loading />
            ) : (
              <>
                <Button
                  label="Upgrade & Save"
                  color="green-gradient"
                  minWidth="220px"
                  onClick={upgradeAndSave}
                />
                <Link
                  className={styles.link}
                  href={`${process.env.publicUrl}/castingcalls`}
                >
                  Decide later
                </Link>
              </>
            )}
            <div className={styles['upgrade-features']}>
              {upgradeFeatures.map(({ title, description }, i) => (
                <div key={i} className={styles['upgrade-feature']}>
                  <span className={styles.dot}></span>
                  <span>
                    <span className={styles['upgrade-feature-title']}>
                      {title}
                    </span>
                    {description}
                  </span>
                </div>
              ))}
            </div>
          </div>
          <div className={styles.divider} />
          <div className={styles['disclaimer-container']}>
            <p>
              Disclaimer: allcasting is not a talent agency, employer or a
              talent scout; the site is only a venue. Allcasting does not
              promise or facilitate employment. The number of casting calls
              available varies by location, roles available and the level of
              experience required. As with any business, results may vary, and
              will be based on individual capacity, experience, expertise, and
              level of desire. There are no guarantees concerning the level of
              income the user may experience.
            </p>
            <p>
              Trademark Disclaimer: Any product names, logos, brands, and other
              trademarks or images featured or referred to within the
              allcasting.com website are the property of their respective
              trademark holders. These trademark holders are not affiliated with
              allcasting.com, our services, or our websites. They do not sponsor
              or endorse allcasting.com or any of our online products.
            </p>
          </div>
        </div>
        {showSpecialTerms && (
          <Modal backdropClose onClose={toggleShowSpecialTerms}>
            <SpecialTerms />
          </Modal>
        )}
      </section>
    </MainLayout>
  );
};

export default Upgrade12Client;
