'use client';
import React, { memo } from 'react';
import PhoneAndEmail from './Connections/PhoneAndEmail';
import PersonalLink from './Connections/PersonalLink';
import SocialNetworks from './Connections/SocialNetworks';

const ProfileConnections = ({
  isPaidOrDelayed,
  profileId,
  accountId,
  phone,
  email,
  allowPhoneNotifications,
  socialNetworks,
  personalUsername,
  allowIndexingProfile,
  refreshSocialNetworks,
  refreshPersonalUrl,
  clearPersonalUrl,
  refreshProfileDetails,
  refreshUserProfiles,
  refreshTouches,
}) => {
  return (
    <>
      <PhoneAndEmail
        accountId={accountId}
        phone={phone}
        email={email}
        allowPhoneNotifications={allowPhoneNotifications}
        refreshTouches={refreshTouches}
        refreshUserProfiles={refreshUserProfiles}
      />
      <PersonalLink
        isPaidOrDelayed={isPaidOrDelayed}
        profileId={profileId}
        allowIndexingProfile={allowIndexingProfile}
        personalUsername={personalUsername}
        refreshPersonalUrl={refreshPersonalUrl}
        clearPersonalUrl={clearPersonalUrl}
        refreshProfileDetails={refreshProfileDetails}
        refreshUserProfiles={refreshUserProfiles}
      />
      <SocialNetworks
        socialNetworks={socialNetworks}
        profileId={profileId}
        refreshSocialNetworks={refreshSocialNetworks}
      />
    </>
  );
};

export default memo(ProfileConnections);
