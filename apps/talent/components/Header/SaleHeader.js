'use client';
import { memo } from 'react';
import styles from './SaleHeader.module.scss';
import { Countdown } from '@components';
import Link from 'next/link';
import cn from 'classnames';

const SaleHeader = ({
  saleExpirationTime,
  onStopSale,
  isMobileSaleHeaderHidden = false,
}) => {
  return (
    <Link
      href="/upgrade"
      className={cn(styles.link, {
        [styles['mobile-hidden']]: isMobileSaleHeaderHidden,
      })}
    >
      <div className={styles['sale-header']}>
        <div className={styles['sale-header-title-container']}>
          <span className={styles['sale-header-title']}>Sale</span>
          <span className={styles['sale-header-sub-title']}>ends in:</span>
        </div>
        <Countdown
          onCountdownEnd={onStopSale}
          expirationTime={saleExpirationTime}
          horizontalLayout
        />
      </div>
    </Link>
  );
};

export default memo(SaleHeader);
