'use client';
import cn from 'classnames';
import EditIcon from '../../public/assets/icons/icon-edit.svg';
import styles from './Profile.module.scss';

export const EditToggle = ({ isEditing, onEdit, onSave, disabled = false }) => {
  return !isEditing ? (
    <EditButton onClick={onEdit} />
  ) : (
    <SaveButton onClick={onSave} disabled={disabled} />
  );
};

export const EditButton = ({ onClick, className = '' }) => {
  return (
    <button onClick={onClick} className={cn(styles['edit-button'], className)}>
      <EditIcon />
    </button>
  );
};

export const SaveButton = ({
  onClick,
  className = '',
  disabled = false,
  ...rest
}) => {
  return (
    <button
      className={cn(styles['save-button'], className)}
      onClick={onClick}
      disabled={disabled}
      type="button"
      {...rest}
    >
      Save
    </button>
  );
};
