'use client';
import styles from './Hint.module.scss';
import Image from 'next/image';
import React, { memo } from 'react';
import cn from 'classnames';

const Hint = ({
  title,
  children,
  canClose,
  onClose,
  classNameAdditional = '',
  iconFileName = 'icon-important.svg',
  inline = false,
}) => {
  return (
    <div className={cn(styles['container'], styles[classNameAdditional])}>
      <div className={styles['title']}>
        <Image
          className={styles['icon']}
          src={`/assets/icons/${iconFileName}`}
          width={17}
          height={17}
          alt="important icon"
          priority
        />
        &nbsp; {title}
        {inline && <span className={styles['text']}>{children}</span>}
      </div>
      {!inline && <div className={styles['text']}>{children}</div>}
      {canClose && <div onClick={onClose} className={styles['close']} />}
    </div>
  );
};

export default memo(Hint);
