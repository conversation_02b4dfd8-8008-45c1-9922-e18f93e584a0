import ContactsClient from './_client';
import { cookies, headers } from 'next/headers';
import { CookieServiceServer } from '@services/cookieServiceServer';
import Api from '@services/api';

export const metadata = {
  title: 'Contact Information',
};

export default async function ContactsPage() {
  const cookieStore = await cookies();
  const headerStore = await headers();
  const accountId = CookieServiceServer.getAccountCookie(cookieStore);
  const resolvedUrl = '/settings/contacts';

  const data = await Api.serverside(
    `/accounts/${accountId}/touches`,
    cookieStore,
    headerStore,
    resolvedUrl,
  );

  const email = data.links?.email?.value || '';
  const phone = data.links?.phone?.value || '';
  const allowNotifications = data.links?.phone?.opt_outed === false;

  return (
    <ContactsClient
      email={email}
      phone={phone}
      allowNotifications={allowNotifications}
    />
  );
}
