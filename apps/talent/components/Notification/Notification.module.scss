@use '@styles/mixins' as *;
@use '@styles/variables' as *;

.container {
  position: relative;
  display: flex;
  width: 100%;
  margin: 0;
  padding: $space-15 $space-35 $space-15 $space-20;
  justify-content: center;
  align-items: center;

  @include desktop {
    padding: $space-15 $space-60;
  }
}

.success {
  background: $green-100;
  color: white;
}

.info {
  background-image: $gradient-light-blue;
  color: white;
}

.error {
  background: $red-80;
  color: white;
}

.content {
  display: flex;
  justify-content: center;
  align-items: center;
}

.close {
  cursor: pointer;
  position: absolute;
  right: 15px;
  top: 17px;
}
