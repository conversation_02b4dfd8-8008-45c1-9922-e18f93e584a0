@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.settings-container {
  height: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;

  @include tablet {
    display: grid;
    grid-template-rows: 1fr;
    grid-template-columns: 240px 1fr;
  }
}

.settings-sidebar-container {
  display: none;

  @include tablet {
    display: initial;
  }
}

.settings-sidebar-container-mobile {
  display: initial;
  position: absolute;
  padding-top: $space-55;
  z-index: 1;
  width: 100%;
  height: 100%;
  background-color: $white;

  @include tablet {
    display: none;
  }
}

.settings-content {
  position: relative;
  z-index: 0;
  flex: 1;
  display: flex;
}
