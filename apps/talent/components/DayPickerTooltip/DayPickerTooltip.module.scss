@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.tooltip {
  z-index: 102;
}

.tooltip-trigger {
  cursor: pointer;
  position: relative;
  height: fit-content;

  &.active {
    z-index: 102;
  }
}

.tooltip-content {
  box-shadow: $shadow-tooltip;
  background-color: $white;
  padding: 15px 10px 0;
  max-width: 100vw;
  border-radius: 12px;
}

div.modal-container {
  border-radius: 12px;
  max-width: 100vw;
}

.modal-content {
  display: flex;
  justify-content: center;
  align-self: center;
  padding: 15px 10px 0;
}

.tooltip-overlay {
  position: fixed;
  inset: 0;
  height: 100%;
  width: 100%;
  z-index: 101;
  display: flex;
  justify-content: center;
  align-items: center;
}
