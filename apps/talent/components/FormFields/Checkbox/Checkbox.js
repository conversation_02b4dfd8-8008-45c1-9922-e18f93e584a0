'use client';
import { memo } from 'react';
import styles from './Checkbox.module.scss';
import cn from 'classnames';

const Checkbox = ({
  children,
  onChange,
  value,
  name,
  error,
  onBlur,
  isTouched,
  className,
  disabled = false,
}) => {
  const showError = isTouched && error;

  return (
    <label
      className={cn(styles.container, className, {
        [styles.error]: showError,
      })}
    >
      <input
        name={name}
        type="checkbox"
        onChange={onChange}
        checked={value}
        onBlur={onBlur}
        disabled={disabled}
      />
      <span className={styles.checkmark} id={name} />
      {children}
    </label>
  );
};

export default memo(Checkbox);
