'use client';
import React, { memo, useRef } from 'react';
import styles from './PhotoUploadTemplate.module.scss';
import Image from 'next/image';
import { PhotoUploadTool } from '@components';
import cn from 'classnames';

const PhotoUploadTemplate = ({
  title = '',
  onFileInputChange,
  previewImage,
  placeholderImageSrc,
  className = '',
}) => {
  const photoUploadToolRef = useRef(null);

  const selectFile = () => {
    photoUploadToolRef.current.selectFile();
  };

  return (
    <PhotoUploadTool
      ref={photoUploadToolRef}
      onFileInputChange={onFileInputChange}
    >
      <div className={cn(styles['photo-upload-container'], styles[className])}>
        <div className={styles['photo-upload']}>
          {title && (
            <span className={styles['photo-upload-title']}>{title}</span>
          )}
          <div className={styles['photo-upload-placeholder']}>
            <div
              className={cn(styles['photo-upload-image-container'], {
                [styles.preview]: !!previewImage?.url,
              })}
            >
              <div
                className={cn(styles['photo-upload-image'], {
                  [styles.preview]: !!previewImage?.url,
                })}
                style={{
                  backgroundImage: `url('${
                    previewImage?.url || placeholderImageSrc
                  }')`,
                }}
              ></div>
            </div>

            <button
              className={styles['photo-upload-button']}
              onClick={selectFile}
            >
              {previewImage ? 'Reupload' : 'Upload'}
            </button>
          </div>
          {previewImage ? (
            <div className={styles['photo-upload-success-container']}>
              <Image
                src={'/assets/icons/icon-checkmark-success.svg'}
                width={14}
                height={14}
                alt="icon"
              />
              <span className={styles['photo-upload-success-text']}>
                Your <b>Title Photo</b> has been uploaded
              </span>
            </div>
          ) : (
            <span className={styles['photo-upload-description']}>
              Min resolution: <b>600x800 px</b>
            </span>
          )}
        </div>
      </div>
    </PhotoUploadTool>
  );
};

export default memo(PhotoUploadTemplate);
