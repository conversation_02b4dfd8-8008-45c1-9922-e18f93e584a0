'use client';
import React, { memo } from 'react';
import styles from './OutgoingMessage.module.scss';
import cn from 'classnames';
import dayjs from 'dayjs';
import { IncomingAttachment } from '../IncomingAttachment/IncomingAttachment';
import IconTrash from '../../../public/assets/icons/icon-trash.svg';
import { MESSAGE_LINK_REGEX } from '@constants/form';
import Link from 'next/link';
import { decodeHtml } from '@utils/messageHelpers';

const OutgoingMessage = ({
  message: { links = {}, created, content, id } = {},
  openImageAttachment,
  onDeleteMessage,
}) => {
  const files = links.files?.items || [];
  const messageFragments = content.split(MESSAGE_LINK_REGEX);

  return (
    <div className={styles['message-row']}>
      <div className={cn(styles['message-wrapper'], styles['outgoing'])}>
        <div className={styles['message-body']}>
          <div className={styles['message-meta']}>
            <span>You</span>
            <span>{dayjs(created).format('h:mm A')}</span>
          </div>
          <div className={styles['message-content']}>
            <div className={styles['message-interaction']}>
              <div
                className={styles['interaction-btn']}
                onClick={() => onDeleteMessage(id)}
              >
                <IconTrash />
                <span className={styles['interaction-btn-text']}>
                  Delete message
                </span>
              </div>
            </div>
            <div className={styles.message}>
              {messageFragments.map((fragment, index) =>
                MESSAGE_LINK_REGEX.test(fragment) ? (
                  <Link
                    key={index}
                    href={fragment}
                    target="_blank"
                    rel="noreferrer"
                    className={styles['inline-link']}
                  >
                    {fragment}
                  </Link>
                ) : (
                  <span key={index}>{decodeHtml(fragment)}</span>
                ),
              )}
            </div>
          </div>
          {files.length > 0 && (
            <div className={styles['attachment-container']}>
              {files.map((attachment, index) => (
                <div className={styles.attachment} key={`attachment-${index}`}>
                  <IncomingAttachment
                    attachment={attachment}
                    openImageAttachment={openImageAttachment}
                  />
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default memo(OutgoingMessage);
