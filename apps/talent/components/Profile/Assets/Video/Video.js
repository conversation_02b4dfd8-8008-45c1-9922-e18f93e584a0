'use client';
import styles from '../../Profile.module.scss';
import React, { memo } from 'react';
import IconCross from '/public/assets/icons/icon-cross.svg';
import declineStyles from './Video.module.scss';
import { EditButton } from '../../EditToggle';
import { YoutubePlayer } from '@components';

const Video = ({
  type = 'default',
  id,
  link_id,
  title,
  onEdit,
  declineReason,
}) => {
  return (
    <div key={id} className={styles['video-card']}>
      <div className={styles['video-container']}>
        <YoutubePlayer videoId={link_id} videoClassName={styles.video} />
        {declineReason && (
          <div className={declineStyles['decline-reason']}>
            <span className={declineStyles['decline-icon']}>
              <IconCross />
            </span>{' '}
            {declineReason}
          </div>
        )}
      </div>

      <p className={styles['profile-video-title']}>
        {type === 'slate' && <strong>Slate</strong>}
        {type === 'demo_reel' && <strong>Demo Reel</strong>}
        {type === 'ugc_demo_reel' && <strong>UGC demo reel</strong>}
        {type === 'default' && title}
        <EditButton onClick={onEdit} />
      </p>
    </div>
  );
};

export default memo(Video);
