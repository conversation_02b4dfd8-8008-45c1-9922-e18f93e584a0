.subscribe-btn-animation-highlight {
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 100%;
    right: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    animation: highlight-1 20s 100 ease-in-out;
    background-color: #ffffff70;
    width: 2.5em;
    height: 2.5em;
    border-radius: 50%;
    transform-origin: top center;
  }

  &::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    animation: highlight-2 20s 0.1s 100 ease-in-out;
    background-color: #ffffff70;
    width: 2.5em;
    height: 2.5em;
    border-radius: 50%;
    transform-origin: top center;
  }
}

@keyframes highlight-1 {
  90% {
    transform: translate(0, 0);
  }

  92% {
    transform: translate(120%, -100%);
  }

  96% {
    transform: translate(400%, -105%);
  }

  98% {
    transform: translate(120%, -110%);
  }
}

@keyframes highlight-2 {
  90% {
    transform: translate(0, 0);
  }

  92% {
    transform: translate(-100%, -100%);
  }

  96% {
    transform: translate(-450%, -110%);
  }

  98% {
    transform: translate(-100%, -100%);
  }
}
