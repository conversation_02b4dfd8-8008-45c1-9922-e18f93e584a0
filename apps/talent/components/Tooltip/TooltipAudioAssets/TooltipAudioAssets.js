'use client';
import React, { memo } from 'react';
import styles from './TooltipAudioAssets.module.scss';
import { allowedFileTypes, maxSizeMB } from '@constants/audio';

const TooltipAudioAssets = () => {
  return (
    <div className={styles['tooltip-container']}>
      <div className={styles['tooltip-text']}>
        Elevate your audio experience with our audiophile-friendly uploading
        feature!
      </div>
      <div className={styles['tooltip-content']}>
        <span>
          <b>Max size:</b> {maxSizeMB} MB
        </span>
        <span>
          <b>File types:</b> {allowedFileTypes.join(', ')}
        </span>
      </div>
    </div>
  );
};

export default memo(TooltipAudioAssets);
