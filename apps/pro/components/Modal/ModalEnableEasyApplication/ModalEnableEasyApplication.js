'use client';
import React, { useState } from 'react';
import styles from './ModalEnableEasyApplication.module.scss';
import Modal from '../Modal';
import Button from '../../Button/Button';
import Api from '@services/api';
import Loading from '../../Loading/Loading';
import { useNotifications } from '@contexts/NotificationContext';
import { ErrorMessage } from '@constants/form';
import * as Sentry from '@sentry/nextjs';

const ModalEnableEasyApplication = ({ onClose, id, onTypeChange }) => {
  const [isLoading, setIsLoading] = useState(false);
  const { setNotification } = useNotifications();

  const handleConfirm = async () => {
    try {
      setIsLoading(true);
      const body = new FormData();

      body.append('type', 'web');

      const response = await Api.clientside(`/casting-calls/${id}`, {
        body,
        method: 'PATCH',
      });

      if (response.status === 'ok') {
        onTypeChange();
      } else {
        setNotification({
          type: 'error',
          timeout: '5000',
          message: response.message || ErrorMessage.Unexpected,
        });
      }
    } catch (error) {
      Sentry.captureException(error);
      setNotification({
        type: 'error',
        timeout: '5000',
        message: ErrorMessage.Unexpected,
      });
    } finally {
      setIsLoading(false);
      onClose();
    }
  };

  return (
    <Modal
      backdropClose
      onClose={onClose}
      classNameContainer={styles['modal-container']}
    >
      <div className={styles.container}>
        <div className={styles.title}>Streamline Your Application Process</div>
        <div className={styles['application-process-container']}>
          <p>
            Switching to easy application will update how you receive
            submissions.
          </p>
          <ul>
            <li>
              <strong>What’s Changing:</strong> New talent submissions will no
              longer be sent to your email.
            </li>
            <li>
              <strong>What to Expect:</strong> All new submissions will be
              accessible directly on your allcasting dashboard.
            </li>
          </ul>
          <p>
            Click <strong>Confirm</strong> to make the change.
          </p>
        </div>
        {isLoading ? (
          <Loading minHeight="50px" padding="20px 0 20px" />
        ) : (
          <div className={styles['button-container']}>
            <Button
              disabled={isLoading}
              onClick={onClose}
              label="Cancel"
              kind="secondary"
              minWidth="200px"
            />
            <Button
              disabled={isLoading}
              onClick={handleConfirm}
              shadow={false}
              label="Confirm"
              minWidth="200px"
            />
          </div>
        )}
      </div>
    </Modal>
  );
};

export default ModalEnableEasyApplication;
