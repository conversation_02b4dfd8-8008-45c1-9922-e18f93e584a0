import Api from '@services/api';
import FilterService from '@entertech/filter-service';
import { kebabToCamelCase } from './kebabToCamelCase';
import { extractResult } from './extractPromiseResult';
import {
  optionsAgeMax,
  optionsAgeMin,
  attributePaths,
  optionsCountry,
  optionsRadius,
  queryFormattingRules,
  optionsLevels,
  optionsGender,
} from '@constants/talentFilter';
import { TALENTS_LIMIT } from '@constants/pagination';

export const getContent = async (
  query,
  resolvedUrl,
  cookies,
  headers,
  accountId,
  queryExtension = '',
  isRecommended = false,
  roleId = null,
  roleCriteria = {},
  recommendedQueryFormattingRules,
) => {
  let filters = await getFilters(
    resolvedUrl,
    cookies,
    headers,
    accountId,
    isRecommended,
    roleCriteria,
  );

  const cities = await getCities(cookies, headers, resolvedUrl);
  const queryParameters = !Object.keys(query || {}).length ? {} : query;

  if (
    queryParameters.city &&
    (!queryParameters.longitude ||
      !queryParameters.latitude ||
      !queryParameters.location)
  ) {
    const city = await getCity(
      cookies,
      headers,
      resolvedUrl,
      queryParameters.city,
    );

    queryParameters.longitude = `${city.longitude}`;
    queryParameters.latitude = `${city.latitude}`;
    queryParameters.location = `${city.title}, ${city.links.state.code}`;
  }

  filters = FilterService.generateFilterFromUrl(
    filters,
    queryParameters,
    isRecommended ? recommendedQueryFormattingRules : queryFormattingRules,
  );

  const filterParameters = FilterService.generateBCUrlFromFilter(
    filters,
    isRecommended ? recommendedQueryFormattingRules : queryFormattingRules,
  );

  const url = `/profiles/search${filterParameters}&rel=talent&limit=${TALENTS_LIMIT}&tf=1${queryExtension}`;
  const urlRecommended = `/roles/${roleId}/recommendations/profiles${filterParameters}&limit=${TALENTS_LIMIT}${queryExtension}`;

  const talents = await Api.serverside(
    isRecommended ? urlRecommended : url,
    cookies,
    headers,
    resolvedUrl,
  );

  return {
    talents,
    filters,
    cities,
  };
};

export const getFilters = async (
  resolvedUrl,
  cookies,
  headers,
  accountId,
  isRecommended,
  roleCriteria,
) => {
  const attributeRequests = attributePaths.map(
    (path) => `/profiles/attributes/${path}`,
  );

  const resultPaths = [
    '/calls/init',
    '/parameters',
    `/accounts/${accountId}/groups`,
    ...attributeRequests,
  ];

  const results = await Promise.allSettled(
    resultPaths.map((path) =>
      Api.serverside(path, cookies, headers, resolvedUrl),
    ),
  );

  const [
    configResponse,
    parametersResponse,
    groupsResponse,
    ...attributesResponses
  ] = results.map((result) => extractResult(result, { items: [] }));

  const attributeOptions = attributePaths.reduce((acc, path, index) => {
    acc[kebabToCamelCase(path)] = attributesResponses[index];

    return acc;
  }, {});

  Object.keys(attributeOptions).forEach((key) => {
    const value = { ...attributeOptions[key] };

    attributeOptions[key].items = formatAttributeOptions(value.items);
  });

  const optionsEyeColor = formatParameterOptions(
    parametersResponse.items['eye-colors'].items,
  );
  const optionsHairColor = formatParameterOptions(
    parametersResponse.items['hair-colors'].items,
  );
  const optionsHeight = formatParameterOptions(
    parametersResponse.items.heights.items,
  );
  const optionsWeight = formatParameterOptions(
    parametersResponse.items.weights.items,
  );
  const optionsCategories = formatConfigOptions(configResponse.data.categories);
  const optionsEthnicities = formatConfigOptions(
    configResponse.data.ethnicities,
  );

  const filters = {
    sections: [
      {
        title: 'Name',
        widgets: [
          {
            class: 'full',
            type: 'input',
            title: 'Name',
            slug: 'name',
            bc: 'name',
            value: '',
          },
        ],
      },
      {
        title: 'Location',
        widgets: [
          {
            class: 'full',
            type: 'select',
            subtype: 'disableClearable',
            title: 'Country',
            slug: 'country',
            bc: 'country',
            options:
              isRecommended && roleCriteria.country
                ? optionsCountry.filter(
                    (option) => option.value === roleCriteria.country,
                  )
                : optionsCountry,
            selected: isRecommended ? roleCriteria.country : '',
          },
          {
            class: 'full',
            type: 'location',
            title: 'Location',
            slug: 'location',
            value:
              isRecommended && roleCriteria.location
                ? roleCriteria.location
                : '',
            summary: '',
            filters: [
              {
                class: 'full',
                type: 'input',
                title: 'City',
                slug: 'city',
                bc: '',
                value:
                  isRecommended && roleCriteria.city ? roleCriteria.city : '',
              },
              {
                class: 'full',
                type: 'input',
                title: 'Longitude',
                slug: 'longitude',
                bc: 'longitude',
                value:
                  isRecommended && roleCriteria.longitude
                    ? roleCriteria.longitude
                    : '',
              },
              {
                class: 'full',
                type: 'input',
                title: 'Latitude',
                slug: 'latitude',
                bc: 'latitude',
                value:
                  isRecommended && roleCriteria.latitude
                    ? roleCriteria.latitude
                    : '',
              },
            ],
          },
          {
            class: 'full',
            type: 'select',
            subtype: 'disableClearable',
            title: 'Radius',
            slug: 'radius',
            bc: 'distance',
            options: isRecommended
              ? optionsRadius.filter((option) => option.value !== '200')
              : optionsRadius,
            selected: isRecommended ? '150' : '200',
          },
        ],
      },
      {
        title: 'Details',
        widgets: [
          {
            class: 'left',
            type: 'select',
            subtype: 'disableClearable',
            title: 'Age min',
            slug: 'agemin',
            bc: 'age_range[]',
            options: isRecommended
              ? optionsAgeMin.filter(
                  (option) =>
                    Number(option.value) >= Number(roleCriteria.ageMin),
                )
              : optionsAgeMin,
            selected: isRecommended ? roleCriteria.ageMin : '18',
          },
          {
            class: 'right',
            type: 'select',
            subtype: 'disableClearable',
            title: 'Age max',
            slug: 'agemax',
            bc: 'age_range[]',
            options: isRecommended
              ? optionsAgeMax.filter(
                  (option) =>
                    Number(option.value) <= Number(roleCriteria.ageMax),
                )
              : optionsAgeMax,
            selected: isRecommended ? roleCriteria.ageMax : '150',
          },
          {
            class: 'full',
            type: 'select',
            subtype: 'disableClearable',
            title: 'Gender',
            slug: 'gender',
            bc: 'gender',
            options:
              isRecommended && roleCriteria.gender
                ? optionsGender.filter(
                    (option) => option.value === roleCriteria.gender,
                  )
                : optionsGender,
            selected: isRecommended ? roleCriteria.gender : null,
          },
          {
            class: 'full',
            type: 'multi-select',
            subtype: 'disableClearable',
            title: 'Ethnicity',
            slug: 'ethnicity',
            bc: 'ethnicities[]',
            options:
              isRecommended && roleCriteria.ethnicities?.length
                ? optionsEthnicities
                    .map((option) =>
                      roleCriteria.ethnicities.some(
                        (ethnicity) => ethnicity.id === option.id,
                      )
                        ? { ...option, checked: true }
                        : option,
                    )
                    .filter((option) =>
                      roleCriteria.ethnicities.some(
                        (ethnicity) => ethnicity.id === option.id,
                      ),
                    )
                : optionsEthnicities,
            selected: null,
          },
          {
            class: 'full',
            type: 'multi-select',
            subtype: 'disableClearable',
            title: 'Twins, Triplets, etc',
            slug: 'multiples',
            bc: 'multiples[]',
            options: [
              { value: '2', label: 'Twins', id: 2, checked: false },
              { value: '3', label: 'Triplets', id: 3, checked: false },
              {
                value: '4',
                label: 'Quadruplets or more',
                id: 4,
                checked: false,
              },
            ],
            selected: null,
          },
          {
            class: 'left',
            type: 'multi-select',
            subtype: 'disableClearable',
            title: 'Eye Color',
            slug: 'eyecolor',
            bc: 'eye_colors[]',
            options: optionsEyeColor,
            selected: null,
          },
          {
            class: 'right',
            type: 'multi-select',
            subtype: 'disableClearable',
            title: 'Hair Color',
            slug: 'haircolor',
            bc: 'hair_colors[]',
            options: optionsHairColor,
            selected: null,
          },
          {
            class: 'left',
            type: 'select',
            subtype: 'disableClearable',
            title: 'Height Min',
            slug: 'heightmin',
            bc: 'height_range[0]',
            options: optionsHeight,
            selected: null,
          },
          {
            class: 'right',
            type: 'select',
            subtype: 'disableClearable',
            title: 'Height Max',
            slug: 'heightmax',
            bc: 'height_range[1]',
            options: optionsHeight,
            selected: null,
          },
          {
            class: 'left',
            type: 'select',
            subtype: 'disableClearable',
            title: 'Weight Min',
            slug: 'weightmin',
            bc: 'weight_range[0]',
            options: optionsWeight,
            selected: null,
          },
          {
            class: 'right',
            type: 'select',
            subtype: 'disableClearable',
            title: 'Weight Max',
            slug: 'weightmax',
            bc: 'weight_range[1]',
            options: optionsWeight,
            selected: null,
          },
          {
            class: 'full',
            type: 'multi-select',
            title: 'Body Type',
            slug: 'bodytype',
            bc: 'attributes[body-type][]',
            options: attributeOptions.bodyType.items,
            selected: null,
          },
          {
            class: 'full',
            type: 'multi-select',
            title: 'Tattoos',
            slug: 'tattoos',
            bc: 'attributes[tattoos][]',
            options: attributeOptions.tattoos.items,
            selected: null,
          },
          {
            class: 'full',
            type: 'multi-select',
            title: 'Piercings',
            slug: 'piercings',
            bc: 'attributes[piercings][]',
            options: attributeOptions.piercings.items,
            selected: null,
          },
          {
            class: 'full',
            type: 'multi-select',
            title: 'Specific Characteristics',
            slug: 'specificcharacteristics',
            bc: 'attributes[specific-characteristics][]',
            options: attributeOptions.specificCharacteristics.items,
            selected: null,
          },
          {
            class: 'full',
            type: 'search-select',
            title: 'Skills',
            slug: 'skills',
            bc: 'skills[]',
            options: [],
            selected: null,
          },
        ],
      },
      {
        title: 'Categories',
        widgets: [
          {
            class: 'full',
            type: 'multi-select',
            title: 'Category',
            slug: 'category',
            bc: 'categories[]',
            options: optionsCategories,
            selected: null,
          },
        ],
      },
      {
        title: 'Advanced',
        widgets: [
          {
            class: 'full',
            type: 'multi-select',
            title: 'Languages',
            slug: 'languages',
            bc: 'attributes[languages][]',
            options: attributeOptions.languages.items,
            selected: null,
          },
          {
            class: 'full',
            type: 'multi-select',
            title: 'Accents',
            slug: 'accents',
            bc: 'attributes[accents][]',
            options: attributeOptions.accents.items,
            selected: null,
          },
          {
            class: 'full',
            type: 'multi-select',
            title: 'Sports',
            slug: 'sports',
            bc: 'attributes[sports][]',
            options: attributeOptions.sports.items,
            selected: null,
          },
          {
            class: 'full',
            type: 'multi-select',
            title: 'Dances',
            slug: 'dances',
            bc: 'attributes[dances][]',
            options: attributeOptions.dances.items,
            selected: null,
          },
          {
            class: 'full',
            type: 'multi-select',
            title: 'Professions',
            slug: 'professions',
            bc: 'attributes[professions][]',
            options: attributeOptions.professions.items,
            selected: null,
          },
          {
            class: 'full',
            type: 'multi-select',
            title: 'Musicianship',
            slug: 'musicianship',
            bc: 'attributes[musicianship][]',
            options: attributeOptions.musicianship.items,
            selected: null,
          },
          {
            class: 'full',
            type: 'multi-select',
            title: 'Family Photoshoot',
            slug: 'familyphotoshoot',
            bc: 'attributes[family-photoshoot][]',
            options: attributeOptions.familyPhotoshoot.items,
            selected: null,
          },
          {
            class: 'full',
            type: 'multi-select',
            title: 'Driving',
            slug: 'driving',
            bc: 'attributes[driving][]',
            options: attributeOptions.driving.items,
            selected: null,
          },
          {
            class: 'full',
            type: 'multi-select',
            title: 'Pets',
            slug: 'pets',
            bc: 'attributes[pets][]',
            options: attributeOptions.pets.items,
            selected: null,
          },
        ],
      },
      {
        title: 'Extra details',
        widgets: [
          {
            class: 'full',
            type: 'checkbox-standalone',
            title: 'Demo reel',
            slug: 'demoreel',
            bc: 'has_demo_reel',
            value: false,
          },
          {
            class: 'full',
            type: 'checkbox-standalone',
            title: 'UGC Demo reel',
            slug: 'ugcdemoreel',
            bc: 'has_ugc_demo_reel',
            value: false,
          },
          {
            class: 'full',
            type: 'checkbox-standalone',
            title: 'Audio reel',
            slug: 'audioreel',
            bc: 'has_audios',
            value: false,
          },
          {
            class: 'full',
            type: 'checkbox-standalone',
            title: 'Slate',
            slug: 'slate',
            bc: 'has_slate',
            value: false,
          },
          {
            class: 'full',
            type: 'checkbox-standalone',
            title: 'Credits',
            slug: 'credits',
            bc: 'has_credits',
            value: false,
          },
        ],
      },
      {
        title: '',
        widgets: [
          {
            class: 'hidden',
            type: 'input',
            title: '',
            slug: 'page',
            bc: 'page',
            value: 1,
          },
        ],
      },
    ],
  };

  const isAccountInternal =
    !!groupsResponse.items.find(({ type }) => type === 'internal') &&
    groupsResponse.status === 'ok';

  if (isAccountInternal && !isRecommended) {
    const internalFilter = {
      title: 'Subscription status',
      widgets: [
        {
          class: 'full',
          type: 'checkbox',
          title: '',
          slug: 'levels',
          bc: 'levels[]',
          options: optionsLevels,
        },
      ],
    };

    filters.sections.splice(filters.sections.length - 2, 0, internalFilter);
  }

  return filters;
};

export const getCities = async (cookies, headers, resolvedUrl) => {
  return await Api.serverside(
    `/cities/search?limit=100&search[popular]=true`,
    cookies,
    headers,
    resolvedUrl,
  );
};

export const getCity = async (cookies, headers, resolvedUrl, slug) => {
  const data = await Api.serverside(
    `/cities/search?filter[slug]=${slug}`,
    cookies,
    headers,
    resolvedUrl,
  );

  return data?.items?.[0];
};

export const formatParameterOptions = (options) => {
  return options
    .filter(({ value }) => value)
    .map(({ title, value }) => ({
      label: title,
      value: title.toLowerCase().replace(/[\W_]+/g, ''),
      id: value,
      checked: false,
    }));
};

export const formatConfigOptions = (options) => {
  return options.map(({ name, id }) => ({
    label: name,
    value: name.toLowerCase().replace(/[\W_]+/g, ''),
    id,
    checked: false,
  }));
};

export const formatAttributeOptions = (options) => {
  return options.map(({ value, slug }) => ({
    label: value,
    value: slug.toLowerCase().replace(/[\W_]+/g, ''),
    id: slug,
    checked: false,
  }));
};
