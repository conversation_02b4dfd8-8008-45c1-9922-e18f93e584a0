import React, {
  createContext,
  useState,
  useContext,
  useEffect,
  useRef,
  useCallback,
} from 'react';
import { useAuth } from './AuthContext';
import { CookieService } from '@services/cookieService';
import Api from '../services/api';

const DEFAULT_SALE = { expiration: 0, hidden: true };

const SaleContext = createContext({});

export const SaleProvider = ({ children }) => {
  const [sale, setSale] = useState(DEFAULT_SALE);

  const loadingRef = useRef(false);

  const { accountLevel, isAuthenticated } = useAuth();

  const isBasicTalent = !accountLevel?.isPaidOrDelayed;

  const stopSale = useCallback(() => {
    const storedSale = CookieService.getSaleCookie();

    if (storedSale) {
      CookieService.deleteSaleCookie();
    }

    setSale((value) => (value.expiration ? DEFAULT_SALE : value));
  }, []);

  const refreshSale = useCallback(async () => {
    if (!loadingRef.current) {
      loadingRef.current = true;

      const clientStatus = await Api.clientside('/clientstatus/info');

      const expirationTime = clientStatus?.info?.promo?.period?.left;

      if (!expirationTime) {
        stopSale();

        return;
      }

      const timeLeft = Math.floor(Date.now() / 1000) + Number(expirationTime);
      const isExpired = timeLeft - Math.floor(Date.now() / 1000) <= 0;

      if (isExpired) {
        stopSale();
      } else {
        const value = {
          expiration: timeLeft,
          hidden: !!clientStatus?.info?.promo?.hide_time,
        };

        setSale(value);
        CookieService.setSaleCookie(value);
      }

      loadingRef.current = false;
    }
  }, [stopSale]);

  useEffect(() => {
    if (!isBasicTalent || !isAuthenticated) {
      stopSale();

      return;
    }

    const storedSale = CookieService.getSaleCookie();
    const isLegacy = storedSale?.id;

    if (isLegacy) {
      refreshSale();

      return;
    }

    if (storedSale?.expiration) {
      const timeLeft = Number(storedSale.expiration);
      const isExpired = timeLeft - Math.floor(Date.now() / 1000) <= 0;

      if (isExpired) {
        refreshSale();
      } else {
        setSale({ expiration: timeLeft, hidden: storedSale.hidden });
      }

      return;
    }

    if (isBasicTalent) {
      refreshSale();
    }
  }, [isBasicTalent, refreshSale, stopSale]);

  return (
    <SaleContext.Provider
      value={{
        saleExpirationTime: sale?.expiration,
        showSale: !sale?.hidden && !!sale.expiration,
        stopSale,
      }}
    >
      {children}
    </SaleContext.Provider>
  );
};

export const useSale = () => useContext(SaleContext);
