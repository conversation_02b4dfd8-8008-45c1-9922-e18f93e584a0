import { CookieService } from './cookieService';

const clientAPIRoute = async (url, options = {}, req = {}) => {
  const response = await fetch(
    `${process.env.externalApiUrl}${url}`,
    compileOptions(options, req),
  );

  const data = await response.json();

  if (
    typeof window !== 'undefined' &&
    (data.messageKey === 'error.access_denied' || response.status === 403)
  ) {
    CookieService.cleanAuthenticationCookies();
    window.location.href = process.env.baseUrl;
  }

  return {
    status: response.status,
    headers: response.headers,
    data,
  };
};

const serverGateway = async (url, options = {}, req = {}, res) => {
  const response = await fetch(
    `${process.env.internalGateway}${url}`,
    compileOptions(options, req),
  );

  const data = await response.json();

  if (data.status === 'error' || response.status !== 200) {
    console.error({
      type: 'Server side response error',
      message: data.messageKey || data.message || 'No error message',
      httpStatusCode: response.status,
      responseUrl: response.url,
      requestCookies: req.cookies,
      requestReferer: req.headers.referer,
      response: data,
    });
  }

  const nextAuthToken = response.headers.get(
    CookieService.cookie.authentication,
  );
  const nextVolatile = response.headers.get(CookieService.cookie.volatile);

  if (nextAuthToken) {
    CookieService.setAuthenticationCookie(nextAuthToken, req, res);
  }

  if (nextVolatile) {
    CookieService.setVolatileCookie(nextVolatile, req, res);
  }

  return {
    status: response.status,
    headers: response.headers,
    data,
  };
};

const clientGateway = async (url, options = {}, req = {}, res) => {
  const response = await fetch(
    `${process.env.publicGateway}${url}`,
    compileOptions(options, req),
  );

  const data = await response.json();

  if (
    typeof window !== 'undefined' &&
    (data.messageKey === 'error.access_denied' || response.status === 403)
  ) {
    CookieService.cleanAuthenticationCookies();
    window.location.href = process.env.baseUrl;
  }

  const nextAuthToken = response.headers.get(
    CookieService.cookie.authentication,
  );
  const nextVolatile = response.headers.get(CookieService.cookie.volatile);

  if (nextAuthToken) {
    CookieService.setAuthenticationCookie(nextAuthToken, req, res);
  }

  if (nextVolatile) {
    CookieService.setVolatileCookie(nextVolatile, req, res);
  }

  return {
    status: response.status,
    headers: response.headers,
    data,
  };
};

const compileOptions = (options = {}, req = {}) => {
  const headers = {};

  const trackingCookie = CookieService.getTrackingCookie(req);
  const volatileCookie = CookieService.getVolatileCookie(req);
  const authenticationCookie = CookieService.getAuthenticationCookie(req);
  const ip = req.headers ? req.headers['x-forwarded-for'] : '';
  const blackFire = req.headers ? req.headers['x-blackfire-query'] : '';
  const referer = req.headers ? req.headers['referer'] : '';

  if (trackingCookie) {
    headers[CookieService.cookie.tracking] = btoa(
      JSON.stringify(trackingCookie),
    );
  }

  if (volatileCookie) {
    headers[CookieService.cookie.volatile] = volatileCookie;
  }

  if (authenticationCookie) {
    headers[CookieService.cookie.authentication] = authenticationCookie;
  }

  if (ip) {
    headers['x-forwarded-for'] = ip;
  }

  if (blackFire) {
    headers['x-blackfire-query'] = blackFire;
  }

  if (referer) {
    headers['referer'] = referer;
  }

  options.headers = {
    ...headers,
    ...(options.headers ? options.headers : {}),
  };

  return options;
};

export default {
  clientAPIRoute,
  clientGateway,
  serverGateway,
};
