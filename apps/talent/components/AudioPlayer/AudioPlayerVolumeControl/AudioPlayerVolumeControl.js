'use client';
import { memo, useEffect, useRef, useState } from 'react';
import Image from 'next/image';
import styles from './AudioPlayerVolumeControl.module.scss';
import cn from 'classnames';

const AudioPlayerVolumeControl = ({ audioRef, isMobileFromUserAgent }) => {
  const [volume, setVolume] = useState(isMobileFromUserAgent ? 100 : 60);
  const [previousVolume, setPreviousVolume] = useState(60);
  const volumeRef = useRef(null);

  useEffect(() => {
    if (volume !== 0) {
      setPreviousVolume(volume);
    }
  }, [volume]);

  useEffect(() => {
    if (audioRef) {
      audioRef.current.volume = volume / 100;
    }
  }, [volume, audioRef]);

  const openVolumeSlider = () => {
    volumeRef.current.classList.add(styles.visible);
  };

  const closeVolumeSlider = () => {
    volumeRef.current.classList.remove(styles.visible);
  };

  const mute = () => {
    setVolume(0);
  };

  const unmute = () => {
    setVolume(previousVolume);
  };

  return (
    <div
      className={cn(styles.volume, {
        [styles.hidden]: !!isMobileFromUserAgent,
      })}
      onMouseLeave={closeVolumeSlider}
    >
      <div
        ref={volumeRef}
        className={styles['volume-slider-container']}
        onMouseLeave={closeVolumeSlider}
      >
        <input
          className={styles['volume-slider']}
          type="range"
          min={0}
          max={100}
          value={volume}
          onChange={(e) => setVolume(Number(e.target.value))}
        />
      </div>
      {volume === 0 ? (
        <Image
          onClick={unmute}
          className={styles.icon}
          src="/assets/icons/icon-muted.svg"
          alt="icon"
          width={24}
          height={20}
          onMouseEnter={openVolumeSlider}
        />
      ) : (
        <Image
          onClick={mute}
          className={styles.icon}
          src="/assets/icons/icon-volume.svg"
          alt="icon"
          width={24}
          height={20}
          onMouseEnter={openVolumeSlider}
        />
      )}
    </div>
  );
};

export default memo(AudioPlayerVolumeControl);
