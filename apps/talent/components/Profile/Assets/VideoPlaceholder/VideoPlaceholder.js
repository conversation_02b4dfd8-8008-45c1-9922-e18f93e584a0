'use client';
import React, { memo } from 'react';
import styles from './VideoPlaceholder.module.scss';
import cn from 'classnames';
import { TooltipProfile } from '@components';

const VideoPlaceholder = ({
  onClick,
  text = 'Upload new video',
  type = 'new',
  tooltipTitle = '',
  tooltipText = '',
  showTooltip = false,
}) => {
  return (
    <div className={styles['profile-video-box']}>
      <div className={styles['profile-section-empty']} onClick={onClick}>
        <div
          className={cn(
            styles['profile-video-box-icon'],
            styles[`icon-${type}`],
          )}
        />
        <span className={styles['profile-video-box-add-button']}>{text}</span>
      </div>
      {showTooltip && (
        <TooltipProfile title={tooltipTitle} text={tooltipText} />
      )}
    </div>
  );
};

export default memo(VideoPlaceholder);
