@use '@styles/mixins' as *;
@use '@styles/variables' as *;

.wrapper {
  border-radius: 10px 10px 0 0;
  box-shadow: $shadow-input-box;
  width: 100%;

  @include desktop {
    border: 1px solid $grey-40;
    border-radius: 10px;
    box-shadow: none;
    max-width: 700px;
  }
}

.form {
  position: relative;
  display: flex;
  align-items: flex-end;
  padding: $space-15 $space-25 0;
}

.form-wrap {
  border-radius: 10px;
  padding-bottom: $space-20;
}

.input-box {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: auto;

  .input-area {
    width: 100%;
    height: 50px;
  }

  .input-controls {
    display: flex;
    justify-content: right;
  }

  .input-submit {
    background: transparent;
    display: flex;
    align-items: center;
    gap: $space-15;
    flex-wrap: wrap;
  }

  .input-counter {
    font-size: 12px;
  }

  .message-input {
    background: transparent;
    border: 0;
    width: 100%;
    padding: $space-10 0;
    outline: none;
    font-size: 16px;
    font-weight: 600;
  }
}
