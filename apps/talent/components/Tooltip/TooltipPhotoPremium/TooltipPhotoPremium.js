'use client';
import React, { memo } from 'react';
import styles from './TooltipPhotoPremium.module.scss';
import { Button } from '@components';
import { Amp } from '@services/amp';
import { useRouter } from 'next/navigation';
import useTrackElementActions from '@utils/useTrackElementActions';
import imageAttributesMale from '../../../public/assets/tooltip/image-photo-premium-male.webp';
import imageAttributesFemale from '../../../public/assets/tooltip/image-photo-premium-female.webp';
import Image from 'next/image';

const TooltipPhotoPremium = ({ genderTitle = 'male' }) => {
  const router = useRouter();

  const { onTrackClick } = useTrackElementActions({
    name: 'Unlock photo analyzer popup',
    type: Amp.element.type.popup,
    context: Amp.element.context.ctaPopup,
    autoTrackEnabled: true,
  });

  const onButtonClick = () => {
    onTrackClick();

    router.push('/upgrade');
  };

  return (
    <div className={styles['tooltip-container']}>
      <span className={styles.title}>
        You&apos;ve discovered a Photo Analyzer
      </span>
      <p className={styles['description']}>
        This feature is a tool that assesses the quality of a photo based on
        different factors, helping users understand its overall effectiveness
        and make improvements.
      </p>
      <Image
        className={styles.image}
        src={
          genderTitle === 'male' ? imageAttributesMale : imageAttributesFemale
        }
        alt="premium image"
      />
      <div className={styles['button-container']}>
        <Button
          className={styles['button-unblock']}
          label="Unlock Photo Analyzer"
          shadow={false}
          color="solid-blue"
          minWidth="75px"
          onClick={onButtonClick}
        />
      </div>
    </div>
  );
};

export default memo(TooltipPhotoPremium);
