@use '@styles/mixins' as *;
@use '@styles/variables' as *;

.add-attribute-modal {
  width: 100%;
  border-radius: 5px;
  padding: $space-15 0 $space-40;
  position: fixed;
  bottom: 0;
  display: flex;
  flex-direction: column;
  max-height: 90vh;

  @include tablet {
    position: relative;
    bottom: auto;
    width: 620px;
    max-width: 100%;
    max-height: 80vh;
    padding: 0 0 $space-40;
  }
}

.add-attribute-modal-content {
  display: flex;
  flex-direction: column;
}

.container {
  padding: $space-10 $space-20 0;
  min-height: 350px;
  display: flex;
  flex-direction: column;

  @include tablet {
    padding: $space-10 $space-40 0;
  }
}

.title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: $space-10;
}

.attribute-field {
  margin-bottom: $space-20;
}

.levels {
  display: grid;
  grid-template-columns: 1fr;
  column-gap: $space-60;
  margin-top: $space-10;

  @include tablet {
    grid-template-columns: 0.5fr 0.5fr;
  }
}

.level {
  display: flex;
  gap: 2px;
  align-items: center;

  &:nth-child(2) {
    order: 1;
  }

  &:nth-child(4) {
    order: 2;
  }

  @include tablet {
    grid-template-columns: 0.5fr 0.5fr;

    &:nth-child(2) {
      order: 0;
    }

    &:nth-child(4) {
      order: 0;
    }
  }

  span {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;

    &.filled {
      background: $black;
    }

    &.empty {
      border: 1px solid $black;
    }

    &:last-of-type {
      margin-right: $space-15;
    }
  }
}

.buttons {
  display: flex;
  gap: $space-15;
  justify-content: center;
  margin-top: $space-40;

  .button {
    height: 40px;
  }
}

.value {
  display: grid;
  grid-template-columns: auto 160px $space-25;
  gap: $space-5 $space-35;
  align-items: center;
  margin-bottom: $space-10;
  background: $grey-10;
  padding: $space-15 $space-20;
  border-radius: 10px;
  font-weight: 700;
  font-size: 16px;

  .delete-btn {
    cursor: pointer;
  }

  .value-title {
    grid-column: auto / span 3;

    @include tablet {
      grid-column: auto / span 1;
    }
  }

  &.no-level {
    .value-title {
      grid-column: auto / span 2;
    }
  }

  .value-select {
    grid-column: auto / span 2;

    &:empty {
      display: none;
    }

    @include tablet {
      grid-column: auto / span 1;
    }
  }
}

.error {
  color: $red-100;
  font-weight: 600;
}

.selected-values-list-wrap {
  height: 100%;
  flex: 1 1;
  overflow: auto;
}

.selected-values-list {
  min-height: 200px;
}

.hint-container {
  margin-bottom: $space-20;
}
