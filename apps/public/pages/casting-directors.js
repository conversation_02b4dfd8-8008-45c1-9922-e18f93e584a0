import {
  <PERSON><PERSON>,
  Carousel,
  <PERSON>dalContactTalent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from '../components';
import styles from '../styles/casting-directors.module.scss';
import seoPageProps from '../utils/seoPageProps';
import Api from '../services/api';
import Arrow from '../public/assets/icons/icon-arrow-1-white.svg';
import SignUpIcon from '../public/assets/icons/icon-signup.svg';
import PostCCIcon from '../public/assets/icons/icon-casting-call-2.svg';
import ShortlistIcon from '../public/assets/icons/icon-shortlist.svg';
import ContactIcon from '../public/assets/icons/icon-contact.svg';
import { useEffect, useState } from 'react';
import { xTracking } from '../services/tracking';
import FAQ from '../components/FAQ/FAQ.js';
import cn from 'classnames';
import {
  blogs,
  faqItems,
  helpTypes,
  quotes,
  statisticsDefault,
} from '../constants/casting-directors';
import Link from 'next/link';
import dayjs from 'dayjs';
import Image from 'next/image';
import QuoteEllipse from '../public/assets/icons/icon-quote-2.svg';
import { getHeaders } from '../utils/headerHelper';
import { MainLayout } from '../components/Layouts';
import { sharedServerSideProps } from '../utils/sharedServerSideProps';
import { Amp } from '../services/amp';

export const getServerSideProps = async ({ resolvedUrl, req, res }) => {
  const talentResponse = await Api.serverAPIRoute(
    `/talent/list`,
    null,
    getHeaders(req, resolvedUrl),
  );
  const sharedProps = await sharedServerSideProps(req, res);

  return {
    props: {
      ...sharedProps,
      seoPage: {
        ...(await seoPageProps(resolvedUrl)),
        ogImageUrl: `${process.env.baseUrl}/assets/meta/director.webp`,
      },
      talents: talentResponse.data?.list?.items || [],
      activeTalentTotal: Math.floor(talentResponse.data?.list?.count / 1000000),
      activeAgentTotal: (
        Math.floor(statisticsDefault.activeAgentTotal / 10) * 10
      ).toLocaleString('en-EN'),
      castingCallsPerYear:
        statisticsDefault.castingCallsPerYear.toLocaleString('en-EN'),
      yearsInIndustry: dayjs().diff(
        dayjs(statisticsDefault.entertechBirthday),
        'year',
      ),
    },
  };
};

export default function CastingDirectors({
  seoPage,
  talents,
  activeTalentTotal,
  activeAgentTotal,
  castingCallsPerYear,
  yearsInIndustry,
}) {
  const [showProfileModal, setShowProfileModal] = useState(false);
  const [showContactTalentModal, setShowContactTalentModal] = useState(false);
  const [selectedProfileId, setSelectedProfileId] = useState(null);

  const openProfileModal = (id) => {
    setSelectedProfileId(id);
    setShowProfileModal(true);
  };

  const closeProfileModal = () => {
    setShowProfileModal(false);
    setSelectedProfileId(null);
  };

  const openContactTalentModal = () => {
    setShowContactTalentModal(true);
  };

  const closeContactTalentModal = () => {
    setShowContactTalentModal(false);
  };

  const track = () => {
    const trackingParams = {
      utm_source: 'b2bpage',
      utm_medium: 'unknown',
      utm_campaign: 'cta',
    };

    xTracking(trackingParams);
  };

  useEffect(() => {
    Amp.track(Amp.events.viewDirectorLanding);
  }, []);

  return (
    <MainLayout isDefaultHeaderVisible isMobileMenuVisible>
      <div className={styles['main-banner']} data-cy="casting-directors-banner">
        <Seo seoPage={seoPage} />
        <div className={cn(styles.container, styles['main-banner-body'])}>
          <div className={styles['main-banner-hero']}>
            <div className={styles['main-banner-image']}>
              <Image
                height={1370}
                width={1176}
                alt="allcasting"
                src={'/assets/casting-directors/hero-image.webp'}
              />
            </div>
          </div>

          <div className={styles['main-banner-welcome']}>
            <div className={styles['main-banner-title']} data-cy="main-title">
              Cast your next <br /> superstar here
            </div>
            <div className={styles['main-banner-text']}>
              It&apos;s 100% FREE for Casting Professionals to Post a Casting
              Call and reach a database of over 2 million talent across the US
              and Canada.
            </div>
            <Button
              type="link"
              href="/register/professional"
              color="purple"
              label="Cast a Project"
              minWidth={'230px'}
              isSEO
              onClick={track}
              shadow
            />
          </div>
        </div>
      </div>
      <div
        className={cn(styles.container, styles['statistics-container'])}
        data-cy="casting-directors-statistics"
      >
        <div className={styles['statistics-panel']}>
          <div className={styles['statistics-box']}>
            <div className={styles['statistics-title']}>
              {activeAgentTotal}+
            </div>
            <div className={styles['statistics-text']}>
              trusted casting professionals
            </div>
          </div>
          <div className={styles['statistics-box']}>
            <div className={styles['statistics-title']}>
              {castingCallsPerYear}+
            </div>
            <div className={styles['statistics-text']}>
              casting projects posted yearly
            </div>
          </div>
          <div className={styles['statistics-box']}>
            <div className={styles['statistics-title']}>{yearsInIndustry}+</div>
            <div className={styles['statistics-text']}>years in industry</div>
          </div>
          <div className={styles['statistics-box']}>
            <div className={styles['statistics-title']}>
              {activeTalentTotal}M+
            </div>
            <div className={styles['statistics-text']}>active talent</div>
          </div>
        </div>
      </div>
      <div
        className={cn(styles.container, styles['with-us-container'])}
        data-cy="our-clients-section"
      >
        <div className={styles['with-us-title']}>Who works with us?</div>
        <div className={styles['with-us']}>
          <div>
            <div className={styles['with-us-heading']}>
              Casting Directors & Film Makers
            </div>
            <div className={styles['with-us-paragraph']}>
              From small production companies to HBO. Extras, speaking roles,
              stunt doubles and more. Whatever you&apos;re looking for,
              allcasting is here to deliver!
            </div>
          </div>
          <div>
            <div className={styles['with-us-heading']}>Brands</div>
            <div className={styles['with-us-paragraph']}>
              Nike, Calvin Klein and others have already found their new faces
              on allcasting. Explore our database of 2+ million talent and make
              your next campaign happen.
            </div>
          </div>
          <div>
            <div className={styles['with-us-heading']}>
              Modeling & Talent Agencies
            </div>
            <div className={styles['with-us-paragraph']}>
              Looking for newbies or industry veterans to walk the runway, do a
              trick or simply pose? NYFW and MMG are already booking talent with
              us!
            </div>
          </div>

          <div>
            <div className={styles['with-us-heading']}>
              Event & Marketing Agencies
            </div>
            <div className={styles['with-us-paragraph']}>
              Is your client looking for people to be at their event or for
              their newest advertising campaign? Look no further, our talent are
              ready to be cast by YOU!
            </div>
          </div>
          <div>
            <div className={styles['with-us-heading']}>
              Independent Creators
            </div>
            <div className={styles['with-us-paragraph']}>
              Need talent for your photography portfolio or a film? In our
              database you&apos;ll have thousands of options to make it happen.
            </div>
          </div>
          <div>
            <div className={styles['more-talent']}>
              <Image
                width={706}
                height={268}
                src="/assets/casting-directors/more_talent.webp"
                alt=""
              />
              <Link
                href={'/register/professional'}
                className={styles['more-talent-go']}
                onClick={track}
              >
                Find Top <br />
                Talent
                <Arrow className={styles.arrow} />
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className={styles['company-section']} data-cy="company-section">
        <div
          className={cn(styles.container, styles['company-section-container'])}
        >
          <div className={styles['company-info']}>
            <div className={styles['company-info-header']}>
              <div className={styles['company-info-title']}>
                You&apos;re in good <br /> company
              </div>
              <div className={styles['company-info-text']}>
                Find out why casting directors trust us <br /> to deliver
                exceptional talent.
              </div>
            </div>
            <div className={styles['carousel-quote-container']}>
              <Carousel
                className="carousel-quote"
                startIndex={0}
                delay={5000}
                loop
                playOnInit
                enablePagination
              >
                {quotes.map(({ text, author, authorInfo }, i) => (
                  <div className={styles['quote-box']} key={i}>
                    <QuoteEllipse className={styles['quote-icon']} />
                    <div className={styles['quote-text']}>{text}</div>
                    <div>
                      <div className={styles['quote-author']}>
                        <span>— </span>
                        {author},
                      </div>
                      <div className={styles['quote-author-info']}>
                        {authorInfo}
                      </div>
                    </div>
                  </div>
                ))}
              </Carousel>
            </div>
          </div>
        </div>
      </div>

      <div className={styles.container}>
        <div className={styles['help-section']} data-cy="help-section">
          <div className={styles['help-heading']}>
            How can allcasting help you?
          </div>
          <div className={styles['help-text']}>
            Allcasting is your magic box with the best talent ready to be seen
            in the easiest possible manner, offering the tools you need to keep
            the casting workflow organised, secure and easy to access.
          </div>
          <div className={styles['help-list']}>
            {helpTypes.map(({ src, title, text }, i) => (
              <div key={i} className={styles['help-box']}>
                <Image src={src} width={30} height={30} alt="icon" />
                <div className={styles['help-box-title']}>{title}</div>
                <div className={styles['help-box-text']}>{text}</div>
              </div>
            ))}
          </div>
          <div className={styles['help-carousel-container']}>
            <Carousel
              className="carousel-help"
              startIndex={0}
              delay={5000}
              loop
              playOnInit
              enablePagination
            >
              {helpTypes.map(({ src, title, text }, i) => (
                <div key={i} className={styles['help-box']}>
                  <Image src={src} width={30} height={30} alt="icon" />
                  <div className={styles['help-box-title']}>{title}</div>
                  <div className={styles['help-box-text']}>{text}</div>
                </div>
              ))}
            </Carousel>
          </div>
          <div className={styles['help-btn-wrapper']}>
            <Button
              type="link"
              href="/register/professional"
              color="blue"
              label="POST A CASTING CALL"
              minWidth={'220px'}
              onClick={track}
            />
          </div>

          <div className={styles['talents-section']}>
            <div className={styles['talents-section-heading']}>
              Be the first to discover rising stars for your project
            </div>
            <div className={styles['carousel-talent-container']}>
              <Carousel
                slidesToScroll={1}
                className="carousel-talent"
                enableArrowNavigation
                dragFree
                loop
                playOnInit
                arrowType="round"
              >
                {talents.map(
                  ({ id, title_photo_url, gender, firstname, location }) => (
                    <div
                      className={styles['talent-slide']}
                      key={id}
                      onClick={() => openProfileModal(id)}
                    >
                      <div className={styles['image-container']}>
                        <Image
                          className={styles.image}
                          src={
                            title_photo_url ||
                            `/assets/placeholders/${
                              gender?.title?.toLowerCase() || 'male'
                            }-head.svg`
                          }
                          alt="talent"
                          width={600}
                          height={800}
                          unoptimized
                        />
                        <div className={styles['details']}>
                          <span className={styles['talent-name']}>
                            {firstname}
                          </span>
                          {location && (
                            <div
                              className={styles['talent-location-container']}
                            >
                              <Image
                                src={'/assets/icons/icon-pin-1.svg'}
                                alt="icon"
                                width={10}
                                height={12}
                              />
                              <span>{`${location.city}, ${location.state}`}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ),
                )}
              </Carousel>
            </div>
            <div className={styles['talent-btn-wrapper']}>
              <Button
                type="link"
                href="/register/professional"
                color="blue"
                label="FIND PERFECT TALENT"
                minWidth={'220px'}
                onClick={track}
              />
            </div>
          </div>
        </div>
      </div>

      <div
        className={styles['how-works-section']}
        data-cy="how-it-works-section"
      >
        <div
          className={cn(
            styles.container,
            styles['how-works-section-container'],
          )}
        >
          <div className={styles['how-works-heading']}>
            How allcasting works
          </div>
          <div className={styles['how-works-subtitle']}>
            With allcasting, you can conveniently search for performers by
            browsing our extensive talent database. This makes the search
            process much simpler and more efficient, as you can quickly find
            performers who match your specific requirements and preferences.
          </div>
          <div className={styles['how-works']}>
            <div className={styles['how-works-block']}>
              <SignUpIcon className={styles['how-works-icon']} />
              <div className={styles['how-works-title']}>1. Sign Up</div>
              <div className={styles['how-works-text']}>
                Register as a Casting Director in a few clicks
              </div>
            </div>
            <div className={styles['how-works-block']}>
              <PostCCIcon className={styles['how-works-icon']} />
              <div className={styles['how-works-title']}>
                2. Post a Casting Call
              </div>
              <div className={styles['how-works-text']}>
                Post the job and wait for submissions to arrive
              </div>
            </div>
            <div className={styles['how-works-block']}>
              <ShortlistIcon className={styles['how-works-icon']} />
              <div className={styles['how-works-title']}>
                3. Shortlist Submissions
              </div>
              <div className={styles['how-works-text']}>
                Browse the applicants and shortlist matching ones
              </div>
            </div>
            <div className={styles['how-works-block']}>
              <ContactIcon className={styles['how-works-icon']} />
              <div className={styles['how-works-title']}>4. Contact talent</div>
              <div className={styles['how-works-text']}>
                Message the talent directly to request additional info
              </div>
            </div>
          </div>
          <div className={styles['how-works-btn-wrapper']}>
            <Button
              type="link"
              href="/register/professional"
              color="blue"
              label="POST A CASTING CALL"
              minWidth={'220px'}
              onClick={track}
            />
          </div>
        </div>
      </div>
      <div className={styles.container} data-cy="team-section">
        <div className={styles['devoted-team']}>
          <div className={cn(styles['devoted-team-heading'], styles['mobile'])}>
            Our devoted team
          </div>
          <Image
            height={884}
            width={848}
            src="/assets/casting-directors/Kate-Taurina.webp"
            alt="Kate Taurina"
            className={styles['devoted-team-image']}
          />
          <div className={styles['devoted-team-info']}>
            <div
              className={cn(styles['devoted-team-heading'], styles['desktop'])}
            >
              Our devoted team
            </div>
            <div className={styles['devoted-team-text']}>
              Just like a Michelin restaurant serves foods of the most
              extraordinary tastes, we bring talent with the most exquisite
              skills, appearances, union or non-union, represented by the agent
              or not represented, any ethnicity, location, hobbies... you name
              it!
            </div>
            <div className={styles['devoted-team-user']}>
              <div className={styles['devoted-team-name']}>Kate Taurina</div>
              <div className={styles['devoted-team-role']}>
                Head of Casting Director Department
              </div>
              <div>
                <Link
                  href={`mailto:<EMAIL>`}
                  className={styles['contact-link']}
                >
                  <EMAIL>
                </Link>
              </div>
            </div>
          </div>
        </div>
        <div className={styles['members-block']}>
          <div className={styles['member']}>
            <div className={styles['member-image']}>
              <Image
                width={320}
                height={336}
                src="/assets/casting-directors/member-1.webp"
                alt="Paul"
              />
            </div>
            <div className={styles['member-name']}>Paul</div>
            <div className={styles['member-role']}>Personal Manager</div>
          </div>
          <div className={styles['member']}>
            <div className={styles['member-image']}>
              <Image
                width={320}
                height={336}
                src="/assets/casting-directors/member-2.webp"
                alt="Danielle"
              />
            </div>
            <div className={styles['member-name']}>Danielle</div>
            <div className={styles['member-role']}>Personal Manager</div>
          </div>
          <div className={styles['member']}>
            <div className={styles['member-image']}>
              <Image
                width={320}
                height={336}
                src="/assets/casting-directors/member-3.webp"
                alt="Mike"
              />
            </div>
            <div className={styles['member-name']}>Mike</div>
            <div className={styles['member-role']}>Personal Manager</div>
          </div>
          <div className={styles['member']}>
            <div className={styles['member-image']}>
              <Image
                width={320}
                height={336}
                src="/assets/casting-directors/member-4.webp"
                alt="Sammuel"
              />
            </div>
            <div className={styles['member-name']}>Sammuel</div>
            <div className={styles['member-role']}>Personal Manager</div>
          </div>
        </div>
      </div>
      <div className={styles['blog-section']} data-cy="blog-section">
        <div className={cn(styles.container, styles['blog-section-container'])}>
          <div className={styles['blog-heading']}>
            <div className={styles['blog-heading-title']}>
              Webinars with Casting Directors
            </div>
            <Link href={'/blog'} className={styles['blog-all-notes']}>
              View more
            </Link>
          </div>
          <>
            <div className={styles['blogs-list']}>
              {blogs.map(({ src, alt, title, href }, i) => (
                <Link key={i} href={href}>
                  <div className={styles['blog-box']}>
                    <div>
                      <Image src={src} alt={alt} width={380} height={211} />
                    </div>
                    <div className={styles['blog-info']}>
                      <div className={styles['blog-title']}>{title}</div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
            <div className={styles['blog-carousel-container']}>
              <Carousel
                className="blog-carousel"
                startIndex={0}
                delay={5000}
                loop
                playOnInit
                enablePagination
              >
                {blogs.map(({ src, alt, title, href }, i) => (
                  <Link key={i} href={href} prefetch={false}>
                    <div className={styles['blog-box']} key={i}>
                      <div>
                        <Image src={src} alt={alt} width={380} height={211} />
                      </div>
                      <div className={styles['blog-info']}>
                        <span className={styles['blog-title']}>{title}</span>
                      </div>
                    </div>
                  </Link>
                ))}
              </Carousel>
            </div>
          </>
        </div>
      </div>
      <div className={styles.container}>
        <div className={styles.faq} data-cy="faq-section">
          <div className={styles['faq-heading']}>
            Frequently asked questions
          </div>
          <FAQ faqItems={faqItems}></FAQ>
        </div>
      </div>
      {showProfileModal && (
        <ModalProfile
          id={selectedProfileId}
          onClose={closeProfileModal}
          onOpenContactTalentModal={openContactTalentModal}
        />
      )}
      {showContactTalentModal && (
        <ModalContactTalent onClose={closeContactTalentModal} />
      )}
    </MainLayout>
  );
}
