import { Html, <PERSON>, <PERSON>, NextScript } from 'next/document';
import Script from 'next/script';

export default function Document() {
  return (
    <Html lang="en" style={{ scrollBehavior: 'smooth' }}>
      <Head>
        <meta name="referrer" content="no-referrer-when-downgrade" />
      </Head>
      <body>
        <NextScript />
        <Script
          src={`https://cdn.cookielaw.org/scripttemplates/otSDKStub.js`}
          strategy="beforeInteractive"
          type={`text/javascript`}
          charSet={`UTF-8`}
          data-domain-script={process.env.oneTrustId}
        />
        <Script id="OneTrustInitialization" strategy="afterInteractive">
          {`function OptanonWrapper() { window.dataLayer ? window.dataLayer.push( { event: 'OneTrustGroupsUpdated' } ) : undefined }`}
        </Script>
        <Main />
        <div style={{ display: 'none' }}>
          <button id="ot-sdk-btn" className="ot-sdk-show-settings">
            <PERSON><PERSON>s
          </button>
        </div>
      </body>
    </Html>
  );
}
