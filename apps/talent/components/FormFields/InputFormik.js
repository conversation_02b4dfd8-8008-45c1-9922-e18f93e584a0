'use client';
import React, { memo, useEffect } from 'react';
import { useField } from 'formik';
import { Input } from '@components';

const InputFormik = (props) => {
  const { name, onSetValue } = props;
  const [
    { onChange, onBlur, value },
    { error, touched, initialValue },
    { setTouched, setValue },
  ] = useField(name);

  // Autofill validation trigger
  useEffect(() => {
    if (initialValue !== value && !touched) {
      setTouched(true);
    }
  }, [value, touched, setTouched, initialValue]);

  const handleChange = onSetValue
    ? (e) => setValue(onSetValue(e.target.value))
    : onChange;

  return (
    <Input
      type="text"
      onBlur={onBlur}
      onChange={handleChange}
      value={value}
      error={error}
      isTouched={touched}
      {...props}
    />
  );
};

export default memo(InputFormik);
