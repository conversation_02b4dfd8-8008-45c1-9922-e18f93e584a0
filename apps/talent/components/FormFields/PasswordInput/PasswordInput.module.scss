@use '@styles/variables' as *;

.password-input-container {
  position: relative;
  width: 100%;
}

.password-field-image {
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  width: 29px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: $space-5;
  height: 46px;
}

.password-icon-container {
  position: relative;
  width: 20px;
  cursor: pointer;
  height: 16px;
  background-image: url('#{$assetUrl}/assets/icons/icon-pupil.svg');
  background-repeat: no-repeat;
  background-size: 18px 16px;

  &:hover {
    filter: invert(24%) sepia(42%) saturate(2140%) hue-rotate(240deg)
      brightness(99%) contrast(86%);
  }

  &.show-password {
    background-image: url('#{$assetUrl}/assets/icons/icon-pupil-closed.svg');
    background-size: 19px 17px;
  }
}

.password-field {
  position: relative;
  width: 100%;
}
