@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.payment-form {
  border-radius: 10px;
  height: 100%;

  @include tablet {
    box-shadow: $shadow-card-container;
    margin: 0 $space-20 $space-50;
    height: fit-content;
  }
}

.payment-form-header {
  display: flex;
  align-items: flex-end;
  background-image: $gradient-port-gore;
  color: $white;
  padding: $space-15 $space-20;
  justify-content: space-between;

  &.lifetime {
    padding: $space-20;
  }

  @include tablet {
    padding: $space-20;
    border-radius: 10px 10px 0 0;
  }
}

.payment-plan {
  display: flex;
  gap: $space-5;
  height: 100%;
  align-items: flex-end;
}

.payment-plan-title {
  font-size: 14px;
  font-weight: 700;
  transition: font-size 0.2s ease-in-out;

  @include tablet {
    line-height: 34px;
    font-size: 30px;
  }
}

.payment-plan-title-break {
  display: initial;

  @include tablet {
    display: none;
  }
}

.payment-certificates-container {
  display: flex;
  justify-content: center;
  gap: $space-10;

  @include mobile {
    gap: $space-50;
  }
}

.payment-certificate {
  display: flex;
  align-items: center;
  gap: $space-10;
  flex-direction: column;

  @include tablet {
    flex-direction: row;
  }
}

.payment-certificate-title {
  font-weight: 300;
  font-size: 12px;
}

.payment-plan-sub-title {
  font-size: 12px;
  font-weight: 300;

  @include tablet {
    font-size: 14px;
    display: initial;
  }
}

.payment-price-container {
  display: flex;
  align-items: flex-end;
  flex-direction: row;
  gap: $space-5;

  .payment-price-currency {
    font-size: 0.6em;
  }
}

.payment-price {
  font-size: 16px;
  font-weight: 300;
  text-decoration: line-through;
  opacity: 0.8;
  line-height: 1.2;

  @include tablet {
    font-size: 18px;
  }
}

.payment-actual-price-container {
  display: flex;
  gap: $space-5;
  align-items: flex-end;
}

.payment-price-with-discount {
  font-size: 24px;
  line-height: 1;
  font-weight: 700;

  @include tablet {
    font-size: 26px;
  }
}

.payment-form-content {
  display: flex;
  width: 100%;
}

.payment-plan-container {
  padding: 70px $space-60 $space-60;
  background-color: $black;
  background-image: $gradient-electric-ultramarine;
  color: $white;
  border-bottom-left-radius: 10px;
  display: none;
  flex-direction: column;
  gap: $space-40;

  @include mobile {
    width: 375px;
  }

  @include tablet {
    display: flex;
  }
}

.payment-information-container {
  display: flex;
  flex-direction: column;
  padding: $space-20 $space-20 0;
  width: 100%;

  @include mobile {
    padding: $space-20 $space-20 $space-30;
  }

  @include tablet {
    padding: $space-20 $space-60 $space-30 $space-50;
    max-width: 650px;
  }
}

.payment-button-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;

  @include tablet {
    padding: $space-10 0;
  }
}

.payment-description {
  font-weight: 300;
  font-size: 12px;
  text-align: center;
  background-color: $grey-10;
  margin: 20px -20px;
  padding: 20px;

  &.payment-description-bottom {
    background-color: $white;
    padding: 0;
    margin: 0;
  }

  @include tablet {
    margin: 10px 0 0;
    background-color: $white;
    padding: 20px 0;
  }
}

.link {
  color: $blue-100;
  cursor: pointer;
  font-weight: 400;
  background-color: transparent;
  border: none;
  font-size: 12px;
  padding: 0;
}

.payment-button-label {
  font-weight: 400;
}

.payment-method-container {
  display: flex;
  gap: 18px;
  padding-bottom: 20px;

  @include tablet {
    gap: $space-20;
    align-items: center;
    width: 100%;
  }
}

.payment-method-mobile {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: $space-10;
  border-radius: 8px;
  flex-basis: 50%;
  border: 1px solid $grey-40;
  height: 53px;
  font-size: 14px;
  font-weight: 700;
  cursor: pointer;

  &.active {
    border: 2px solid $violet-100;
    background-color: rgb($violet-100, 0.04);
  }

  &:hover {
    background-color: rgb($violet-100, 0.04);
  }
}

.separator {
  display: none;
  height: 1px;
  background-color: $grey-60;
  margin: 0 0 20px;
  border: none;

  @include tablet {
    display: block;
  }
}

.payment-information-title-container {
  display: none;
  width: 100%;
  align-items: center;
  gap: $space-20;
  padding-bottom: 20px;

  @include tablet {
    display: flex;
  }

  img {
    filter: invert(14%) sepia(70%) saturate(1524%) hue-rotate(230deg)
      brightness(92%) contrast(97%);
  }
}

.payment-information-title {
  font-size: 16px;
  font-weight: 700;
  font-family: Roboto, sans-serif;
  color: $violet-100;
}

.form-row {
  display: flex;
  align-items: center;
  gap: $space-30;
  flex-direction: column;
  padding-bottom: $space-10;

  @include tablet {
    gap: $space-40;
  }

  @include desktop {
    flex-direction: row;
  }
}

.form-inner-row {
  width: 100%;
  display: flex;
  align-items: center;
  gap: $space-20;
}

.payment-plan-features {
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: $space-10;
}

.payment-plan-features-title {
  margin: 0;
}

.payment-plan-feature {
  list-style-type: none;
  display: flex;
  gap: $space-20;
  align-items: flex-start;
}

.payment-plan-feature-icon {
  margin-top: $space-5;
  filter: invert(100%) sepia(1%) saturate(0%) hue-rotate(141deg)
    brightness(108%) contrast(100%);
}

.payment-form-footer {
  display: flex;
  flex-direction: column;
  gap: $space-20;
  margin: 0 (-$space-20);
  padding: 0 $space-20;

  @include tablet {
    padding: 0 $space-20 $space-20;
    background-color: $white;
  }
}

.form-field {
  display: flex;
  width: 100%;
  align-items: flex-end;
  gap: $space-20;
}

.cvv-icon-container {
  width: 100%;
  display: flex;
  align-items: center;

  @include desktop {
    width: initial;
  }
}

.cvv-icon {
  filter: invert(73%) sepia(2%) saturate(436%) hue-rotate(260deg)
    brightness(90%) contrast(90%);
  margin-bottom: 2px;
}

.location {
  display: flex;
  font-weight: 300;
  min-width: 50%;
  justify-content: flex-start;
  align-items: center;

  @include desktop {
    align-items: flex-end;
    justify-content: flex-end;
  }
}

.stripe-separator {
  display: flex;
  align-items: center;
  text-align: center;
  color: $grey-60;
  padding: 20px 0;

  &::before,
  &::after {
    content: '';
    flex: 1;
    border-bottom: 1px solid $grey-40;
  }

  &::before {
    margin-right: 10px;
  }

  &::after {
    margin-left: 10px;
  }
}
