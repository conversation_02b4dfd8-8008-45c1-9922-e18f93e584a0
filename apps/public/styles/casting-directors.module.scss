@import '../styles/mixins';
@import '../styles/variables';

.container {
  display: flex;
  flex-direction: column;
  margin-left: auto;
  margin-right: auto;
  max-width: $content-max-width;
  width: 100%;
  justify-content: center;
  padding-left: $space-20;
  padding-right: $space-20;

  @include desktop {
    padding-left: $space-60;
    padding-right: $space-60;
  }
}

.main-banner {
  align-items: stretch;
  display: flex;
  width: 100%;
  padding: 42px 0 0;
  background: $gradient-violet-supper;
  overflow: hidden;

  @include desktop {
    padding: 0;
  }
}

.main-banner-body {
  text-align: center;
  flex-direction: column-reverse;
  padding: 0 $space-20;
  width: 100%;
  max-width: $content-max-width;

  @include desktop {
    padding: 0 $space-60;
    min-height: 569px;
    text-align: left;
    background-position: right bottom;
    flex-direction: row;
    justify-content: flex-start;
  }
}

.main-banner-hero {
  text-align: center;
  width: 100%;

  @include desktop {
    align-self: flex-end;
    order: 2;
  }
}

.main-banner-image {
  margin-left: -18px;
  margin-right: auto;

  & img {
    width: 106%;
    height: auto;
    margin-bottom: -23px;
    max-width: 684px;
  }

  @include tablet {
    margin-left: 0;
    margin-right: -90px;

    & img {
      width: 100%;
      margin-bottom: -8px;
    }
  }
}

.main-banner-welcome {
  z-index: 1;
  margin: 0 auto 7px;

  @include desktop {
    margin: 0;
    width: 50%;
    min-width: 462px;
    padding: 126px 0 110px;
    order: 1;
  }
}

.main-banner-title {
  font-style: normal;
  font-size: 28px;
  line-height: 1.3;
  color: $violet-70;
  font-weight: 800;
  margin: 0 0 23px;

  @include desktop {
    font-size: 48px;
    margin-bottom: 22px;
  }
}

.main-banner-text {
  font-style: normal;
  font-size: 16px;
  line-height: 1.6;
  color: $violet-70;
  font-weight: 400;
  margin: 0 auto $space-20;
  max-width: 462px;

  @include desktop {
    max-width: none;
    margin: 0 0 22px;
  }
}

.statistics-container {
  padding-left: 0;
  padding-right: 0;

  @include tablet {
    padding-left: $space-20;
    padding-right: $space-20;
  }

  @include desktop {
    padding-left: $space-60;
    padding-right: $space-60;
  }
}

.statistics-panel {
  display: grid;
  width: 100%;
  grid-template-columns: repeat(2, 1fr);
  flex-direction: row;
  padding: 39px 24px $space-40;
  gap: $space-30 28px;
  background: $white;
  box-shadow: $shadow-statistics-panel;

  @include tablet {
    transform: translateY(-50%);
    padding: $space-40 $space-50;
    border-radius: 4px;
  }

  @include desktop {
    padding: $space-30 64px;
    grid-template-columns: repeat(4, auto);
    gap: 0 74px;
  }
}

.statistics-box {
  display: flex;
  flex-direction: column;
  row-gap: $space-5;

  @include desktop {
    row-gap: 0;
  }
}

.statistics-title {
  font-weight: 800;
  font-size: 24px;
  color: $black;

  @include desktop {
    font-size: 30px;
    text-align: center;
  }
}

.statistics-text {
  font-weight: 300;
  font-size: 16px;
  color: $black;

  @include desktop {
    text-align: center;
  }
}

.with-us-container {
  padding-top: 38px;

  @include desktop {
    padding-top: 13px;
    padding-bottom: 28px;
  }
}

.with-us {
  display: grid;
  flex-direction: column;
  align-items: flex-start;
  padding-bottom: 66px;
  gap: 39px 0;

  @include tablet {
    grid-template-columns: repeat(2, 1fr);
    gap: 28px $space-35;
  }

  @include desktop {
    grid-template-columns: repeat(3, 1fr);
  }
}

.with-us-title {
  font-weight: 800;
  font-size: 24px;
  color: $black;
  margin-bottom: 34px;

  @include desktop {
    font-size: 32px;
    margin-bottom: $space-50;
  }
}

.with-us-heading {
  font-weight: 700;
  font-size: 18px;
  color: $black;
  margin-bottom: $space-15;
}

.with-us-paragraph {
  font-weight: 300;
  font-size: 16px;
  line-height: 1.6;
  color: $black;
}

.more-talent {
  position: relative;
  display: flex;

  img {
    width: 100%;
    height: auto;
    border-radius: 4px;
  }
}

.more-talent-go {
  position: absolute;
  display: flex;
  align-items: flex-end;
  right: 11%;
  top: 50%;
  transform: translateY(-50%);
  font-weight: 700;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: -0.02em;
  text-transform: capitalize;
  color: $white;

  & .arrow {
    display: inline-block;
    margin-left: $space-5;
    margin-bottom: $space-5;
  }
}

.company-section-container {
  @include desktop {
    padding-left: $space-60;
    padding-right: $space-60;
  }
}

.company-section {
  padding-top: $space-50;
  padding-bottom: 58px;
  background-color: $grey-10;

  @include desktop {
    padding-top: $space-45;
    padding-bottom: 88px;
  }
}

.company-info {
  @include tablet {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: $space-10;
  }
}

.company-info-title {
  font-weight: 800;
  font-size: 24px;
  line-height: 1.3;
  color: $black;
  margin-bottom: 23px;

  @include desktop {
    font-size: 32px;
  }
}

.company-info-text {
  font-weight: 300;
  font-size: 16px;
  line-height: 1.6;
  color: $black;
}

.help-section {
  padding: 54px 0 53px;

  @include desktop {
    padding: 89px 0 92px;
  }
}

.help-heading {
  font-weight: 800;
  font-size: 24px;
  line-height: 1.3;
  color: $black;
  margin-bottom: $space-25;
  text-align: center;

  @include desktop {
    font-size: 30px;
  }
}

.help-text {
  font-size: 16px;
  line-height: 160%;
  color: $black;
  margin: auto;
  text-align: center;
  font-weight: 300;

  @include desktop {
    max-width: 815px;
    margin-bottom: 48px;
  }
}

.help-btn-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}

.talents-section {
  padding: $space-55 0 0;

  @include desktop {
    padding: 95px 0 0;
  }
}

.talents-section-heading {
  max-width: 610px;
  font-weight: 800;
  font-size: 24px;
  line-height: 1.3;
  text-align: center;
  color: $black;
  margin: auto auto 0;

  @include desktop {
    font-size: 30px;
  }
}

.talent-btn-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}

.how-works-section {
  background: $grey-10;
  padding-top: 57px;
  padding-bottom: 54px;

  @include desktop {
    padding-top: 90px;
    padding-bottom: 90px;
  }
}

.how-works-section-container {
  padding: 0 $space-20;
  width: 100%;
  max-width: $content-max-width;

  @include desktop {
    padding: 0 $space-60;
  }
}

.blog-section-container {
  padding: 0 $space-20;
  width: 100%;
  max-width: $content-max-width;

  @include desktop {
    padding: 0 $space-60;
  }
}

.how-works-heading {
  font-weight: 800;
  font-size: 24px;
  line-height: 1.3;
  text-align: center;
  color: $black;
  margin-bottom: 27px;

  @include desktop {
    font-size: 30px;
    margin-bottom: 24px;
  }
}

.how-works-subtitle {
  font-weight: 300;
  font-size: 16px;
  line-height: 1.6;
  text-align: center;
  color: $black;
  max-width: 721px;
  margin: auto auto 54px;

  @include desktop {
    margin-bottom: $space-50;
  }
}

.how-works {
  display: grid;
  grid-gap: 57px $space-20;
  margin: 0 auto $space-30;
  max-width: 450px;

  @include desktop {
    grid-template-columns: repeat(4, 1fr);
    margin: 0 0 48px;
    max-width: none;
  }
}

.how-works-block {
  position: relative;
  padding-left: 84px;

  &:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 41px;
    left: 30px;
    height: 100%;
    border-left: 1px dashed $grey-60;
  }

  @include desktop {
    padding-left: 0;
    max-width: none;
    margin: 0;

    &:not(:first-child)::before {
      content: '';
      position: absolute;
      top: 24px;
      left: -11px;
      width: 50%;
      height: auto;
      border-left: none;
      border-top: 1px dashed $grey-60;
    }

    &:not(:last-child)::after {
      top: 24px;
      right: -11px;
      left: auto;
      width: 50%;
      height: auto;
      border-top: 1px dashed $grey-60;
    }
  }
}

.how-works-icon {
  position: absolute;
  top: -15px;
  left: 2px;
  display: block;
  z-index: 1;

  @include desktop {
    position: relative;
    margin: auto auto 18px;
    top: auto;
    left: auto;
  }
}

.how-works-title {
  font-weight: 700;
  font-size: 18px;
  color: $black;
  margin-bottom: 14px;

  @include desktop {
    text-align: center;
  }
}

.how-works-text {
  font-weight: 300;
  font-size: 16px;
  line-height: 1.5;
  font-feature-settings: 'liga' off;
  color: $black;

  @include desktop {
    padding: 0 38px;
    text-align: center;
  }
}

.how-works-btn-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}

.devoted-team {
  display: grid;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding-top: 36px;
  padding-bottom: $space-50;

  @include tablet {
    margin: 0;
    grid-template-columns: 1fr 1fr;
    grid-column-gap: $space-30;
  }

  @include desktop {
    padding-top: 120px;
    padding-bottom: 93px;
    margin: 0 9%;
    grid-template-columns: auto 1fr;
    grid-column-gap: 106px;
  }

  & .contact-link {
    color: $blue-100;
    font-weight: 400;
    cursor: pointer;
    font-size: 14px;
  }
}

.devoted-team-name {
  font-weight: 700;
  font-size: 24px;
  line-height: 1.3;
  color: $black;
  margin-bottom: 9px;

  @include tablet {
    margin-top: auto;
    margin-bottom: $space-10;
  }
}

.devoted-team-role {
  position: relative;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.3;
  color: $black;
  margin-bottom: $space-10;

  @include tablet {
    margin-bottom: 3px;
  }
}

.devoted-team-image {
  width: 100%;
  height: auto;
  max-width: 424px;
  max-height: 442px;
  margin: auto auto $space-35;
  align-self: flex-start;

  @include tablet {
    margin: 0;
  }
}

.devoted-team-user {
  @include tablet {
    margin-top: auto;
  }
}

.devoted-team-info {
  position: relative;
  align-self: flex-start;

  @include tablet {
    display: flex;
    flex-flow: column nowrap;
    align-self: stretch;
  }
}

.devoted-team-heading {
  font-weight: 800;
  font-size: 24px;
  line-height: 1.3;
  color: $black;
  text-align: center;
  margin-bottom: 33px;

  &.desktop {
    display: none;
  }

  @include tablet {
    margin-bottom: 42px;
    text-align: left;
    font-size: 30px;

    &.mobile {
      display: none;
    }

    &.desktop {
      display: block;
    }
  }
}

.devoted-team-text {
  font-weight: 300;
  font-size: 18px;
  line-height: 1.6;
  color: $black;
  margin-bottom: $space-30;

  @include tablet {
    margin-bottom: $space-40;
  }
}

.members-block {
  display: grid;
  justify-content: center;
  align-items: center;
  flex-direction: row;
  grid-template-columns: repeat(2, 1fr);
  gap: 4px $space-30;
  margin: 0 17px $space-35;

  @include desktop {
    grid-template-columns: repeat(4, 1fr);
    gap: 0 $space-60;
    margin: 0 90px 93px;
  }
}

.member {
  display: flex;
  flex-flow: column nowrap;
  justify-content: center;
  align-items: center;
  padding: 0 0 $space-30;
}

.member-image {
  display: block;
  width: 110px;
  margin-bottom: 27px;

  @include tablet {
    width: 160px;
    margin-bottom: 31px;
  }

  img {
    width: 100%;
    height: auto;
  }
}

.member-name {
  font-weight: 700;
  font-size: 16px;
  line-height: 1.3;
  text-align: center;
  color: $black;
  margin-bottom: 6px;

  @include tablet {
    font-size: 18px;
  }
}

.member-role {
  font-weight: 400;
  font-size: 14px;
  line-height: 1.3;
  text-align: center;
  color: $black;

  @include tablet {
    font-size: 16px;
  }
}

.blog-section {
  position: relative;
  display: flex;
  flex-direction: column;
  background: $grey-10;
  padding: $space-50 0 54px;

  @include desktop {
    padding: 91px 0 57px;
  }
}

.blog-heading {
  display: flex;
  margin-bottom: $space-20;
  align-items: center;
  justify-content: space-between;
  flex-flow: row wrap;
}

.blog-heading-title {
  font-weight: 800;
  font-size: 30px;
  line-height: 1.3;
  color: $black;
}

.blog-all-notes {
  font-size: 16px;
  color: $blue-100;
  font-weight: 400;
  cursor: pointer;
}

.faq {
  padding: 42px 0 23px;

  @include desktop {
    padding: 73px 0 61px;
  }
}

.faq-heading {
  font-weight: 800;
  font-size: 30px;
  line-height: 1.3;
  color: $black;
  margin-bottom: 2px;

  @include desktop {
    margin-bottom: 42px;
  }
}

.blogs-list {
  display: none;

  @include tablet {
    width: auto;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-column-gap: $space-15;
  }

  @include desktop {
    grid-column-gap: 42px;
  }
}

.blog-box {
  background-color: $white;
  box-shadow: $shadow-blog-box;
  display: flex;
  flex-direction: column;
  height: 100%;
  transition: all 0.3s ease-in-out;

  &:hover {
    box-shadow: $shadow-blog-box-hover;
  }

  img {
    display: block;
    width: 100%;
    height: 100%;
  }

  .blog-info {
    padding: $space-25 $space-30;
  }

  .blog-title {
    font-size: 14px;
    font-weight: 700;
    color: $black;
    line-height: 1.5;
    word-break: break-word;

    @include mobile {
      font-size: 18px;
    }
  }
}

.blog-carousel-container {
  display: initial;

  .blog-box {
    margin: $space-40 $space-20;
    height: calc(100% - 80px);
  }

  @include tablet {
    display: none;
  }
}

.help-list {
  display: none;

  @include tablet {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-column-gap: $space-15;
    margin-bottom: $space-50;
  }

  @include desktop {
    grid-column-gap: 32px;
  }
}

.help-box {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 24px 14px $space-30;
  margin: $space-40 $space-10;
  background: $white;
  box-shadow: $shadow-statistics-panel;
  border-radius: 4px;
  transition: all ease-in-out 0.3s;
  cursor: pointer;

  .help-box-title {
    font-weight: 700;
    font-size: 18px;
    color: $black;
    margin-top: 24px;
    margin-bottom: 14px;

    @include desktop {
      margin-top: 19px;
    }
  }

  .help-box-text {
    font-weight: 300;
    font-size: 14px;
    line-height: 1.5;
    color: $black;
    word-break: break-word;

    @include mobile {
      font-size: 16px;
    }
  }

  @include desktop {
    padding: 24px 24px $space-40;
    margin: 0;
  }

  &:hover {
    box-shadow: $shadow-blog-box-hover;
  }
}

.help-carousel-container {
  display: block;
  margin-bottom: $space-30;

  .help-box {
    margin: $space-40 $space-20;
    height: calc(100% - 80px);
    cursor: grab;

    &:active {
      cursor: grabbing;
    }

    .help-box-text {
      flex: 1;
    }
  }

  @include tablet {
    display: none;
  }
}

.carousel-quote-container {
  width: 100%;

  .quote-box {
    box-sizing: border-box;
    display: flex;
    position: relative;
    flex-flow: column nowrap;
    justify-content: space-between;
    margin: $space-40 $space-20;
    padding: 34px $space-35 29px;
    background: $white;
    box-shadow: $shadow-quote-box;
    border-radius: 16px 16px 16px 0;
    transition: all ease-in-out 0.3s;

    &:hover {
      box-shadow: $shadow-quote-box-hover;
    }

    @include desktop {
      padding: $space-40 80px $space-40 178px;
    }
  }

  .quote-icon {
    display: none;

    @include desktop {
      display: block;
      position: absolute;
      left: 79px;
      font-weight: 300;
      font-size: 20px;
      width: 74px;
      height: 75.55px;
    }
  }

  .quote-text {
    font-weight: 300;
    font-size: 16px;
    line-height: 1.5;
    color: $black;
    margin-bottom: 24px;
    word-break: break-word;

    @include desktop {
      margin-bottom: 29px;
    }
  }

  .quote-author {
    font-weight: 700;
    font-size: 16px;
    line-height: 1.6;
    color: $black;

    span {
      display: none;
    }

    @include desktop {
      span {
        display: inline;
      }
    }
  }

  .quote-author-info {
    font-weight: 400;
    font-size: 14px;
    line-height: 1.82;
    color: $black;
  }

  @include tablet {
    max-width: 650px;
  }
}

.carousel-talent-container {
  width: 100%;
  margin-bottom: $space-40;
  margin-top: $space-20;
  position: relative;

  .talent-slide {
    display: flex;
    flex-direction: column;
    height: 240px;
    box-shadow: $shadow-blog-box;
    margin: $space-40 0 $space-60;
    transition: all 0.3s ease-in-out;
    border-radius: 4px;
    width: 180px;
    flex-shrink: 0;

    &:hover {
      box-shadow: $shadow-talent-slide-hover;
      transform: scale(1.1);
    }
  }

  .details {
    padding: $space-10 $space-20;
  }

  .image-container {
    width: 180px;
    height: 180px;
    border-top-right-radius: 4px;
    border-top-left-radius: 4px;

    .image {
      display: block;
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-top-right-radius: 4px;
      border-top-left-radius: 4px;
    }
  }

  .talent-name {
    font-weight: 700;
    font-size: 14px;
    color: $black;
    margin-bottom: $space-15;
  }

  .talent-location-container {
    display: flex;
    align-items: center;
    gap: $space-10;
    font-weight: 400;
    font-size: 12px;
    color: $grey-100;
    line-height: 1;
  }

  @include mobile {
    padding: 0 $space-20;
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -20px;
    bottom: 0;
    width: 80px;
    background: $gradient-black-snooze;
    z-index: 1;
    display: block;

    @include mobile {
      width: 90px;
    }

    @include desktop {
      left: -60px;
      width: 180px;
    }
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: -20px;
    bottom: 0;
    width: 80px;
    background: $gradient-black-snooze-reverse;
    z-index: 1;
    display: block;

    @include mobile {
      width: 90px;
    }

    @include desktop {
      right: -60px;
      width: 180px;
    }
  }
}
