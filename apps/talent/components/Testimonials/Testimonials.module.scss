@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.container {
  width: calc(100% + calc(var(--header-side-padding) * 2));
}

.card {
  --img-size: 42px;
  --card-gap: 15px;

  background-color: $white;
  color: $black;
  border-radius: 16px;
  margin: 10px 20px 23px;
  width: 100%;
  max-width: 600px;
  position: relative;
  box-shadow: 0 1px 10px #0000004d;
  font-weight: 400;

  @include desktop {
    --img-size: 52px;

    overflow: unset;
    margin: 0 20px 35px;
    min-height: 136px;
  }
}

.content {
  padding: 4.5% 3%;
  display: flex;
  gap: var(--card-gap);

  &.brands {
    flex-direction: column;
    gap: 5px;
    padding: 20px 15px;
  }

  @include desktop {
    padding: 20px 30px;
  }
}

.right {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.image-container {
  position: relative;
  border: 2px solid $grey-20;
  border-radius: 50%;
  width: var(--img-size);
  height: var(--img-size);
  flex-shrink: 0;

  @include desktop {
    border: 2px solid #f0f0f0;
    background-color: #f0f0f0;
  }
}

.image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.name {
  font-weight: 600;

  @include desktop {
    order: 3;
  }
}

.text {
  position: relative;
  left: calc((var(--img-size) + var(--card-gap)) * -1);
  width: calc(100% + var(--img-size) + var(--card-gap));

  @include desktop {
    position: initial;
    left: unset;
    width: initial;
  }
}

.brand-title {
  font-size: 14px;
  font-weight: 600;
  text-align: center;

  @include desktop {
    padding-top: 20px;
    padding-bottom: 5px;
  }
}
