@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.progress-wrapper {
  position: fixed;
  inset: 55px 0 0;
  background: white;
  border-radius: 20px 20px 0 0;
  z-index: 1001;

  @include tablet {
    position: absolute;
    inset: 100% -22px auto auto;
    padding: $space-50 70px;
    border-radius: 10px;
    width: 754px;
    max-width: 870px;
  }

  @include desktop {
    width: 870px;
  }
}

.progress-content {
  display: grid;
  height: 100%;
  overflow: auto;
  padding-bottom: $space-30;
  border-radius: 20px 20px 0 0;

  @include tablet {
    border-radius: 0;
    grid-template-columns: minmax(230px, 230px) 1fr;
    grid-column-gap: $space-20;
    padding-bottom: 0;
  }

  @include desktop {
    grid-column-gap: $space-40;
  }
}

.progress-close {
  position: absolute;
  top: 18px;
  right: 20px;
  width: 25px;
  height: 25px;
  cursor: pointer;
  z-index: 2;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    right: 0;
    left: 0;
    margin: 0 auto;
    width: 100%;
    height: 1px;
    background-color: $grey-80;
    transform: rotate(45deg);
  }

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 0;
    left: 0;
    margin: 0 auto;
    width: 100%;
    height: 1px;
    background-color: $grey-80;
    transform: rotate(-45deg);
  }
}

.progress-title {
  font-size: 24px;
  font-weight: 700;
  color: $black;
  margin-bottom: $space-20;

  @include tablet {
    margin-bottom: 28px;
  }
}

.progress-rating {
  padding: $space-30;
  border-radius: 10px 10px 0 0;
  background-color: $grey-10;

  .progress-title {
    text-align: center;
    margin-bottom: $space-5;
  }

  @include tablet {
    background-color: transparent;
    border-radius: 0;
    padding: 0;
    grid-column: 1/2;
    grid-row: 1/2;

    .progress-title {
      text-align: left;
      margin-bottom: 0;
    }
  }
}

.profile-rating {
  display: flex;
  align-items: flex-start;
}

.profile-rating-count {
  display: flex;
  align-items: center;
  font-weight: 600;
  font-size: 21px;
  color: red;

  span {
    margin: 0 0 0 4px;
    color: $black;
  }
}

.profile-rating-star {
  width: 18px;
  height: 18px;
  margin-right: 4px;
  color: $blue-60;

  @include tablet {
    width: 25px;
    height: 26px;
  }
}

.progress-percentage {
  width: 100%;
  height: 100%;
  align-self: center;
  justify-content: space-around;
}

.circular-chart {
  display: block;
  width: 100%;
  height: 100%;
}

.progress-good {
  stroke: $blue-80;
}

.progress-bad {
  stroke: $red-60;
}

.circular-chart-bg {
  fill: none;
  stroke: $grey-20;
  stroke-width: 1.4;
}

.circular-chart-percentage {
  fill: none;
  stroke-width: 1.4;
  stroke-linecap: round;
  animation: progress 1s ease-out forwards;
}

@keyframes progress {
  0% {
    stroke-dasharray: 0 100;
  }
}

div.progress-user-rating {
  margin-bottom: $space-20;
}

.user-image-block {
  position: relative;
  width: 250px;
  height: 250px;
  margin: auto auto 4px;

  @include tablet {
    margin: 17px 0 3px -10px;
  }
}

.user-profile-image {
  position: absolute;
  inset: 29px;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: 50% 50%;
  border-radius: 100%;
}

.progress-statistic {
  padding: $space-30 $space-20 0;

  @include tablet {
    padding: 0;
    grid-row: 1/4;
  }
}

.progress-statistic-content {
  display: grid;
  grid-row-gap: 33px;

  @include tablet {
    grid-row-gap: $space-45;
  }
}

.progress-statistic-header {
  display: flex;
  align-items: flex-start;
}

.progress-statistic-title {
  font-weight: 700;
  color: $black;
  min-width: 71px;
  margin-right: $space-10;
  margin-bottom: $space-5;
}

.progress-statistic-filling {
  position: relative;
  background: $grey-20;
  width: 140px;
  height: 20px;
  border-radius: 30px;

  > div {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    background: $black;
    border-radius: 30px;
    color: $white;
    font-size: 12px;
    font-weight: 600;
    min-width: 30px;
  }
}

.progress-statistic-text {
  font-weight: 400;
  color: $black;
  line-height: 1.6;
  margin-bottom: 8px;
}

.progress-statistic-link {
  color: $blue-100;
  font-weight: 500;
  line-height: 1.3;
  align-items: baseline;

  &::after {
    content: '';
    display: inline-flex;
    width: 15px;
    height: 8px;
    margin-left: 3px;
    background: url('#{$assetUrl}/assets/icons/icon-arrow-right-blue.svg') 0 0
      no-repeat;
  }

  &:hover {
    cursor: pointer;
  }
}

.user-rating {
  display: flex;
  align-items: flex-start;
  justify-content: center;

  @include tablet {
    margin-bottom: 26px;
  }
}

.user-rating-count {
  display: flex;
  align-items: center;
  font-weight: 600;
  font-size: 16px;
  color: $grey-80;

  span {
    margin: 0 0 0 4px;
    color: $black;
    font-weight: 700;
  }

  @include tablet {
    font-size: 14px;
  }
}

.user-rating-star {
  width: 20px;
  height: 20px;
  margin-right: 4px;
  color: $yellow-60;
}
