@use '@styles/mixins' as *;
@use '@styles/variables' as *;

.icon {
  margin-left: $space-5;
  cursor: pointer;
  border-radius: 50%;
  padding: $space-5;

  &:hover {
    background-color: $grey-10;
    cursor: pointer;
  }
}

.tooltip {
  display: flex;
  flex-direction: column;
  gap: $space-5;
}

.tooltip-action {
  color: $red-60;
  display: flex;
  align-items: center;
  gap: $space-5;
  background-color: transparent;
  border: none;
  padding: 0;

  &:hover {
    cursor: pointer;
    text-decoration: underline;
  }
}

.tooltip-container {
  display: flex;
  align-items: center;
  height: 100%;
  cursor: pointer;
}
