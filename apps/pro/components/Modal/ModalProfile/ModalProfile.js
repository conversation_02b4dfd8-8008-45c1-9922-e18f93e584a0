'use client';
import { memo, useCallback, useEffect, useState } from 'react';
import { Loading, Modal, ProfilePreview } from '@components';
import styles from './ModalProfile.module.scss';
import Api from '@services/api';
import { useNotifications } from '@contexts/NotificationContext';
import { extractResult } from '@utils/extractPromiseResult';
import { formatTalentProfile } from '@utils/formatTalentProfile';
import { formatProProfile } from '@utils/formatProProfile';
import { ErrorMessage } from '@constants/form';

const ModalProfile = ({
  id,
  onClose,
  onInvite,
  isInvited,
  criteria,
  isFullMatch,
  role,
  isDeclined,
  onApprove,
  onDecline,
  isApproved,
  isApplied,
}) => {
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [canContactTalent, setCanContactTalent] = useState(false);
  const { setNotification } = useNotifications();

  const onError = useCallback(
    (message) => {
      setNotification({
        type: 'error',
        message: message || ErrorMessage.Unexpected,
        timeout: '5000',
      });

      onClose();
    },
    [onClose, setNotification],
  );

  useEffect(() => {
    const getProfile = async () => {
      const paths = [
        `/profiles/${id}?expand=personal_url,location,categories,touches`,
        `/profiles/${id}/images?expand=profile_image_characteristics&width=600&height=800`,
      ];

      const results = await Promise.allSettled(
        paths.map((path) => Api.clientside(path)),
      );

      const [profileResponse, imagesResponse] = results.map((result) =>
        extractResult(result, {}),
      );

      if (profileResponse.status !== 'ok' || imagesResponse.status !== 'ok') {
        onError(profileResponse.message || imagesResponse.message);
      } else {
        if (profileResponse.rel === 'agent') {
          setProfile(formatProProfile(profileResponse, imagesResponse.items));
        } else {
          await onExtendTalentProfile(profileResponse, imagesResponse);
        }
      }
    };

    if (id) {
      getProfile()
        .then(() => setLoading(false))
        .catch(() => onError(''));
    }
  }, [id, onError]);

  const onExtendTalentProfile = async (profileResponse, imagesResponse) => {
    const paths = [
      `/profiles/${id}/ethnicities`,
      `/profiles/${id}/notes`,
      '/clientstatus/info',
    ];

    const results = await Promise.allSettled(
      paths.map((path) => Api.clientside(path)),
    );

    const [ethnicitiesResponse, notesResponse, clientStatus] = results.map(
      (result) => extractResult(result, {}),
    );

    if (
      ethnicitiesResponse.status !== 'ok' ||
      notesResponse.status !== 'ok' ||
      clientStatus.status !== 'ok'
    ) {
      onError(
        ethnicitiesResponse.message ||
          notesResponse.message ||
          clientStatus.message,
      );
    } else {
      setCanContactTalent(
        clientStatus.info?.allowed_to?.hasOwnProperty('contact_talent') ||
          false,
      );
      setProfile(
        formatTalentProfile(
          profileResponse,
          imagesResponse.items,
          ethnicitiesResponse.items[0],
          notesResponse.items,
        ),
      );
    }
  };

  return (
    <Modal
      backdropClose
      onClose={onClose}
      floatCloseButton
      showDefaultLayout={false}
      classNameContainer={styles['profile-modal']}
      showMobileBorderRadius={false}
      hideMobileCloseButton
      classNameContent={styles['profile-modal-content']}
    >
      {loading ? (
        <Loading minHeight="80px" padding="20px" />
      ) : (
        <ProfilePreview
          profile={profile}
          onClose={onClose}
          onInvite={() => {
            onInvite(profile.id);
            onClose();
          }}
          isInvited={isInvited}
          criteria={criteria}
          isFullMatch={isFullMatch}
          role={role}
          isDeclined={isDeclined}
          onApprove={onApprove}
          isApproved={isApproved}
          onDecline={onDecline}
          isApplied={isApplied}
          canContactTalent={canContactTalent}
        />
      )}
    </Modal>
  );
};

export default memo(ModalProfile);
