'use client';
import React, { memo } from 'react';
import { Modal } from '@components';
import styles from './ModalEditPhoto.module.scss';
import Image from 'next/image';

const ModalEditPhoto = ({
  onClose,
  onDelete,
  onMakeTitle,
  onUpload,
  title = '',
  isCurrentTitlePhoto = false,
}) => {
  return (
    <Modal
      backdropClose
      onClose={onClose}
      classNameContainer={styles['photo-edit-modal']}
      classNameOverlay={styles['photo-edit-modal-container']}
      showDefaultLayout={false}
    >
      <div className={styles['photo-edit-modal-title']}>Edit {title}:</div>
      <div className={styles['photo-edit-actions']}>
        <div className={styles['photo-edit-action-row']} onClick={onMakeTitle}>
          <Image
            className={isCurrentTitlePhoto ? styles['title-icon'] : ''}
            src={`/assets/icons/icon-title-photo${
              isCurrentTitlePhoto ? '-current-blue' : '-blue'
            }.svg`}
            width={isCurrentTitlePhoto ? 32 : 25}
            height={isCurrentTitlePhoto ? 25 : 25}
            alt="icon"
          />
          <span>
            {isCurrentTitlePhoto ? 'Adjust Title Photo' : 'Make Title'}
          </span>
        </div>
        <div className={styles['photo-edit-action-row']} onClick={onUpload}>
          <Image
            src="/assets/icons/icon-photo-blue.svg"
            width={25}
            height={25}
            alt="icon"
          />
          <span>Upload New Photo</span>
        </div>
        <div className={styles['photo-edit-action-row']} onClick={onDelete}>
          <Image
            src="/assets/icons/icon-trash-round-red.svg"
            width={25}
            height={25}
            alt="icon"
          />
          <span className={styles['remove-text']}>Remove Photo</span>
        </div>
      </div>
    </Modal>
  );
};

export default memo(ModalEditPhoto);
