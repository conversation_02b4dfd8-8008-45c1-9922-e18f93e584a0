'use client';
import React, { memo } from 'react';
import { Button, Loading, Modal } from '@components';
import styles from './ModalDeletePhoto.module.scss';

const ModalDeletePhoto = ({ onClose, onDeletePhoto, loading }) => {
  return (
    <Modal
      backdropClose={!loading}
      onClose={onClose}
      classNameContainer={styles['photo-delete-modal']}
      classNameOverlay={styles['photo-delete-modal-container']}
      showDefaultLayout={false}
    >
      {loading ? (
        <Loading padding="10px 0 30px 0" />
      ) : (
        <>
          <div className={styles['photo-delete-modal-title']}>
            Are you sure?
          </div>
          <div className={styles['photo-delete-modal-text']}>
            You have selected to delete this photo.
          </div>
          <div className={styles['photo-delete-modal-btn-container']}>
            <Button
              minWidth="130px"
              color="plain-blue"
              label="Cancel"
              onClick={onClose}
            />
            <Button
              minWidth="130px"
              color="plain-red"
              label="Delete"
              onClick={onDeletePhoto}
            />
          </div>
        </>
      )}
    </Modal>
  );
};

export default memo(ModalDeletePhoto);
