'use client';
import React, { createContext, useContext, useState } from 'react';
import {
  DEFAULT_SETTINGS,
  LIVE_CHAT_GROUP,
  LIVE_CHAT_VISIBILITY,
} from '@constants/liveChat';
import { CookieService } from '@services/cookieService';
import * as Sentry from '@sentry/nextjs';
import { deepEqual } from '@utils/deepEqual';
import { useAuth } from './AuthContext';
import Api from '@services/api';

const LiveChatContext = createContext({});

export const LiveChatProvider = ({ children }) => {
  const [settings, setSettings] = useState(DEFAULT_SETTINGS);

  const { isAuthenticated } = useAuth();

  const initiateLiveChat = async () => {
    if (isAuthenticated) {
      const customerInfo = await getCustomerInfo();

      if (customerInfo) {
        setSettings((previousSettings) => {
          const newSettings = {
            show: true,
            visibility: LIVE_CHAT_VISIBILITY.Minimized,
            group: LIVE_CHAT_GROUP,
            sessionVariables: customerInfo.sessionVariables,
            customerName: customerInfo.customerName,
          };

          return deepEqual(previousSettings, newSettings)
            ? previousSettings
            : newSettings;
        });
      } else {
        setSettings((previousSettings) =>
          deepEqual(previousSettings, DEFAULT_SETTINGS)
            ? previousSettings
            : DEFAULT_SETTINGS,
        );
      }
    }
  };

  const onVisibilityChanged = (value) => {
    if (value.visibility !== settings.visibility) {
      const widget = document.getElementById('chat-widget-container');

      if (widget) {
        widget.style.setProperty(
          'z-index',
          value.visibility === 'maximized' ? '10' : '8',
          'important',
        );
      }
      setSettings({ ...settings, visibility: value.visibility });
    }
  };

  const getCustomerInfo = async () => {
    try {
      const accountId = CookieService.getAccountCookie();
      const profiles = await Api.clientside(`/accounts/${accountId}/profiles`);
      const sessionVariables = {};

      const { id, client_id, firstname, lastname } = profiles?.items?.[0] || {};

      if (id) {
        sessionVariables.ProfileID = id;
      }

      if (client_id) {
        sessionVariables.ClientID = client_id;
      }

      if (firstname || lastname) {
        sessionVariables.Name = `${firstname} ${lastname}`;
      }

      return {
        sessionVariables,
        customerName: firstname || 'Casting Professional',
      };
    } catch (error) {
      Sentry.captureException(error);

      return null;
    }
  };

  return (
    <LiveChatContext.Provider
      value={{
        ...settings,
        initiateLiveChat,
        onVisibilityChanged,
      }}
    >
      {children}
    </LiveChatContext.Provider>
  );
};

export const useLiveChat = () => useContext(LiveChatContext);
