'use client';
import { memo, useState } from 'react';
import Image from 'next/image';
import styles from './PaymentForm.module.scss';
import { Loading } from '@components';
import { useNotifications } from '@contexts/NotificationContext';
import cn from 'classnames';
import { CookieService } from '@services/cookieService';
import Api from '@services/api';

const PaymentFormMethods = ({
  promoCode,
  memberLevelId,
  isAndroidDevice,
  isApplePaySupported,
}) => {
  const { setNotification } = useNotifications();
  const [isStripeSessionLoading, setIsStripeSessionLoading] = useState(false);

  const isGooglePaySupported = isAndroidDevice;

  const onPaymentButtonClick = async () => {
    setIsStripeSessionLoading(true);

    const stripeCheckoutSessionParams = new URLSearchParams();

    stripeCheckoutSessionParams.append('member_level', memberLevelId);

    if (promoCode) {
      stripeCheckoutSessionParams.append('promo_code', promoCode);
    }

    stripeCheckoutSessionParams.append(
      'success_url',
      `${process.env.baseUrl}/checkout/success`,
    );

    stripeCheckoutSessionParams.append(
      'cancel_url',
      `${process.env.baseUrl}/upgrade`,
    );

    const stripeCheckoutSession = await Api.clientside(
      `/stripe/checkout-session`,
      {
        body: stripeCheckoutSessionParams,
        method: 'POST',
      },
    );

    if (stripeCheckoutSession.status === 'ok') {
      window.sessionStorage.setItem(
        'invoiceHash',
        stripeCheckoutSession?.invoice_hash,
      );
      CookieService.setShowStripeCheckoutSuccess(true);
      window.location.href = stripeCheckoutSession?.session_url || '';
    } else {
      setNotification({
        type: 'error',
        message: stripeCheckoutSession?.message || 'Something went wrong',
        timeout: '5000',
      });
      setIsStripeSessionLoading(false);
    }
  };

  return (
    <>
      <div className={styles['payment-method-container']}>
        <div className={cn(styles['payment-method-mobile'], styles['active'])}>
          <Image
            src="/assets/icons/icon-credit-card.svg"
            alt="credit card icon"
            width={22}
            height={16}
          />
          Card
        </div>
        {isStripeSessionLoading && (
          <div className={styles['payment-method-mobile']}>
            <Loading />
          </div>
        )}
        {isGooglePaySupported && !isStripeSessionLoading && (
          <div
            className={styles['payment-method-mobile']}
            onClick={onPaymentButtonClick}
          >
            <Image
              src="/assets/payment-method/google-pay.svg"
              alt="google pay"
              width={41}
              height={19.52}
            />
          </div>
        )}
        {isApplePaySupported && !isStripeSessionLoading && (
          <div
            className={styles['payment-method-mobile']}
            onClick={onPaymentButtonClick}
          >
            <Image
              src="/assets/payment-method/apple-pay.svg"
              alt="apple pay"
              width={41}
              height={16.83}
            />
          </div>
        )}
      </div>
      <hr className={styles['separator']} />
    </>
  );
};

export default memo(PaymentFormMethods);
