'use client';
import React, { memo, useRef, useState } from 'react';
import cn from 'classnames';
import styles from './MessageForm.module.scss';
import AttachCastingCall from '../AttachCastingCall/AttachCastingCall';
import { Button, ModalAttachLink } from '@components';
import FileListUpload from '../FileListUpload/FileListUpload';
import { Form, Formik } from 'formik';
import Api from '@services/api';
import { useNotifications } from '@contexts/NotificationContext';
import { Amp } from '@services/amp';
import * as Yup from 'yup';
import Image from 'next/image';
import { ErrorMessage } from '@constants/form';
import Link from 'next/link';

const DEFAULT_MESSAGE = {
  message: '',
  attachments: [],
  castingCall: null,
  links: [],
};

const DEFAULT_TEXT = {
  CastingCall: `Hello! I am thrilled to invite you to audition for our upcoming project. 
  Your talent and unique qualities make you an excellent fit for the role we are casting. 
  Please find the details of the casting call below:`,
  File: 'File:',
  Link: 'Link:',
};

const LINK_LIMIT = 3;

const MessageForm = ({
  conversationId,
  onMessageSuccess,
  openImageAttachment = () => {},
  onSetActiveConversation = () => {},
  shadowHidden = false,
  isWidget = false,
  isBatch = false,
  initialCastingCall = null,
  onScrollToLastMessage = () => {},
  isPartnerActive = true,
}) => {
  const [showModalAttachLink, setShowModalAttachLink] = useState(false);

  const { setNotification } = useNotifications();
  const fileAttachRef = useRef(null);

  const initialValues = {
    ...DEFAULT_MESSAGE,
    castingCall: initialCastingCall,
  };

  const validationSchema = Yup.object({
    message: Yup.string().test(
      'Required',
      ErrorMessage.MessageRequired,
      async (value, { parent }) => {
        const { attachments, castingCall, links } = parent;

        return attachments.length || castingCall || links.length
          ? true
          : !!value?.length;
      },
    ),
  });

  const onSubmit = async (
    { castingCall, message, attachments, links },
    { resetForm },
  ) => {
    const body = new FormData();

    if (!message) {
      if (castingCall) {
        message = DEFAULT_TEXT.CastingCall;
      } else if (links.length) {
        message = DEFAULT_TEXT.Link;
      } else {
        message = DEFAULT_TEXT.File;
      }
    }

    body.append('content', message);

    attachments.forEach((file) => {
      body.append('files[]', file.id);
    });

    links.forEach(({ title, url }, index) => {
      body.append(`links[${index}][title]`, title);
      body.append(`links[${index}][link]`, url);
    });

    if (castingCall && !isBatch) {
      body.append('cc[]', castingCall.id);
    }

    const response = await Api.clientside(
      isBatch
        ? `/roles/${conversationId}/messages`
        : `/profiles/${conversationId}/messages`,
      {
        body,
        method: 'POST',
      },
    );

    if (response.status !== 'ok') {
      setNotification({
        type: 'error',
        message: response.message || ErrorMessage.MessageFailed,
        timeout: '5000',
      });
    } else {
      Amp.track(Amp.events.elementClicked, {
        name: `${isBatch ? 'batch ' : ''}message submit`,
        scope: isBatch
          ? Amp.element.scope.submissionsPage
          : `${isWidget ? Amp.element.scope.global : Amp.element.scope.messageCenter}`,
        section: Amp.element.section.messageForm,
        type: Amp.element.type.button,
      });

      onMessageSuccess(response);
      resetForm({ values: DEFAULT_MESSAGE });
      fileAttachRef.current.clear();
    }
  };

  const onFileAttach = () => {
    fileAttachRef.current.attach();
  };

  const toggleShowModalAttachLink = () => {
    setShowModalAttachLink(!showModalAttachLink);
  };

  const onSetFieldValue = (key, value, setFieldValue) => {
    setFieldValue(key, value);
  };

  const onLinkDelete = (value, index, setFieldValue) => {
    const newLinks = [...value];

    newLinks.splice(index, 1);

    setFieldValue('links', newLinks);
  };

  const onLinkAttach = (value, links, setFieldValue) => {
    onSetFieldValue('links', [...links, value], setFieldValue);
    toggleShowModalAttachLink();
    onScrollToLastMessage();
  };

  const LinkPreview = ({ title, href, onClick }) => (
    <div className={styles['link-preview']}>
      <Link href={href} target="_blank">
        {title}
      </Link>
      {!isBatch && (
        <Image
          src="/assets/icons/icon-close-2.svg"
          className={styles['delete-btn']}
          onClick={onClick}
          width={12}
          height={12}
          alt="icon"
        />
      )}
    </div>
  );

  const LinkPreviewContainer = ({ links, castingCall, setFieldValue }) => (
    <div className={styles['link-preview-wrapper']}>
      {links.length === LINK_LIMIT && (
        <div className={styles.warning}>
          You have included the maximum amount of links per message
        </div>
      )}
      <div className={styles['link-preview-container']}>
        {castingCall && (
          <LinkPreview
            title={castingCall.title}
            href={`${process.env.publicUrl}/castingcall/${castingCall.id}`}
            onClick={() => onSetFieldValue('castingCall', null, setFieldValue)}
          />
        )}
        {links.map(({ title, url }, index) => (
          <LinkPreview
            key={index}
            title={title}
            href={url}
            onClick={() => onLinkDelete(links, index, setFieldValue)}
          />
        ))}
      </div>
    </div>
  );

  return (
    <>
      {isPartnerActive ? (
        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={onSubmit}
        >
          {({ isValid, values, setFieldValue, handleChange, isSubmitting }) => {
            const { message, castingCall, links } = values;

            return (
              <div
                onClick={onSetActiveConversation}
                className={cn(styles.body, {
                  [styles.widget]: isWidget,
                  [styles['no-shadow']]: shadowHidden,
                })}
              >
                <div
                  className={cn(styles['form-wrap'], [styles['is-focused']])}
                >
                  <Form className={styles['form']}>
                    <div className={styles['input-box']}>
                      <div className={styles['input-area']}>
                        <input
                          autoComplete="off"
                          className={styles['message-input']}
                          name="message"
                          type="text"
                          onChange={handleChange}
                          value={message}
                          placeholder="Message"
                        />
                      </div>
                      <div className={styles['input-controls']}>
                        <div className={styles['attachment-controls']}>
                          <Image
                            className={styles['attach-btn']}
                            onClick={onFileAttach}
                            src="/assets/icons/icon-attachment.svg"
                            alt="icon"
                            width={30}
                            height={30}
                          />
                          {!isBatch && (
                            <AttachCastingCall
                              className={styles['attach-cc-btn']}
                              onAttach={(value) =>
                                onSetFieldValue(
                                  'castingCall',
                                  value,
                                  setFieldValue,
                                )
                              }
                            />
                          )}
                          <button
                            type="button"
                            onClick={toggleShowModalAttachLink}
                            className={styles['attach-link-btn']}
                            disabled={links.length === LINK_LIMIT}
                          >
                            <Image
                              src="/assets/icons/icon-link.svg"
                              alt="icon"
                              width={26}
                              height={26}
                            />
                          </button>
                        </div>
                        <div className={styles['input-submit']}>
                          <span className={styles['input-counter']}>
                            {message.length} / 1000
                          </span>
                          <Button
                            type="submit"
                            label="SEND"
                            minWidth="80px"
                            disabled={!isValid || isSubmitting}
                          />
                        </div>
                      </div>
                    </div>
                  </Form>
                  <div className={styles['file-list']}>
                    <FileListUpload
                      ref={fileAttachRef}
                      onUploadEnd={(value) =>
                        onSetFieldValue('attachments', value, setFieldValue)
                      }
                      openImageAttachment={openImageAttachment}
                    />
                  </div>
                  {isWidget && (castingCall || links.length > 0) && (
                    <LinkPreviewContainer
                      links={links}
                      castingCall={castingCall}
                      setFieldValue={setFieldValue}
                    />
                  )}
                </div>
                {!isWidget && (castingCall || links.length > 0) && (
                  <LinkPreviewContainer
                    links={links}
                    castingCall={castingCall}
                    setFieldValue={setFieldValue}
                  />
                )}
                {showModalAttachLink && (
                  <ModalAttachLink
                    onClose={toggleShowModalAttachLink}
                    onSubmit={(value) =>
                      onLinkAttach(value, links, setFieldValue)
                    }
                  />
                )}
              </div>
            );
          }}
        </Formik>
      ) : (
        <div className={styles.inactive}>
          This profile is no longer active and will not be receiving or
          responding to messages
        </div>
      )}
    </>
  );
};

export default memo(MessageForm);
