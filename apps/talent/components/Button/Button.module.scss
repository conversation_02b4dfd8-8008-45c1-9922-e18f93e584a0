@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.btn {
  font-weight: 700;
  border: 0;
  border-radius: 3em;
  cursor: pointer;
  display: inline-block;
  line-height: 1;
  padding: 12px $space-20;
  text-transform: uppercase;
  position: relative;
}

a.btn {
  text-decoration: none;
  text-align: center;

  &:hover {
    text-decoration: none;
  }
}

.primary {
  border: 2px solid transparent;
  background-size: 200% auto;
  background-position: 0 0;
  color: $white;
  transition: all 0.2s ease-in-out;

  &.shadow {
    box-shadow: $shadow-button;
  }

  &.blue {
    background-color: $blue-100;
    background-image: $gradient-btn-blue;

    &:hover:enabled {
      background-position: 100% 50%;
      transition: all 0.2s ease-in-out;
    }
  }

  &.orange {
    background-color: $yellow-100;
    background-image: $gradient-btn-orange;

    &.shadow {
      box-shadow: $shadow-melon;
    }

    &:hover:enabled {
      background-position: 100% 50%;
      transition: all 0.2s ease-in-out;
    }
  }

  &.green-gradient {
    background-color: $green-100;
    background-image: $gradient-btn-green-gradient;

    &.shadow {
      box-shadow: $shadow-tea-green;
    }

    &:hover:enabled {
      background-position: 100% 50%;
      transition: all 0.2s ease-in-out;
    }
  }

  &.grey {
    opacity: 0.3;
    background-color: transparent;
    border: 1px solid $white;
    color: $white;

    &:hover {
      opacity: 0.7;
    }
  }

  &.red-gradient {
    background-color: $red-60;
    background-image: $gradient-btn-red-gradient;

    &.shadow {
      box-shadow: $shadow-pastel-pink;
    }
  }

  &.plain-blue {
    background: $blue-20;
    color: $blue-100;
    box-shadow: none;
    text-transform: none;
    padding: 6px $space-20;

    &:hover {
      background: $blue-20;
      color: $blue-100;
    }
  }

  &.solid-blue {
    background: $blue-100;
    color: $white;
    box-shadow: none;
    text-transform: uppercase;
    padding: 0;

    &:hover {
      background: rgb($blue-100, 0.7);
    }
  }

  &.plain-red {
    background: $red-20;
    color: $red-100;
    box-shadow: none;
    text-transform: none;
    padding: 6px $space-20;

    &:hover {
      background: $red-20;
      color: $red-100;
    }
  }
}

.secondary {
  background: transparent;

  &.blue {
    color: $blue-100;
    border: 2px solid $blue-100;

    &:hover:enabled {
      box-shadow: $shadow-blue-bolt;
    }
  }

  &.orange {
    color: $white;
    border: 2px solid $yellow-100;

    &:hover:enabled {
      box-shadow: $shadow-beer;
    }
  }
}

.disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.promo {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

.promo-label {
  background: $red-80;
  color: $white;
  font-size: 10px;
  font-weight: 600;
  line-height: 1.2;
  max-width: 40px;
  max-height: 40px;
  text-align: center;
  padding: $space-5 33px $space-5 $space-30;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin-right: -$space-10;
  cursor: pointer;

  &::after {
    content: '';
    background-color: #3b3b53;
    position: absolute;
    width: 20px;
    height: 20px;
    margin-left: -$space-65;
    border-radius: 50%;

    @include desktop {
      background-color: $white;
    }
  }
}

.spinner {
  position: absolute;
  right: 10px;
  top: 25%;
  width: 20px;
  height: 20px;
  border: 2px solid white;
  border-bottom-color: transparent;
  border-radius: 50%;
  display: inline-block;
  box-sizing: border-box;
  animation: rotation 1s linear infinite;
}

@keyframes rotation {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
