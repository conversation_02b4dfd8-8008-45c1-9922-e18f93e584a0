'use client';
import styles from './CastingCallForm.module.scss';
import {
  Button,
  Checkbox,
  HeaderMobile,
  Input,
  Loading,
  MultiSelect,
  Radio,
  Select,
  Textarea,
} from '@components';
import cn from 'classnames';
import React, { useEffect, useState } from 'react';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { maskPhoneNumber } from '@utils/maskPhoneNumber';
import Api from '@services/api';
import dayjs from 'dayjs';
import { getDayOptionsByMonth } from '@utils/getTimeOptions';
import { formatNumberWithCommas } from '@utils/maskNumber';
import FileListUpload from './FileListUpload';
import Role from './Role/Role';
import { useNotifications } from '@contexts/NotificationContext';
import { useRouter } from 'next/navigation';
import ModalDeleteRole from './ModalDeleteRole/ModalDeleteRole';
import { Amp } from '@services/amp';
import {
  ErrorMessage,
  PHONE_NUMBER_REGEX,
  URL_REGEX,
  EMAIL_REGEX,
} from '@constants/form';
import {
  directMethods,
  locationOptions,
  countryOptions,
  TYPE,
} from '@constants/castingCalls';
import PostCastingCallSuccess from './PostCastingCallSuccess/PostCastingCallSuccess';
import {
  getErrorMessage,
  getInitialLocation,
  getLocationDescription,
  getPostRequestBody,
} from '@utils/castingCallPostHelper';
import PostCastingCallSidebar from './PostCastingCallSidebar/PostCastingCallSidebar';
import ModalAddRole from './ModalAddRole/ModalAddRole';
import ModalMobileRoles from './ModalMobileRoles/ModalMobileRoles';

const getAddress = (address) => {
  return address?.city && address?.state
    ? `${address.city}, ${address.state}`
    : '';
};

const CastingCallForm = ({
  castingCallType = TYPE.Web,
  profile,
  castingCallData,
  castingCallParameters,
  monthOptions,
  dayOptions,
  yearOptions,
  timeOptions,
  timeFormat,
  editRoleId,
}) => {
  const [additionalCategories, setAdditionalCategories] = useState(
    castingCallParameters.categories,
  );
  const [currentDayOptions, setCurrentDayOptions] = useState(dayOptions);
  const [isValidExpiration, setIsValidExpiration] = useState(false);
  const [locationLoading, setLocationLoading] = useState(false);
  const [showRoleForm, setShowRoleForm] = useState(false);
  const [showRoleDeleteModal, setShowRoleDeleteModal] = useState(null);
  const [location, setLocation] = useState(
    getAddress(castingCallData?.address),
  );
  const [roleIndexToEdit, setRoleIndexToEdit] = useState(null);
  const [roles, setRoles] = useState(castingCallData?.roles || []);
  const [isFormLoading, setIsFormLoading] = useState(false);
  const [castingCallUrl, setCastingCallUrl] = useState('');
  const [isValidCastingCallForm, setIsValidCastingCallForm] = useState(false);
  const [isFileLoading, setIsFileLoading] = useState(false);
  const [isFormStarted, setIsFormStarted] = useState(false);
  const [error, setError] = useState('');
  const [showMobileRolesModal, setShowMobileRolesModal] = useState(false);

  const router = useRouter();
  const { setNotification } = useNotifications();

  const formik = useFormik({
    initialValues: {
      id: castingCallData?.id || 0,
      type: castingCallType,
      notification_target: 'email',
      application_method: {
        method: castingCallData?.type || '',
        phone: castingCallData?.phone || '',
        url: castingCallData?.website || '',
        email: castingCallData?.agent_email || '',
      },
      details: {
        title: castingCallData?.title || '',
        category: castingCallData?.category?.id || '',
        additionalCategories:
          castingCallData?.additional_categories?.map((item) => item.id) || [],
        location: getInitialLocation(castingCallData),
        zip: castingCallData?.address?.zip || '',
        state: castingCallData?.address?.state || '',
        nationwideCountry: castingCallData?.address?.country?.code,
        description: castingCallData?.description || '',
        online_audition: !!castingCallData?.online_audition,
      },
      address: castingCallData?.address?.address || '',
      expiration: {
        month: castingCallData
          ? dayjs(castingCallData.expiration).month() + 1
          : '',
        day: castingCallData ? dayjs(castingCallData.expiration).date() : '',
        year: castingCallData ? dayjs(castingCallData.expiration).year() : '',
      },
      payment: {
        amount: castingCallData
          ? formatNumberWithCommas(String(castingCallData.payment_amount))
          : '0',
        period: castingCallData?.payment_period || 'None',
        currency: castingCallData?.payment_currency || 'None',
      },
      company: {
        title: castingCallData?.company || profile.company_name || '',
        company_phone:
          maskPhoneNumber(castingCallData?.company_phone) ||
          maskPhoneNumber(profile.links?.touches?.links?.phone?.value) ||
          '',
        company_website:
          castingCallData?.company_website || profile.website || '',
      },
      attachments: castingCallData?.files || [],
      roles: castingCallData?.roles || [],
    },
    onSubmit: async (values) => {
      Amp.track(Amp.events.elementClicked, {
        name: `post casting call`,
        scope: Amp.element.scope.postCastingCallPage,
        section: Amp.element.section.sidemenu,
        type: Amp.element.type.button,
      });
      setIsFormLoading(true);

      const postResponse = await Api.clientside(`/calls/post`, {
        method: 'POST',
        body: getPostRequestBody(values),
      });

      if (postResponse.status !== 'ok') {
        Amp.track(Amp.events.formSubmitted, {
          name: castingCallData?.id
            ? `update casting call`
            : `post new casting call`,
          scope: Amp.element.scope.postCastingCallPage,
          result: Amp.element.result.fail,
        });
        setIsFormLoading(false);
        setNotification({
          type: 'error',
          message: postResponse.message || ErrorMessage.Unexpected,
          timeout: '5000',
        });
      } else {
        Amp.track(Amp.events.formSubmitted, {
          name: castingCallData?.id
            ? `update casting call`
            : `post new casting call`,
          scope: Amp.element.scope.postCastingCallPage,
          result: Amp.element.result.success,
        });

        if (castingCallData?.id) {
          router.push(`/castingcall/${castingCallData.id}`);
        } else {
          setIsFormLoading(false);
          setCastingCallUrl(`/castingcall/${postResponse.data.id}`);
        }
      }
    },
    validationSchema: Yup.object({
      application_method:
        castingCallType === 'direct'
          ? Yup.object()
              .shape({
                method: Yup.string().required(ErrorMessage.MethodRequired),
                phone: Yup.string().when('method', {
                  is: (value) => value === TYPE.Phone,
                  then: () =>
                    Yup.string()
                      .required(ErrorMessage.PhonePattern)
                      .matches(PHONE_NUMBER_REGEX, ErrorMessage.PhonePattern),
                  otherwise: () => Yup.string().notRequired(),
                }),
                url: Yup.string().when('method', {
                  is: (value) => value === TYPE.Url,
                  then: () =>
                    Yup.string()
                      .required(ErrorMessage.URLRequired)
                      .matches(URL_REGEX, ErrorMessage.URLPattern),
                  otherwise: () => Yup.string().notRequired(),
                }),
                email: Yup.string().when('method', {
                  is: (value) => value === TYPE.Email,
                  then: () =>
                    Yup.string()
                      .required(ErrorMessage.EmailRequired)
                      .matches(EMAIL_REGEX, ErrorMessage.EmailPattern),
                  otherwise: () => Yup.string().notRequired(),
                }),
              })
              .required(ErrorMessage.MethodRequired)
          : Yup.object().notRequired(),
      details: Yup.object()
        .shape({
          title: Yup.string()
            .required(ErrorMessage.TitleRequired)
            .max(
              64,
              ErrorMessage.MaxTitleCharacters.replace(
                '{Y}',
                'Casting call',
              ).replace('{X}', '64'),
            ),
          category: Yup.string().required(ErrorMessage.CategoryRequired),
          zip: Yup.string().when('location', {
            is: (value) => value === 'local',
            then: () =>
              Yup.string()
                .required(ErrorMessage.ZipRequired)
                .min(5, ErrorMessage.ZipPattern)
                .max(6, ErrorMessage.ZipPattern)
                .test('zipIsValid', ErrorMessage.ZipPattern, async (value) => {
                  const length = value?.length || 0;

                  if (
                    length >= 4 &&
                    length <= 6 &&
                    value !== formik.values.details.zip
                  ) {
                    setLocationLoading(true);

                    const response = await getLocation(value);

                    setLocation(
                      response.count > 0
                        ? `${response.items[0]?.links?.city?.title}, ${response.items[0]?.links?.state?.code}`
                        : '',
                    );
                    setLocationLoading(false);
                    await formik.setFieldValue(
                      'details.state',
                      response.items[0]?.links?.state?.code,
                    );

                    return response.count > 0;
                  }

                  return !!location;
                }),
            otherwise: () => Yup.string().notRequired(),
          }),
          nationwideCountry: Yup.string().when('location', {
            is: (value) => value === 'nationWide',
            then: () => Yup.string().required(ErrorMessage.CountryRequired),
            otherwise: () => Yup.string().notRequired(),
          }),
          description: Yup.string()
            .required(ErrorMessage.DescriptionRequired)
            .max(
              1000,
              ErrorMessage.MaxDescriptionCharacters.replace('{X}', '1,000'),
            )
            .test(
              'descriptionIsValid',
              ErrorMessage.MinDescriptionWords.replace('{X}', '10'),
              async (value) => {
                return value.trim().split(' ').length >= 10;
              },
            ),
        })
        .required(),
      expiration: Yup.object()
        .test(
          'expirationValid',
          ErrorMessage.ExpirationDatePattern,
          async (value) => {
            const inputDate = dayjs(
              `${value.year}-${value.month}-${value.day}`,
            ).unix();

            const tomorrow = dayjs().add(1, 'day').startOf('day').unix();

            setIsValidExpiration(inputDate > tomorrow);

            return inputDate > tomorrow;
          },
        )
        .shape({
          month: Yup.string().required(ErrorMessage.MonthRequired),
          day: Yup.string().required(ErrorMessage.DayRequired),
          year: Yup.string().required(ErrorMessage.YearRequired),
        }),
      payment: Yup.object().shape({
        amount: Yup.string()
          .test(
            'amountMax',
            ErrorMessage.MaxAmount.replace('{X}', '1,000,000'),
            (amount) => Number((amount || '').replaceAll(',', '')) < 1_000_000,
          )
          .test(
            'amountRequired',
            ErrorMessage.AmountRequired,
            (amount, { parent }) => {
              const { period, currency } = parent;
              const amountHasValue = amount && amount !== '0';
              const periodHasValue = period && period !== 'None';
              const periodIsTFP = period === 'TFP';
              const currencyHasValue = currency && currency !== 'None';

              return (
                amountHasValue ||
                periodIsTFP ||
                !(periodHasValue || currencyHasValue)
              );
            },
          ),
        period: Yup.string().test(
          'periodRequired',
          ErrorMessage.PeriodRequired,
          (period, { parent }) => {
            const { amount, currency } = parent;
            const amountHasValue = amount && amount !== '0';
            const currencyHasValue = currency && currency !== 'None';
            const periodHasValue = period && period !== 'None';

            return periodHasValue || !(amountHasValue || currencyHasValue);
          },
        ),
        currency: Yup.string().test(
          'currencyRequired',
          ErrorMessage.CurrencyRequired,
          (currency, { parent }) => {
            const { amount, period } = parent;
            const amountHasValue = amount && amount !== '0';
            const currencyHasValue = currency && currency !== 'None';
            const periodHasValue = period && period !== 'None';
            const periodIsTFP = period === 'TFP';

            return (
              currencyHasValue ||
              periodIsTFP ||
              !(amountHasValue || periodHasValue)
            );
          },
        ),
      }),
      address:
        castingCallType === TYPE.Open
          ? Yup.string().required(ErrorMessage.AddressRequired)
          : Yup.string().notRequired(),
      company: Yup.object().shape({
        title: Yup.string().max(
          128,
          ErrorMessage.MaxTitleCharacters.replace('{Y}', 'Company').replace(
            '{X}',
            '128',
          ),
        ),
        company_phone: Yup.string().matches(
          PHONE_NUMBER_REGEX,
          ErrorMessage.PhonePattern,
        ),
        company_website: Yup.string().matches(
          URL_REGEX,
          ErrorMessage.URLPattern,
        ),
      }),
      roles: Yup.array().test(
        'rolesValid',
        ErrorMessage.MinRolesCount,
        async (value) => {
          return !!value.length;
        },
      ),
    }),
  });

  useEffect(() => {
    const keys = Object.keys(formik.errors);
    const castingCallHasNoErrors =
      keys.length === 0 || (keys.length === 1 && keys[0] === 'roles');

    setIsValidCastingCallForm(castingCallHasNoErrors);

    if (error || formik.isSubmitting) {
      setError(getErrorMessage(formik.errors));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formik.errors, formik.isSubmitting]);

  useEffect(() => {
    if (formik.touched && formik.dirty && !castingCallData && !isFormStarted) {
      setIsFormStarted(true);
      Amp.track(Amp.events.formStarted, {
        name: 'post casting call',
        scope: Amp.element.scope.postCastingCallPage,
      });
      sendFormStartedEvent();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formik.touched, formik.dirty]);

  useEffect(() => {
    if (editRoleId && roles.length) {
      const index = roles.findIndex(({ id }) => id === editRoleId);

      onRoleEdit(index);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [editRoleId]);

  useEffect(() => {
    const { period, currency, amount } = formik.values.payment;

    if (period === 'TFP' && currency && currency !== 'None') {
      formik.setFieldValue('payment.currency', 'None');
    }

    if (period === 'TFP' && amount && amount !== '0') {
      formik.setFieldValue('payment.amount', '0');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formik.values]);

  const onRoleSubmit = (role) => {
    const updatedRoles = [...formik.values.roles];

    if (roleIndexToEdit === null) {
      updatedRoles.push({ ...role });
    } else {
      updatedRoles[roleIndexToEdit] = {
        ...updatedRoles[roleIndexToEdit],
        ...role,
      };
    }
    formik.setFieldValue('roles', updatedRoles);
    setRoles([...updatedRoles]);
    setShowRoleForm(false);

    setRoleIndexToEdit(null);
  };

  const getLocation = async (zip) => {
    return await Api.clientside(`/locations?query=${zip}`);
  };

  const onMethodSelect = (value) => {
    formik.setFieldValue('application_method.method', value);
    formik.setFieldValue('type', value);
  };

  const onCategorySelect = (value) => {
    formik.setFieldValue('details.category', value);
    setAdditionalCategories((current) => {
      return current.map((category) => {
        category.disabled = category.value === value;

        return category;
      });
    });
  };

  const onAdditionalCategorySelect = (value) => {
    formik.setFieldValue('details.additionalCategories', value);
  };

  const onCountrySelect = (value) => {
    formik.setFieldValue('details.nationwideCountry', value);
  };

  const onPeriodSelect = (value) => {
    formik.setFieldValue('payment.period', value);
  };

  const onCurrencySelect = (value) => {
    formik.setFieldValue('payment.currency', value);
  };

  const onLocationChange = (event) => {
    formik.setFieldValue('details.location', event.target.value);
  };

  const formatPhoneNumber = (field, e) => {
    formik.setFieldValue(field, maskPhoneNumber(e.target.value));
  };

  const formatNumber = (e) => {
    formik.setFieldValue(
      'payment.amount',
      formatNumberWithCommas(e.target.value),
    );
  };

  const setFieldTouched = (field) => {
    formik.setFieldTouched(field);
  };

  const setMonthValue = (month) => {
    const updatedDayOptions = getDayOptionsByMonth(
      formik.values.expiration.year,
      month,
    );
    const resetDay =
      formik.values.expiration.day &&
      !updatedDayOptions.find(
        (option) => option.value === formik.values.expiration.day,
      );

    formik.setFieldValue('expiration.month', month);
    setCurrentDayOptions(updatedDayOptions);

    if (resetDay) {
      formik.setFieldValue('expiration.day', '');
    }
  };

  const setDayValue = (e) => {
    formik.setFieldValue('expiration.day', e);
  };

  const setYearValue = (year) => {
    const updatedDayOptions = getDayOptionsByMonth(
      year,
      formik.values.expiration.month,
    );
    const resetDay =
      formik.values.expiration.day &&
      !updatedDayOptions.find(
        (option) => option.value === formik.values.expiration.day,
      );

    formik.setFieldValue('expiration.year', year);
    setCurrentDayOptions(updatedDayOptions);

    if (resetDay) {
      formik.setFieldValue('expiration.day', '');
    }
  };

  const onUploadEnd = (files) => {
    formik.setFieldValue('attachments', files);
  };

  const onRoleDelete = (index) => {
    const newRoles = [...formik.values.roles];

    newRoles.splice(index, 1);

    formik.setFieldValue('roles', newRoles);
    setRoles([...newRoles]);
    setShowRoleDeleteModal(null);
  };

  const onRoleEdit = (index) => {
    setRoleIndexToEdit(index);
    setShowRoleForm(true);
  };

  const submitBasicInfo = async () => {
    if (isValidCastingCallForm) {
      if (!formik.values.roles.length) {
        setShowRoleForm(true);
      } else {
        setShowMobileRolesModal(true);
      }
    } else {
      setError(getErrorMessage(formik.errors));
      await formik.submitForm();
    }
  };

  const returnFromRoleBlock = () => {
    setShowRoleForm(false);

    if (formik.values.roles.length) {
      setShowMobileRolesModal(true);
    }
  };

  const navigateToCastingCalls = () => {
    router.push('/castingcalls');
  };

  const onPaymentAmountBlur = (event) => {
    formik.handleBlur(event);

    if (formik.values.payment.amount.endsWith('.')) {
      formik.setFieldValue(
        'payment.amount',
        formik.values.payment.amount.replace(/\.$/, ''),
      );
    }
  };

  const sendFormStartedEvent = () => {
    return Api.clientside(`/casting-calls/form-started`, {
      method: 'POST',
    });
  };

  return castingCallUrl ? (
    <PostCastingCallSuccess
      castingCallUrl={
        castingCallData?.id
          ? `/castingcall/${castingCallData.id}`
          : castingCallUrl
      }
    />
  ) : (
    <section className={styles['form-section']}>
      <div className={styles['form-container']}>
        <form className={styles['form']} onSubmit={formik.handleSubmit}>
          <>
            <HeaderMobile
              onClose={navigateToCastingCalls}
              title={
                castingCallData
                  ? 'Edit Casting Call'
                  : 'Post a new casting call'
              }
            />
            <h1 className={styles.heading}>
              {castingCallData
                ? 'Edit Casting Call'
                : 'Post a new casting call'}
            </h1>
            {castingCallType === 'direct' && (
              <>
                <div id="method" className={styles['block-title']}>
                  Application method
                </div>
                <div className={cn(styles['field-row'], styles['columns-2'])}>
                  <div className={styles['field']}>
                    <Select
                      name="application_method.method"
                      options={directMethods}
                      value={formik.values.application_method?.method}
                      isTouched={formik.touched.application_method?.method}
                      setFormFieldTouched={() =>
                        formik.setFieldTouched('application_method.method')
                      }
                      onChange={onMethodSelect}
                      placeholder="Method"
                      error={formik.errors.application_method?.method}
                    />
                  </div>
                  <div className={styles['field']}>
                    {formik.values.application_method.method === TYPE.Phone && (
                      <Input
                        name="application_method.phone"
                        placeholder="Phone number"
                        hint="Talent will contact via your phone number"
                        onChange={(e) => {
                          formatPhoneNumber('application_method.phone', e);
                        }}
                        onBlur={formik.handleBlur}
                        value={formik.values.application_method.phone}
                        isTouched={formik.touched.application_method?.phone}
                        error={formik.errors.application_method?.phone}
                      />
                    )}
                    {formik.values.application_method.method === TYPE.Email && (
                      <Input
                        name="application_method.email"
                        placeholder="email"
                        hint="Talent will send their Comp Cards to your email"
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        value={formik.values.application_method.email}
                        isTouched={formik.touched.application_method?.email}
                        error={formik.errors.application_method?.email}
                      />
                    )}
                    {formik.values.application_method.method === TYPE.Url && (
                      <Input
                        name="application_method.url"
                        placeholder="URL"
                        hint="Talent will apply via your URL. Website URL should follow this pattern: https://example.com"
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        value={formik.values.application_method.url}
                        isTouched={formik.touched.application_method?.url}
                        error={formik.errors.application_method?.url}
                      />
                    )}
                  </div>
                </div>
              </>
            )}
            <div id="details" className={styles['block-title']}>
              Details
            </div>
            <div className={styles['field-row']}>
              <div className={styles['field']}>
                <Input
                  name="details.title"
                  required
                  placeholder="Casting call title"
                  hint="Max 64 characters"
                  onChange={(e) => {
                    if (formik.values.details?.title) {
                      setFieldTouched('details.title');
                    }

                    formik.handleChange(e);
                  }}
                  onBlur={formik.handleBlur}
                  value={formik.values.details?.title}
                  isTouched={formik.touched.details?.title}
                  error={formik.errors.details?.title}
                />
              </div>
            </div>
            <div className={cn(styles['field-row'], styles['columns-2'])}>
              <div className={styles['field']}>
                <Select
                  name="details.category"
                  options={castingCallParameters.categories}
                  value={formik.values.details?.category}
                  isTouched={formik.touched.details?.category}
                  setFormFieldTouched={() =>
                    formik.setFieldTouched('details.category')
                  }
                  onChange={onCategorySelect}
                  required
                  placeholder="Main category"
                  error={formik.errors.details?.category}
                />
              </div>
              <div className={styles['field']}>
                <MultiSelect
                  options={additionalCategories}
                  name="details.additionalCategories"
                  placeholder="Additional categories"
                  selectedOptions={formik.values.details?.additionalCategories}
                  onChange={onAdditionalCategorySelect}
                />
              </div>
            </div>
            <div
              className={cn(styles['field-row'], styles['location-field-row'])}
            >
              <div className={styles['small-field-group']}>
                {locationOptions.map((option, i) => (
                  <div className={styles['field']} key={i}>
                    <Radio
                      name={'details.location'}
                      label={option.title}
                      value={option.value}
                      onChange={onLocationChange}
                      color={'midnight'}
                      checked={formik.values.details?.location === option.value}
                    />
                  </div>
                ))}
              </div>
              {formik.values.details?.location && (
                <div className={styles['field-hint']}>
                  {getLocationDescription(formik.values.details?.location)}
                </div>
              )}
            </div>

            {formik.values.details?.location === 'local' && (
              <div className={cn(styles['field-row'], styles['columns-2'])}>
                <div className={styles['field']}>
                  <Input
                    name="details.zip"
                    placeholder="ZIP / Postal Code"
                    value={formik.values.details?.zip}
                    error={formik.errors.details?.zip}
                    isTouched={formik.touched.details?.zip}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    required
                  />
                  {location && !locationLoading && (
                    <span className={styles['zip-hint']}>{location}</span>
                  )}
                  {locationLoading && <Loading minHeight="40px" padding="0" />}
                </div>
              </div>
            )}
            {formik.values.details?.location === 'nationWide' && (
              <div className={cn(styles['field-row'], styles['columns-2'])}>
                <div className={styles['field']}>
                  <Select
                    name="details.nationwideCountry"
                    options={countryOptions}
                    value={formik.values.details?.nationwideCountry}
                    isTouched={formik.touched.details?.nationwideCountry}
                    setFormFieldTouched={() =>
                      formik.setFieldTouched('details.nationwideCountry')
                    }
                    onChange={onCountrySelect}
                    placeholder="Country"
                    error={formik.errors.details?.nationwideCountry}
                  />
                </div>
              </div>
            )}
            <div className={styles['field-row']}>
              <div className={styles['field']}>
                <Textarea
                  name="details.description"
                  placeholder="Write a short description"
                  value={formik.values.details?.description}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  isTouched={formik.touched.details?.description}
                  error={formik.errors.details?.description}
                  hint="Max 1,000 characters"
                  charCounter={1000}
                  required
                  autosize
                />
              </div>
            </div>
            {castingCallType !== TYPE.Open && (
              <div className={styles['field-row']}>
                <div className={styles['field']}>
                  <Checkbox
                    name="details.online_audition"
                    onChange={formik.handleChange}
                    value={formik.values.details?.online_audition || false}
                    error={formik.errors.details?.online_audition}
                    onBlur={formik.handleBlur}
                    isTouched={formik.touched.details?.online_audition}
                  >
                    <span className={styles['field-label']}>
                      Online audition
                    </span>
                  </Checkbox>
                </div>
                <div className={styles['field']}>
                  <div className={styles['field-hint']}>
                    Online auditions means your auditions will take place
                    remotely.
                  </div>
                </div>
              </div>
            )}
            <div id="expiration" className={styles['block-title']}>
              Expiration date
            </div>
            <div className={cn(styles['field-row'], styles['columns-2'])}>
              <div className={styles['field']}>
                <div className={styles['small-field-group']}>
                  <Select
                    name="expiration.month"
                    onChange={setMonthValue}
                    value={formik.values.expiration.month}
                    isTouched={formik.touched.expiration?.month}
                    error={formik.errors.expiration?.month}
                    placeholder="Month"
                    options={monthOptions}
                    required
                    setFormFieldTouched={() =>
                      setFieldTouched('expiration.month')
                    }
                  />
                  <Select
                    name="expiration.day"
                    onChange={setDayValue}
                    value={formik.values.expiration.day}
                    isTouched={formik.touched.expiration?.day}
                    error={formik.errors.expiration?.day}
                    placeholder="Day"
                    options={currentDayOptions}
                    required
                    setFormFieldTouched={() =>
                      setFieldTouched('expiration.day')
                    }
                  />
                  <Select
                    name="expiration.year"
                    onChange={setYearValue}
                    value={formik.values.expiration.year}
                    isTouched={formik.touched.expiration?.year}
                    error={formik.errors.expiration?.year}
                    placeholder="Year"
                    options={yearOptions}
                    required
                    setFormFieldTouched={() =>
                      setFieldTouched('expiration.year')
                    }
                  />
                </div>
                {formik.values.expiration.month &&
                  formik.values.expiration.day &&
                  formik.values.expiration.year &&
                  !isValidExpiration && (
                    <div className={styles['field-error']}>
                      Expiration date should be no earlier than tomorrow
                    </div>
                  )}
              </div>
            </div>
            {castingCallType === TYPE.Open && (
              <>
                <div id="location" className={styles['block-title']}>
                  Open casting call location
                </div>
                <div className={cn(styles['field-row'], styles['columns-2'])}>
                  <div className={styles['field']}>
                    <Input
                      name="address"
                      placeholder="Address"
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      value={formik.values.address}
                      isTouched={formik.touched.address}
                      error={formik.errors.address}
                    />
                  </div>
                  <div className={styles['field']}>
                    <span className={styles['field-hint']}>
                      This is where you want talent to show up
                    </span>
                  </div>
                </div>
              </>
            )}
            <div id="payment" className={styles['block-title']}>
              Payment
              <i className={styles['optional']}>&nbsp;(optional)</i>
            </div>
            <div className={cn(styles['field-row'], styles['columns-2'])}>
              <div className={styles['field']}>
                <div className={styles['small-field-group']}>
                  <Input
                    name="payment.amount"
                    placeholder="Amount $"
                    onChange={formatNumber}
                    onBlur={onPaymentAmountBlur}
                    value={formik.values.payment?.amount}
                    isTouched={formik.touched.payment?.amount}
                    error={formik.errors.payment?.amount}
                    disabled={formik.values.payment.period === 'TFP'}
                  />
                  <Select
                    name="payment.period"
                    options={castingCallParameters.paymentPeriods}
                    value={formik.values.payment?.period}
                    isTouched={formik.touched.payment?.period}
                    setFormFieldTouched={() =>
                      formik.setFieldTouched('payment.period')
                    }
                    onChange={onPeriodSelect}
                    placeholder="Period"
                    error={formik.errors.payment?.period}
                  />
                  <Select
                    name="payment.currency"
                    options={castingCallParameters.paymentCurrencies}
                    value={formik.values.payment?.currency}
                    isTouched={formik.touched.payment?.currency}
                    setFormFieldTouched={() =>
                      formik.setFieldTouched('payment.currency')
                    }
                    onChange={onCurrencySelect}
                    placeholder="Currency"
                    disabled={formik.values.payment.period === 'TFP'}
                    error={formik.errors.payment?.currency}
                  />
                </div>
              </div>
            </div>
            <div id="company" className={styles['block-title']}>
              Company info
              <i className={styles['optional']}>&nbsp;(optional)</i>
            </div>
            <div className={styles['field-row']}>
              <div className={styles['field']}>
                <Input
                  name="company.title"
                  placeholder="Company title"
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  value={formik.values.company?.title}
                  isTouched={formik.touched.company?.title}
                  error={formik.errors.company?.title}
                  hint={'Max 128 characters'}
                />
              </div>
            </div>
            <div className={cn(styles['field-row'], styles['columns-2'])}>
              <div className={styles['field']}>
                <Input
                  name="company.company_phone"
                  placeholder="Phone number"
                  hint="Include area code"
                  onChange={(e) => {
                    formatPhoneNumber('company.company_phone', e);
                  }}
                  onBlur={formik.handleBlur}
                  value={formik.values.company?.company_phone}
                  isTouched={formik.touched.company?.company_phone}
                  error={formik.errors.company?.company_phone}
                />
              </div>
              <div className={styles['field']}>
                <Input
                  name="company.company_website"
                  placeholder="Website"
                  hint="Website URL should follow this pattern: https://example.com"
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  value={formik.values.company?.company_website}
                  isTouched={formik.touched.company?.company_website}
                  error={formik.errors.company?.company_website}
                />
              </div>
            </div>
            <div id="files" className={styles['block-title']}>
              Attach files
              <i className={styles['optional']}>&nbsp;(optional)</i>
            </div>
            <FileListUpload
              savedFiles={castingCallData?.files}
              onUploadEnd={onUploadEnd}
              isFileUploading={(value) => {
                setIsFileLoading(value);
              }}
            />
          </>
          <div id="roles" className={styles['roles-block']}>
            <div className={styles['roles-header']}>
              <h2 className={styles['heading']}>
                {formik.values.roles.length}
                <span>&nbsp;Roles</span>
              </h2>
              <Button
                label={
                  formik.values.roles.length ? 'Add another role' : 'Add a role'
                }
                kind="secondary"
                minWidth="200px"
                onClick={() => {
                  setShowRoleForm(!showRoleForm);
                }}
              />
            </div>
            {!formik.values.roles.length && (
              <div className={styles['roles-provide']}>
                You need to provide at least one role in order to have your
                casting call published
              </div>
            )}
            <div className={styles['role-list']}>
              {roles.map((role, i) => (
                <Role
                  role={role}
                  key={i}
                  parameters={castingCallParameters}
                  onDelete={() => {
                    setShowRoleDeleteModal(i);
                  }}
                  onEdit={() => {
                    onRoleEdit(i);
                  }}
                />
              ))}
            </div>
            <div className={styles['submit-block']}>
              <Button
                label="Add more roles"
                kind="secondary"
                minWidth="220px"
                onClick={() => {
                  setShowRoleForm(true);
                }}
              />
            </div>
          </div>
        </form>
        {!isValidCastingCallForm && (
          <div
            className={cn(styles['form-error'], {
              [styles.error]: error,
            })}
          >
            {error || 'Please fill in all required fields accurately'}
          </div>
        )}
        {!isFileLoading && (
          <div className={styles['submit-block']}>
            {isValidCastingCallForm && (
              <div className={styles['title']}>
                Next step:
                <div className={styles['title-bolder']}>Add Roles</div>
              </div>
            )}
            <Button
              label="Continue"
              minWidth="220px"
              onClick={submitBasicInfo}
            />
          </div>
        )}
      </div>
      <PostCastingCallSidebar
        castingCallType={castingCallType}
        errors={formik.errors}
        values={formik.values}
        loading={isFormLoading}
        isValid={formik.isValid}
        onClick={formik.handleSubmit}
        error={error}
      />

      {showMobileRolesModal && (
        <ModalMobileRoles
          roles={formik.values.roles}
          castingCallParameters={castingCallParameters}
          onClose={() => setShowMobileRolesModal(false)}
          onAddRole={() => setShowRoleForm(true)}
          onSubmit={formik.handleSubmit}
          isValid={formik.isValid}
          onDeleteRole={(i) => setShowRoleDeleteModal(i)}
          onEditRole={(i) => onRoleEdit(i)}
        />
      )}
      {showRoleForm && (
        <ModalAddRole
          onClose={() => {
            setShowRoleForm(false);

            if (formik.values.roles?.length) {
              setShowMobileRolesModal(true);
            }
          }}
          parameters={castingCallParameters}
          castingCallType={castingCallType}
          monthOptions={monthOptions}
          dayOptions={dayOptions}
          yearOptions={yearOptions}
          timeOptions={timeOptions}
          timeFormat={timeFormat}
          onRoleSubmit={onRoleSubmit}
          role={formik.values.roles[roleIndexToEdit]}
          returnFromRoleBlock={returnFromRoleBlock}
        />
      )}
      {showRoleDeleteModal !== null && (
        <ModalDeleteRole
          onClose={() => {
            setShowRoleDeleteModal(null);
          }}
          onDeleteRole={() => {
            onRoleDelete(showRoleDeleteModal);
          }}
        />
      )}
    </section>
  );
};

export default CastingCallForm;
