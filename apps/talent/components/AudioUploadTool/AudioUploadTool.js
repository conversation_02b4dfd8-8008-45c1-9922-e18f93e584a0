'use client';
import React, {
  forwardRef,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import styles from './AudioUploadTool.module.scss';
import cn from 'classnames';
import { allowedFileTypes, maxSizeMB } from '@constants/audio';
import { Modal } from '@components';

const AudioUploadTool = forwardRef(({ onFileInputChange, children }, ref) => {
  const fileInputRef = useRef(null);
  const [error, setError] = useState(''); //  max | type

  const selectFile = () => {
    fileInputRef.current.click();
  };

  useImperativeHandle(ref, () => ({
    selectFile() {
      selectFile();
    },
  }));

  const onChange = async (event) => {
    const file = event.target.files[0];

    if (file) {
      // Sometimes MB refers to both 1000 Kb or 1024 Kb
      // MiB always 1024 Kb
      const fileSize = file.size / 1000 / 1000;

      if (fileSize > maxSizeMB) {
        setError('max');
      } else if (!allowedFileTypes.some((type) => file.name.endsWith(type))) {
        setError('type');
      } else {
        onFileInputChange(file);
      }
    }

    event.target.value = null;
  };

  const clearError = () => {
    setError('');
  };

  const restartUpload = () => {
    clearError();
    selectFile();
  };

  return (
    <>
      {children}
      <input
        ref={fileInputRef}
        className={styles['file-input']}
        type="file"
        name="file-input"
        onChange={onChange}
        accept="audio/*"
      />
      {error && (
        <Modal
          onClose={clearError}
          closeButtonHidden
          classNameContent={styles['upload-error-modal']}
          showDefaultLayout={false}
          classNameContainer={styles['upload-error-modal-container']}
          classNameOverlay={styles['upload-error-modal-overlay']}
        >
          <div className={styles['audio-upload-error-modal']}>
            <div className={styles['audio-upload-error']}>
              {error === 'max' && (
                <>
                  <span className={styles['audio-upload-error-title']}>
                    Ooops! Wrong file size!
                  </span>
                  <p>
                    We&apos;re sorry, the file you tried to upload was too
                    large. Maximum size: <b>{maxSizeMB} MB</b>.
                  </p>
                </>
              )}
              {error === 'type' && (
                <>
                  <span className={styles['audio-upload-error-title']}>
                    Ooops! Wrong file type!
                  </span>
                  <p>
                    We&apos;re sorry, but the file is in an incorrect format.
                    Please make sure your file is in one of the supported
                    formats: <b>{allowedFileTypes.join(', ')}</b>.
                  </p>
                </>
              )}
            </div>

            <div className={styles['audio-upload-error-actions']}>
              <button
                onClick={clearError}
                className={styles['audio-upload-error-action-button']}
              >
                Cancel
              </button>
              <button
                onClick={restartUpload}
                className={cn(
                  styles['audio-upload-error-action-button'],
                  styles['audio-upload-error-action-button-upload'],
                )}
              >
                Upload a new file
              </button>
            </div>
          </div>
        </Modal>
      )}
    </>
  );
});

AudioUploadTool.displayName = 'AudioUploadTool';

export default AudioUploadTool;
