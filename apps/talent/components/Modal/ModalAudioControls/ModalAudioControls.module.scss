@use '@styles/mixins' as *;
@use '@styles/variables' as *;

.audio-control-modal {
  border-radius: 10px;
}

.audio-preview-actions-mobile {
  display: flex;
  flex-direction: column;
  gap: $space-20;
  padding: $space-30 38px;
}

.audio-preview-action {
  display: flex;
  align-items: center;
  gap: $space-15;
  font-size: 14px;
  font-weight: 600;
}

.icon {
  cursor: pointer;
  opacity: 0.6;
  transition: all 0.2s ease-in-out;

  &:hover {
    opacity: 1;
    transition: all 0.2s ease-in-out;
  }
}
