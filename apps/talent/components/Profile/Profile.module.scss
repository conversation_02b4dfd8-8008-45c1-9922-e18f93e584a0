@use '@styles/mixins' as *;
@use '@styles/variables' as *;

.profile-container-back {
  display: none;

  @include desktop {
    display: flex;
  }
}

.profile-panel-info-title {
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  font-size: 12px;
  font-weight: 700;
  margin-bottom: 2px;
}

.profile-panel-info-text {
  font-size: 12px;
  font-weight: 500;
  line-height: 1.3;
  margin-bottom: $space-10;
}

.profile-panel-info-link {
  color: $blue-100;
  font-weight: 500;
  font-size: 12px;
  line-height: 1.3;
  align-items: baseline;

  &::after {
    content: '';
    display: inline-flex;
    width: 15px;
    height: 8px;
    margin-left: 3px;
    background: url('#{$assetUrl}/assets/icons/icon-arrow-right-blue.svg') 0 0
      no-repeat;
  }
}

.profile-panel-info-close {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 12px;
  height: 12px;
  cursor: pointer;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    right: 0;
    left: 0;
    margin: 0 auto;
    width: 100%;
    height: 1px;
    background-color: $grey-60;
    transform: rotate(45deg);
  }

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 0;
    left: 0;
    margin: 0 auto;
    width: 100%;
    height: 1px;
    background-color: $grey-60;
    transform: rotate(-45deg);
  }
}

.edit-name-close {
  width: 24px;
  height: 24px;
  top: 19px;
  right: 17px;

  &::before {
    background-color: $grey-80;
  }

  &::after {
    background-color: $grey-80;
  }

  @include tablet {
    display: none;
  }
}

.profile-section {
  display: flex;
  justify-content: space-between;
  margin: 0 auto;
  max-width: 1240px;

  @include desktop {
    margin: $space-40 auto 0;
  }
}

.profile-container {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  align-items: stretch;

  .desktop {
    display: none;

    @include desktop {
      display: block;
    }
  }

  .mobile {
    display: block;

    @include tablet {
      display: none;
    }
  }

  .tablet {
    display: none;

    @include tablet {
      display: block;
      padding: $space-20;
    }

    @include desktop {
      display: none;
    }
  }
}

.profile {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  scroll-margin-top: 80px;
  align-items: stretch;

  @include tablet {
    position: relative;
    display: grid;
    grid-template-columns: 280px auto;
    margin-top: $space-20;
    grid-column-gap: $space-30;
    align-items: flex-start;
  }

  @include desktop {
    grid-template-columns: 330px auto;
    grid-column-gap: $space-40;
  }
}

.profile-overview-header {
  display: flex;
  align-items: baseline;
  color: $black;

  @include tablet {
    position: relative;
    margin-bottom: $space-30;
  }
}

.profile-overview {
  margin-left: auto;
  padding-bottom: $space-30;
  width: 100%;

  .profile-overview-header {
    display: none;
  }

  @include tablet {
    grid-row: 1 / 4;
    padding-bottom: $space-40;

    .profile-overview-header {
      display: flex;
    }
  }
}

.profile-section-title-edit {
  @include tablet {
    display: flex;
    width: 100%;
    flex-flow: column nowrap;
  }
}

.edit-name-layer {
  position: fixed;
  inset: 0;
  background: $black-100-opacity-30;
  transition: all 0.3s ease-in-out;
  z-index: 4;

  @include tablet {
    display: none;
  }

  &.active {
    opacity: 1;
    z-index: 3;
    visibility: visible;
  }
}

.name-field-change {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  margin: 0;
  display: grid;
  grid-row-gap: $space-30;
  flex-flow: column nowrap;
  line-height: 1.05;
  background-color: white;
  border-radius: 10px 10px 0 0;
  padding: $space-55 $space-20 $space-30;
  z-index: 4;

  @include tablet {
    position: absolute;
  }

  @include tablet {
    position: relative;
    bottom: auto;
    display: grid;
    flex-flow: row nowrap;
    grid-template-columns: 1fr 1fr;
    grid-column-gap: $space-50;
    margin-top: 13px;
    padding: 0;
  }
}

.profile-rating {
  display: flex;
  align-items: flex-start;
  white-space: nowrap;
}

.profile-rating-count {
  display: flex;
  align-items: center;
  font-weight: 600;
  font-size: 21px;
  color: $grey-80;

  span {
    margin: 0 0 0 4px;
    color: $black;
  }
}

.profile-rating-star {
  width: 18px;
  height: 18px;
  margin-right: 4px;
  color: $yellow-60;

  @include tablet {
    width: 25px;
    height: 26px;
  }
}

.profile-rating-excellent {
  .profile-rating-count {
    color: $green-40;
  }

  .profile-rating-star {
    color: $green-40;
  }
}

.profile-rating-good {
  .profile-rating-count {
    color: $blue-60;
  }

  .profile-rating-star {
    color: $blue-60;
  }
}

.profile-rating-bad {
  .profile-rating-count {
    color: $red-60;
  }

  .profile-rating-star {
    color: $red-60;
  }
}

.profile-main-images {
  position: relative;
  display: grid;
  margin: 0 (-$space-20);

  @include tablet {
    grid-column: 1 / 2;
    grid-row: 1 / 2;
    grid-row-gap: 13px;
    margin: 0;
  }

  @include desktop {
    width: 330px;
    box-shadow: $shadow-profile-image-main;
    border-radius: 10px;
    padding: $space-20 28px;
  }

  .profile-rating {
    position: absolute;
    top: 20px;
    left: 20px;
    display: flex;
    align-items: center;
    background: $white;
    box-shadow: $shadow-btn-edit;
    border-radius: 65px;
    padding: 3px $space-10;
    z-index: 2;

    @include tablet {
      display: none;
    }
  }

  .profile-rating-count {
    font-size: 16px;
  }

  .profile-overview-header {
    @include tablet {
      display: none;
    }
  }
}

.profile-image {
  position: relative;

  @include tablet {
    .profile-overview-header {
      display: none;
    }
  }
}

.profile-image-edit {
  position: absolute;
  top: 20px;
  right: 20px;
  background: $white;
  box-shadow: $shadow-btn-edit;
  border-radius: 65px;
  padding: 2px $space-10 4px;
  font-size: 16px;
  color: $black;
  font-weight: 500;
  z-index: 1;
  cursor: pointer;

  @include tablet {
    display: none;
  }
}

main h1.profile-name {
  position: absolute;
  bottom: 30px;
  left: 0;
  right: 0;
  margin: 0;
  font-size: 38px;
  font-weight: 700;
  line-height: 1.05;
  color: white;
  text-align: center;
  word-break: word-break;
  z-index: 2;
  pointer-events: none;
  padding: 0 $space-45;

  &.smaller {
    font-size: 30px;
  }

  @include tablet {
    position: relative;
    letter-spacing: 0.5px;
    padding: 0 $space-35 0 0;
    text-align: left;
    bottom: auto;
    font-size: 44px;
    color: $black;
    line-height: 1.05;

    &.smaller {
      font-size: 44px;
    }
  }
}

.profile-main-images-container-mobile {
  overflow: hidden;

  @include tablet {
    display: none;
  }
}

.profile-main-images-container-desktop {
  display: none;

  @include tablet {
    display: grid;
    grid-row-gap: 13px;
  }

  @include desktop {
    margin: 0 0 $space-30;
  }
}

.edit-button svg {
  position: absolute;
  top: -1px;
  right: -1px;
  width: 24px;
  height: 25px;
  color: $grey-60;
  transition: all 0.3s ease-in-out;
}

.edit-button-header {
  position: absolute;
  bottom: 0;
  width: 34px;
  height: 34px;
  padding: 4px;
  margin-left: $space-5;
  flex-shrink: 0;
  cursor: pointer;
  pointer-events: auto;
  display: inline-block;
  background-color: inherit;
  border: none;

  &::before {
    content: '';
    display: block;
    width: 100%;
    height: 100%;
    background: rgb(white, 20%);
    border-radius: 50%;
    transition: all 0.3s ease-in-out;
  }

  svg {
    position: absolute;
    top: 1px;
    right: 2px;
    width: 24px;
    height: 25px;
    color: white;
    transition: all 0.3s ease-in-out;
  }

  @include tablet {
    top: unset;
    width: 26px;
    height: 26px;

    &::before {
      background: $grey-20;
    }

    svg {
      width: 17px;
      height: 18px;
      color: $grey-60;
    }
  }

  &:hover {
    &::before {
      background: $grey-40;
    }

    svg {
      color: $grey-80;
    }
  }
}

.edit-button {
  position: relative;
  display: flex;
  width: 26px;
  height: 26px;
  margin-left: 12px;
  background: $grey-20;
  border-radius: 12.9873px;
  transition: all 0.3s ease-in-out;
  cursor: pointer;
  flex-shrink: 0;
  border: none;

  @include tablet {
    width: 18px;
    height: 18px;
    border-radius: 9px;

    svg {
      width: 17px;
      height: 18px;
      top: -2px;
      right: -1px;
    }
  }

  &:hover {
    background: $grey-40;

    svg {
      color: $grey-80;
    }
  }
}

.star {
  width: 25px;
  height: 26px;
  margin-right: 4px;
  color: $blue-60;
}

.profile-tabs-panel {
  display: flex;
  flex-flow: column nowrap;
  position: sticky;
  top: 0;
  z-index: 3;
  margin-bottom: $space-20;
  border-bottom: 1px solid $grey-40;
  background-color: $white;

  @include tablet {
    border-bottom: none;
    margin-bottom: 0;
  }

  @include desktop {
    position: initial;
  }

  @include desktopxl {
    align-items: flex-end;
    flex-flow: row nowrap;
    margin-bottom: 34px;
    border-bottom: 1px solid $grey-40;
  }
}

.profile-tabs {
  display: flex;
  flex-flow: row nowrap;
  grid-column-gap: 3px;
  order: 2;
  background-color: $white;

  @include tablet {
    grid-column-gap: $space-10;
    order: 1;
    border-bottom: 1px solid $grey-40;
  }

  @include desktopxl {
    border-bottom: none;
  }
}

.profile-tab {
  position: relative;
  text-align: center;
  font-size: 16px;
  color: $grey-80;
  font-weight: 500;
  cursor: pointer;
  max-width: 80px;
  width: 100%;
  transition: all 0.3s ease-in-out;
  white-space: nowrap;
  padding: 10px 0;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    bottom: -2px;
    height: 3px;
    background-color: $black;
    opacity: 0;
    transition: all 0.3s ease-in-out;
  }

  @include desktop {
    min-width: 85px;
  }

  &:hover {
    font-weight: 700;
    color: $black;
  }

  &.active {
    font-weight: 700;
    color: $black;

    &::before {
      opacity: 1;
    }
  }
}

.profile-tab-label {
  position: absolute;
  top: 0;
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background-color: $red-60;
}

.profile-section-title {
  display: none;
  flex-flow: row nowrap;
  align-items: center;
  text-transform: uppercase;
  color: $grey-80;
  line-height: 1.875;
  font-size: 16px;
  font-weight: 700;
  gap: $space-5;

  @include tablet {
    display: flex;
  }
}

.delete-button {
  background-color: $red-20;
  border-radius: 10px;
  padding: 3px $space-10;
  color: $red-100;
  font-weight: 600;
  line-height: 16px;
  margin-left: $space-10;
  text-transform: capitalize;
  cursor: pointer;
  border: none;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
}

.save-button {
  background-color: $blue-20;
  border-radius: 10px;
  padding: 3px $space-10;
  color: $blue-100;
  font-weight: 600;
  line-height: 16px;
  margin-left: $space-10;
  text-transform: capitalize;
  cursor: pointer;
  border: none;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
}

.save-button-name-mobile {
  padding: 11px $space-20;
  line-height: 1.3;
  font-size: 14px;
  text-align: center;
  border-radius: 60px;
  margin: 0;

  @include tablet {
    display: none;
  }
}

.profile-section-empty {
  display: flex;
  justify-content: space-between;
  padding: 24px $space-30;
  border: 1px dashed $grey-60;
  border-radius: 10px;
  cursor: pointer;
}

.profile-section-empty-add-button {
  position: relative;
  margin-left: $space-10;
  color: $blue-100;
  font-weight: 700;
  text-decoration: none;

  &::after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 2px;
    background-color: $blue-100;
    opacity: 0;
  }

  &:hover::after {
    opacity: 1;
  }
}

.profile-connections-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;

  @include tablet {
    grid-column: 1/2;
    grid-row: 2/3;
  }
}

.profile-connections {
  display: flex;
  flex-flow: column nowrap;
  gap: $space-20;
  padding: $space-20 32px;
  background-color: white;
  box-shadow: $shadow-profile-image-main;
  border-radius: 10.193px;

  @include tablet {
    order: 2;
  }

  @include desktop {
    width: 330px;
    margin: 0 0 $space-30;
    padding: $space-20 28px;
  }
}

.profile-id-container {
  position: relative;
  padding-top: 13px;
  color: $grey-80;
  font-weight: 400;
  line-height: normal;

  &::before {
    content: '';
    display: block;
    position: absolute;
    height: 1px;
    width: calc(100% + 64px);
    left: -32px;
    top: 0;
    background: $grey-10;

    @include desktop {
      left: -28px;
      width: calc(100% + 56px);
    }
  }
}

.profile-secondary-images {
  display: none;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  grid-gap: $space-20;

  @include tablet {
    display: grid;
  }
}

.profile-video-title {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  font-weight: 300;
}

.video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
}

.video-container {
  position: relative;
  padding-top: 56%;
  margin-bottom: $space-15;
}

.video-card {
  flex: 0 0 50%;
}

.progress-panel {
  padding: $space-20;
  width: 100%;
  margin: 0;
  display: flex;
  align-items: center;
  order: 1;
  background-color: $grey-10;

  @include tablet {
    padding: 0 0 $space-20;
    margin: 0;
    background-color: transparent;
  }

  @include desktop {
    padding: $space-25 0;
  }

  @include desktopxl {
    flex-flow: row nowrap;
    order: 2;
    margin: 0 0 0 auto;
    padding: 0;
  }
}

.progress-percentage {
  width: 45px;
  height: 45px;
  min-width: 45px;
  min-height: 45px;
  align-self: center;
  justify-content: space-around;

  @include tablet {
    display: flex;
    width: 28px;
    height: 28px;
    min-width: 28px;
    min-height: 28px;
  }
}

.circular-chart {
  display: block;
  width: 100%;
  height: 100%;
}

.progress-excellent {
  stroke: $green-40;
}

.progress-good {
  stroke: $blue-80;
}

.progress-bad {
  stroke: $red-60;
}

.circular-chart-bg {
  fill: none;
  stroke: #eee;
  stroke-width: 8;
}

.circular-chart-percentage {
  fill: none;
  stroke-width: 8;
  animation: progress 1s ease-out forwards;
}

@keyframes progress {
  0% {
    stroke-dasharray: 0 100;
  }
}

.progress-section {
  position: relative;
  display: flex;
  flex-grow: 1;
  margin-left: $space-35;

  @include tablet {
    margin-left: 17px;
  }

  @media (width >= 1160px) {
    flex-grow: 0;
    margin-left: $space-10;
  }
}

.progress-btn {
  display: flex;
  flex-grow: 1;
  align-items: center;
  padding: 9px 24px;
  font-size: 14px;
  justify-content: center;
  letter-spacing: 0.2px;
  font-weight: 600;
  background: $gradient-btn-profile-progress-excellent;
  border-radius: 20px;
  color: white;
  cursor: pointer;

  @include tablet {
    display: flex;
  }

  @media (width >= 1160px) {
    border-radius: 20px 20px 0 0;
  }
}

.progress-btn-excellent {
  background: $gradient-btn-profile-progress-excellent;
}

.progress-btn-good {
  background: $gradient-btn-profile-progress-good;
}

.progress-btn-bad {
  background: $gradient-btn-profile-progress-bad;
}

.progress-btn-icon {
  position: relative;
  display: flex;
  top: 4px;
  margin-left: $space-5;
  border: 5px solid transparent;
  border-top: 6px solid white;
  transition: all 0.3s ease-in-out;
}

.btn-is-active {
  .progress-btn-icon {
    top: -3px;
    border-bottom-color: white;
    border-top-color: transparent;
  }

  @include tablet {
    border-radius: 20px 20px 0 0;
    z-index: 1001;
  }
}

.gallery-modal-overlay {
  & .gallery-modal {
    width: 100vw;
    background-color: transparent;
    display: flex;
    justify-content: center;

    & .gallery-modal-content {
      width: 500px;
      height: unset;
      background-color: transparent;
      border-radius: unset;
    }
  }
}

.carousel-image {
  width: 100%;
}

.icon-hint-container {
  display: flex;
  align-items: center;
}

.profile-image-container {
  display: flex;
  flex-direction: column;
  gap: $space-5;
}

.profile-pdf-btn {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  padding: $space-10 28px;
  line-height: 1.2;
  background: $blue-100;
  border-radius: 60px;
  color: $white;
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 0.3px;
  transition: all 0.3s ease-in-out;
  cursor: pointer;
  text-transform: capitalize;

  &:hover {
    text-decoration: none;
    background: rgb($blue-100, 0.7);
  }

  & img {
    margin-right: 12px;
  }
}

.comp-card-download-container {
  margin: $space-20 0;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 40px;

  @include tablet {
    order: 1;
  }
}

.display-none-on-mobile {
  display: none;

  @include tablet {
    display: flex;
  }
}

.hidden-profile-block {
  margin: 0 (-$space-20);

  @include desktop {
    margin: 0;
  }
}

.progress-panel-container {
  display: flex;
  align-items: center;
  order: 1;
  background-color: $grey-10;
  margin: 0 (-$space-20) $space-20;

  @include tablet {
    display: flex;
    align-items: flex-end;
    order: 1;
    flex-direction: column;
    background-color: $white;
    padding: $space-20 0 0;
    margin: 0;
  }

  @include desktop {
    padding: 0;
  }

  @include desktopxl {
    flex-flow: row nowrap;
    order: 2;
    margin: 0 0 0 auto;
    padding: 0;
  }
}

.profile-tabs-panel-with-sale {
  top: 84px;
}
