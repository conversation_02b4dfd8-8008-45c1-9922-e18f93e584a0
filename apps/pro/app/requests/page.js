import RequestsClient from './_client';
import Api from '@services/api';
import { extractResult } from '@utils/extractPromiseResult';
import { cookies, headers } from 'next/headers';
import { CookieServiceServer } from '@services/cookieServiceServer';
import {
  formatSearchedTalents,
  getActiveCastingCall,
  getActiveRole,
  getCastingCallGroups,
  prepareCastingCalls,
} from '@utils/submissionsHelper';
import { TABS } from '@constants/submissions';
import { getContent, getFilters } from '@utils/talentFilterHelper';
import { ELASTIC_PAGE_LIMIT, TALENTS_LIMIT } from '@constants/pagination';
import { getResolvedUrl } from '@utils/apiHelper';

export const metadata = {
  title: 'Requests',
};

export default async function RequestsPage(req) {
  const searchParams = await req.searchParams;
  const resolvedUrl = getResolvedUrl('/requests', searchParams);
  const cookieStore = await cookies();
  const headerStore = await headers();
  const page = 1;
  const userProfiles = CookieServiceServer.getUserProfilesCookie(cookieStore);
  const clientId = userProfiles.length ? userProfiles[0].clientId || 0 : 0;
  const profileId = CookieServiceServer.getProfileCookie(cookieStore);

  const paths = [
    `/profiles/${profileId}/contact-groups`,
    `/profiles/${profileId}/contacts`,
    '/clientstatus/info',
    `/accounts/${clientId}/castingcalls?es=1&context[]=for_submissions&filter[state]=active&filter[status][]=approved`,
    `/accounts/${clientId}/castingcalls?es=1&context[]=for_submissions&filter[status][]=approved`,
    `/accounts/${clientId}/castingcalls?es=1&context[]=for_submissions&filter[status][]=new`,
    `/accounts/${clientId}/castingcalls?es=1&context[]=for_submissions&filter[status][]=hold`,
    `/accounts/${clientId}/castingcalls?es=1&context[]=for_submissions&filter[status][]=edited`,
    `/accounts/${clientId}/castingcalls?es=1&context[]=for_submissions&filter[status][]=declined`,
    `/accounts/${clientId}/castingcalls?es=1&context[]=for_submissions&filter[status][]=deactivated`,
  ];

  const results = await Promise.allSettled(
    paths.map((path) =>
      Api.serverside(path, cookieStore, headerStore, resolvedUrl),
    ),
  );

  const [
    groupsResponse,
    contactsResponse,
    clientStatusResponse,
    runningCastingCallsResponse,
    approvedCastingCallsResponse,
    newCastingCallsResponse,
    holdCastingCallsResponse,
    editedCastingCallsResponse,
    declinedCastingCallsResponse,
    deactivatedCastingCallsResponse,
  ] = results.map((result) => extractResult(result, {}));

  const castingCalls = prepareCastingCalls(
    runningCastingCallsResponse,
    approvedCastingCallsResponse,
    newCastingCallsResponse,
    holdCastingCallsResponse,
    editedCastingCallsResponse,
    declinedCastingCallsResponse,
    deactivatedCastingCallsResponse,
  );

  const castingCallGroups = getCastingCallGroups(castingCalls);
  const activeCastingCall = getActiveCastingCall(
    castingCalls,
    castingCallGroups,
  );
  const activeRole = getActiveRole(activeCastingCall);

  let candidateData = {};

  if (activeCastingCall && activeRole) {
    const recommendedResponse = await Api.serverside(
      `/roles/${activeRole.id}/recommendations/profiles?page=1&limit=1`,
      cookieStore,
      headerStore,
      resolvedUrl,
    );

    activeRole.recommended = recommendedResponse.count || 0;

    const accountId = CookieServiceServer.getAccountCookie(cookieStore);

    const response = await getContent(
      {},
      resolvedUrl,
      cookieStore,
      headerStore,
      accountId,
      `&inclusions[role_id]=${activeRole.id}&candidates[status][]=new&expand=notes,candidates&profile=${profileId}`,
    );

    const candidatesTotal = response.talents?.count || 0;
    const pagesTotal = Math.ceil(candidatesTotal / TALENTS_LIMIT);
    const maximumPage =
      ELASTIC_PAGE_LIMIT > pagesTotal ? pagesTotal : ELASTIC_PAGE_LIMIT;

    candidateData = {
      candidates: formatSearchedTalents(response.talents?.items || []),
      candidatesTotal,
      candidatesTotalLimit: maximumPage * TALENTS_LIMIT,
      defaultFilters: await getFilters(
        resolvedUrl,
        cookieStore,
        headerStore,
        accountId,
      ),
      activeFilters: response.filters,
      cities: response.cities,
    };
  }

  return (
    <RequestsClient
      tab={TABS.Applied}
      groups={groupsResponse}
      contacts={contactsResponse}
      canContactTalent={
        clientStatusResponse.info?.allowed_to?.hasOwnProperty(
          'contact_talent',
        ) || false
      }
      castingCalls={castingCallGroups}
      role={activeRole}
      activeCastingCall={activeCastingCall}
      candidates={candidateData.candidates || []}
      page={page}
      candidatesTotal={candidateData.candidatesTotal || 0}
      candidatesTotalLimit={candidateData.candidatesTotalLimit || 0}
      clientId={clientId}
      defaultFilters={candidateData.defaultFilters || {}}
      cities={candidateData.cities || {}}
      activeFilters={candidateData.activeFilters || {}}
    />
  );
}
