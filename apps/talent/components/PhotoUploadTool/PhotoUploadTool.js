'use client';
import React, {
  forwardRef,
  useCallback,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { createImage } from '@utils/cropToolHelpers';
import styles from './PhotoUploadTool.module.scss';
import cn from 'classnames';
import { Modal } from '@components';
import Compressor from 'compressorjs';

const PhotoUploadTool = forwardRef(
  (
    {
      onFileInputChange,
      minImageWidth = 600,
      minImageHeight = 800,
      maxImageSizeMB = 10,
      children,
      maxImageWidth = 4096,
      maxImageHeight = 4096,
    },
    ref,
  ) => {
    const fileInputRef = useRef(null);
    const [error, setError] = useState(''); // min | max | resolution | resize

    const selectFile = () => {
      fileInputRef.current.click();
    };

    useImperativeHandle(ref, () => ({
      selectFile() {
        selectFile();
      },
    }));

    const onChange = async (event) => {
      const file = event.target.files[0];

      if (file) {
        // Sometimes MB refers to both 1000 Kb or 1024 Kb
        // MiB always 1024 Kb
        const fileSize = file.size / 1000 / 1000;
        const imageUrl = await readFile(file);
        const image = await createImage(imageUrl);

        if (image.width < minImageWidth || image.height < minImageHeight) {
          setError('resolution');
        } else if (fileSize > maxImageSizeMB) {
          setError('max');
        } else {
          onFileInputChange(imageUrl);
        }
      }

      event.target.value = null;
    };

    const clearError = () => {
      setError('');
    };

    const restartUpload = () => {
      clearError();
      selectFile();
    };

    const readFile = useCallback((file) => {
      return new Promise((resolve, reject) => {
        try {
          const reader = new FileReader();

          reader.onload = () => resolve(reader.result);
          getNormalizedFile(file)
            .then((normalizedFile) => reader.readAsDataURL(normalizedFile))
            .catch((error) => {
              setError('resize');
              reject(error);
            });
        } catch (error) {
          setError('resize');
          reject(error);
        }
      });
    }, []);

    const getNormalizedFile = (file) => {
      return new Promise((resolve, reject) => {
        new Compressor(file, {
          maxWidth: maxImageWidth,
          maxHeight: maxImageHeight,
          success(normalizedFile) {
            resolve(normalizedFile);
          },
          error(error) {
            setError('resize');
            reject(error);
          },
        });
      });
    };

    return (
      <>
        {children}
        <input
          ref={fileInputRef}
          className={styles['file-input']}
          type="file"
          name="file-input"
          onChange={onChange}
          accept="image/png, image/jpeg"
        />
        {error && (
          <Modal
            onClose={clearError}
            closeButtonHidden
            classNameContent={styles['upload-error-modal']}
            showDefaultLayout={false}
            classNameContainer={styles['upload-error-modal-container']}
            classNameOverlay={styles['upload-error-modal-overlay']}
          >
            <div className={styles['photo-upload-error-modal']}>
              <div className={styles['photo-upload-error']}>
                <span className={styles['photo-upload-error-title']}>
                  Ooops! Wrong image size!
                </span>
                {error === 'max' && (
                  <p>
                    We&apos;re sorry, the photo you tried to upload was too
                    large. Maximum size: <b>{maxImageSizeMB}MB</b>.
                  </p>
                )}
                {error === 'resolution' && (
                  <p>
                    We&apos;re sorry, the photo you tried to upload was too
                    small. Minimal resolution:{' '}
                    <b>
                      {minImageWidth}×{minImageHeight} px
                    </b>
                    .
                  </p>
                )}
                {error === 'resize' && (
                  <p>
                    We&apos;re sorry, the photo you tried to upload was too
                    large and resizing failed. Maximum size:{' '}
                    <b>
                      {maxImageWidth}×{maxImageHeight} px
                    </b>
                    .
                  </p>
                )}
              </div>

              <div className={styles['photo-upload-error-actions']}>
                <button
                  onClick={clearError}
                  className={styles['photo-upload-error-action-button']}
                >
                  Cancel
                </button>
                <button
                  onClick={restartUpload}
                  className={cn(
                    styles['photo-upload-error-action-button'],
                    styles['photo-upload-error-action-button-upload'],
                  )}
                >
                  Upload new photo
                </button>
              </div>
            </div>
          </Modal>
        )}
      </>
    );
  },
);

PhotoUploadTool.displayName = 'PhotoUploadTool';

export default PhotoUploadTool;
