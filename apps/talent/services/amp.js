import * as amplitude from '@amplitude/analytics-browser';
import { AMP_ELEMENTS, AMP_EVENTS } from '@constants/amplitude';
import { OneTrustService } from '@services/onetrust';
import { CookieService } from '@services/cookieService';
import { parseQueryString } from '@services/tracking';

export const Amp = {
  initialized: false,

  events: AMP_EVENTS,
  element: AMP_ELEMENTS,

  track(name, properties = {}) {
    if (!OneTrustService.checkIsPerformanceCookiesEnabled()) {
      return;
    }

    if (!this.initialized) {
      this.init();
    }

    amplitude.track(name, { ...properties });
  },

  async trackAsync(name, properties = {}) {
    if (!OneTrustService.checkIsPerformanceCookiesEnabled()) {
      return;
    }

    if (!this.initialized) {
      await this.init();
    }

    return amplitude.track(name, { ...properties }).promise;
  },

  // Setting custom tracking parameters in user properties: cid, utm_id, etc.
  // Can view current user properties in the Amplitude dashboard.
  // Make sure Identity is set immediately.
  identifyUser() {
    const identifyEvent = new amplitude.Identify();
    const params = getTrackingParams();

    for (const [key, value] of Object.entries(params)) {
      // Updates value every time a new value appears.
      identifyEvent.set(key, value);
      // Updates value, if not set previously.
      identifyEvent.setOnce(`initial_${key}`, value);
    }

    amplitude.identify(identifyEvent);
  },

  init() {
    if (!OneTrustService.checkIsPerformanceCookiesEnabled()) {
      return;
    }

    if (this.initialized) {
      return;
    }

    const accountId = CookieService.getAccountCookie();
    const userId = accountId ? String(accountId) : undefined;
    const options = {
      // To make sure the event will be scheduled right away.
      flushIntervalMillis: 0,
      flushQueueSize: 1,
      defaultTracking: {
        sessions: true,
        formInteractions: false,
        fileDownloads: false,
        // To add custom user properties starting from the first event.
        // Custom user properties require Identity setting.
        // Automatic page tracking fires before new Identity is set.
        pageViews: false,
      },
    };

    amplitude.init(process.env.amplitudeId, userId, options);

    // Add custom user properties before any events are sent.
    this.identifyUser();

    // Enrich events with custom data.
    amplitude.add(updateUserId());
    amplitude.add(addProperties());
    amplitude.add(updateReferrer());

    this.initialized = true;
  },
};

// Update $identify event referrer properties only if it's third-party.
// Update the page view event referrer, tracking properties on each navigation: external and internal.
// For SPA applications, internal navigation with router does not automatically update referrer.
const updateReferrer = () => {
  return {
    name: 'referrer',
    type: 'enrichment',
    setup: () => undefined,
    execute: (event) => {
      const pageLocation = event.event_properties.page_location;
      const referrerData = resolveReferrer(pageLocation);

      if (event.event_type.startsWith('View ')) {
        event.event_properties = {
          ...event.event_properties,
          ...getTrackingParams(),
        };

        if (referrerData) {
          event.event_properties.referrer = referrerData.referrer;
          event.event_properties.referring_domain =
            referrerData.referring_domain;
        }
      } else if (event.event_type === '$identify') {
        event.user_properties.$set = event.user_properties.$set || {};
        const isThirdPartyReferrer = !referrerData?.referring_domain.endsWith(
          new URL(process.env.publicUrl).hostname,
        );

        if (referrerData && isThirdPartyReferrer) {
          event.user_properties.$set.referrer = referrerData.referrer;
          event.user_properties.$set.referring_domain =
            referrerData.referring_domain;
        } else {
          // first-party or missing: remove to ensure nothing is sent
          delete event.user_properties.$set.referrer;
          delete event.user_properties.$set.referring_domain;
        }
      }

      return event;
    },
  };
};

const resolveReferrer = (pageLocation) => {
  const storageReferrer = sessionStorage.getItem('amplitudeReferrer');
  const storageReferringDomain = sessionStorage.getItem(
    'amplitudeReferringDomain',
  );

  if (!storageReferrer || storageReferrer === pageLocation) {
    const docReferrer = document.referrer;

    if (docReferrer && URL.canParse(docReferrer)) {
      return {
        referrer: docReferrer,
        referring_domain: new URL(docReferrer).hostname,
      };
    }

    return;
  }

  return {
    referrer: storageReferrer,
    referring_domain: storageReferringDomain,
  };
};

export const addProperties = () => {
  return {
    name: 'properties',
    type: 'enrichment',
    setup: () => undefined,
    execute: (event) => {
      // Remove default fields
      for (const key in event.event_properties) {
        if (key.startsWith('[Amplitude] Page ')) {
          delete event.event_properties[key];
        }
      }

      event.event_properties = {
        ...event.event_properties,
        ...userType(),
        ...userLevel(),
        ...pageInfo(),
      };

      event.user_properties = {
        ...event.user_properties,
        ...experimentsInfo(),
      };

      return event;
    },
  };
};

export const updateUserId = () => {
  return {
    name: 'user',
    type: 'before',
    setup: () => undefined,
    execute: (event) => {
      const accountId = CookieService.getAccountCookie();
      const userId = accountId ? String(accountId) : undefined;

      if (amplitude.getUserId() !== userId) {
        amplitude.setUserId(userId);
      }

      return event;
    },
  };
};

export const userType = () => {
  return {
    user_type: CookieService.getUserTypeCookie() || 'undefined',
  };
};

export const userLevel = () => {
  const accountLevel = CookieService.getAccountLevelCookie();

  let userLevel = 'guest';

  if (accountLevel) {
    switch (true) {
      case accountLevel.isBasic:
        userLevel = 'basic';
        break;
      case accountLevel.isDelayedSubscriptionCancel:
        userLevel = 'delayed_cancel';
        break;
      case accountLevel.isPaid:
        userLevel = 'paid';
        break;
      case accountLevel.isPortfolioOnly:
        userLevel = 'portfolio_only';
        break;
    }
  }

  return { user_level: userLevel };
};

export const getPageTheme = (width) => {
  if (width < 768) {
    return 'mobile';
  }

  if (width < 1024) {
    return 'tablet';
  }

  return 'desktop';
};

export const pageInfo = () => {
  const { title } = document;
  const { location, innerWidth, innerHeight } = window;
  const { href, pathname, hash, search } = location;

  return {
    page_title: title,
    page_location: href,
    page_url: href.split('?')[0],
    page_path: pathname,
    page_hash: hash,
    page_search: search,
    page_width: innerWidth,
    page_height: innerHeight,
    page_theme: getPageTheme(innerWidth),
  };
};

export const experimentsInfo = () => {
  const experiments = CookieService.getExperimentsCookie() ?? {};
  const info = {};

  for (const slug in experiments) {
    info[`experiments_${slug}`] = experiments[slug].group;
  }

  return info;
};

const getTrackingParams = () => {
  const queries = parseQueryString(window.location.search);

  const filteredQueries = {};

  for (const [key, value] of Object.entries(queries)) {
    const isKeyAllowed = key === 'cid' || key.startsWith('utm_');

    if (isKeyAllowed) {
      filteredQueries[key] = value;
    }
  }

  return filteredQueries;
};
