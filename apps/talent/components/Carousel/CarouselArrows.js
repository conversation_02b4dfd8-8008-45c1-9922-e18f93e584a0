'use client';
import cn from 'classnames';
import styles from './CarouselArrows.module.scss';
import Image from 'next/image';

export const CarouselArrow = ({
  onClick,
  disabled,
  direction = 'right', // right | left
  className = '',
}) => {
  return (
    <div
      className={cn(
        styles['carousel-arrow-container'],
        styles[direction],
        styles[className],
      )}
    >
      <button
        className={cn(styles['carousel-arrow'], styles[direction])}
        disabled={disabled}
        onClick={onClick}
      >
        <Image
          className={cn(styles['carousel-arrow-icon'], styles[direction])}
          src={'/assets/icons/icon-angle-right.svg'}
          width={30}
          height={45}
          alt="icon-arrow"
        />
      </button>
    </div>
  );
};
