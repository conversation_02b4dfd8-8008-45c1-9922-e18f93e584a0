'use client';
import React, { memo, useMemo, useState } from 'react';
import { useField } from 'formik';
import dayjs from 'dayjs';
import SelectFormik from '@components/FormFields/SelectFormik';
import {
  getDayOptionsByMonth,
  getMonthOptionsWithFullTitles,
  getYearOptionsWithRange,
} from '@utils/getTimeOptions';
import styles from './BirthdayFormik.module.scss';

const BirthdayFormik = ({
  name = 'birthday',
  label = '',
  hideFloatingPlaceholder = false,
}) => {
  const [{ value }, { error }, { setValue }] = useField(name);
  const [dayOptions, setDayOptions] = useState(
    getDayOptionsByMonth(dayjs().year(), 1),
  );

  const monthOptions = useMemo(
    () => getMonthOptionsWithFullTitles(false, dayjs().year()),
    [],
  );
  const yearOptions = useMemo(() => getYearOptionsWithRange(), []);

  const setMonthValue = (month) => {
    const updatedDayOptions = getDayOptionsByMonth(value.year, month);
    const resetDay =
      value.day &&
      !updatedDayOptions.find((option) => option.value === value.day);

    setValue({ ...value, month });
    setDayOptions(updatedDayOptions);

    if (resetDay) {
      setValue({ ...value, day: '' });
    }
  };

  const setYearValue = (year) => {
    const updatedDayOptions = getDayOptionsByMonth(year, value.month);
    const resetDay =
      value.day &&
      !updatedDayOptions.find((option) => option.value === value.day);

    setValue({ ...value, year });
    setDayOptions(updatedDayOptions);

    if (resetDay) {
      setValue({ ...value, day: '' });
    }
  };

  return (
    <fieldset className={styles.wrapper}>
      {label && <legend>{label}</legend>}
      <SelectFormik
        name={`${name}.month`}
        placeholder="Month"
        options={monthOptions}
        onChange={setMonthValue}
        hideFloatingPlaceholder={hideFloatingPlaceholder}
      />
      <SelectFormik
        name={`${name}.day`}
        placeholder="Day"
        options={dayOptions}
        hideFloatingPlaceholder={hideFloatingPlaceholder}
      />
      <SelectFormik
        name={`${name}.year`}
        placeholder="Year"
        options={yearOptions}
        onChange={setYearValue}
        hideFloatingPlaceholder={hideFloatingPlaceholder}
      />
      {error?.date && <div className={styles.error}>{error?.date}</div>}
    </fieldset>
  );
};

export default memo(BirthdayFormik);
