'use client';
import React, { memo, useEffect, useRef, useState } from 'react';
import {
  Checkbox,
  Loading,
  ModalAddContact,
  ModalProfile,
  Paginator,
} from '@components';
import { TABS } from '@constants/submissions';
import { useRouter } from 'next/navigation';
import styles from './ShortListedTab.module.scss';
import Image from 'next/image';
import cn from 'classnames';
import { useSubmissions } from '@contexts/SubmissionsContext';
import Api from '@services/api';
import { useAuth } from '@contexts/AuthContext';
import { useNotifications } from '@contexts/NotificationContext';
import { useMessageWidget } from '@contexts/MessageWidgetContext';
import { useModalContext } from '@contexts/ModalContext';
import { IconDecline, IconMessage } from '../../Icon';
import { SUBMISSIONS_LIMIT } from '@constants/pagination';

const ShortListedTab = ({
  candidates,
  page,
  candidatesTotal,
  castingCallId,
  roleId,
  canContactTalent,
  role,
  loadingPage,
  castingCall,
}) => {
  const [selectedCandidateIds, setSelectedCandidateIds] = useState([]);
  const [compCardUrl, setCompCardUrl] = useState('');
  const [showProfileModal, setShowProfileModal] = useState(false);
  const [selectedCandidate, setSelectedCandidate] = useState(null);
  const [showModalAddContact, setShowModalAddContact] = useState(false);
  const downloadLinkRef = useRef(null);
  const router = useRouter();
  const { onDecline, loading } = useSubmissions();
  const { accountId } = useAuth();
  const { setNotification } = useNotifications();
  const { toggleShowContactTalentModal } = useModalContext();
  const { openBatchConversation, openConversation } = useMessageWidget();

  useEffect(() => {
    if (downloadLinkRef.current && compCardUrl) {
      downloadLinkRef.current.click();
    }
  }, [compCardUrl]);

  const onPaginationChange = (event, nextPage) => {
    event.preventDefault();
    if (page !== nextPage) {
      router.push(
        `/requests/castingcall/${castingCallId}/role/${roleId}?tab=${TABS.Shortlist}&page=${nextPage}`,
      );
    }
  };

  const toggleShowModalAddContact = (candidate) => {
    setSelectedCandidate(candidate || null);
    setShowModalAddContact(!showModalAddContact);
  };

  const onSelectCandidate = (event, id) => {
    const selected = event.target.checked;

    if (selected) {
      setSelectedCandidateIds([...selectedCandidateIds, id]);
    } else {
      setSelectedCandidateIds(
        selectedCandidateIds.filter(
          (selectedCandidateId) => selectedCandidateId !== id,
        ),
      );
    }
  };

  const onContact = (candidate) => {
    if (!canContactTalent) {
      toggleShowContactTalentModal();
    } else {
      openConversation({
        id: candidate.id,
        firstName: candidate.firstName,
        lastName: candidate.lastName,
        image: candidate.titlePhotoUrl,
        gender: candidate.gender,
      });
    }
  };

  const onDownload = async () => {
    const raw = JSON.stringify({
      profile_ids: selectedCandidateIds,
    });

    const response = await Api.clientside(
      `/accounts/${accountId}/compcards`,
      {
        method: 'POST',
        body: raw,
      },
      {
        'Content-Type': 'application/json',
      },
    );

    if (response.headers?.get('content-type') === 'application/pdf') {
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);

      setCompCardUrl(url);

      return;
    }

    if (response.status === 'error') {
      setNotification({
        type: 'error',
        message: response.message,
        timeout: '5000',
      });
    }
  };

  const toggleShowProfileModal = (candidate) => {
    setSelectedCandidate(candidate || null);
    setShowProfileModal(!showProfileModal);
  };

  const onOpenBatchConversation = () => {
    if (!canContactTalent) {
      toggleShowContactTalentModal();
    } else {
      openBatchConversation(castingCall, role, 'Shortlist');
    }
  };

  return (
    <div className={styles.container}>
      {candidates.length ? (
        <>
          <div className={styles.header}>
            <button
              onClick={onOpenBatchConversation}
              className={styles.button}
              disabled={loadingPage}
            >
              Send batch message
            </button>
            <button
              className={styles.button}
              disabled={!selectedCandidateIds.length || loadingPage}
              onClick={onDownload}
            >
              Download PDF compcards
            </button>
            <a
              href={compCardUrl}
              download={'compcards.pdf'}
              ref={downloadLinkRef}
              style={{ display: 'none' }}
            />
            {selectedCandidateIds.length > 0 && (
              <div className={styles.selected}>
                {selectedCandidateIds.length} selected
              </div>
            )}
          </div>
          <div style={{ position: 'relative' }}>
            {(loading || loadingPage) && (
              <div className={styles['loading-overlay']}>
                <Loading />
              </div>
            )}
            <div className={styles.candidates}>
              {candidates.map((candidate) => (
                <div key={candidate.id} className={styles.candidate}>
                  <div
                    onClick={() => toggleShowProfileModal(candidate)}
                    className={styles['info-container']}
                  >
                    <div className={styles['checkbox-container']}>
                      <Checkbox
                        name={candidate.id}
                        value={selectedCandidateIds.includes(candidate.id)}
                        onChange={(e) => onSelectCandidate(e, candidate.id)}
                      />
                    </div>
                    <div className={styles['image-container']}>
                      <Image
                        className={styles.image}
                        src={
                          candidate.titlePhotoUrl ||
                          `/assets/placeholders/${
                            candidate.gender || 'male'
                          }-head.svg`
                        }
                        alt="image"
                        width={64}
                        height={64}
                      />
                    </div>
                    <div className={styles.info}>
                      <span className={styles.name}>{candidate.fullName}</span>
                      <div className={styles['rating-container']}>
                        <div className={styles.rating}>
                          <Image
                            className={styles.star}
                            src={'/assets/icons/icon-star-3.svg'}
                            width={17}
                            height={17}
                            alt="star icon"
                          />
                          <span>{candidate.rating}</span>
                        </div>
                        <div className={styles['location-container']}>
                          <Image
                            src={'/assets/icons/icon-pin-2.svg'}
                            width={14}
                            height={14}
                            alt="icon"
                          />
                          <span className={styles.location}>
                            {String(candidate.location)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    onClick={() => toggleShowModalAddContact(candidate)}
                    className={styles['contact-container']}
                    title="Add to contacts"
                  >
                    <Image
                      src="/assets/icons/icon-add-3.svg"
                      alt="icon"
                      width={30}
                      height={30}
                    />
                  </div>
                  <div className={styles['action-container']}>
                    <div
                      onClick={() => toggleShowProfileModal(candidate)}
                      className={styles['match-container']}
                    >
                      {candidate.isFullMatch ? (
                        <div className={cn(styles['full-match'], styles.match)}>
                          full match
                        </div>
                      ) : (
                        <div
                          className={cn(styles['not-full-match'], styles.match)}
                        >
                          not a full match
                        </div>
                      )}
                    </div>
                    <div className={styles.actions}>
                      <button
                        className={styles.action}
                        title="Decline"
                        onClick={() => onDecline(candidate, TABS.Shortlist)}
                      >
                        <IconDecline className={styles['icon-action']} />
                      </button>
                      <button
                        className={styles.action}
                        onClick={() => onContact(candidate)}
                        title="Message"
                      >
                        <IconMessage className={styles['icon-action']} />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            <Paginator
              page={page}
              perPage={SUBMISSIONS_LIMIT}
              total={candidatesTotal}
              prefixUrl={`/requests/castingcall/${castingCallId}/role/${roleId}?tab=${TABS.Declined}`}
              prefixPage={'&page='}
              handleChange={onPaginationChange}
            />
          </div>
        </>
      ) : (
        <div className={styles['no-candidates']}>
          {(loading || loadingPage) && (
            <div className={styles['loading-overlay']}>
              <Loading padding="20px 0" minHeight="40px" />
            </div>
          )}
          You haven&apos;t added any talent to shortlist yet.
        </div>
      )}
      {showProfileModal && (
        <ModalProfile
          id={selectedCandidate?.id}
          onClose={toggleShowProfileModal}
          isApproved
          criteria={selectedCandidate?.criteria}
          isFullMatch={selectedCandidate?.isFullMatch}
          role={role}
          onDecline={() => onDecline(selectedCandidate, TABS.Shortlist)}
        />
      )}
      {showModalAddContact && (
        <ModalAddContact
          id={selectedCandidate.id}
          firstName={selectedCandidate.firstName}
          lastName={selectedCandidate.lastName}
          gender={selectedCandidate.gender}
          imageSrc={selectedCandidate.titlePhotoUrl}
          onClose={toggleShowModalAddContact}
        />
      )}
    </div>
  );
};

export default memo(ShortListedTab);
