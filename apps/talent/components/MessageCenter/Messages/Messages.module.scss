@use '@styles/mixins' as *;
@use '@styles/variables' as *;

.message-container {
  margin-top: auto;
  flex-direction: column;
  justify-content: flex-end;
  align-self: center;
  width: 100%;
  overflow-y: auto;
  padding: $space-50 $space-20;

  @include desktop {
    padding: $space-25 0;
    max-width: 700px;
  }
}

.no-messages {
  margin-top: 70px;
  text-align: center;
  line-height: 1.9;
  width: 100%;
  max-width: 500px;
  padding: 0 $space-20;
  height: 100%;

  @include desktop {
    height: fit-content;
  }
}

.conversation-wrapper {
  display: flex;
  flex-flow: column nowrap;
  height: 100%;
  width: 100%;
  position: relative;
  overflow: auto;
  align-items: center;
}

.image-preview {
  width: 100%;
  height: auto;
  max-width: 800px;
  max-height: 800px;
}

.unread-separator {
  position: relative;
  margin: $space-10 0;
  padding-left: 7px;

  &::before {
    content: ' ';
    position: absolute;
    height: 1px;
    width: 100%;
    left: 0;
    top: 50%;
    background: $red-60;
  }

  .text-wrap {
    display: inline-block;
    background: $white;
    position: relative;
    z-index: 2;
    padding: 0 $space-5;
  }

  .text {
    font-size: 12px;
    text-transform: uppercase;
    color: $red-60;
  }
}

.premium {
  padding: 0 $space-20;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-grow: 1;
}

.container {
  display: grid;
  overflow-x: hidden;
  width: 100%;
  height: 100%;
  grid-column-gap: $space-20;
  grid-template-columns: 1fr;

  @include desktop {
    grid-template-columns: 1fr 320px;
    padding: 0 $space-20;
  }

  @include xlarge {
    grid-template-columns: 1fr 380px;
  }
}

.container-right {
  position: absolute;
  background-color: $white;
  width: 100%;
  height: 100%;
  padding-top: $space-55;
  overflow-x: hidden;
  display: none;

  @include desktop {
    position: relative;
    padding-top: $space-40;
    overflow-x: visible;
    display: initial;
  }

  @include xlarge {
    padding-right: $space-60;
  }
}

.visible-mobile {
  display: initial;
}
