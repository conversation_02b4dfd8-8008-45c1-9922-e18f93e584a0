import UpgradeClient from '../upgrade/_client';
import { cookies, headers } from 'next/headers';
import { formatPlans } from '@utils/planSelectHelper';
import Api from '@services/api';
import { getResolvedUrl } from '@utils/apiHelper';

export const metadata = {
  title: 'Subscribe Now',
};

export default async function UpgradePreviewPage(req) {
  const searchParams = await req.searchParams;
  const { discount } = searchParams;
  const headerStore = await headers();
  const cookieStore = await cookies();
  const resolvedUrl = getResolvedUrl('/upgrade-preview', searchParams);

  const data = await Api.serverside(
    `/discounts/${discount}`,
    cookieStore,
    headerStore,
    resolvedUrl,
  );

  return (
    <UpgradeClient
      initialPlans={formatPlans(data?.prices)}
      initialDiscount={data?.discount || {}}
      isPreview
      referer={resolvedUrl}
      hidePlanSelectPromotions={true}
    />
  );
}
