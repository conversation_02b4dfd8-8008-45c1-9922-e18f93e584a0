'use client';
import { memo } from 'react';
import styles from './ProCard.module.scss';
import Image from 'next/image';
import Link from 'next/link';
import cn from 'classnames';
import { CastingCallIcon } from '@components';

const ProCard = ({ profile }) => {
  const {
    id,
    firstName,
    lastName,
    titlePhotoUrl,
    resume,
    categories,
    activeCastingCalls,
    expiredCastingCall,
    isActive,
  } = profile;

  return (
    <div className={styles.card}>
      <div className={styles.header}>Casting professional</div>
      <div className={styles.container}>
        <div className={cn(styles.section, styles['image-section'])}>
          <Image
            className={styles.image}
            src={titlePhotoUrl || '/assets/placeholders/casting-director.svg'}
            width={64}
            height={64}
            alt="icon"
          />
          <div className={styles.column}>
            <span className={styles.name}>
              {firstName} {lastName}
            </span>
            {isActive ? (
              <Link
                className={styles.link}
                href={`${process.env.publicUrl}/director/${id}`}
              >
                View profile
              </Link>
            ) : (
              <span className={styles.inactive}>Inactive</span>
            )}
          </div>
        </div>
        {resume && (
          <div className={styles.section}>
            <span className={styles.title}>About me</span>
            <p>{resume}</p>
          </div>
        )}
        {categories?.length > 0 && (
          <div className={styles.section}>
            <span className={styles.title}>Categories I cast</span>
            <div className={styles['category-container']}>
              {categories.map(({ id, title }) => (
                <div key={id} className={styles['category-item']}>
                  {title}
                </div>
              ))}
            </div>
          </div>
        )}
        {(activeCastingCalls?.length > 0 || expiredCastingCall) && (
          <div className={styles.section}>
            {activeCastingCalls.length > 0 && (
              <div className={styles.column}>
                <span className={styles.title}>My active casting calls</span>
                <div
                  className={cn(styles.column, {
                    [styles['bottom-spacing']]: !!expiredCastingCall,
                  })}
                >
                  {activeCastingCalls.map(({ id, title, category }) => (
                    <Link
                      className={styles['casting-call']}
                      key={id}
                      href={`${process.env.publicUrl}/castingcall/${id}`}
                    >
                      <CastingCallIcon
                        title={title}
                        slug={category.name.toLowerCase().replaceAll(' ', '')}
                      />
                      <span>{title}</span>
                    </Link>
                  ))}
                </div>
              </div>
            )}
            {expiredCastingCall && (
              <div className={styles.column}>
                <span className={styles.title}>My expired casting calls</span>
                <Link
                  className={styles['casting-call']}
                  href={`${process.env.publicUrl}/castingcall/${expiredCastingCall.id}`}
                >
                  <CastingCallIcon
                    title={expiredCastingCall.title}
                    slug={expiredCastingCall.category.name
                      .toLowerCase()
                      .replaceAll(' ', '')}
                  />
                  <span>{expiredCastingCall.title}</span>
                </Link>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default memo(ProCard);
