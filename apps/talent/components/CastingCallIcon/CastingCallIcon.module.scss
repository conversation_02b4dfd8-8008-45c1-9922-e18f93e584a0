@use '@styles/mixins' as *;
@use '@styles/variables' as *;

.icon {
  background-image: linear-gradient(to top, #f45f85 0%, #ea8916 100%);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 40px;

  &.icon-acting {
    background-image: linear-gradient(135deg, #ea8916 0%, #f45f85 100%);
  }

  &.icon-musicvideos {
    background-image: linear-gradient(135deg, #e8198b 0%, #6d0986 100%);
  }

  &.icon-dancing {
    background-image: linear-gradient(135deg, #b224ef 0%, #7579ff 100%);
  }

  &.icon-modeling {
    background-image: linear-gradient(135deg, #cc165f 0%, #6d0b32 100%);
  }

  &.icon-promotionalmodels {
    background-image: linear-gradient(135deg, #1ee3d7 0%, #5fd568 100%);
  }

  &.icon-realitytv {
    background-image: linear-gradient(135deg, #eb7b4c 0%, #9e8df8 100%);
  }

  &.icon-singing {
    background-image: linear-gradient(135deg, #2575fc 0%, #6a11cb 100%);
  }

  &.icon-theater {
    background-image: linear-gradient(135deg, #fb8eaf 0%, #f30707 100%);
  }

  &.icon-voice-over {
    background-image: linear-gradient(135deg, #3ddafe 0%, #00cfff 100%);
  }

  &.icon-pageant {
    background-image: linear-gradient(135deg, #df4aa4 0%, #b54ce7 100%);
  }

  &.icon-other {
    background-image: linear-gradient(135deg, #86a1ff 0%, #4d6fc8 100%);
  }

  &.icon-extras {
    background-image: linear-gradient(135deg, #4689ee 0%, #4ff4cd 100%);
  }

  &.icon-commercials {
    background-image: linear-gradient(135deg, #f7d14e 0%, #ef7734 100%);
  }

  &.icon-contentcreators {
    background-image: linear-gradient(135deg, #3be4da 0%, #f764c5 100%);
  }

  &.icon-influencers {
    background-image: linear-gradient(135deg, #eb7b4c 0%, #ff8ded 100%);
  }
}
