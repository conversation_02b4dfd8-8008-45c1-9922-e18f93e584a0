'use client';
import React, { useEffect, useState } from 'react';
import { Button, InputFormik, Loading, MainLayout, Modal } from '@components';
import styles from '@styles/missing-info.module.scss';
import ImageLogo from '../../public/assets/logo/logo-violet.svg';
import { Form, Formik } from 'formik';
import { useAuth } from '@contexts/AuthContext';
import { useNotifications } from '@contexts/NotificationContext';
import { useRouter } from 'next/navigation';
import * as Yup from 'yup';
import { EMAIL_REGEX, ErrorMessage, NAME_REGEX } from '@constants/form';
import Api from '@services/api';
import { Amp } from '@services/amp';

const MissingInfoClient = ({ profile, isEmailValid }) => {
  const [isLoading, setLoading] = useState(false);

  const { profileId, accountId, refreshUserProfiles } = useAuth();
  const { setNotification } = useNotifications();
  const router = useRouter();

  const initialValues = {
    firstName: profile.firstname || '',
    lastName: profile.lastname || '',
    email: isEmailValid ? profile.links.account.email : '',
  };

  const validationSchema = Yup.object({
    firstName: Yup.string()
      .required(ErrorMessage.FirstNameRequired)
      .max(35, ErrorMessage.NamePattern)
      .matches(NAME_REGEX, ErrorMessage.NamePattern),
    lastName: Yup.string()
      .required(ErrorMessage.LastNameRequired)
      .max(35, ErrorMessage.NamePattern)
      .matches(NAME_REGEX, ErrorMessage.NamePattern),
    email: Yup.string()
      .required(ErrorMessage.EmailPattern)
      .matches(EMAIL_REGEX, ErrorMessage.EmailPattern),
  });

  const handleSubmit = async (values) => {
    setLoading(true);
    const profileBody = new FormData();
    const accountBody = new FormData();

    profileBody.append('lastname', values.lastName);
    profileBody.append('firstname', values.firstName);

    accountBody.append('email', values.email);

    const profileResponse = await Api.clientside(`/profiles/${profileId}`, {
      body: profileBody,
      method: 'PATCH',
    });

    const accountResponse = await Api.clientside(
      `/accounts/${accountId}/touches`,
      {
        body: accountBody,
        method: 'PUT',
      },
    );

    if (profileResponse.status !== 'ok' || accountResponse.status !== 'ok') {
      const errorMessage =
        profileResponse.message ||
        accountResponse.message ||
        ErrorMessage.Unexpected;

      setNotification({
        type: 'error',
        timeout: '5000',
        message: errorMessage,
      });
    } else {
      await refreshUserProfiles();
      router.push(`/welcome`);
    }

    setLoading(false);
  };

  useEffect(() => {
    Amp.track(Amp.events.viewMissingInfo);
  }, []);

  return (
    <MainLayout>
      <div className={styles.logo}>
        <ImageLogo />
      </div>
      <Modal
        classNameOverlay={styles.overlay}
        classNameContainer={styles.container}
        classNameContent={styles.content}
      >
        <div className={styles['form-header']}>
          <span className={styles['form-header-title']}>
            Fill in missing fields
          </span>
          <p>Please fill in the remaining details to continue</p>
        </div>
        <Formik
          initialValues={initialValues}
          onSubmit={handleSubmit}
          validationSchema={validationSchema}
        >
          {({ isValid }) => (
            <Form className={styles.form}>
              {!initialValues.firstName && (
                <InputFormik
                  name="firstName"
                  placeholder="First Name"
                  required
                />
              )}
              {!initialValues.lastName && (
                <InputFormik name="lastName" placeholder="Last Name" required />
              )}
              {!isEmailValid && (
                <InputFormik name="email" placeholder="Email" required />
              )}
              <div className={styles['form-footer']}>
                <div>
                  {isLoading ? (
                    <Loading minHeight="60px" />
                  ) : (
                    <Button
                      type="submit"
                      label="Continue"
                      className={styles.button}
                      minWidth={'220px'}
                      shadow={false}
                      disabled={!isValid}
                    />
                  )}
                </div>
              </div>
            </Form>
          )}
        </Formik>
      </Modal>
    </MainLayout>
  );
};

export default MissingInfoClient;
