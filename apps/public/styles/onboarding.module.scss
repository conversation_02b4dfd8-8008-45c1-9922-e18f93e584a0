@import '../styles/variables';
@import '../styles/mixins';

.page-wrapper {
  background: $violet-100 url('#{$assetUrl}/assets/onboarding/bg-ellipse.webp')
    bottom -10% center no-repeat;
  background-size: 300% auto;
  color: $white;

  @include tablet {
    background-size: 100% auto;
  }

  img:not([src$='.svg']) {
    height: auto;
  }
}

.container {
  margin: auto;
  max-width: 1240px;
  padding: 0 $space-20;
  position: relative;

  @include tablet {
    padding: 0 $space-60;
  }

  p {
    line-height: 1.8;
    color: $white;
    opacity: 0.7;
  }

  h1,
  h2 {
    font-weight: 700;
    margin: 0;
    color: $white;
  }

  h2 {
    font-size: 24px;
    margin-bottom: $space-5;
    padding: 0 $space-20;

    @include tablet {
      font-size: 40px;
      margin-bottom: $space-35;
      padding: 0;
    }
  }

  img {
    width: 100%;
    display: block;
  }

  form {
    img {
      width: 20px;
    }
  }
}

.ellipse {
  position: absolute;
  top: 0;
  right: 0;
  height: auto;
}

.hero-block {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  position: relative;
  padding: 10vh 0;

  @include tablet {
    min-height: calc(100vh - 70px);
    flex-direction: row;
    justify-content: center;
    gap: 75px;
    padding: 0;
  }

  h1 {
    font-size: 40px;
    margin-bottom: $space-40;
    line-height: 1.2;

    @include xlarge {
      font-size: 64px;
      margin-bottom: 67px;
    }
  }

  p {
    line-height: 1.6;
  }

  .block-image {
    max-height: 30vh;
    margin-top: $space-15;

    @include tablet {
      flex-shrink: 0;
      flex-basis: 49%;
      max-height: 50%;
      margin-top: 0;
    }

    img {
      max-height: 30vh;
      width: auto;

      @include tablet {
        width: 100%;
        max-height: none;
      }
    }
  }
}

.phone-block-container {
  position: relative;
}

.phone-block {
  position: absolute;
  inset: 0;
  width: 100vw;
  max-width: 1240px;
  min-height: 100vh;
  left: 50%;
  transform: translateX(-50%);
  display: none;

  @include tablet {
    display: block;
  }

  &.fixed {
    position: fixed;
    top: 0;
  }

  .phone-block-content {
    width: 100%;
    height: 100vh;
    min-height: 812px;
  }

  .phone-image {
    position: absolute;
    left: 50%;
    top: 10%;
    transform: translateX(-50%);
    width: 40%;

    @include tablet {
      left: 15%;
      top: 50%;
      transform: translateY(-50%);
      width: 27%;
    }
  }

  .eclipse-image {
    position: absolute;
    left: 5%;
    top: 50%;
    transform: translateY(-50%);
    width: 48%;
  }

  .profile-picture {
    position: absolute;
    left: 34%;
    top: 17vh;
    z-index: 2;
    width: 32%;

    @include tablet {
      width: 22%;
      left: 17.5%;
    }

    img {
      height: auto;
    }
  }

  .phone-content {
    position: absolute;
    bottom: 23vh;
    height: 59vh;
    width: 100%;
    overflow: hidden;
    z-index: 3;

    .photo-ux-image {
      position: absolute;
      bottom: 2vh;
      left: 35%;
      width: 30%;

      @include tablet {
        left: 17.5%;
        width: 22%;
      }
    }

    .profile-ux-image {
      position: absolute;
      bottom: 0;
      left: 25%;
      width: 50%;
      transform: translateY(150%);

      @include tablet {
        left: 17%;
        width: 23%;
      }
    }

    .apply-card-image {
      position: absolute;
      top: 7%;
      left: 27%;
      width: 46%;
      transform: translateY(500%);
      z-index: 2;
      box-shadow: 0 4px 50px 0 rgba(0, 0, 0, 25%);
      border-radius: 30px;

      @include tablet {
        top: 9%;
        left: 17.5%;
        width: 22%;
      }
    }

    .messages-image {
      position: absolute;
      bottom: 0;
      left: 16%;
      width: 68%;
      transform: translateY(500%);

      @include tablet {
        left: 12%;
        width: 33%;
      }
    }

    .apply-message-image {
      position: absolute;
      bottom: 33%;
      left: 35%;
      width: 17%;
      box-shadow: 0 4px 50px 0 rgba(0, 0, 0, 25%);
      border-radius: 15px 15px 15px 0;
      display: none;

      @include tablet {
        display: block;
      }
    }

    .check-mark-image {
      position: absolute;
      bottom: 35%;
      left: 45%;
      width: 5%;
      opacity: 0;

      @include tablet {
        bottom: 33%;
        left: 26%;
        width: 3%;
      }
    }
  }

  &.apply-phone {
    .phone-content {
      bottom: 18vh;
      height: 68vh;
    }
  }

  .views-image {
    position: absolute;
    z-index: 3;
    display: none;
    opacity: 0;

    @include tablet {
      left: 34%;
      width: 19%;
      display: block;
    }
  }

  .attributes-image-1 {
    display: none;
    position: absolute;
    z-index: 3;
    opacity: 0;

    @include tablet {
      width: 16%;
      left: 5%;
      top: 45%;
      display: block;
    }
  }

  .attributes-image-2 {
    display: none;
    position: absolute;
    box-shadow: 0 4px 50px 0 rgba(0, 0, 0, 25%);
    border-radius: 20%;
    z-index: 3;
    opacity: 0;

    @include tablet {
      width: 16%;
      left: 5%;
      top: 50%;
      display: block;
    }
  }

  .attributes-image-3 {
    display: none;
    position: absolute;
    box-shadow: 0 4px 50px 0 rgba(0, 0, 0, 25%);
    border-radius: 20%;
    z-index: 3;
    opacity: 0;

    @include tablet {
      width: 18%;
      left: 35%;
      top: 59%;
      display: block;
    }
  }

  .card-acting {
    position: absolute;
    width: 46%;
    left: 27%;
    top: 21vh;
    box-shadow: 0 4px 50px 0 rgba(0, 0, 0, 25%);
    border-radius: 20px;
    z-index: 2;

    @include tablet {
      width: 19%;
      left: 19%;
    }

    img {
      display: block;
    }
  }
}

.description-block {
  display: flex;
  flex-direction: column;
  justify-content: stretch;
  align-items: stretch;
  text-align: center;
  margin-bottom: $space-40;
  gap: $space-20;

  @include tablet {
    flex-direction: row;
    text-align: left;
    height: calc(100vh - 70px);
    margin-bottom: 0;
    gap: 7%;
  }

  .phone-image-mobile {
    position: relative;
    left: 50%;
    top: 10%;
    transform: translateX(-50%);
    width: 40%;

    &.large {
      width: 60%;
    }

    @include tablet {
      display: none;
    }
  }

  & > div {
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;

    &.description-images {
      order: 2;

      @include tablet {
        order: 0;
      }
    }

    @include tablet {
      flex-basis: 100%;
    }

    &:nth-child(2) {
      flex-basis: 40%;
      flex-shrink: 0;
    }
  }

  &.upload-description {
    .fullheight-image {
      position: absolute;
      top: 25vh;
      left: -4%;
      width: 20%;
      display: none;

      @include tablet {
        left: 0;
        display: block;
      }
    }

    .sideview-image {
      position: absolute;
      top: 40%;
      width: 20%;
      left: 84%;
      display: none;

      @include tablet {
        left: 80%;
        display: block;
      }
    }

    .camera-image {
      position: absolute;
      top: 80%;
      left: 9%;
      width: 8%;
      display: none;

      @include tablet {
        top: 55%;
        display: block;
      }
    }
  }

  &.apply-description {
    @include tablet {
      height: 200vh;
    }

    .filter-image {
      position: absolute;
      top: 80%;
      width: 26%;
      left: 70%;
      box-shadow: 0 4px 50px 0 rgba(0, 0, 0, 25%);
      border-radius: 15px;
      display: none;

      @include tablet {
        top: 30%;
        display: block;
      }
    }

    .text-block {
      @include tablet {
        height: 100vh;
        min-height: 812px;
      }
    }
  }
}

.block-title {
  color: #9272ea;
  display: none;
  align-items: center;
  gap: $space-15;
  margin-bottom: $space-15;

  @include tablet {
    display: flex;
  }
}

.explore-block {
  padding: 15vh 0;

  @include tablet {
    padding-top: 15vh;
  }

  .explore-block-content {
    position: sticky;
    top: 100px;
    display: flex;
    align-items: center;
    flex-direction: column;

    h2 {
      font-size: 40px;
      max-width: 600px;
      line-height: 1.1;
      margin-bottom: $space-40;
      text-align: center;

      @include xlarge {
        font-size: 64px;
        line-height: 1.2;
        margin-bottom: $space-25;
      }
    }

    & > :not(.map-block) {
      z-index: 2;
    }

    .map-block {
      width: 100%;
      text-align: center;
      position: relative;
      overflow: hidden;

      @include tablet {
        width: calc(100% + 120px);
      }

      .map-image {
        width: 150%;

        @include tablet {
          width: 100%;
        }

        circle {
          opacity: 0;
        }
      }

      .pin {
        position: absolute;
        width: 12%;

        @include tablet {
          transform: scale(0);
        }

        .pin-desktop {
          display: none;

          @include tablet {
            display: block;
          }
        }

        .pin-mobile {
          @include tablet {
            display: none;
          }
        }

        @include tablet {
          width: 14%;
        }

        svg {
          width: 100%;
        }
      }

      .card {
        display: none;
        position: absolute;
        width: 19%;
        transform: scale(0);

        @include tablet {
          display: block;
        }
      }

      .pin-modeling {
        top: 40%;
        left: 20%;
      }

      .card-modeling {
        top: 35%;
        left: 15%;
      }

      .pin-acting {
        top: 30%;
        left: 40%;
      }

      .pin-promo {
        top: 45%;
        left: 60%;
      }

      .card-promo {
        top: 40%;
        left: 55%;
      }

      .pin-extras {
        top: 40%;
        left: 80%;
      }

      .card-extras {
        top: 35%;
        left: 75%;
      }
    }
  }
}

.reward-block {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;

  @include tablet {
    height: 100vh;
    min-height: 812px;
    flex-direction: row;
  }

  .background {
    z-index: 2;
    order: 2;

    @include tablet {
      position: absolute;
      inset: 0;
      width: 55%;
      left: 45%;
      background: url('#{$assetUrl}/assets/onboarding/reward-girl.webp') center
        no-repeat;
      background-size: contain;
      height: 100%;
      order: 0;

      img {
        display: none;
      }
    }
  }

  .confetti-blurred {
    position: absolute;
    inset: 0;
    background: url('#{$assetUrl}/assets/onboarding/confetti-blurred.webp')
      center no-repeat;
    background-size: contain;
    display: none;

    @include tablet {
      display: block;
    }
  }

  .confetti-violet {
    position: absolute;
    inset: 0;
    background: url('#{$assetUrl}/assets/onboarding/confetti-violet.webp')
      center no-repeat;
    background-size: contain;
    display: none;

    @include tablet {
      display: block;
    }
  }

  .confetti-yellow {
    position: absolute;
    inset: 0;
    background: url('#{$assetUrl}/assets/onboarding/confetti-yellow.webp')
      center no-repeat;
    background-size: contain;
    display: none;

    @include tablet {
      display: block;
    }
  }

  .text-content {
    text-align: center;
    z-index: 2;

    @include tablet {
      width: 40%;
      margin-left: 8%;
      text-align: left;
    }

    h2 {
      font-size: 40px;
    }
  }
}

.registration-form,
.classroom-block,
.subscription-block {
  padding: 70px 0 100px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;

  p {
    max-width: 600px;
    margin-bottom: 80px;
  }
}

.registration-form {
  .form {
    max-width: 570px;
    background: $violet-80;
    border-radius: 20px;
    padding: $space-60 $space-10 $space-65;
    border: 1px solid $violet-70;
  }

  .redirecting {
    padding-left: $space-20;
    padding-right: $space-20;
  }
}

.subscription {
  p {
    margin-bottom: 0;
  }

  button {
    margin-top: $space-30;

    @include tablet {
      margin-top: $space-25;
    }
  }
}
