// Next.js API route support: https://nextjs.org/docs/api-routes/introduction

import Api from '../../../services/api';
import {
  CastingCallEmailFullMatch,
  CastingCallMultipleRoles,
  CastingCallOneRole,
  CastingCallOneRoleAttended,
  CastingCallPhoneFullMatch,
  CastingCallWebFullMatch,
} from '../../../mocks';

const getMockData = (castingCallId) => {
  switch (castingCallId) {
    case '000':
      return CastingCallOneRole;
    case '001':
      return CastingCallWebFullMatch;
    case '002':
      return CastingCallEmailFullMatch;
    case '003':
      return CastingCallPhoneFullMatch;
    case '004':
      return CastingCallMultipleRoles;
    case '005':
      return CastingCallOneRoleAttended;
    default:
      return null;
  }
};

export default async function handler(req, res) {
  let data = getMockData(req.query.slug);

  if (!data) {
    if (req.query.isTalent) {
      data = await Api.serverGateway(
        `/calls/get?id=${req.query.slug}`,
        req.headers,
      );
    } else {
      const parts = req.query.slug.split('-');
      const id = parts[parts.length - 1];

      data = await Api.serverGateway(
        `/calls/get?id=${id}`,
        req.headers,
        Api.TTL_TYPE_LONG,
      );
    }
  }

  res.status(200).json(data);
}
