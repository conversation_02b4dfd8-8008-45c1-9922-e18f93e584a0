import Checkout12Client from './_client';
import { cookies, headers } from 'next/headers';
import Api from '@services/api';
import { CookieServiceServer } from '@services/cookieServiceServer';
import { getResolvedUrl } from '@utils/apiHelper';
import { formatBillingInfo } from '@utils/formatBillingInfo';
import { getMonthOptions, getYearOptions } from '@utils/getTimeOptions';
import { extractResult } from '@utils/extractPromiseResult';

export const metadata = {
  title: 'Subscribe Now',
};

export default async function Checkout12Page(req) {
  const searchParams = await req.searchParams;
  const resolvedUrl = getResolvedUrl('/checkout-12', searchParams);
  const cookieStore = await cookies();
  const headerStore = await headers();

  const accountId = CookieServiceServer.getAccountCookie(cookieStore);

  const paths = [
    `/accounts/${accountId}/prices`,
    '/payment/billing',
    `/accounts/${accountId}/touches`,
    '/clientstatus/info',
  ];

  const results = await Promise.allSettled(
    paths.map((path) =>
      Api.serverside(path, cookieStore, headerStore, resolvedUrl),
    ),
  );

  const [upgradePlans, billing, touches, clientStatus] = results.map((result) =>
    extractResult(result, {}),
  );

  const pricePlan = upgradePlans.prices[0];
  const stripePK = upgradePlans.stripe_pk;
  const { id: discount_id, ab_experiment } = upgradePlans.discount || {};
  const formattedTracking = {};
  // If no tracking, response is an empty array.
  // If it has tracking, response is an object
  const tracking = clientStatus.info?.tracking || {};
  const trackingKeys = Object.keys(tracking);

  if (trackingKeys?.length) {
    trackingKeys.forEach((key) => {
      formattedTracking[`tracking.${key}`] = tracking[key];
    });
  }

  return (
    <Checkout12Client
      pricePlan={pricePlan || null}
      stripePK={stripePK}
      billing={formatBillingInfo(billing)}
      monthOptions={getMonthOptions()}
      yearOptions={getYearOptions()}
      email={touches.links?.email?.value || ''}
      campaign={clientStatus.info?.campaign || ''}
      campaignGroup={clientStatus.info?.campaign_group || ''}
      tracking={formattedTracking}
      discountId={discount_id}
      discountAbExperiment={ab_experiment}
    />
  );
}
