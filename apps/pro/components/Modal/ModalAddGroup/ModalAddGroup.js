'use client';
import React, { memo, useState } from 'react';
import { Input, Loading, Modal } from '@components';
import styles from './ModalAddGroup.module.scss';
import { useAuth } from '@contexts/AuthContext';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { useNotifications } from '@contexts/NotificationContext';
import Api from '@services/api';
import { useContacts } from '@contexts/ContactContext';
import { ErrorMessage } from '@constants/form';

const ModalAddGroup = ({
  onClose,
  onAddContactToGroup,
  addContactEnabled = false,
}) => {
  const [loading, setLoading] = useState(false);
  const { setNotification } = useNotifications();
  const { profileId } = useAuth();
  const { addGroup } = useContacts();

  const formik = useFormik({
    initialValues: {
      title: '',
    },
    onSubmit: async (values) => {
      setLoading(true);

      const body = new FormData();

      body.append('title', values.title);

      const { status, id, title, message } = await Api.clientside(
        `/profiles/${profileId}/contact-groups`,
        {
          method: 'POST',
          body,
        },
      );

      if (status !== 'ok') {
        setNotification({
          type: 'error',
          timeout: '5000',
          message: message || ErrorMessage.Unexpected,
          showOnModal: true,
        });
      } else {
        const newGroup = { id, title, total: 0 };

        if (addContactEnabled) {
          onAddContactToGroup(newGroup.id, newGroup);
        } else {
          addGroup({ id, title, total: 0 });
          setNotification({
            type: 'success',
            timeout: '5000',
            message: 'Group added',
          });
        }
      }

      onClose();
      setLoading(false);
    },
    validationSchema: Yup.object({
      title: Yup.string().required(ErrorMessage.FieldRequired),
    }),
  });

  return (
    <Modal
      backdropClose
      onClose={onClose}
      classNameContainer={styles['modal-container']}
      showDefaultLayout={false}
      floatCloseButton
      hideMobileCloseButton
    >
      {loading ? (
        <div className={styles['loading-container']}>
          <div>Adding group...</div>
          <Loading minHeight="40px" padding="0" />
        </div>
      ) : (
        <>
          <div className={styles.container}>
            <span className={styles.title}>Create group</span>
            <Input
              name="title"
              placeholder="Group title"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.title}
              isTouched={formik.touched.title}
              error={formik.errors.title}
            />
          </div>
          <div className={styles.actions}>
            <button onClick={onClose} className={styles.action}>
              Cancel
            </button>
            <button onClick={formik.submitForm} className={styles.action}>
              Create group
            </button>
          </div>
        </>
      )}
    </Modal>
  );
};

export default memo(ModalAddGroup);
