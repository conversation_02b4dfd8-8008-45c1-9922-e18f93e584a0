import https from 'https';
import http from 'http';

const check = async (experimentName, identifier) => {
  const url = `${process.env.abApiUrl}/experiments/${experimentName}/identifiers/${identifier}`;

  try {
    const response = await fetchWithTimeout(url);

    return await response.json();
  } catch (error) {
    console.error(error);

    return {};
  }
};

const checkClientSide = async (experimentName, identifier) => {
  const url = `${process.env.externalApiUrl}/ab/identifier?experimentName=${experimentName}&identifier=${identifier}`;
  const response = await fetch(url);

  if (response?.status !== 200) {
    return null;
  }

  return await response.json();
};

async function fetchWithTimeout(resource, options = {}) {
  const { timeout = 3000 } = options;

  let agent = new http.Agent();

  if (process.env.abApiUrl.includes(`https`)) {
    agent = new https.Agent({ rejectUnauthorized: false });
  }

  const controller = new AbortController();
  const id = setTimeout(() => controller.abort(), timeout);
  const response = await fetch(resource, {
    ...options,
    signal: controller.signal,
    agent: agent,
  });

  clearTimeout(id);

  return response;
}

export default { check, checkClientSide };
