export const FeatureManager = {
  cacheTimeInSeconds: 60,
  featureKeys: {
    premiumSupport: 'premium_support',
    premiumInstagramBoost: 'promote_instagram_story',
    paymentMaintenance: 'payment_maintenance',
    promoCode: 'promo_code',
  },

  async enabled(key, headers = {}) {
    const response = await fetch(
      `${process.env.baseUrl}/api/features?key=${key}`,
      {
        headers,
        method: 'GET',
      },
    );

    return await response.json();
  },

  async refresh(headers = {}) {
    const response = await fetch(`${process.env.baseUrl}/api/features`, {
      headers,
      method: 'GET',
    });

    return await response.json();
  },
};
