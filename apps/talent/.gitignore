# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.idea

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env*.local

# Sentry Config File
.env.sentry-build-plugin

# types while developing
tsconfig.json
next-env.d.ts

# .vscode settings / debugging config
.vscode/launch.json
