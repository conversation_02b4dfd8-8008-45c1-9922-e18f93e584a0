@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.content-protection-form {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-weight: 400;
  gap: 20px;

  @include tablet {
    min-height: unset;
  }
}

.content-protection-form-title {
  font-size: 30px;
  font-weight: 700;
  text-align: center;
  padding: 0 $space-20;
}

.password-input-container {
  max-width: 335px;
  display: flex;
  gap: 20px;
  flex-direction: column;
  width: 100%;
  padding: 0 $space-20;

  @include tablet {
    padding: 0;
  }
}

.content-protection-form-header {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.link {
  display: block;
  color: $blue-100;
  font-size: 16px;
  font-weight: 400;
  margin-bottom: $space-20;
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
}
