@use '@styles/variables' as *;
@use '@styles/mixins' as *;
@use '@styles/keyframes' as *;

@keyframes errorSlideLeft {
  from {
    opacity: 0;
    transform: translateX(-15%);
  }

  to {
    opacity: 1;
    transform: none;
  }
}

@keyframes errorSlideRight {
  from {
    opacity: 0;
    transform: translateX(15%);
  }

  to {
    opacity: 1;
    transform: none;
  }
}

.box {
  position: relative;
  width: 100%;
  max-width: 335px;
  justify-self: center;
  background-color: $white;
  box-shadow: 0 0.57px 40px 0 #1e294514;
  border-radius: 10px;
  padding: 1.5rem;
  text-align: center;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  display: flex;
  animation: errorSlide 0.4s ease-in-out;

  &.left {
    animation-name: errorSlideLeft;
  }

  &.right {
    animation-name: errorSlideRight;
  }

  @include desktop {
    position: absolute;
    z-index: 1;
    max-width: 260px;
  }
}

.button {
  position: absolute;
  top: 0;
  right: 0;
  width: 2.25rem;
  height: 2.25rem;
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close {
  width: 0.75rem;
  height: auto;
  color: $grey-60;
}

.title {
  font-weight: 600;
}

.text {
  color: $grey-80;
  font-size: 0.875rem;
  font-weight: 400;
}

.triangle {
  display: none;
  position: absolute;
  left: 100%;
  height: 25%;
  width: auto;
  color: $white;
}

.left .triangle {
  right: 100%;
  left: unset;
  transform: rotate(180deg);
}

.left,
.right {
  .triangle {
    @include desktop {
      display: initial;
    }
  }
}
