@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.mobile-menu-container {
  display: block;
  bottom: 0;
  position: sticky;
  top: auto;
  align-self: auto;
  z-index: 3;

  @include desktop {
    display: none;
    align-self: flex-start;
    top: 0;
  }

  @include tablet {
    top: auto;
    align-self: auto;
  }
}

.mobile-menu {
  min-width: 0;
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-evenly;
  height: $mobile-menu-height;
  background: $white;
  border: 0 solid $black-100-opacity-30;
  border-top-width: 1px;
  padding-inline-start: 0;
  margin-block: 0;

  @supports not (justify-content: space-evenly) {
    justify-content: space-between;
  }

  @include tablet {
    align-items: center;
    border-top-width: 1px;
    border-left-width: 0;
    min-height: auto;
    width: auto;
    flex-direction: row;
    justify-content: space-evenly;
    padding-bottom: 0;

    @supports not (justify-content: space-evenly) {
      justify-content: space-between;
    }
  }
}

.mobile-menu-item {
  display: flex;
  align-items: center;
  text-align: center;
  line-height: 1.3;
  color: $black;
  font-size: 12px;
  text-transform: lowercase;
  padding: $space-10 0;
  flex-direction: column;

  @include tablet {
    display: inline-block;
    white-space: nowrap;
    flex-basis: auto;
    position: relative;
  }

  & .mobile-menu-icon-container {
    position: relative;
  }
}

.mobile-menu-link {
  display: flex;
  flex-direction: column;
  color: $black;
  row-gap: 2px;
  align-items: center;
  position: relative;

  @include tablet {
    gap: $space-5;
    flex-direction: row;
  }

  &:hover,
  &:active {
    text-decoration: none;
  }
}

.badge {
  position: absolute;
  top: 0;
  right: 0;
}

.mobile-menu-icon {
  display: inline-block;
  position: relative;
  vertical-align: middle;
  opacity: 0.5;
  cursor: pointer;

  @include tablet {
    position: static;
  }
}

.mobile-menu-txt {
  display: block;
  white-space: nowrap;
  opacity: 0.7;
  position: relative;
  font-weight: 300;
  margin: 0;
  font-size: 14px;
  cursor: pointer;

  @include mobile {
    font-size: 16px;
  }

  @include tablet {
    display: flex;
    align-items: center;
  }
}

.active {
  --active-filter: invert(46%) sepia(94%) saturate(1201%) hue-rotate(168deg)
    brightness(112%) contrast(102%);

  .mobile-menu-icon {
    filter: var(--active-filter);
  }

  .mobile-menu-txt {
    filter: var(--active-filter);
  }
}

.main-mobile-menu-item {
  position: relative;
  top: -28px;
  padding: 0;

  @include tablet {
    top: -30px;
  }

  .mobile-menu-link {
    flex-direction: column;
    gap: 2px;
  }

  .mobile-menu-icon-container {
    background-color: $violet-80;
    padding: $space-15;
    border-radius: 50%;
    position: relative;
    border: 8px solid $white;
    margin-bottom: -8px;

    &::before {
      content: '';
      position: absolute;
      display: block;
      background-color: $black-100-opacity-30;
      width: 68px;
      height: 68px;
      top: -9px;
      left: -9px;
      border-radius: 50%;
      z-index: -1;
    }

    .mobile-menu-icon {
      opacity: 1;
    }
  }
}
