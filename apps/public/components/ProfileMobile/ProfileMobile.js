import React, { memo, useState } from 'react';
import styles from './ProfileMobile.module.scss';
import Image from 'next/image';
import {
  Audio,
  Breadcrumbs,
  Button,
  CarouselOpacity,
  CastingCall,
  Loading,
  ModalContactTalent,
  ProfileCreditCard,
  ProfileOverview,
  YoutubePlayer,
} from '../index';
import { useRouter } from 'next/router';
import { useProfileContext } from '../../contexts/ProfileContext';
import Api from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';
import Link from 'next/link';
import Carousel from '../Carousel/Carousel';
import cn from 'classnames';
import { TYPE } from '../../constants/castingCalls';

const ProfileMobile = ({ isMobileFromUserAgent, menuItems }) => {
  const {
    id,
    name,
    images,
    videos,
    credits,
    rating,
    closeUpImage,
    isAgent,
    castingCalls,
    castingCallTotal,
    clientId,
    audios,
    gender,
    directorImage,
  } = useProfileContext();

  const router = useRouter();
  const [showContactTalentPopup, setShowContactTalentPopup] = useState(false);
  const [visibleCastingCalls, setVisibleCastingCalls] = useState(castingCalls);
  const [visibleCastingCallsPage, setVisibleCastingCallsPage] = useState(1);
  const [visibleCastingCallsLoading, setVisibleCastingCallsLoading] =
    useState(false);
  const { userType, accountLevel } = useAuth();

  const handleNavigateBack = () => {
    router.push(isAgent ? '/castingcalls/' : '/talent/');
  };

  const loadMoreCastingCalls = async () => {
    setVisibleCastingCallsLoading(true);

    const profileCastingCalls = await Api.clientAPIRoute(
      `/profiles/casting-calls/${clientId}?limit=3&page=${
        visibleCastingCallsPage + 1
      }`,
    );

    setVisibleCastingCallsLoading(false);
    setVisibleCastingCallsPage(visibleCastingCallsPage + 1);
    setVisibleCastingCalls([
      ...visibleCastingCalls,
      ...profileCastingCalls.data?.calls,
    ]);
  };

  const renderTalentProfile = () => {
    return (
      <>
        <div className={styles['photo-container']}>
          {closeUpImage ? (
            <div>
              <img
                className={styles.image}
                src={closeUpImage.proxy_url}
                alt="image"
              />
            </div>
          ) : (
            <div className={styles['placeholder-image-container']}>
              <div
                className={cn(
                  styles['placeholder-image'],
                  styles[gender ? gender.toLowerCase() : 'male'],
                )}
              ></div>
            </div>
          )}

          <div className={styles['profile-contact']}>
            <span className={styles['profile-name']}>{name}</span>
            <div className={styles['profile-rating']}>
              <Image
                className={styles['star']}
                src={'/assets/icons/icon-star-2.svg'}
                width={17}
                height={17}
                alt="star icon"
              />
              <span className={styles['profile-rating-count']}>{rating}</span>
            </div>
            {!isAgent && userType !== 'talent' && (
              <Button
                kind={'secondary'}
                label={'Contact Talent'}
                onClick={() => {
                  setShowContactTalentPopup(true);
                }}
              />
            )}
          </div>
        </div>
        <div
          className={cn(styles['menu-container'], {
            [styles['is-upgrade']]:
              accountLevel &&
              (!accountLevel?.isPaidOrDelayed ||
                !!accountLevel?.canUpgradeExistingSubscription),
          })}
        >
          {menuItems.map(({ title, selector }) => (
            <Link
              key={title}
              href={selector}
              scroll={false}
              className={styles['menu-item']}
            >
              {title}
            </Link>
          ))}
        </div>
        <div id="details" className={styles['id-container']}>
          <span>Profile ID: </span>
          <span>{id}</span>
        </div>
        <div className={styles['profile-content']}>
          <div className={styles['profile-overview-container']}>
            <ProfileOverview
              fontSize={'16px'}
              contactTalentCallback={(event) => {
                event.preventDefault();
                event.stopPropagation();
                setShowContactTalentPopup(true);
              }}
            />
          </div>

          <div className={styles.media}>
            {(videos?.length > 0 || audios?.length > 0) && (
              <>
                <hr id="media" className={styles['separator']} />
                <h2 className={styles['profile-title']}>Assets</h2>
              </>
            )}
            {videos.length > 0 && (
              <div className={styles['profile-content-section']}>
                <h2 id="slate" className={styles['profile-subtitle']}>
                  Videos:
                </h2>
                <div style={{ padding: '0 20px' }}>
                  <CarouselOpacity arrowNavigationEnabled>
                    {videos.map(({ id, link_id }) => (
                      <YoutubePlayer
                        key={id}
                        videoId={link_id}
                        videoClassName={styles['video-mobile']}
                      />
                    ))}
                  </CarouselOpacity>
                </div>
              </div>
            )}

            {audios.length > 0 && (
              <div className={styles['profile-content-section']}>
                <h2 className={styles['profile-subtitle']}>Audio Files:</h2>
                {audios.length === 1 ? (
                  <div key={id} className={styles['audio']}>
                    <Audio
                      title={audios[0].title}
                      url={audios[0].full_path}
                      isMobileFromUserAgent={isMobileFromUserAgent}
                    />
                  </div>
                ) : (
                  <div style={{ margin: '0 20px' }}>
                    <Carousel
                      draggable={false}
                      enableArrowNavigation
                      slidesToScroll={1}
                      playOnInit={false}
                    >
                      {audios.map(({ id, title, full_path }) => (
                        <div style={{ margin: '0 50px' }} key={id}>
                          <Audio
                            title={title}
                            url={full_path}
                            isMobileFromUserAgent={isMobileFromUserAgent}
                          />
                        </div>
                      ))}
                    </Carousel>
                  </div>
                )}
              </div>
            )}

            {images.length > 0 && (
              <div className={styles['profile-content-section']}>
                <hr className={styles['separator']} />
                <h2 id="photos" className={styles['profile-title']}>
                  Photos
                </h2>
                <div style={{ padding: '0 20px' }}>
                  <CarouselOpacity>
                    {images.map(({ proxy_url }, index) => (
                      <img
                        className={styles['embla-slide-img']}
                        key={index}
                        src={proxy_url}
                        alt={index}
                      />
                    ))}
                  </CarouselOpacity>
                </div>
              </div>
            )}
          </div>

          {credits.length > 0 && (
            <div>
              <hr className={styles['separator']} />
              <h2 id="credits" className={styles['profile-title']}>
                Credits
              </h2>
              <div className={styles['profile-credits']}>
                {credits.map(
                  ({ id, title, description, month, year, company }) => (
                    <ProfileCreditCard
                      key={id}
                      title={title}
                      year={year}
                      company={company.title}
                      month={month}
                      description={description}
                    />
                  ),
                )}
              </div>
            </div>
          )}
        </div>
      </>
    );
  };

  const renderAgentProfile = () => {
    return (
      <>
        <div className={styles['photo-container']}>
          {directorImage ? (
            <div>
              <img
                className={styles.image}
                src={directorImage.proxy_url}
                alt="image"
              />
            </div>
          ) : (
            <div className={styles['placeholder-image-container']}>
              <div
                className={cn(styles['placeholder-image'], styles.director)}
              />
            </div>
          )}
          <div className={styles['profile-contact']}>
            <span className={styles['profile-name']}>{name}</span>
          </div>
        </div>
        <div
          className={cn(styles['menu-container'], styles['is-agent'], {
            [styles['is-upgrade']]:
              accountLevel &&
              (!accountLevel?.isPaidOrDelayed ||
                !!accountLevel?.canUpgradeExistingSubscription),
          })}
        >
          {menuItems.map(({ title, selector }) => (
            <Link
              key={title}
              href={selector}
              scroll={false}
              className={styles['menu-item']}
            >
              {title}
            </Link>
          ))}
        </div>
        <div className={styles['id-container']}>
          <span>Profile ID: </span>
          <span>{id}</span>
        </div>
        <div className={styles['profile-content']}>
          <div className={styles['profile-overview-container']}>
            <ProfileOverview fontSize={'16px'} />
          </div>

          {visibleCastingCalls?.length > 0 && (
            <>
              <hr className={styles['separator']} />
              <h2 id="casting-calls" className={styles['profile-title']}>
                Casting Calls
              </h2>
              <div className={styles['casting-calls']}>
                {visibleCastingCalls.map((item, index) => (
                  <CastingCall
                    key={index}
                    type={item.type}
                    mainCategory={item.category}
                    additionalCategories={item.additional_categories}
                    description={item.description.slice(0, 300)}
                    categoryName={item.category.name}
                    location={item.location}
                    title={item.title}
                    rolesCount={item.roles.length}
                    expires={item.expiration_date || item.expiration}
                    isOnline={item.online_audition}
                    isEasyToApply={item.type === TYPE.Web}
                    hot={item.hot}
                    id={item.id}
                    paymentAmount={item.payment_amount}
                    paymentPeriod={item.payment_period}
                    paymentCurrency={item.payment_currency}
                  />
                ))}
              </div>

              {visibleCastingCalls.length < castingCallTotal &&
                !visibleCastingCallsLoading && (
                  <div className={styles['profile-btn-container']}>
                    <Button
                      onClick={loadMoreCastingCalls}
                      label={'Load more'}
                      kind={'secondary'}
                      minWidth={'220px'}
                    />
                  </div>
                )}

              {visibleCastingCallsLoading && (
                <Loading minHeight="40px" padding="0" />
              )}
            </>
          )}

          <div id="media" className={styles.media}>
            {images.length > 0 && (
              <div className={styles['profile-content-section']}>
                <hr className={styles['separator']} />
                <h2 id="photos" className={styles['profile-title']}>
                  Photos
                </h2>
                <div style={{ padding: '0 20px' }}>
                  <CarouselOpacity>
                    {images.map(({ proxy_url }, index) => (
                      <img
                        className={styles['embla-slide-img']}
                        key={index}
                        src={proxy_url}
                        alt={index}
                      />
                    ))}
                  </CarouselOpacity>
                </div>
              </div>
            )}
          </div>
        </div>
      </>
    );
  };

  return (
    <section className={styles['profile-section-mobile']}>
      {isAgent ? (
        <div className={styles['profile-header']}>
          <div className={styles['back-icon-container']}>
            <Image
              className={styles['back-icon']}
              src={'/assets/icons/icon-angle-5.svg'}
              width={16}
              height={16}
              onClick={handleNavigateBack}
              alt=""
            />
          </div>
          <h1>{name}</h1>
        </div>
      ) : (
        <div className={styles['breadcrumbs']}>
          <Breadcrumbs
            crumbs={[
              { text: 'Talent Database', href: '/talent' },
              { text: name },
            ]}
          />
        </div>
      )}

      {isAgent ? renderAgentProfile() : renderTalentProfile()}
      {showContactTalentPopup && (
        <ModalContactTalent
          onClose={() => {
            setShowContactTalentPopup(false);
          }}
        />
      )}
    </section>
  );
};

export default memo(ProfileMobile);
