import * as Sentry from '@sentry/nextjs';
import { CookieService, options, strictOptions } from '@services/cookieService';

export const CookieServiceServer = {
  // Setters
  setAuthenticationCookie(data, cookies) {
    const isDecoded = data.includes('Account');

    if (isDecoded) {
      cookies.set({
        name: CookieService.cookie.authentication,
        value: btoa(JSON.stringify(data)),
        ...options(),
      });
    } else {
      cookies.set({
        name: CookieService.cookie.authentication,
        value: data,
        ...options(),
      });
    }
  },
  setVolatileCookie(data, cookies) {
    cookies.set({
      name: CookieService.cookie.volatile,
      value: data,
      ...options(),
    });
  },
  setExpiresCookie(data, cookies) {
    cookies.set({
      name: CookieService.cookie.expires,
      value: data,
      ...options(),
    });
  },
  setAccountCookie(data, cookies) {
    cookies.set({
      name: CookieService.cookie.account,
      value: data,
      ...options(),
    });
  },
  setUserTypeCookie(data, cookies) {
    cookies.set({
      name: CookieService.cookie.userType,
      value: data,
      ...options(),
    });
  },
  setProfileCookie(data, cookies) {
    cookies.set({
      name: CookieService.cookie.profile,
      value: data,
      ...options(),
    });
  },
  setAccountLevelCookie(data, cookies) {
    if (typeof data === 'string') {
      cookies.set({
        name: CookieService.cookie.accountLevel,
        value: data,
        ...options(),
      });
    } else {
      cookies.set({
        name: CookieService.cookie.accountLevel,
        value: JSON.stringify(data),
        ...options(),
      });
    }
  },
  setUserProfilesCookie(data, cookies) {
    if (typeof data === 'string') {
      cookies.set({
        name: CookieService.cookie.userProfiles,
        value: data,
        ...options(),
      });
    } else {
      cookies.set({
        name: CookieService.cookie.userProfiles,
        value: JSON.stringify(data),
        ...options(),
      });
    }
  },
  setExperimentsCookie(data, cookies) {
    cookies.set({
      name: CookieService.cookie.experiments,
      value: JSON.stringify(data),
      ...strictOptions(),
    });
  },
  // Getters
  getAuthenticationCookie(cookies) {
    const value = CookieService.exportString(
      cookies.get(CookieService.cookie.authentication)?.value,
    );

    if (!value) return '';

    const isDecoded = value.includes('Account');

    if (isDecoded) {
      return value.replaceAll('"', '');
    } else {
      try {
        return JSON.parse(atob(value)).replaceAll('"', '');
      } catch (e) {
        return '';
      }
    }
  },
  getVolatileCookie(cookies) {
    return CookieService.exportString(
      cookies.get(CookieService.cookie.volatile)?.value,
    );
  },
  getExpiresCookie(cookies) {
    return CookieService.exportInteger(
      cookies.get(CookieService.cookie.expires)?.value,
    );
  },
  getAccountCookie(cookies) {
    return CookieService.exportInteger(
      cookies.get(CookieService.cookie.account)?.value,
    );
  },
  getUserTypeCookie(cookies) {
    return CookieService.exportString(
      cookies.get(CookieService.cookie.userType)?.value,
    );
  },
  getProfileCookie(cookies) {
    return CookieService.exportInteger(
      cookies.get(CookieService.cookie.profile)?.value,
    );
  },
  getAccountLevelCookie(cookies) {
    const value = CookieService.exportString(
      cookies.get(CookieService.cookie.accountLevel)?.value,
    );

    try {
      return JSON.parse(value);
    } catch (e) {
      return null;
    }
  },
  getUserProfilesCookie(cookies) {
    const value = CookieService.exportString(
      cookies.get(CookieService.cookie.userProfiles)?.value,
    );

    try {
      return JSON.parse(value);
    } catch (e) {
      return null;
    }
  },
  getTrackingCookie(cookies) {
    const value = CookieService.exportString(
      cookies.get(CookieService.cookie.tracking)?.value,
    );

    try {
      return JSON.parse(atob(value));
    } catch (e) {
      return null;
    }
  },
  getExperimentsCookie(cookies) {
    const value = CookieService.exportString(
      cookies.get(CookieService.cookie.experiments)?.value,
    );

    try {
      return JSON.parse(value);
    } catch (e) {
      return null;
    }
  },
  getRedirectCookie(cookies) {
    return CookieService.exportString(
      cookies.get(CookieService.cookie.redirect)?.value,
    );
  },
  // Delete for middleware
  deleteAuthenticationCookie(response) {
    response.cookies.set({
      name: CookieService.cookie.authentication,
      value: '',
      ...options(new Date(0)),
    });
  },
  deleteVolatileCookie(response) {
    response.cookies.set({
      name: CookieService.cookie.volatile,
      value: '',
      ...options(new Date(0)),
    });
  },
  deleteExpiresCookie(response) {
    response.cookies.set({
      name: CookieService.cookie.expires,
      value: '',
      ...options(new Date(0)),
    });
  },
  deleteAccountCookie(response) {
    response.cookies.set({
      name: CookieService.cookie.account,
      value: '',
      ...options(new Date(0)),
    });
  },
  deleteUserTypeCookie(response) {
    response.cookies.set({
      name: CookieService.cookie.userType,
      value: '',
      ...options(new Date(0)),
    });
  },
  deleteProfileCookie(response) {
    response.cookies.set({
      name: CookieService.cookie.profile,
      value: '',
      ...options(new Date(0)),
    });
  },
  deleteAccountLevelCookie(response) {
    response.cookies.set({
      name: CookieService.cookie.accountLevel,
      value: '',
      ...options(new Date(0)),
    });
  },
  deleteUserProfilesCookie(response) {
    response.cookies.set({
      name: CookieService.cookie.userProfiles,
      value: '',
      ...options(new Date(0)),
    });
  },
  // Middleware methods
  passCookiesFromRequestToResponse(request, response) {
    request.cookies.getAll().forEach((key) => {
      const cookie = request.cookies.get(key);

      if (cookie) {
        switch (cookie.name) {
          case CookieService.cookie.authentication:
            this.setAuthenticationCookie(cookie.value, response.cookies);
            break;
          case CookieService.cookie.volatile:
            this.setVolatileCookie(cookie.value, response.cookies);
            break;
          case CookieService.cookie.expires:
            this.setExpiresCookie(cookie.value, response.cookies);
            break;
          case CookieService.cookie.account:
            this.setAccountCookie(cookie.value, response.cookies);
            break;
          case CookieService.cookie.userType:
            this.setUserTypeCookie(cookie.value, response.cookies);
            break;
          case CookieService.cookie.profile:
            this.setProfileCookie(cookie.value, response.cookies);
            break;
          case CookieService.cookie.accountLevel:
            this.setAccountLevelCookie(cookie.value, response.cookies);
            break;
          case CookieService.cookie.userProfiles:
            this.setUserProfilesCookie(cookie.value, response.cookies);
            break;
          case CookieService.cookieLegacy.reviewReminded:
          case CookieService.cookie.features:
            // intentionally ignored
            break;
          default:
            if (CookieService.isInternal(cookie.name)) {
              response.cookies.set({
                name: cookie.name,
                value: cookie.value,
                ...strictOptions(),
              });
            }
            break;
        }
      }
    });

    return response;
  },
  cleanAuthenticationCookies(response) {
    this.deleteAuthenticationCookie(response);
    this.deleteVolatileCookie(response);
    this.deleteExpiresCookie(response);
    this.deleteAccountCookie(response);
    this.deleteUserTypeCookie(response);
    this.deleteProfileCookie(response);
    this.deleteUserProfilesCookie(response);
    this.deleteAccountLevelCookie(response);
    Sentry.setUser(null);

    return response;
  },
};
