'use client';
import React, { memo } from 'react';
import styles from './LogoHeader.module.scss';
import Link from 'next/link';
import Logo from '../../public/assets/logo/logo-4.svg';

const LogoHeader = () => {
  return (
    <section className={styles['header-container']}>
      <div className={styles.header}>
        <Link
          href={`${process.env.publicUrl}/castingcalls`}
          passHref
          aria-label="allcasting"
          className={styles['logo-link']}
          prefetch={false}
        >
          <Logo className={styles.logo} />
        </Link>
      </div>
    </section>
  );
};

export default memo(LogoHeader);
