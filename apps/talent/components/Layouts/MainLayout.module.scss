@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.sign-up-title {
  h1 {
    color: $black;
    text-align: center;
    font-weight: 300;
    margin: 0;
    font-size: 16px;
    line-height: 1;
  }

  @include desktop {
    h1 {
      color: $white;
      text-align: left;
      font-size: 48px;
      font-weight: 700;
      line-height: 1.3;
    }
  }
}

.content {
  width: 100%;
  flex: 1;
  flex-basis: 0;
  display: flex;
  flex-direction: column;

  @include desktop {
    margin-left: $space-50;
  }
}

.content-without-sidebar {
  margin-left: 0;
}

.header-section {
  display: block;
  position: sticky;
  z-index: $z-index-header;
  top: 0;
  bottom: auto;
  box-shadow: $shadow-card-container;
  background-color: $white;
}

.header-section-headers {
  display: none;

  @include desktop {
    display: block;
  }
}

.main-wrapper {
  display: block;
}

.content-wrapper {
  min-height: 100vh;
  min-height: 100dvh;
  display: flex;
  flex-direction: column;

  @include desktop {
    flex-direction: row;
    min-height: calc(100vh - 70px);
  }
}
