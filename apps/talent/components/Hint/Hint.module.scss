@use '@styles/mixins' as *;
@use '@styles/variables' as *;

.container {
  display: grid;
  position: relative;
  grid-row-gap: $space-5;
  background: $grey-10;
  border-radius: 10px;
  padding: $space-20 $space-40 $space-20 29px;

  &.gray {
    background-color: $grey-10;
  }

  &.red {
    background-color: $red-10;
  }

  &.slim {
    padding: $space-20;
  }
}

.title {
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  font-size: 12px;
  font-weight: 700;
  margin-bottom: 2px;
}

.text {
  font-size: 12px;
  font-weight: 500;
  line-height: 1.3;

  a {
    color: $blue-100;
    font-weight: 500;
    font-size: 12px;
    line-height: 1.3;

    &::after {
      content: '';
      display: inline-flex;
      width: 15px;
      height: 8px;
      margin-left: 3px;
      background: url('#{$assetUrl}/assets/icons/icon-arrow-right-blue.svg') 0 0
        no-repeat;
    }
  }
}

.close {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  cursor: pointer;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    right: 0;
    left: 0;
    margin: 0 auto;
    width: 16px;
    height: 1px;
    background-color: $grey-60;
    transform: rotate(45deg);
  }

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 0;
    left: 0;
    margin: 0 auto;
    width: 16px;
    height: 1px;
    background-color: $grey-60;
    transform: rotate(-45deg);
  }
}
