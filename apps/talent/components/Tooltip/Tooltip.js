'use client';
import React, { memo, useRef, useState } from 'react';
import Image from 'next/image';
import styles from './Tooltip.module.scss';
import { ArrowContainer, Popover } from 'react-tiny-popover';
import cn from 'classnames';
import { useViewport } from '@utils/useViewport';

const Tooltip = ({
  children, // Tooltip trigger
  content,
  clickable = false, // Allow interaction with elements inside the tooltip
  openOnHover = false, // Set false to only open on trigger click
  positions = ['right', 'top', 'bottom', 'left'], // Preferred positions by priority
  onShow = () => {},
  backdropEnabled = true,
  showCloseButton = true,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const triggerRef = useRef(null);
  const { width } = useViewport();

  const closeTooltip = () => {
    if (isOpen) {
      setIsOpen(false);
    }
  };

  const toggleTooltip = () => {
    if (!isOpen) {
      onShow();
    }
    setIsOpen(!isOpen);
  };

  const onContentClick = () => {
    if (!clickable) {
      closeTooltip();
    }
  };

  const onMouseEnter = () => {
    if (openOnHover && width > 768) {
      if (!isOpen) {
        onShow();
      }
      setIsOpen(true);
    }
  };

  const onOverlayMouseEnter = () => {
    if (openOnHover) {
      closeTooltip();
    }
  };

  const onTouchEnd = () => {
    if (openOnHover) {
      toggleTooltip();
    }
  };

  const onClick = () => {
    if (!openOnHover) {
      toggleTooltip();
    }
  };

  return (
    <>
      <Popover
        ref={triggerRef}
        containerClassName={styles['tooltip']}
        isOpen={isOpen}
        positions={positions}
        align="center"
        content={({ position, childRect, popoverRect }) => (
          <ArrowContainer
            position={position}
            childRect={childRect}
            popoverRect={popoverRect}
            arrowColor={'white'}
            arrowSize={10}
            className={styles['tooltip-arrow-container']}
            arrowClassName={cn(styles['tooltip-arrow'], 'tooltip-arrow')}
          >
            <div
              onClick={onContentClick}
              className={cn(styles['tooltip-content'], {
                [styles.clickable]: clickable,
              })}
            >
              {showCloseButton && (
                <div className={styles['icon-close-container']}>
                  <Image
                    className={styles['icon-close']}
                    src="/assets/icons/icon-close-2.svg"
                    alt="icon"
                    width={15}
                    height={15}
                    onClick={closeTooltip}
                  />
                </div>
              )}
              {content}
            </div>
          </ArrowContainer>
        )}
        onClickOutside={closeTooltip}
      >
        <div
          className={cn(styles['tooltip-trigger'], {
            [styles.active]: isOpen,
          })}
          onTouchEnd={onTouchEnd}
          onMouseEnter={onMouseEnter}
          onClick={onClick}
        >
          {children}
        </div>
      </Popover>
      {isOpen && (
        <div
          className={cn(styles['tooltip-overlay'], {
            [styles.backdrop]: backdropEnabled,
          })}
          onTouchEnd={onOverlayMouseEnter}
          onMouseEnter={onOverlayMouseEnter}
        />
      )}
    </>
  );
};

export default memo(Tooltip);
