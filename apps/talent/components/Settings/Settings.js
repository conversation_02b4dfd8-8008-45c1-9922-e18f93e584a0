'use client';
import { memo, useState } from 'react';
import styles from './Settings.module.scss';
import { HeaderMobile, SettingsSidebar } from '@components';

const Settings = ({
  children,
  title = '',
  showSidebar = false,
  showButton = false,
  onClose = () => {},
  onClick = () => {},
  buttonLabel = 'Save',
  buttonDisabled = false,
}) => {
  const [showMobileSettingsSidebar, setShowMobileSettingsSidebar] =
    useState(showSidebar);
  const [showMobileSettingsContent, setShowMobileSettingsContent] =
    useState(!showSidebar);

  const setMobileSettingsSidebarVisible = () => {
    setShowMobileSettingsContent(false);
    setShowMobileSettingsSidebar(true);

    if (onClose) {
      onClose();
    }
  };

  return (
    <div className={styles['settings-container']}>
      <HeaderMobile
        hideOnTablet
        title={showMobileSettingsContent ? title : 'Settings'}
        onClose={setMobileSettingsSidebarVisible}
        showButton={showButton && showMobileSettingsContent}
        onClick={onClick}
        buttonLabel={buttonLabel}
        buttonDisabled={buttonDisabled}
        showIcon={showMobileSettingsContent}
      />
      <div className={styles['settings-sidebar-container']}>
        <SettingsSidebar />
      </div>
      {showMobileSettingsSidebar && (
        <div className={styles['settings-sidebar-container-mobile']}>
          <SettingsSidebar />
        </div>
      )}
      <div className={styles['settings-content']}>{children}</div>
    </div>
  );
};

export default memo(Settings);
