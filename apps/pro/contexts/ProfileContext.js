'use client';
import { createContext, useContext, useState } from 'react';
import Api from '@services/api';
import { useNotifications } from './NotificationContext';
import { useAuth } from './AuthContext';
import { CookieService } from '@services/cookieService';
import {
  formatImages,
  formatProfileDetails,
  formatTouches,
} from '@utils/formatProProfile';
import { ErrorMessage } from '@constants/form';

const ProfileContext = createContext(null);

export const useProfileContext = () => {
  return useContext(ProfileContext);
};

export const ProfileProvider = ({ children, value }) => {
  const [profile, setProfile] = useState(value);
  const { setNotification } = useNotifications();
  const { refreshUserProfiles, userProfiles } = useAuth();

  const refreshSocialNetworks = async () => {
    const id = CookieService.getProfileCookie();
    const response = await Api.clientside(`/profiles/${id}/socialities`);

    if (response.status !== 'ok') {
      showErrorMessage(response.message);
    } else {
      setProfile((current) => {
        return {
          ...current,
          socialNetworks: response.items?.length ? response.items : [],
        };
      });
    }
  };

  const refreshCategories = async () => {
    const id = CookieService.getProfileCookie();
    const response = await Api.clientside(`/profiles/${id}/categories`);

    if (response.status !== 'ok') {
      showErrorMessage(response.message);
    } else {
      setProfile((current) => {
        return {
          ...current,
          categories: response.items?.length ? response.items : [],
        };
      });
    }
  };

  const refreshTouches = async () => {
    const id = CookieService.getAccountCookie();
    const response = await Api.clientside(`/accounts/${id}/touches`);

    if (response.status !== 'ok') {
      showErrorMessage(response.message);
    } else {
      setProfile((current) => {
        return {
          ...current,
          ...formatTouches(response.links),
        };
      });
    }
  };

  const refreshPersonalUrl = async () => {
    const id = CookieService.getProfileCookie();
    const response = await Api.clientside(`/profiles/${id}/personal-urls/last`);

    if (response.status !== 'ok') {
      showErrorMessage(response.message);
    } else {
      setProfile((current) => {
        return {
          ...current,
          personalUsername: response.path || null,
        };
      });
    }
  };

  const clearPersonalUrl = async () => {
    setProfile((current) => {
      return {
        ...current,
        personalUsername: null,
      };
    });
  };

  const refreshProfileDetails = async () => {
    const id = CookieService.getProfileCookie();
    const response = await Api.clientside(
      `/profiles/${id}?expand=ethnicities,location`,
    );

    if (response.status !== 'ok') {
      showErrorMessage(response.message);
    } else {
      const newProfile = {
        ...profile,
        ...formatProfileDetails(response),
      };

      setProfile(newProfile);
    }
  };

  const refreshImages = async () => {
    const id = CookieService.getProfileCookie();
    const response = await Api.clientside(
      `/profiles/${id}/images?expand=profile_image_characteristics&width=600&height=800`,
    );

    if (response.status !== 'ok') {
      showErrorMessage(response.message);
    } else {
      setProfile((current) => {
        return {
          ...current,
          ...formatImages(response.items),
        };
      });
    }
  };

  const uploadImage = async (file, type, isCurrentTitlePhoto) => {
    const clientId = userProfiles[0].clientId;
    const body = new FormData();

    body.append('profile_id', profile.id);
    body.append('client_id', clientId);
    body.append('file', file);
    body.append('type', type);

    const response = await Api.clientside('/profile-images-upload', {
      method: 'POST',
      body,
    });

    if (response.status === 'error') {
      showErrorMessage(response.message);

      return null;
    }

    if (isCurrentTitlePhoto && profile.titleImage?.id) {
      await deleteTitleImage(profile.titleImage.id);
    }

    await refreshImages();
    await refreshUserProfiles();

    return response;
  };

  const uploadTitleImage = async (file, id) => {
    const clientId = userProfiles[0].clientId;
    const body = new FormData();

    body.append('profile_id', profile.id);
    body.append('client_id', clientId);
    body.append('file', file);
    body.append('type', 'pc');
    body.append('source_id', id);

    const response = await Api.clientside('/profile-images-upload', {
      method: 'POST',
      body,
    });

    if (response.status === 'error') {
      showErrorMessage(response.message);
    } else {
      await refreshImages();
      await refreshUserProfiles();
    }
  };

  const deleteImage = async (id) => {
    const body = new FormData();

    body.append('id', `${id}`);

    const response = await Api.clientside(`/photo/delete`, {
      body: body,
      method: 'POST',
    });

    if (response.status !== 'ok') {
      showErrorMessage(response.message);

      return;
    }

    if (profile.titleImage?.source_id === id) {
      await deleteTitleImage(profile.titleImage.id);
    }

    await refreshUserProfiles();
    await refreshImages();
  };

  const deleteTitleImage = async (id) => {
    const body = new FormData();

    body.append('id', `${id}`);

    const response = await Api.clientside(`/photo/delete`, {
      body: body,
      method: 'POST',
    });

    if (response.status !== 'ok') {
      showErrorMessage(response.message);
    }
  };

  const showErrorMessage = (message) => {
    setNotification({
      type: 'error',
      message: message || ErrorMessage.Unexpected,
      timeout: '5000',
    });
  };

  return (
    <ProfileContext.Provider
      value={{
        ...profile,
        refreshProfileDetails,
        refreshTouches,
        refreshSocialNetworks,
        refreshCategories,
        refreshPersonalUrl,
        clearPersonalUrl,
        refreshImages,
        deleteImage,
        uploadTitleImage,
        uploadImage,
      }}
    >
      {children}
    </ProfileContext.Provider>
  );
};
