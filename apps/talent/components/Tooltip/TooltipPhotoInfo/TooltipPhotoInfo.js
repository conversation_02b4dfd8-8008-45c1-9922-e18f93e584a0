'use client';
import React, { memo } from 'react';
import styles from './TooltipPhotoInfo.module.scss';
import Image from 'next/image';

const TooltipPhotoInfo = () => {
  return (
    <div className={styles['tooltip-container']}>
      <div className={styles['profile-panel-info-title']}>
        <Image
          className={styles['profile-panel-info-icon']}
          src={'/assets/icons/icon-important.svg'}
          width={17}
          height={17}
          alt="important icon"
          priority
        />
        &nbsp; What you need to know
      </div>
      <div className={styles['profile-panel-info-text']}>
        Comp card photos will be evaluated by a photo analyzer.
      </div>
      <a
        target="_blank"
        rel="noreferrer"
        href={`${process.env.publicUrl}/blog/skill-development/the-importance-of-headshots`}
        className={styles['profile-panel-info-link']}
      >
        What makes a good photo
      </a>
    </div>
  );
};

export default memo(TooltipPhotoInfo);
