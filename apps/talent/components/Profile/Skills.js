'use client';
import React, { memo, useEffect, useState } from 'react';
import Api from '@services/api';
import styles from './ProfileInfo.module.scss';
import { useNotifications } from '@contexts/NotificationContext';
import { SearchSelect } from '@components';
import Image from 'next/image';
import cn from 'classnames';
import { Amp } from '@services/amp';
import { EditToggle } from './EditToggle';
import * as Sentry from '@sentry/nextjs';

const Skills = ({ profileId, skills, refreshSkills, updateProfileRating }) => {
  const { setNotification } = useNotifications();
  const [isSkillsContainerEditing, setSkillsContainerEditing] = useState(false);
  const [skillOptions, setSkillOptions] = useState([]);
  const [selectedSkills, setSelectedSkills] = useState(skills || []);

  let skillsRequestAbortController = new AbortController();

  const editSkills = () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'edit skills',
      scope: Amp.element.scope.profile,
      section: Amp.element.section.skills,
      type: Amp.element.type.button,
    });
    setSkillsContainerEditing(true);
  };

  const fetchSkills = async (title = '') => {
    if (title.length > 1) {
      try {
        skillsRequestAbortController?.abort();
        skillsRequestAbortController = new AbortController();

        const response = await Api.clientside(
          `/skills/search?limit=10&title=${title}`,
          { signal: skillsRequestAbortController.signal },
        );

        setSkillOptions(
          response.items.map((skill) => ({ ...skill, value: skill.id })) || [],
        );
      } catch (error) {
        if (error.name !== 'AbortError') {
          Sentry.captureException(error);
        }
      }
    } else {
      setSkillOptions([]);
    }
  };

  const saveSkills = async () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'save skills',
      scope: Amp.element.scope.profile,
      section: Amp.element.section.skills,
      type: Amp.element.type.button,
    });

    const body = {
      items: selectedSkills.map(({ title }) => ({ title })),
    };

    const response = await Api.clientside(
      `/profiles/${profileId}/skills/batch?expand=profile`,
      {
        method: 'PUT',
        body: JSON.stringify(body),
      },
    );

    if (response.status !== 'ok') {
      setNotification({
        type: 'error',
        message: response.message,
        timeout: '5000',
      });
    } else {
      await refreshSkills();
      updateProfileRating(
        response.items.length
          ? response.items[0]?.links?.profile?.rating
          : response.profile?.rating,
      );
    }

    setSkillsContainerEditing(false);
  };

  const addSkill = async (value) => {
    const skill = skillOptions.find((option) => option.value === value) || {
      id: value,
      title: value,
    };
    const isSkillAlreadySelected = selectedSkills.every(
      (option) => option.title !== value,
    );

    if (isSkillAlreadySelected) {
      setSelectedSkills([...selectedSkills, skill]);
    }
  };

  const removeSkill = (id) => {
    setSelectedSkills(selectedSkills.filter((skill) => skill.id !== id));
  };

  useEffect(() => {
    setSelectedSkills(skills || []);
  }, [skills]);

  return (
    <>
      <section>
        <div className={styles['profile-section-title']}>
          Skills:
          <EditToggle
            isEditing={isSkillsContainerEditing}
            onEdit={editSkills}
            onSave={saveSkills}
          />
        </div>
        {!isSkillsContainerEditing ? (
          <>
            {selectedSkills?.length ? (
              <div className={styles['profile-badge-container']}>
                {selectedSkills.map(({ title }, index) => (
                  <span
                    key={index}
                    className={cn(
                      styles['profile-badge-filled'],
                      styles.clickable,
                    )}
                    onClick={editSkills}
                  >
                    {title}
                  </span>
                ))}
              </div>
            ) : (
              <div
                className={styles['profile-section-empty']}
                onClick={editSkills}
              >
                List all the skills to increase your chances of getting noticed.
                <span className={styles['profile-section-empty-add-button']}>
                  Add
                </span>
              </div>
            )}
          </>
        ) : (
          <>
            <SearchSelect
              name="skills"
              placeholder="Write your skills..."
              onChange={fetchSkills}
              options={skillOptions.filter(
                (skill) =>
                  !selectedSkills.some(
                    (selectedSkill) => selectedSkill.id === skill.id,
                  ),
              )}
              onAdd={addSkill}
              hint="Max 25 characters"
              charCounter
              maxChars={25}
            />
            <div
              className={cn(
                styles['profile-badge-container'],
                styles['profile-badge-container-filled'],
              )}
            >
              {selectedSkills?.map(({ id, title }, index) => (
                <div key={index} className={styles['profile-badge-filled']}>
                  {title}
                  <Image
                    className={styles['delete-button']}
                    onClick={() => removeSkill(id)}
                    src="/assets/icons/icon-close.svg"
                    alt="icon"
                    width={10}
                    height={10}
                  />
                </div>
              ))}
            </div>
          </>
        )}
      </section>
    </>
  );
};

export default memo(Skills);
