'use client';
import { useEffect, useState } from 'react';
import * as Sentry from '@sentry/nextjs';
import styles from '../../PaymentForm.module.scss';
import {
  useElements,
  useStripe,
  ExpressCheckoutElement,
} from '@stripe/react-stripe-js';
import { useLiveChat } from '@contexts/LiveChatContext';
import { GTM_ACTIONS, GTM_CATEGORIES, GTM_EVENTS } from '@constants/analytics';
import { useAnalytics } from 'use-analytics';
import { LIVE_CHAT_GROUP } from '@constants/liveChat';
import { CookieService } from '@services/cookieService';
import { ErrorMessage } from '@constants/form';
import { useNotifications } from '@contexts/NotificationContext';

const StripeExpress = ({ clientSecret, invoiceHash, updatePaymentLoading }) => {
  const [isExpressAvailable, setIsExpressAvailable] = useState(false);

  const { setNotification } = useNotifications();
  const { initiateLiveChat } = useLiveChat();
  const { track } = useAnalytics();

  const stripe = useStripe();
  const elements = useElements();

  useEffect(() => {
    if (!stripe || !clientSecret) {
      return;
    }

    const storedInvoiceHash = window.sessionStorage.getItem('invoiceHash');

    if (storedInvoiceHash !== invoiceHash) {
      window.sessionStorage.setItem('invoiceHash', invoiceHash);
    }

    const storedCookie = CookieService.getShowStripeCheckoutSuccess();

    if (!storedCookie) {
      CookieService.setShowStripeCheckoutSuccess(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [stripe]);

  const onConfirm = async () => {
    if (!stripe || !elements) {
      Sentry.captureException(
        new Error('[Express] Stripe or elements not found'),
        {
          tags: {
            feature: 'stripe',
          },
          extra: {
            stripe: !!stripe,
            elements: !!elements,
          },
        },
      );

      return;
    }

    updatePaymentLoading(true);

    const { error } = await stripe.confirmPayment({
      elements,
      confirmParams: {
        return_url: `${process.env.baseUrl}/checkout/success`,
      },
    });

    const { type, code, decline_code, message } = error;

    Sentry.captureException(
      new Error(
        `Type: ${type};${code ? ` Code: ${code};` : ''}${decline_code ? ` Decline code: ${decline_code};` : ''}`,
      ),
      {
        tags: {
          feature: 'stripe-express',
        },
        extra: {
          message,
        },
      },
    );

    if (code === 'payment_intent_unexpected_state') {
      showErrorMessage(ErrorMessage.SalesClosed, false);
    } else if (
      error.type === 'card_error' ||
      error.type === 'validation_error'
    ) {
      showErrorMessage(error.message);
    } else {
      showErrorMessage(ErrorMessage.Unexpected);
    }

    initiateLiveChat(true, LIVE_CHAT_GROUP.AllcastingPayments);

    track(GTM_EVENTS.interaction, {
      target: GTM_CATEGORIES.payment,
      action: GTM_ACTIONS.submit,
      label:
        error.type === 'card_error' ? error.message : ErrorMessage.Unexpected,
    });

    updatePaymentLoading(false);
  };

  const showErrorMessage = (message, timeout = true) => {
    setNotification({
      type: 'error',
      message: message || ErrorMessage.Unexpected,
      timeout: timeout ? '15000' : null,
    });
  };

  const onReady = (event) => {
    const { applePay, googlePay } = event.availablePaymentMethods || {};
    const current = !!(applePay || googlePay);

    setIsExpressAvailable((previous) =>
      previous === current ? previous : current,
    );
  };

  const onError = (event) => {
    const { error } = event || {};
    const { type, code, setup_intent, message } = error || {};

    Sentry.captureException(
      new Error('ExpressCheckoutElement failed to load'),
      {
        tags: {
          feature: 'stripe-express',
        },
        extra: {
          type,
          code,
          setup_intent,
          message,
        },
      },
    );
  };

  return (
    <>
      <ExpressCheckoutElement
        onLoadError={onError}
        onReady={onReady}
        onConfirm={onConfirm}
        options={{
          buttonType: {
            googlePay: 'subscribe',
          },
        }}
      />
      {isExpressAvailable && (
        <div className={styles['stripe-separator']}>Or pay with card</div>
      )}
    </>
  );
};

export default StripeExpress;
