'use client';
import styles from './AttachCastingCall.module.scss';
import { useRef, useState } from 'react';
import cn from 'classnames';
import { Loading, Tooltip } from '@components';
import Api from '@services/api';
import { useAuth } from '@contexts/AuthContext';
import { STATUS } from '@constants/castingCalls';
import Image from 'next/image';

const AttachCastingCall = ({
  className = '',
  onAttach,
  width = 50,
  height = 32,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [castingCalls, setCastingCalls] = useState([]);
  const { userProfiles } = useAuth();
  const tooltipRef = useRef(null);

  const iconClick = () => {
    getCastingCalls().catch();
  };

  const handleCastingCallClick = (castingCall) => {
    onAttach(castingCall);
    tooltipRef.current.close();
  };

  const getCastingCalls = async () => {
    setIsLoading(true);
    setCastingCalls([]);

    const clientId = userProfiles.length ? userProfiles[0].clientId : 0;
    const response = await Api.clientside(
      `/calls/search?es=1&&limit=100&filter=active&status[]=approved&casting_director_id=${clientId}`,
    );

    setCastingCalls([
      ...(response.data?.calls?.length
        ? response.data.calls.filter((item) => item.status !== STATUS.Expired)
        : []),
    ]);
    setIsLoading(false);
  };

  return (
    <div className={className}>
      <Tooltip
        ref={tooltipRef}
        onShow={iconClick}
        backdropEnabled={false}
        clickable
        className="casting-call-tooltip"
        classNameTrigger="casting-call-tooltip-trigger"
        positions={['top']}
        content={
          <div className={cn(styles['attach-popup'])}>
            <div className={styles.heading}>Attach a Casting Call:</div>
            <div className={styles['cc-list']}>
              {isLoading && <Loading />}
              {castingCalls.map((item, i) => (
                <div
                  key={i}
                  className={styles['casting-call']}
                  onClick={() => {
                    handleCastingCallClick(item);
                  }}
                >
                  {item.title}
                </div>
              ))}
              {!castingCalls.length && !isLoading && (
                <div className={styles['empty-list']}>
                  You have no active casting calls
                </div>
              )}
            </div>
          </div>
        }
      >
        <Image
          src="/assets/icons/icon-attach-cc.svg"
          width={width}
          height={height}
          alt="icon"
        />
      </Tooltip>
    </div>
  );
};

export default AttachCastingCall;
