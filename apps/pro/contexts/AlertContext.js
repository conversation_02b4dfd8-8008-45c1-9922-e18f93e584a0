'use client';
import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
} from 'react';
import { useAuth } from './AuthContext';
import { useInterval } from '@utils/useInterval';
import Api from '@services/api';

const POOLING_INTERVAL = 25000;
const DEFAULT_ALERTS = {
  newConversationCount: 0,
  newReviewCount: 0,
  newCandidateCount: 0,
};

const AlertContext = createContext(DEFAULT_ALERTS);

export const AlertProvider = ({ children }) => {
  const [alerts, setAlerts] = useState(DEFAULT_ALERTS);

  const { isAuthenticated, accountId } = useAuth();

  const fetchAlerts = useCallback(async () => {
    const { status, conversations, testimonials, candidates } =
      await Api.clientside(`/accounts/${accountId}/alerts`);

    if (status !== 'ok') return;

    setAlerts((previousAlerts) => {
      const { newConversationCount, newReviewCount, newCandidateCount } =
        previousAlerts;

      return conversations !== newConversationCount ||
        testimonials !== newReviewCount ||
        candidates !== newCandidateCount
        ? {
            newConversationCount: conversations,
            newReviewCount: testimonials,
            newCandidateCount: candidates,
          }
        : previousAlerts;
    });
  }, [accountId]);

  useEffect(() => {
    accountId && fetchAlerts();
  }, [accountId, fetchAlerts]);

  useInterval(() => {
    isAuthenticated && accountId && fetchAlerts();
  }, POOLING_INTERVAL);

  return (
    <AlertContext.Provider value={{ ...alerts, fetchAlerts }}>
      {children}
    </AlertContext.Provider>
  );
};

export const useAlert = () => useContext(AlertContext);
