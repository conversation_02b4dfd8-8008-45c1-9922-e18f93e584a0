import './global.scss';
import { Plus_Jakarta_Sans } from 'next/font/google';
import Script from 'next/script';
import { Providers } from './providers';
import { ReferrerTracker } from '@components';
import { Suspense } from 'react';

const jakarta = Plus_Jakarta_Sans({
  subsets: ['latin'],
  fallback: [
    'system-ui',
    'Segoe UI',
    'Roboto',
    'Helvetica',
    'Arial',
    'sans-serif',
  ],
  variable: '--font-jakarta',
});

export default function RootLayout({ children }) {
  return (
    <html
      lang="en"
      style={{ scrollBehavior: 'smooth' }}
      className={jakarta.variable}
    >
      <head>
        <title>allcasting</title>
        <meta name="referrer" content="no-referrer-when-downgrade" />
        <meta name="apple-mobile-web-app-title" content="allcasting" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </head>
      <body>
        <Suspense fallback={null}>
          <ReferrerTracker />
        </Suspense>
        <Providers>{children}</Providers>
        <Script
          src={`https://cdn.cookielaw.org/scripttemplates/otSDKStub.js`}
          strategy="afterInteractive"
          type={`text/javascript`}
          data-domain-script={process.env.NEXT_PUBLIC_ONETRUST_ID}
        />
        <Script id="OneTrustInitialization" strategy="afterInteractive">
          {`function OptanonWrapper() { window.dataLayer ? window.dataLayer.push( { event: 'OneTrustGroupsUpdated' } ) : undefined }`}
        </Script>
        <div style={{ display: 'none' }}>
          <button id="ot-sdk-btn" className="ot-sdk-show-settings">
            Cookie Settings
          </button>
        </div>
      </body>
    </html>
  );
}
