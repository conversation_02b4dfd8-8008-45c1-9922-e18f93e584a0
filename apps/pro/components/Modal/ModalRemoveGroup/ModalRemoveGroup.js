'use client';
import React, { memo, useState } from 'react';
import { useNotifications } from '@contexts/NotificationContext';
import Api from '@services/api';
import { Loading, Modal } from '@components';
import styles from './ModalRemoveGroup.module.scss';
import { useRouter } from 'next/navigation';
import { ErrorMessage } from '@constants/form';

const ModalRemoveGroup = ({ groupId, onClose = () => {} }) => {
  const [loading, setLoading] = useState(false);
  const { setNotification } = useNotifications();
  const router = useRouter();

  const onRemove = async () => {
    setLoading(true);
    const response = await Api.clientside(`/contact-groups/${groupId}`, {
      method: 'DELETE',
    });

    if (response.status !== 'removed') {
      setNotification({
        type: 'error',
        timeout: '5000',
        message: response.message || ErrorMessage.Unexpected,
      });
    } else {
      router.push('/contacts');
    }

    onClose();
    setLoading(false);
  };

  return (
    <Modal
      onClose={onClose}
      backdropClose
      showDefaultLayout={false}
      classNameContainer={styles['modal-container']}
      hideMobileCloseButton
      floatCloseButton
    >
      {loading ? (
        <div className={styles['loading-container']}>
          <div>Removing group...</div>
          <Loading minHeight="40px" padding="0" />
        </div>
      ) : (
        <>
          <div className={styles.container}>
            <span>
              Are you sure you want to
              <br />
              <b>remove this group</b> ?
            </span>
          </div>
          <div className={styles.actions}>
            <button onClick={onClose} className={styles.action}>
              Cancel
            </button>
            <button onClick={onRemove} className={styles.action}>
              Remove
            </button>
          </div>
        </>
      )}
    </Modal>
  );
};

export default memo(ModalRemoveGroup);
