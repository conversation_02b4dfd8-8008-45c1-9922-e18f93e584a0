'use client';
import React, { memo, useState } from 'react';
import styles from './TooltipProfile.module.scss';
import cn from 'classnames';
import HintIcon from '../../public/assets/icons/icon-text-hint.svg';

const TooltipProfile = ({ title = '', text = '', learnMoreLink = '' }) => {
  const [showTooltipBar, setShowTooltipBar] = useState(false);
  const toggleTooltipBar = () => {
    setShowTooltipBar(!showTooltipBar);
  };

  return (
    <div
      className={cn(styles['tooltip-title'], {
        [styles['is-open']]: showTooltipBar,
      })}
    >
      {title}
      <HintIcon onClick={toggleTooltipBar} className={styles['tooltip-icon']} />
      <div className={styles['tooltip-box']}>
        {text}
        {!!learnMoreLink.length && (
          <>
            &nbsp;
            <a href={learnMoreLink}>Learn more</a>
          </>
        )}
      </div>
    </div>
  );
};

export default memo(TooltipProfile);
