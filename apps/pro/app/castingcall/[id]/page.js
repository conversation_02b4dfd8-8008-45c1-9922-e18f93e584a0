import CastingCallClient from './_client';
import { cookies, headers } from 'next/headers';
import { CookieServiceServer } from '@services/cookieServiceServer';
import Api from '@services/api';
import { getResolvedUrl } from '@utils/apiHelper';
import { extractResult } from '@utils/extractPromiseResult';
import { getSideMenuItems } from '@utils/castingCallHelper';
import { STATUS, TYPE } from '@constants/castingCalls';
import dayjs from 'dayjs';

export const metadata = {
  title: 'Casting call',
};

export default async function CastingCallPage(req) {
  const params = await req.params;
  const searchParams = await req.searchParams;
  const resolvedUrl = getResolvedUrl(`/castingcall/${params.id}`, searchParams);
  const cookieStore = await cookies();
  const headerStore = await headers();
  const userProfiles = CookieServiceServer.getUserProfilesCookie(cookieStore);
  const clientId = userProfiles.length ? userProfiles[0].clientId || 0 : 0;
  const profileUrl = userProfiles.length
    ? userProfiles[0].profileUrl || ''
    : '';

  const { id } = params;
  const { stop } = searchParams;

  const castingCallPaths = [
    `/calls/get?id=${id}`,
    `/calls/search?es=1&limit=5&casting_director_id=${clientId}`,
  ];

  const castingCallResults = await Promise.allSettled(
    castingCallPaths.map((path) =>
      Api.serverside(path, cookieStore, headerStore, resolvedUrl),
    ),
  );

  const [castingCallResponse, relatedCastingCallsResponse] =
    castingCallResults.map((result) => extractResult(result, {}));

  const roles = castingCallResponse.data?.roles || [];
  const sideMenuItems = getSideMenuItems(roles);

  let formattedRoles = [...roles];

  if (roles?.length) {
    const candidatePaths = roles.map(
      (role) => `/roles/${role.id}/candidates?limit=20`,
    );

    const candidateResults = await Promise.allSettled(
      candidatePaths.map((path) =>
        Api.serverside(path, cookieStore, headerStore, resolvedUrl),
      ),
    );

    const [...candidatesResponses] = candidateResults.map((result) =>
      extractResult(result, { items: [] }),
    );

    formattedRoles = formattedRoles.map((role, i) => ({
      ...role,
      candidates: { ...role.candidates, items: candidatesResponses[i].items },
    }));
  }

  const castingCall = castingCallResponse.data || {};

  return (
    <CastingCallClient
      relatedCastingCalls={
        relatedCastingCallsResponse.data?.calls?.slice(0, 4) || []
      }
      castingCall={castingCall}
      profileUrl={profileUrl}
      sideMenuItems={sideMenuItems}
      roles={formattedRoles || []}
      isExpired={
        castingCall.status === STATUS.Expired ||
        dayjs(castingCall.expiration).unix() < dayjs().unix()
      }
      showPaymentLabel={
        castingCall.payment_amount || castingCall.payment_period === 'TFP'
      }
      isStopSubmissionsRequest={
        castingCall.type === TYPE.Email ? !!stop : false
      }
    />
  );
}
