'use client';
import { memo } from 'react';
import styles from './Radio.module.scss';
import cn from 'classnames';

const Radio = ({
  checked = false,
  readOnly = false,
  color = 'emerald', // emerald | midnight | white | black
  className = '',
}) => {
  return (
    <label
      className={cn(styles['radio-input-label'], className, styles[color])}
    >
      <input
        className={styles['radio-input']}
        readOnly={readOnly}
        type="radio"
        name="radio"
        checked={checked}
      />
      <span className={cn(styles['radio-input-checkmark'], styles[color])} />
    </label>
  );
};

export default memo(Radio);
