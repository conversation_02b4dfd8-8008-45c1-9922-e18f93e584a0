'use client';
import React, { createContext, useState, useContext, useEffect } from 'react';
import Api from '../services/api';
import { useNotifications } from './NotificationContext';
import { useAnalytics } from 'use-analytics';
import { GTM_ACTIONS, GTM_CATEGORIES, GTM_EVENTS } from '@constants/analytics';
import { CookieService } from '@services/cookieService';
import dayjs from 'dayjs';
import { formatAccountLevel } from '@utils/formatAccountLevel';
import * as Sentry from '@sentry/nextjs';
import { usePathname } from 'next/navigation';
import { ErrorMessage } from '@constants/form';
import { isPublicRoute } from '@utils/isPublicRoute';

const AuthContext = createContext({});

export const AuthProvider = ({ children }) => {
  const [userProfiles, setUserProfiles] = useState([]);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [userType, setUserType] = useState(null);
  const [accountId, setAccountId] = useState(null);
  const [profileId, setProfileId] = useState(null);

  const path = usePathname();
  const { setNotification } = useNotifications();
  const { track } = useAnalytics();

  const redirectOnLogout = () => {
    window.location.href = process.env.publicUrl;
  };

  const authVerify = () => {
    if (
      CookieService.getAuthenticationCookie() &&
      CookieService.getVolatileCookie() &&
      CookieService.getExpiresCookie() &&
      CookieService.getAccountCookie()
    ) {
      const currentEpoch = dayjs().unix();
      const tokenExpires = CookieService.getExpiresCookie();

      if (tokenExpires > currentEpoch) {
        setIsAuthenticated(true);
        setAccountId(CookieService.getAccountCookie());
        setUserType(CookieService.getUserTypeCookie());
        setProfileId(CookieService.getProfileCookie());
        setUserProfiles(CookieService.getUserProfilesCookie());

        accountLevelVerify().catch();
      } else {
        authLogout();
      }
    } else {
      authLogout();
    }
  };

  const accountLevelVerify = async () => {
    const level = await Api.clientside(
      `/accounts/${CookieService.getAccountCookie()}/levels/last`,
    );

    if (level.status !== 'ok') {
      setNotification({
        type: 'error',
        message: ErrorMessage.SessionExpired,
        timeout: '5000',
      });
      authLogout();
    } else {
      const modifiedAccountLevel = formatAccountLevel(
        level?.links?.level?.status,
      );

      CookieService.setAccountLevelCookie(modifiedAccountLevel);
    }
  };

  const refreshUserProfiles = async () => {
    const userProfilesResponse = await Api.clientside(
      `/accounts/${CookieService.getAccountCookie()}/profiles?expand=location,personal_url,touches`,
    );

    if (userProfilesResponse.status !== 'ok') {
      setNotification({
        type: 'error',
        message: ErrorMessage.FetchProfilesFailed,
        timeout: '5000',
      });
    } else {
      updateUserProfiles(userProfilesResponse.items);
    }
  };

  const updateUserProfiles = (userProfiles) => {
    const modifiedUserProfiles = userProfiles.map((el) => {
      return {
        titlePhotoUrl: el.title_photo_url,
        profileUrl: `/director/${el.links?.personal_url?.path || el.id}`,
        id: el.id,
        firstName: el.firstname,
        lastName: el.lastname,
        fullName: `${el.firstname} ${el.lastname}`,
        href: el.href || '',
        zipCode: el.links?.location?.links?.zip?.code || '',
        city: el.links?.location?.links?.city?.slug || '',
        country: el.links?.location?.links?.country?.code || '',
        clientId: el.client_id || null,
        company: el.company_name || null,
        isEmailValid:
          !!el?.links?.touches?.links?.email?.value &&
          !el.links.touches.links.email.value.endsWith(
            '@privaterelay.appleid.com',
          ),
      };
    });

    if (modifiedUserProfiles.length) {
      CookieService.setUserProfilesCookie(modifiedUserProfiles);
    } else {
      CookieService.deleteUserProfilesCookie();
    }
    setUserProfiles(modifiedUserProfiles || []);
  };

  useEffect(() => {
    authVerify();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [path]);

  const clearAuthData = () => {
    CookieService.cleanAuthenticationCookies();
    setIsAuthenticated(false);
    setAccountId(null);
    setUserType(null);
    setProfileId(null);
    setUserProfiles([]);
    Sentry.setUser(null);
  };

  const authLogoutWithTracking = () => {
    track(GTM_EVENTS.interaction, {
      target: GTM_CATEGORIES.logout,
      action: GTM_ACTIONS.logout,
      label: path,
    });
    authLogout();
  };

  const authLogout = () => {
    if (isPublicRoute()) {
      clearAuthData();
    } else {
      clearAuthData();
      redirectOnLogout();
    }
  };

  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        accountId,
        userType,
        userProfiles,
        authLogoutWithTracking,
        profileId,
        refreshUserProfiles,
        accountLevelVerify,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => useContext(AuthContext);
