@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.locked-feature-container {
  gap: $space-10;
  align-items: center;
  background: $grey-10;
  margin-top: $space-20;
  border-radius: 10px;
  padding: $space-15 $space-10;
  display: none;

  @include tablet {
    display: flex;
  }

  .button-unblock {
    padding: $space-5 $space-10;
    text-transform: capitalize;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 700;
  }
}

.locked-feature-description-container {
  .locked-feature-title {
    font-size: 14px;
    font-weight: 600;
  }

  .locked-feature-description {
    font-size: 12px;
    color: $grey-100;
    font-weight: 400;
    margin: 0;
  }
}

.icon-locked-container {
  box-shadow: $shadow-locked-icon;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 46px;
}

.icon-locked {
  display: flex;
}
