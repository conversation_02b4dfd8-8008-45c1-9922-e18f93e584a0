import { NextResponse } from 'next/server';
import dayjs from 'dayjs';
import {
  basicAccountGuard,
  blogGuard,
  defaultGuard,
  paidAccountGuard,
} from '@constants/guards';
import {
  callbackAccount,
  extractAccountIdFromToken,
  extractExpiresFromAuthenticationToken,
  responseAccountLevel,
  responseExtractProfileId,
  responseExtractProfiles,
  responseExtractType,
} from '@utils/authHelper';
import { CookieServiceServer } from '@services/cookieServiceServer';
import { profileTabs } from '@constants/profile';
import * as Sentry from '@sentry/nextjs';
import { getUpgradeValue } from '@utils/formatAccountLevel';

export const config = {
  // Exclude from middleware
  matcher: [
    '/((?!api|_next|favicon.ico|sitemap.xml|robots.txt|assets|apple-developer-merchantid-domain-association|icon.|manifest|android-chrome|upgrade-preview).*)',
  ],
};

export async function middleware(request) {
  const nextPath = request.nextUrl.pathname;
  const queryParams = request.nextUrl.search || '';
  const redirectUrl = request.nextUrl.origin;
  const publicUrl = process.env.publicUrl;

  const authenticationToken = CookieServiceServer.getAuthenticationCookie(
    request.cookies,
  );
  const volatileToken = CookieServiceServer.getVolatileCookie(request.cookies);

  if (!authenticationToken || !volatileToken) {
    if (nextPath.includes('/profile')) {
      return CookieServiceServer.passCookiesFromRequestToResponse(
        request,
        NextResponse.redirect(
          `${publicUrl}${nextPath.split('/').slice(0, 3).join('/')}${
            queryParams || ''
          }`,
          307,
        ),
      );
    }

    return CookieServiceServer.cleanAuthenticationCookies(
      NextResponse.redirect(`${publicUrl}`, 307),
    );
  }

  await insureRequiredCookies(
    request,
    authenticationToken,
    volatileToken,
    request.cookies,
    nextPath,
  );

  const tokenExpirationCookie = CookieServiceServer.getExpiresCookie(
    request.cookies,
  );
  const accountLevelCookie = CookieServiceServer.getAccountLevelCookie(
    request.cookies,
  );
  const userProfilesCookie = CookieServiceServer.getUserProfilesCookie(
    request.cookies,
  );
  const accountIdCookie = CookieServiceServer.getAccountCookie(request.cookies);
  const profileIdCookie = CookieServiceServer.getProfileCookie(request.cookies);
  const showCheckoutSuccessCookie =
    CookieServiceServer.getShowCheckoutSuccessCookie(request.cookies);
  const showStripeCheckoutSuccess =
    CookieServiceServer.getShowStripeCheckoutSuccess(request.cookies);
  const isTalent =
    CookieServiceServer.getUserTypeCookie(request.cookies) === 'talent';

  const currentEpoch = dayjs().unix();
  const tokenExpiration = tokenExpirationCookie
    ? Number(tokenExpirationCookie)
    : null;
  const isAuthenticated = tokenExpiration
    ? authenticationToken &&
      volatileToken &&
      accountIdCookie &&
      profileIdCookie &&
      tokenExpiration > currentEpoch
    : false;
  const isPaidOrDelayed = accountLevelCookie?.isPaidOrDelayed;
  const canUpgradeExistingSubscription = CookieServiceServer.getUpgradeCookie(
    request.cookies,
  );
  const {
    profileUrl,
    isEmailValid,
    firstName,
    lastName,
    birthday,
    zipCode,
    gender,
  } = userProfilesCookie?.[0] || {};
  const isFullProfile = !!(
    firstName &&
    lastName &&
    birthday &&
    zipCode &&
    gender &&
    isEmailValid
  );

  if (!isAuthenticated || !isTalent) {
    return CookieServiceServer.cleanAuthenticationCookies(
      NextResponse.redirect(`${publicUrl}`, 307),
    );
  }

  if (!isFullProfile && !nextPath.includes('/wizard')) {
    return CookieServiceServer.passCookiesFromRequestToResponse(
      request,
      NextResponse.redirect(`${redirectUrl}/wizard${queryParams}`, 307),
    );
  }

  if (nextPath === '/' && profileUrl) {
    return CookieServiceServer.passCookiesFromRequestToResponse(
      request,
      NextResponse.redirect(
        `${redirectUrl}${profileUrl}/info${queryParams}`,
        307,
      ),
    );
  }

  if (
    nextPath.includes('/profile') &&
    !profileTabs.some((tab) => nextPath === `${profileUrl}/${tab.slug}`)
  ) {
    return CookieServiceServer.passCookiesFromRequestToResponse(
      request,
      NextResponse.redirect(`${redirectUrl}${profileUrl}/info`, 307),
    );
  }

  if (defaultGuard.some((url) => nextPath.includes(url))) {
    return CookieServiceServer.passCookiesFromRequestToResponse(
      request,
      NextResponse.redirect(`${publicUrl}${nextPath}${queryParams}`, 307),
    );
  }

  if (blogGuard.some((url) => nextPath.includes(url))) {
    // ToDo: check up on this. Shouldn't really be happening
    const blogNextPath = nextPath
      .replace('/news', '/blog')
      .replace('/academy', '/blog');

    return CookieServiceServer.passCookiesFromRequestToResponse(
      request,
      NextResponse.redirect(`${publicUrl}${blogNextPath}${queryParams}`, 307),
    );
  }

  if (nextPath.includes('/checkout/success') && showStripeCheckoutSuccess) {
    return CookieServiceServer.passCookiesFromRequestToResponse(
      request,
      NextResponse.next(),
    );
  }

  if (
    !isPaidOrDelayed &&
    basicAccountGuard.some((url) => nextPath.includes(url))
  ) {
    return CookieServiceServer.passCookiesFromRequestToResponse(
      request,
      NextResponse.redirect(`${publicUrl}/castingcalls`, 307),
    );
  }

  if (nextPath.includes('/checkout/success') && showCheckoutSuccessCookie) {
    return CookieServiceServer.passCookiesFromRequestToResponse(
      request,
      NextResponse.next(),
    );
  }

  if (
    isPaidOrDelayed &&
    !canUpgradeExistingSubscription &&
    paidAccountGuard.some((url) => nextPath.includes(url))
  ) {
    return CookieServiceServer.passCookiesFromRequestToResponse(
      request,
      NextResponse.redirect(`${publicUrl}/castingcalls`, 307),
    );
  }

  if (isPaidOrDelayed && nextPath === '/onboarding') {
    return CookieServiceServer.passCookiesFromRequestToResponse(
      request,
      NextResponse.redirect(`${publicUrl}/onboarding`, 307),
    );
  }

  if (
    canUpgradeExistingSubscription &&
    (nextPath === '/checkout' || nextPath === '/upgrade')
  ) {
    return CookieServiceServer.passCookiesFromRequestToResponse(
      request,
      NextResponse.redirect(`${publicUrl}/castingcalls`, 307),
    );
  }

  return CookieServiceServer.passCookiesFromRequestToResponse(
    request,
    NextResponse.next(),
  );
}

const insureRequiredCookies = async (
  req,
  authenticationToken,
  volatileToken,
  cookies,
  path,
) => {
  const cookiesToCheck = [
    CookieServiceServer.getAuthenticationCookie(req.cookies),
    CookieServiceServer.getVolatileCookie(req.cookies),
    CookieServiceServer.getExpiresCookie(req.cookies),
    CookieServiceServer.getAccountCookie(req.cookies),
    CookieServiceServer.getUserTypeCookie(req.cookies),
    CookieServiceServer.getProfileCookie(req.cookies),
    CookieServiceServer.getUserProfilesCookie(req.cookies),
    CookieServiceServer.getAccountLevelCookie(req.cookies),
  ];

  let allGood = true;

  cookiesToCheck.forEach((val) => {
    if (!val) {
      allGood = false;
    }
  });

  if (!allGood) {
    Sentry.captureMessage('Middleware cookie refresh', {
      extra: {
        values: cookiesToCheck.join(','),
        path,
      },
    });

    const accountId = extractAccountIdFromToken(authenticationToken);

    const accountResponse = await callbackAccount(
      accountId,
      cookies,
      authenticationToken,
      volatileToken,
      path,
    );

    if (accountResponse.status === 'error') {
      return null;
    }

    const type = responseExtractType(accountResponse);

    Sentry.setUser({ id: accountId, type });

    CookieServiceServer.setAuthenticationCookie(authenticationToken, cookies);
    CookieServiceServer.setVolatileCookie(volatileToken, cookies);
    CookieServiceServer.setExpiresCookie(
      extractExpiresFromAuthenticationToken(authenticationToken),
      cookies,
    );
    CookieServiceServer.setAccountCookie(accountId, cookies);
    CookieServiceServer.setUserTypeCookie(type, cookies);
    CookieServiceServer.setProfileCookie(
      responseExtractProfileId(accountResponse),
      cookies,
    );
    CookieServiceServer.setUserProfilesCookie(
      responseExtractProfiles(accountResponse),
      cookies,
    );
    CookieServiceServer.setAccountLevelCookie(
      responseAccountLevel(accountResponse),
      cookies,
    );
    CookieServiceServer.setUpgradeCookie(
      getUpgradeValue(accountResponse.links?.account_level),
      cookies,
    );
  }

  return true;
};
