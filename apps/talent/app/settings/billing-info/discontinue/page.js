import DiscontinueClient from './_client';
import { cookies, headers } from 'next/headers';
import { checkIsPasswordSet } from '@utils/authHelper';
import { CookieServiceServer } from '@services/cookieServiceServer';
import Api from '@services/api';
import { extractResult } from '@utils/extractPromiseResult';

export const metadata = {
  title: 'Discontinue your subscription',
};

export default async function DiscontinuePage() {
  const cookieStore = await cookies();
  const headerStore = await headers();
  const isPasswordSet = checkIsPasswordSet(
    CookieServiceServer.getAuthenticationCookie(cookieStore),
  );
  const accountId = CookieServiceServer.getAccountCookie(cookieStore);
  const resolvedUrl = '/settings/billing-info/discontinue';

  const paths = [
    `/accounts/${accountId}/identities`,
    `/accounts/${accountId}/levels/last`,
  ];

  const results = await Promise.allSettled(
    paths.map((path) =>
      Api.serverside(path, cookieStore, headerStore, resolvedUrl),
    ),
  );

  const [identities, levels] = results.map((result) =>
    extractResult(result, {}),
  );

  return (
    <DiscontinueClient
      isPasswordSet={isPasswordSet}
      identifier={identities?.items?.[0]?.identifier || ''}
      endDate={levels.end}
    />
  );
}
