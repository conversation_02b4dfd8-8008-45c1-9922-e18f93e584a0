import styles from '../../styles/reviews.module.scss';
import {
  AnnouncementBanner,
  ArticleBanner,
  Breadcrumbs,
  Button,
  FeaturedTalent,
  ModalContactTalent,
  ModalProfile,
  ModalReview,
  Paginator,
  ReviewCard,
  Seo,
} from '../../components';
import React, { useEffect, useState } from 'react';
import { useViewport } from '../../utils/useViewport';
import Masonry from 'react-masonry-css';
import { MainLayout, PageLayout } from '../../components/Layouts';
import { withRouter } from 'next/router';
import Api from '../../services/api';
import { isInViewPort } from '../../utils/isInViewPort';
import seoPageProps from '../../utils/seoPageProps';
import { useAuth } from '../../contexts/AuthContext';
import ApiNoCache from '../../services/apiNoCache';
import { CookieService } from '../../services/cookieService';
import { formatAnnouncement } from '../../utils/formatAnnouncement';
import { isMobile } from '../../utils/isMobile';
import Link from 'next/link';
import { getGatewayHeaders, getHeaders } from '../../utils/headerHelper';
import { sharedServerSideProps } from '../../utils/sharedServerSideProps';
import { fetchArticles } from '../../services/endpoints/articles';
import { ARTICLE_CATEGORY } from '../../constants/articles';
import { Amp } from '../../services/amp';

export const getServerSideProps = async ({ req, query, resolvedUrl, res }) => {
  const isTalent = CookieService.getUserTypeCookie(req, res) === 'talent';
  const page = parseInt(query.page?.replace('page-', ''));
  const limit = query.limit || 8;
  const reviewResponse = isTalent
    ? (
        await ApiNoCache.serverGateway(
          `/testimonials?page=${page}&limit=${limit}`,
          getGatewayHeaders(resolvedUrl),
          req,
          res,
        )
      ).data
    : await Api.serverAPIRoute(
        `/reviews/testimonials?page=${page}&limit=${limit}`,
        null,
        getHeaders(req, resolvedUrl),
      );

  const profileResponse = await Api.serverAPIRoute(
    `/reviews/profiles`,
    null,
    getHeaders(req, resolvedUrl),
  );
  const articlesResponse = await fetchArticles({
    limit: 5,
    category: ARTICLE_CATEGORY.lessons,
  });
  const countResponse = await Api.serverAPIRoute(
    `/reviews/testimonials?page=1&limit=1`,
    null,
    getHeaders(req, resolvedUrl),
  );

  const maximumPage = Math.ceil(countResponse.count / limit);

  if (maximumPage && page > maximumPage) {
    return {
      redirect: {
        destination:
          maximumPage === 1 ? `/reviews` : `/reviews/page-${maximumPage}`,
        permanent: false,
      },
    };
  }

  if (page <= 0) {
    return {
      notFound: true,
    };
  }

  if (req.url.includes('page-1') && page === 1) {
    return {
      redirect: {
        destination: req.url.replace(`page-1`, ``),
        permanent: true,
      },
    };
  }

  const accountLevel = CookieService.getAccountLevelCookie(req, res);

  let formattedAnnouncement = null;

  if (accountLevel?.isPaidOrDelayed) {
    const announcements = (
      await Api.serverAPIRoute(
        `/promotions?types=Announcements&targets=talent-is-paid&limit=1`,
        null,
        getHeaders(req, resolvedUrl),
      )
    ).data;
    const announcement = announcements ? announcements[0] : null;

    formattedAnnouncement = formatAnnouncement(announcement);
  }
  const isMobileFromUserAgent = isMobile(req.headers['user-agent']);
  const sharedProps = await sharedServerSideProps(req, res);

  return {
    props: {
      ...sharedProps,
      initialReviews: reviewResponse.items,
      reviewCount: reviewResponse.count,
      profiles: profileResponse.items,
      articles: articlesResponse.items || [],
      initialReviewQuery: { page, limit },
      announcement: formattedAnnouncement,
      seoPage: {
        ...(await seoPageProps(resolvedUrl)),
        ogImageUrl: `${process.env.baseUrl}/assets/meta/reviews.webp`,
      },
      defaultIsMobile: isMobileFromUserAgent,
    },
  };
};

function Reviews({
  initialReviews,
  reviewCount,
  articles,
  profiles,
  router,
  initialReviewQuery,
  announcement,
  seoPage,
  defaultIsMobile,
}) {
  const [reviews, setReviews] = useState(initialReviews);
  const [activeUserProfile, setActiveUserProfile] = useState({});
  const [showProfileModal, setShowProfileModal] = useState(false);
  const [page, setPage] = useState(initialReviewQuery.page);
  const { width } = useViewport();
  const [showContactTalentModal, setShowContactTalentModal] = useState(false);
  const [showReviewModal, setShowReviewModal] = useState(false);
  const [selectedProfileId, setSelectedProfileId] = useState(null);
  const [isMobile, setIsMobile] = useState(defaultIsMobile);
  const { userType, profileId } = useAuth();

  const loadReviews = async (event, value) => {
    event?.stopPropagation();
    event?.preventDefault();

    const query = { limit: 8, page: value };
    const newReviews = await fetchReviews(query.page, query.limit);

    setReviews(newReviews.items);
    setPage(value);

    if (value === 1) {
      router.push('/reviews');
    } else {
      router.push('/reviews/page-' + value);
    }
  };

  const openProfileModal = (id) => {
    setSelectedProfileId(id);
    setShowProfileModal(true);
  };

  const closeProfileModal = () => {
    setShowProfileModal(false);
    setSelectedProfileId(null);
  };

  const openReviewModal = async () => {
    const profile = await fetchProfile(profileId);

    setActiveUserProfile(profile);
    setShowReviewModal(true);
  };

  const closeReviewModal = async (update) => {
    setShowReviewModal(false);
    setActiveUserProfile(null);

    if (update === true) {
      await loadReviews(null, 1);
    }
  };

  const openContactTalentModal = () => {
    setShowContactTalentModal(true);
  };

  const closeContactTalentModal = () => {
    setShowContactTalentModal(false);
  };

  const fetchProfile = async (id) => {
    return await Api.clientAPIRoute(`/profiles/${id}`);
  };

  const fetchReviews = async (page, limit) => {
    return userType === 'talent'
      ? (
          await ApiNoCache.clientGateway(
            `/testimonials?page=${page}&limit=${limit}`,
          )
        ).data
      : await Api.clientAPIRoute(
          `/reviews/testimonials?page=${page}&limit=${limit}`,
        );
  };

  useEffect(() => {
    if (width) {
      setIsMobile(width < 1024);
    }
  }, [width]);

  useEffect(() => {
    Amp.track(Amp.events.viewReviews, {
      page: initialReviewQuery.page,
    });
  }, [initialReviewQuery.page]);

  return (
    <MainLayout
      isDefaultHeaderVisible
      isMobileMenuVisible
      isUserMenuVisible
      isSaleHeaderVisible
      isMobileSubscribeHeaderVisible
    >
      {isMobile && (
        <>
          <div className={styles['breadcrumbs-mobile']}>
            <Breadcrumbs crumbs={[{ text: 'allcasting Reviews' }]} />
          </div>
          {announcement && <AnnouncementBanner announcement={announcement} />}
          <ArticleBanner />
        </>
      )}
      <PageLayout>
        {!isMobile && (
          <>
            {announcement && <AnnouncementBanner announcement={announcement} />}
            <ArticleBanner />
            <div className={styles['breadcrumbs-desktop']}>
              <Breadcrumbs crumbs={[{ text: 'allcasting Reviews' }]} />
            </div>
          </>
        )}
        <section className={styles['reviews-section']}>
          <div className={styles['reviews-container']}>
            <div className={styles['reviews-heading']}>
              <div>
                <span>{reviewCount} </span>
                {page > 1 ? (
                  <Seo
                    seoPage={seoPage}
                    overrideTitle={`Reviews ● allcasting - page ${page}`}
                    overrideDescription={`Reviews ● allcasting - page ${page}`}
                    overrideH1={`allcasting Reviews - page ${page}`}
                  />
                ) : (
                  <Seo seoPage={seoPage} />
                )}
              </div>
              {userType === 'talent' && (
                <div>
                  <Button
                    minWidth={'200px'}
                    kind="secondary"
                    color={'blue'}
                    label={'Leave your review'}
                    onClick={openReviewModal}
                  />
                </div>
              )}
            </div>
            <Masonry
              breakpointCols={isInViewPort(width, 'tablet', 'min') ? 2 : 1}
              className={styles['masonry-grid']}
              columnClassName={styles['masonry-grid-column']}
            >
              {!!reviews &&
                reviews.map(({ id, rating, content, author, published_at }) => (
                  <ReviewCard
                    key={id}
                    title={`${author.firstname} ${author.lastname}`}
                    subTitle={
                      author.type === 'agent'
                        ? 'Casting Professional'
                        : 'Talent'
                    }
                    date={published_at}
                    description={content
                      .replace(/(<([^>]+)>)/gi, '')
                      .replace(' ￼', ' ')}
                    imageSrc={
                      author.title_photo_url ||
                      `/assets/placeholders/circle-${
                        author.gender?.title.toLowerCase() || 'male'
                      }-headshot.svg`
                    }
                    rating={rating}
                    openProfilePopup={() => openProfileModal(author.id)}
                    isClickable
                  />
                ))}
            </Masonry>
            <Paginator
              page={page}
              perPage={initialReviewQuery.limit}
              total={reviewCount}
              prefixUrl={'/reviews/'}
              prefixPage={'page-'}
              handleChange={loadReviews}
            />
          </div>
          <div className={styles['featured-container']}>
            <div className={styles['featured-heading']}>
              <h3 className={styles['featured-title']}>Featured Talent</h3>
            </div>
            <div className={styles['featured-talent']}>
              {profiles &&
                profiles.map(({ id, rating, title_photo_url }) => (
                  <FeaturedTalent
                    key={id}
                    rating={rating?.toLocaleString('en-EN')}
                    imageSrc={title_photo_url}
                    openProfilePopup={() => openProfileModal(id)}
                  />
                ))}
            </div>
            <div className={styles['article-container']}>
              {articles &&
                articles.map((article) => (
                  <Link
                    key={article.id}
                    href={`/blog/${article.categories[0].slug}/${article.slug}`}
                    className={styles['article-link']}
                  >
                    <img
                      className={styles['article-img']}
                      src={article.thumbnailImage?.size5?.href}
                      width={310}
                      alt=""
                    />
                  </Link>
                ))}
            </div>
          </div>
          {showProfileModal && (
            <ModalProfile
              id={selectedProfileId}
              onClose={closeProfileModal}
              onOpenContactTalentModal={openContactTalentModal}
            />
          )}
          {showContactTalentModal && (
            <ModalContactTalent onClose={closeContactTalentModal} />
          )}
          {showReviewModal && (
            <ModalReview
              onClose={closeReviewModal}
              profileId={profileId}
              imgSrc={
                activeUserProfile.title_photo_url ||
                `/assets/placeholders/circle-${
                  activeUserProfile.gender?.title?.toLowerCase() || 'male'
                }-headshot.svg`
              }
            />
          )}
        </section>
      </PageLayout>
    </MainLayout>
  );
}

export default withRouter(Reviews);
