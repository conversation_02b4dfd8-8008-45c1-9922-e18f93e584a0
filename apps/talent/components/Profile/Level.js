'use client';
import styles from './Level.module.scss';
import { attributeLevel } from '@constants/attributeLevel';
import React from 'react';

const Level = ({ value, max }) => {
  return (
    <div className={styles['attribute-level']}>
      {[...Array(attributeLevel[value].value)].map((e, i) => (
        <span key={i} className={styles['filled']}></span>
      ))}
      {[...Array(max - attributeLevel[value].value)].map((e, i) => (
        <span key={i} className={styles['empty']}></span>
      ))}
    </div>
  );
};

export default Level;
