import LocationClient from './_client';
import { cookies, headers } from 'next/headers';
import { CookieServiceServer } from '@services/cookieServiceServer';
import Api from '@services/api';
import { getResolvedUrl } from '@utils/apiHelper';

export const metadata = {
  title: 'Location',
};

export default async function LocationPage(req) {
  const searchParams = await req.searchParams;
  const resolvedUrl = getResolvedUrl('/settings/location', searchParams);
  const cookieStore = await cookies();
  const headerStore = await headers();
  const accountId = CookieServiceServer.getAccountCookie(cookieStore);

  const data = await Api.serverside(
    `/accounts/${accountId}/location`,
    cookieStore,
    headerStore,
    resolvedUrl,
  );

  const zip = data.links?.zip?.code || '';

  const location = zip
    ? `${data.links?.city?.title}, ${data.links?.state?.code}`
    : '';

  return <LocationClient zip={zip} location={location} />;
}
