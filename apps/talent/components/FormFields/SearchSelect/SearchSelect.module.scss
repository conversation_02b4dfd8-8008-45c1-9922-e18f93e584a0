@use '@styles/variables' as *;

@keyframes growOut {
  0% {
    transform: scale(0);
  }

  100% {
    transform: scale(1);
  }
}

.select-container {
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
  height: 50px;
  justify-content: flex-end;
}

.focus-border {
  width: 100%;
  position: absolute;
  bottom: -1px;

  &::before {
    display: block;
    border-bottom: 3px solid $black;
    content: '';
    transition: transform 300ms cubic-bezier(0, 0, 0.2, 1) 0ms;
    transform: scaleX(0);
  }
}

.select-field {
  width: 100%;
  border-bottom: 1px solid $grey-80;
  padding: 16px 8px 4px 0;
  font-size: 20px;
  cursor: pointer;
  line-height: 1.1;
  background-color: $white;
  background-image: $gradient-ghost-white;
  align-items: center;
  position: relative;
  display: flex;

  &.open {
    .focus-border::before {
      transform: scaleX(1);
    }
  }

  &:hover,
  &:has(.select:focus) {
    .focus-border::before {
      transform: scaleX(1);
    }
  }
}

.select {
  appearance: none;
  background-color: transparent;
  border: none;
  padding: 0 2px 0 0;
  margin: 0;
  width: 100%;
  font-size: 16px;
  cursor: inherit;
  outline: none;
  font-weight: 600;
  text-overflow: ellipsis;

  &:disabled {
    color: $black;
  }
}

.label {
  position: absolute;
  bottom: 5px;
  left: 0;
  margin: auto;
  cursor: pointer;
  font-size: 16px;
  font-weight: 300;
  color: $black;
  transform-origin: left;
  transition: all 100ms ease-in-out;
  line-height: 20px;

  &.floating-label {
    font-size: 20px;
    font-weight: 600;
    top: 1px;
    transform: scale(0.7) translateY(-50%);
  }
}

.options {
  min-width: 100%;
  position: absolute;
  top: 42px;
  background-color: white;
  border: 1px solid $grey-40;
  max-height: 256px;
  overflow: auto;
  overflow-x: hidden;
  z-index: 100;
  animation: growOut 200ms ease-in-out forwards;
  transform-origin: top center;
  border-radius: 0 0 10px 10px;
  box-shadow: $shadow-box-dropdown;
}

.option {
  font-weight: 300;
  font-size: 14px;
  padding: 7px $space-10;
  min-height: 30px;

  &:hover,
  &:focus {
    background: $grey-10;
    outline: none;
  }
}

.selected-option,
.selected-option:hover,
.selected-option:focus {
  color: $black;
  font-weight: 600;
  background-color: $grey-10;
}

.button-add {
  color: $blue-100;
  cursor: pointer;
  background-color: transparent;
  border: none;
  font-weight: 600;
  font-size: 14px;
  padding: 0;
}

.select-trigger {
  width: 10px;
  height: 6px;
  background: url('#{$assetUrl}/assets/icons/icon-caret-down.svg') center center
    no-repeat;
  background-size: cover;
  justify-self: flex-end;
  transition: all 200ms ease-in-out;

  &.open {
    transform: rotate(180deg);
  }
}

.hint {
  align-items: center;
  color: $grey-80;
  justify-content: space-between;
  display: flex;
  font-size: 12px;
  font-weight: 300;
  text-align: initial;
  width: 100%;
  position: absolute;
  top: 54px;
}

.counter {
  color: $black;
}
