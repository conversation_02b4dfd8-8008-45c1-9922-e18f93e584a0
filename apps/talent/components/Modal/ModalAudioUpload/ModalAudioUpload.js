'use client';
import React, { memo, useState } from 'react';
import { AudioPlayer, Button, Input, Loading, Modal } from '@components';
import styles from './ModalAudioUpload.module.scss';
import { useProfileContext } from '@contexts/ProfileContext';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { ErrorMessage } from '@constants/form';

const ModalAudioUpload = ({
  onClose,
  audio,
  onReplaceAudio,
  id,
  isEditing,
  defaultTitle = '',
  isMobileFromUserAgent,
}) => {
  const { uploadAudio, updateAudio } = useProfileContext();
  const [loading, setLoading] = useState(false);

  const formik = useFormik({
    initialValues: {
      title: defaultTitle || '',
    },
    onSubmit: async (values) => {
      setLoading(true);
      id
        ? await updateAudio(values.title, audio, id)
        : await uploadAudio(values.title, audio);
      formik.resetForm({ values });
      setLoading(false);
      onClose();
    },
    validationSchema: Yup.object({
      title: Yup.string().required(ErrorMessage.TitleRequired),
    }),
  });

  return (
    <Modal
      classNameContainer={styles['audio-upload-modal']}
      classNameOverlay={styles['audio-upload-modal-overlay']}
      onClose={onClose}
      backdropClose={!loading}
    >
      <form
        className={styles['audio-upload-modal-content']}
        onSubmit={formik.handleSubmit}
      >
        <Input
          name="title"
          placeholder="Title"
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          value={formik.values.title}
          isTouched={formik.touched.title}
          error={formik.errors.title}
        />
        {audio && (
          <div className={styles['audio-container']}>
            <AudioPlayer
              url={!isEditing && id ? audio : URL.createObjectURL(audio)}
              isMobileFromUserAgent={isMobileFromUserAgent}
            />
          </div>
        )}
        <span className={styles['replace-btn']} onClick={onReplaceAudio}>
          Replace current file
        </span>
        <div>
          {loading ? (
            <Loading padding="11px 0" />
          ) : (
            <Button
              label="Save"
              minWidth="200px"
              type="submit"
              disabled={!(formik.isValid && (formik.dirty || isEditing))}
            />
          )}
        </div>
      </form>
    </Modal>
  );
};

export default memo(ModalAudioUpload);
