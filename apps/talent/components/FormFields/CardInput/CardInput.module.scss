@use '@styles/variables' as *;

.card-input-container {
  position: relative;
}

.card-icons-outer-container {
  position: absolute;
  top: 10px;
  bottom: 0;
  right: 0;
  width: 64px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 46px;
}

.card-icons-container {
  position: relative;
  width: 64px;
  display: flex;
  align-items: center;
  gap: $space-5;
  justify-content: flex-end;

  &:has(.hidden) {
    gap: 0;
    transition: all 0s ease;
  }
}

.card-icon-container {
  z-index: 1;
  position: relative;
  transition: width 0.5s ease;
  overflow: hidden;
  width: 28px;

  &.hidden {
    width: 0;
  }
}

.card-field-icon {
  z-index: 1;
  position: relative;
}

.card-input-field {
  position: relative;
  width: 100%;
}
