import ContactsClient from './_client';
import { cookies, headers } from 'next/headers';
import { CookieServiceServer } from '@services/cookieServiceServer';
import { notFound, redirect } from 'next/navigation';
import { CONTACTS_LIMIT } from '@constants/pagination';
import Api from '@services/api';
import { extractResult } from '@utils/extractPromiseResult';
import { getResolvedUrl } from '@utils/apiHelper';
import { formatGroups } from '@utils/contactHelpers';

export const metadata = {
  title: 'Contacts',
};

export default async function ContactsPage(req) {
  const cookieStore = await cookies();
  const headerStore = await headers();
  const params = await req.params;
  const searchParams = await req.searchParams;
  const { id } = params;
  const resolvedUrl = getResolvedUrl(`/contacts/${params.id}`, searchParams);
  const profileId = CookieServiceServer.getProfileCookie(cookieStore);
  const page = searchParams.page ? Number(searchParams.page) : 1;

  if (page <= 0 || isNaN(page)) {
    notFound();
  }

  if (searchParams.page === '1') {
    redirect(`/contacts/${id}`);
  }

  const paths = [
    `/profiles/${profileId}/contact-groups`,
    `/profiles/${profileId}/contacts?page=1&limit=1`,
    `/contact-groups/${id}?page=${page}&limit=${CONTACTS_LIMIT}`,
    '/clientstatus/info',
  ];

  const results = await Promise.allSettled(
    paths.map((path) =>
      Api.serverside(path, cookieStore, headerStore, resolvedUrl),
    ),
  );

  const [groups, contacts, group, clientStatus] = results.map((result) =>
    extractResult(result, { items: [], count: 0 }),
  );

  return (
    <ContactsClient
      groups={formatGroups(groups.items || [])}
      group={group}
      canContactTalent={
        clientStatus.info?.allowed_to?.hasOwnProperty('contact_talent') || false
      }
      contactsTotal={contacts.count || 0}
      page={page}
    />
  );
}
