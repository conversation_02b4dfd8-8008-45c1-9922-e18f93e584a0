import RequestsClient from './_client';
import Api from '@services/api';
import { extractResult } from '@utils/extractPromiseResult';
import { getResolvedUrl } from '@utils/apiHelper';
import { cookies, headers } from 'next/headers';
import { CookieServiceServer } from '@services/cookieServiceServer';
import {
  formatCandidates,
  formatSearchedTalents,
  getActiveCastingCall,
  getActiveRole,
  getCastingCallGroups,
  getQueryExtension,
  getRecommendedQueryFormattingRules,
  getRoleCriteria,
  prepareCastingCalls,
} from '@utils/submissionsHelper';
import { TABS } from '@constants/submissions';
import {
  ELASTIC_PAGE_LIMIT,
  SUBMISSIONS_LIMIT,
  TALENTS_LIMIT,
} from '@constants/pagination';
import { getContent, getFilters } from '@utils/talentFilterHelper';
import { redirect } from 'next/navigation';

export const metadata = {
  title: 'Requests',
};

export default async function RequestsPage(req) {
  const params = await req.params;
  const searchParams = await req.searchParams;
  const resolvedUrl = getResolvedUrl(
    `requests`,
    { castingcall: params.castingCallId, role: params.roleId },
    searchParams,
  );
  const cookieStore = await cookies();
  const headerStore = await headers();
  const userProfiles = CookieServiceServer.getUserProfilesCookie(cookieStore);
  const clientId = userProfiles.length ? userProfiles[0].clientId || 0 : 0;
  const castingCallId = params.castingCallId ? Number(params.castingCallId) : 0;
  const profileId = CookieServiceServer.getProfileCookie(cookieStore);
  const roleId = params.roleId ? Number(params.roleId) : 0;
  const page = searchParams.page ? Number(searchParams.page) : 1;

  const { tab } = searchParams;

  const paths = [
    `/profiles/${profileId}/contact-groups`,
    `/profiles/${profileId}/contacts`,
    '/clientstatus/info',
    `/accounts/${clientId}/castingcalls?es=1&context[]=for_submissions&filter[state]=active&filter[status][]=approved`,
    `/accounts/${clientId}/castingcalls?es=1&context[]=for_submissions&filter[status][]=approved`,
    `/accounts/${clientId}/castingcalls?es=1&context[]=for_submissions&filter[status][]=new`,
    `/accounts/${clientId}/castingcalls?es=1&context[]=for_submissions&filter[status][]=hold`,
    `/accounts/${clientId}/castingcalls?es=1&context[]=for_submissions&filter[status][]=edited`,
    `/accounts/${clientId}/castingcalls?es=1&context[]=for_submissions&filter[status][]=declined`,
    `/accounts/${clientId}/castingcalls?es=1&context[]=for_submissions&filter[status][]=deactivated`,
  ];

  const results = await Promise.allSettled(
    paths.map((path) =>
      Api.serverside(path, cookieStore, headerStore, resolvedUrl),
    ),
  );

  const [
    groupsResponse,
    contactsResponse,
    clientStatusResponse,
    runningCastingCallsResponse,
    approvedCastingCallsResponse,
    newCastingCallsResponse,
    holdCastingCallsResponse,
    editedCastingCallsResponse,
    declinedCastingCallsResponse,
    deactivatedCastingCallsResponse,
  ] = results.map((result) => extractResult(result, {}));

  const castingCalls = prepareCastingCalls(
    runningCastingCallsResponse,
    approvedCastingCallsResponse,
    newCastingCallsResponse,
    holdCastingCallsResponse,
    editedCastingCallsResponse,
    declinedCastingCallsResponse,
    deactivatedCastingCallsResponse,
  );

  const castingCallGroups = getCastingCallGroups(castingCalls);
  const activeCastingCall = getActiveCastingCall(
    castingCalls,
    castingCallGroups,
    castingCallId,
  );
  const activeRole = getActiveRole(activeCastingCall, roleId);

  let candidateData = {};

  if (activeCastingCall && activeRole) {
    const recommendedResponse = await Api.serverside(
      `/roles/${activeRole.id}/recommendations/profiles?page=1&limit=1`,
      cookieStore,
      headerStore,
      resolvedUrl,
    );

    activeRole.recommended = recommendedResponse.count || 0;

    if (tab === TABS.Shortlist || tab === TABS.Declined) {
      const response = await Api.serverside(
        `/roles/${activeRole.id}/candidates?limit=${SUBMISSIONS_LIMIT}&page=${page}&status=${tab === TABS.Declined ? TABS.Declined : 'accepted'}`,
        cookieStore,
        headerStore,
        resolvedUrl,
      );

      candidateData = {
        candidates: formatCandidates(response.items || []),
        candidatesTotal: response.count || 0,
      };
    } else if (tab === TABS.Invited) {
      const response = await Api.serverside(
        `/casting-calls/${activeCastingCall.id}/invites?limit=${SUBMISSIONS_LIMIT}&page=${page}&filter[role_id]=${activeRole.id}`,
        cookieStore,
        headerStore,
        resolvedUrl,
      );

      const candidatesTotal = response.count || 0;
      const pagesTotal = Math.ceil(candidatesTotal / SUBMISSIONS_LIMIT);

      candidateData = {
        candidates: formatCandidates(response.items || []),
        candidatesTotal,
        candidatesTotalLimit: pagesTotal * TALENTS_LIMIT,
      };
    } else if (tab === TABS.Recommended) {
      const extendedCastingCall = await Api.serverside(
        `/calls/get?id=${activeCastingCall.id}`,
        cookieStore,
        headerStore,
        resolvedUrl,
      );

      const extendedRole = extendedCastingCall.data.roles.find(
        (role) => role.id === activeRole.id,
      );

      const roleCriteria = getRoleCriteria(
        extendedRole,
        extendedCastingCall.data.address,
      );

      const recommendedQueryFormattingRules =
        getRecommendedQueryFormattingRules(roleCriteria);

      const response = await getContent(
        searchParams,
        resolvedUrl,
        cookieStore,
        headerStore,
        CookieServiceServer.getAccountCookie(cookieStore),
        getQueryExtension(
          tab,
          activeRole.id,
          CookieServiceServer.getProfileCookie(cookieStore),
        ),
        true,
        activeRole.id,
        roleCriteria,
        recommendedQueryFormattingRules,
      );

      const candidatesTotal = response.talents?.count || 0;
      const pagesTotal = Math.ceil(candidatesTotal / TALENTS_LIMIT);
      const maximumPage =
        ELASTIC_PAGE_LIMIT > pagesTotal ? pagesTotal : ELASTIC_PAGE_LIMIT;

      candidateData = {
        candidates: formatSearchedTalents(response.talents?.items || []),
        candidatesTotal,
        candidatesTotalLimit: maximumPage * TALENTS_LIMIT,
        defaultFilters: await getFilters(
          resolvedUrl,
          cookieStore,
          headerStore,
          CookieServiceServer.getAccountCookie(cookieStore),
          true,
          roleCriteria,
        ),
        activeFilters: response.filters,
        cities: response.cities,
        recommendedQueryFormattingRules,
      };

      if (maximumPage && page > maximumPage) {
        redirect(
          resolvedUrl.replace(
            `page=${page}`,
            maximumPage === 1 ? `` : `page${maximumPage}`,
          ),
        );
      }
    } else {
      const response = await getContent(
        searchParams,
        resolvedUrl,
        cookieStore,
        headerStore,
        CookieServiceServer.getAccountCookie(cookieStore),
        getQueryExtension(
          tab || TABS.Applied,
          activeRole.id,
          CookieServiceServer.getProfileCookie(cookieStore),
        ),
      );

      const candidatesTotal = response.talents?.count || 0;
      const pagesTotal = Math.ceil(candidatesTotal / TALENTS_LIMIT);
      const maximumPage =
        ELASTIC_PAGE_LIMIT > pagesTotal ? pagesTotal : ELASTIC_PAGE_LIMIT;

      candidateData = {
        candidates: formatSearchedTalents(response.talents?.items || []),
        candidatesTotal,
        candidatesTotalLimit: maximumPage * TALENTS_LIMIT,
        defaultFilters: await getFilters(
          resolvedUrl,
          cookieStore,
          headerStore,
          CookieServiceServer.getAccountCookie(cookieStore),
        ),
        activeFilters: response.filters,
        cities: response.cities,
      };

      if (maximumPage && page > maximumPage) {
        redirect(
          resolvedUrl.replace(
            `page=${page}`,
            maximumPage === 1 ? `` : `page${maximumPage}`,
          ),
        );
      }
    }
  }

  return (
    <RequestsClient
      tab={tab || TABS.Applied}
      groups={groupsResponse}
      contacts={contactsResponse}
      canContactTalent={
        clientStatusResponse.info?.allowed_to?.hasOwnProperty(
          'contact_talent',
        ) || false
      }
      castingCalls={castingCallGroups}
      role={activeRole}
      candidates={candidateData.candidates || []}
      page={page}
      candidatesTotal={candidateData.candidatesTotal || 0}
      candidatesTotalLimit={candidateData.candidatesTotalLimit || 0}
      clientId={clientId}
      defaultFilters={candidateData.defaultFilters || {}}
      cities={candidateData.cities || {}}
      activeFilters={candidateData.activeFilters || {}}
      recommendedQueryFormattingRules={
        candidateData.recommendedQueryFormattingRules || {}
      }
      activeCastingCall={activeCastingCall}
    />
  );
}
