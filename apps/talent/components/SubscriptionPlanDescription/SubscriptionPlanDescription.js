'use client';
import styles from './SubscriptionPlanDescription.module.scss';
import cn from 'classnames';
import Image from 'next/image';
import React, { useRef } from 'react';

const SubscriptionPlanDescription = ({ features, fullWidth = false }) => {
  const planSelectFeaturesRef = useRef(null);

  return (
    <div
      ref={planSelectFeaturesRef}
      className={cn(
        styles['profile-features-table'],
        fullWidth ? styles['full-width'] : '',
      )}
    >
      <h1 className={styles['profile-features-header-mobile']}>
        Experience Premium
      </h1>
      <div
        className={cn(
          styles['profile-features-row'],
          styles['profile-features-header-row'],
        )}
      >
        <div className={styles['profile-features-header-premium']}>
          Experience Premium
        </div>
        <div className={styles['profile-features-header-members']}>Members</div>
        <div className={styles['profile-features-header-subscribers']}>
          <span>SUBSCRIBERS</span>
        </div>
      </div>
      {features.map(({ title, basic, premium }) => (
        <div className={styles['profile-features-row']} key={title}>
          <div>{title}</div>
          <div className={styles['profile-feature-checkmark-container']}>
            {basic && (
              <Image
                className={styles['profile-feature-checkmark-basic']}
                src="/assets/icons/icon-checkmark.svg"
                width={14}
                height={12}
                alt="icon checkmark"
              />
            )}
          </div>
          <div className={styles['profile-feature-checkmark-container']}>
            {premium && (
              <Image
                className={styles['profile-feature-checkmark-premium']}
                src="/assets/icons/icon-checkmark.svg"
                width={14}
                height={12}
                alt="icon checkmark"
              />
            )}
          </div>
        </div>
      ))}
    </div>
  );
};

export default SubscriptionPlanDescription;
