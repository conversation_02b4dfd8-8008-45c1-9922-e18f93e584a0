'use client';
import { useCallback, useEffect } from 'react';
import styles from '@styles/checkout.module.scss';
import { useSale } from '@contexts/SaleContext';
import { useAuth } from '@contexts/AuthContext';
import { useFeature } from '@contexts/FeatureContext';
import { useNotifications } from '@contexts/NotificationContext';
import { Countdown, MainLayout, PaymentForm } from '@components';
import { Amp } from '@services/amp';
import Api from '@services/api';

const CheckoutClient = ({
  pricePlan,
  promoCode,
  billing,
  monthOptions,
  yearOptions,
  email,
  campaign,
  campaignGroup,
  tracking,
  isStripe,
  stripePK,
  stripeClientSecret,
  stripeInvoiceHash,
  isMobile,
  isAndroidDevice,
  isPremium,
  isLifetime,
  experiments = {},
  referer,
  discountId,
  discountAbExperiment,
  isStripeError,
}) => {
  const { saleExpirationTime, showSale, stopSale } = useSale();
  const {
    accountId,
    userProfiles,
    accountLevelVerify,
    getIsProfileOutsideUSA,
  } = useAuth();
  const { paymentMaintenance } = useFeature();
  const { setNotification } = useNotifications();
  const isProfileOutsideUSA = getIsProfileOutsideUSA();

  useEffect(() => {
    if (paymentMaintenance) {
      setNotification({
        type: 'error',
        message:
          'We are undergoing scheduled maintenance. During this time payments will be temporarily unavailable for up to few hours.',
      });
    }
  }, [paymentMaintenance]);

  const handleTrackingEvent = useCallback(async () => {
    const body = new FormData();

    body.append('event_name', 'checkout-page-view');
    body.append('account_id', String(accountId));
    body.append('context[member_level_id]', pricePlan.member_level_id);

    if (discountId) {
      body.append('context[discount_id]', discountId);
    }

    await Api.clientside(`/tracking/events`, {
      body,
      method: 'POST',
    });
  }, [accountId, pricePlan.member_level_id]);

  useEffect(() => {
    if (accountId) {
      Amp.track(Amp.events.viewCheckout, {
        is_premium: isPremium,
        is_lifetime: isLifetime,
        discount_id: discountId,
        ab_experiment: discountAbExperiment,
        sale_interval: pricePlan
          ? `${pricePlan.period} ${pricePlan.period_length}`
          : null,
      });
      handleTrackingEvent();
    }
  }, [handleTrackingEvent, accountId]);

  return (
    <MainLayout
      isCheckoutHeaderVisible
      isPremiumCheckout={isPremium}
      isLifetimeCheckout={
        isLifetime &&
        !referer.includes('/checkout') &&
        !referer.includes('/upgrade')
      }
    >
      <section className={styles['checkout-section']}>
        {isStripeError ? (
          <div className={styles['error-container']}>
            <h1>Sales have closed for today.</h1>
            <p>Please try again tomorrow.</p>
          </div>
        ) : (
          <>
            <div className={styles['checkout-section-header']}>
              <h1 className={styles['checkout-section-header-title']}>
                Complete Payment Info
              </h1>
              <span className={styles['checkout-section-header-description']}>
                Instant Access | Proven Value | Real Results
              </span>
              {showSale && (
                <Countdown
                  expirationTime={saleExpirationTime}
                  onCountdownEnd={stopSale}
                />
              )}
            </div>
            <div className={styles['payment-form-container']}>
              {pricePlan && (
                <PaymentForm
                  period={pricePlan.period}
                  price={pricePlan.price}
                  baseMonthPrice={pricePlan.base_month_price}
                  promoCode={promoCode}
                  nameOnCard={billing.billingName}
                  zip={billing.zip}
                  expMonth={billing.exp_month}
                  expYear={billing.exp_year}
                  memberLevelId={pricePlan.member_level_id}
                  accountId={accountId}
                  monthOptions={monthOptions}
                  yearOptions={yearOptions}
                  clientId={userProfiles[0]?.clientId}
                  email={email}
                  campaign={campaign}
                  campaignGroup={campaignGroup}
                  tracking={tracking}
                  stripePK={stripePK}
                  accountLevelVerify={accountLevelVerify}
                  isStripe={isStripe}
                  stripeClientSecret={stripeClientSecret}
                  stripeInvoiceHash={stripeInvoiceHash}
                  isMobile={isMobile}
                  isAndroidDevice={isAndroidDevice}
                  isProfileOutsideUSA={isProfileOutsideUSA}
                  isPremium={isPremium}
                  isLifetime={isLifetime}
                />
              )}
            </div>
          </>
        )}
      </section>
    </MainLayout>
  );
};

export default CheckoutClient;
