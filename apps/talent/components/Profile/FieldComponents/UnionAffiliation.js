'use client';
import React, { memo, useEffect, useState } from 'react';
import styles from '../ProfileInfo.module.scss';
import { useNotifications } from '@contexts/NotificationContext';
import { MultiSelect } from '@components';
import Image from 'next/image';
import cn from 'classnames';
import { arePrimitiveArraysEqual } from '@utils/arePrimitiveArraysEqual';
import { Amp } from '@services/amp';
import { EditToggle } from '../EditToggle';
import { ErrorMessage } from '@constants/form';

const UnionAffiliation = ({
  attribute,
  selectedOptions,
  saveAttribute,
  refreshProfileAttributes,
}) => {
  const { setNotification } = useNotifications();
  const [isContainerEditing, setContainerEditing] = useState(false);
  const [selectedIds, setSelectedIds] = useState([]);
  const [loading, setLoading] = useState(false);
  const options =
    attribute.items
      ?.map((item) => ({
        value: item.id,
        title: item.value,
      }))
      .sort((a, b) => a.value - b.value) || [];

  useEffect(() => {
    setSelectedIds([...selectedOptions.map((item) => item.id)]);
  }, []);

  const removeCategory = (id) => {
    setSelectedIds(selectedIds.filter((categoryId) => categoryId !== id));
  };

  const editCategories = () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'edit union affiliation',
      scope: Amp.element.scope.profile,
      section: Amp.element.section.unionAffiliation,
      type: Amp.element.type.button,
    });
    setContainerEditing(true);
  };

  const saveCategories = async () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'save union affiliation',
      scope: Amp.element.scope.profile,
      section: Amp.element.section.unionAffiliation,
      type: Amp.element.type.button,
    });
    if (
      !arePrimitiveArraysEqual(
        options.map((category) => category.id),
        selectedIds,
      )
    ) {
      const formData = {
        ...attribute,
        items: [],
      };

      selectedIds.forEach((id) => {
        formData.items.push({
          id,
          name: attribute.name,
          value: attribute.items.find((i) => i.id === id).value,
        });
      });

      setLoading(true);

      const response = await saveAttribute(formData);

      if (response.status !== 'ok') {
        setNotification({
          type: 'error',
          message: response.message || ErrorMessage.Unexpected,
          timeout: '5000',
        });
      } else {
        await refreshProfileAttributes();
        setContainerEditing(false);
      }

      setLoading(false);
    } else {
      setContainerEditing(false);
    }
  };

  return (
    <>
      {options?.length && (
        <section>
          <div className={styles['profile-section-title']}>
            Union affiliation:
            <EditToggle
              isEditing={isContainerEditing}
              onEdit={editCategories}
              onSave={saveCategories}
              disabled={loading}
            />
          </div>
          {!isContainerEditing ? (
            <>
              {selectedIds.length ? (
                <div className={styles['profile-badge-container']}>
                  {selectedIds.map((id, index) => (
                    <span
                      key={index}
                      className={cn(
                        styles['profile-badge-filled'],
                        styles.clickable,
                      )}
                      onClick={editCategories}
                    >
                      {options.find((option) => option.value === id).title}
                    </span>
                  ))}
                </div>
              ) : (
                <div
                  className={styles['profile-section-empty']}
                  onClick={editCategories}
                >
                  Please add your union affiliation status.
                  <span className={styles['profile-section-empty-add-button']}>
                    Add
                  </span>
                </div>
              )}
            </>
          ) : (
            <>
              <MultiSelect
                options={options}
                name="category"
                placeholder="Choose unions"
                selectedOptions={selectedIds}
                onChange={(selectedOptions) => {
                  setSelectedIds(selectedOptions);
                }}
                hint="You are affiliated to these unions:"
              />
              <div
                className={cn(
                  styles['profile-badge-container'],
                  styles['profile-badge-container-filled'],
                )}
              >
                {selectedIds.map((id, index) => (
                  <div key={index} className={styles['profile-badge-filled']}>
                    {options.find((option) => option.value === id).title}
                    <Image
                      className={styles['delete-button']}
                      onClick={() => removeCategory(id)}
                      src="/assets/icons/icon-close.svg"
                      alt="icon"
                      width={10}
                      height={10}
                    />
                  </div>
                ))}
              </div>
            </>
          )}
        </section>
      )}
    </>
  );
};

export default memo(UnionAffiliation);
