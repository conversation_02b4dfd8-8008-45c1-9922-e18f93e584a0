@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.photo-upload-container {
  width: 100%;
  height: 100%;
  margin: 0;
  border: 1px dashed $grey-40;
  border-radius: 15px;
}

.photo-upload {
  display: flex;
  width: 100%;
  justify-content: space-between;
  flex-direction: column;
  align-items: center;
  height: 100%;
  padding: 20px;
}

.photo-upload-button {
  font-weight: 700;
  font-size: 16px;
  color: $blue-80;
  background-color: $white;
  border: none;
  cursor: pointer;
}

.photo-upload-placeholder {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.photo-upload-description {
  color: $grey-60;
  font-weight: 400;
  font-size: 12px;
  text-align: center;
}

.photo-upload-title {
  color: $grey-100;
  font-weight: 400;
  font-size: 14px;
}

.photo-upload-image-container {
  border-radius: 50%;
  width: fit-content;
  height: fit-content;

  &.preview {
    border: 1px solid $grey-20;
    padding: 2px;
  }
}

.photo-upload-image {
  border-radius: 50%;
  width: 165px;
  height: 165px;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

.photo-upload-success-container {
  display: flex;
  align-items: center;
  gap: 5px;
}

.photo-upload-success-text {
  background-image: $gradient-lime-green;
  color: transparent;
  background-clip: text;
  font-weight: 600;
  font-size: 12px;
  text-align: center;
}
