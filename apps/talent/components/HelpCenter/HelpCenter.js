'use client';
import React, { memo, useEffect, useRef } from 'react';
import cn from 'classnames';
import styles from '../HelpCenter/HelpCenter.module.scss';
import Link from 'next/link';
import {
  companyLinks,
  legalLinks,
} from '@constants/menuLinks/help-center-links';

const HelpCenter = ({ isOpen, onClose }) => {
  const helpCenterRef = useRef();

  useEffect(() => {
    const currenRef = helpCenterRef.current;
    const callback = (event) => {
      event.stopPropagation();
    };

    currenRef.addEventListener('mouseup', callback);

    return () => currenRef.removeEventListener('mouseup', callback);
  }, []);

  return (
    <section
      className={cn(
        styles['sidebar-container'],
        isOpen && styles['sidebar-open'],
      )}
      ref={helpCenterRef}
    >
      <div className={styles.sidebar}>
        <div className={styles.content}>
          <div className={styles.title}>Help</div>
          <ul className={styles['item-list']}>
            <li className={styles['item-list-wrap']}>
              <div className={styles['list-title']}>Legal</div>
              {legalLinks.map(({ title, id, routerLink, fragment }) => (
                <Link
                  key={id}
                  href={routerLink}
                  className={styles['menu-item']}
                  onClick={onClose}
                >
                  <span className={cn(styles.icon, styles[fragment])} />
                  {title}
                </Link>
              ))}
            </li>
            <li className={styles['item-list-wrap']}>
              <div className={styles['list-title']}>company</div>
              {companyLinks.map(({ title, id, routerLink, fragment }) => (
                <Link
                  key={id}
                  href={routerLink}
                  className={styles['menu-item']}
                  onClick={onClose}
                >
                  <span className={cn(styles.icon, styles[fragment])} />
                  {title}
                </Link>
              ))}
            </li>
          </ul>
        </div>
      </div>
    </section>
  );
};

export default memo(HelpCenter);
