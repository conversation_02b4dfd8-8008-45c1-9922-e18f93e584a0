import ReviewsClient from '../_client';
import { cookies, headers } from 'next/headers';
import Api from '@services/api';
import { getResolvedUrl } from '@utils/apiHelper';
import { notFound, redirect } from 'next/navigation';

export const metadata = {
  title: 'Reviews',
};

export default async function ReviewsPage(req) {
  const params = await req.params;
  const searchParams = await req.searchParams;
  const resolvedUrl = getResolvedUrl(`/reviews/${params.page}`, searchParams);
  const cookieStore = await cookies();
  const headerStore = await headers();
  const page = parseInt(params.page?.replace('page-', ''));
  const limit = searchParams.limit || 8;

  const reviewResponse = await Api.serverside(
    `/testimonials?page=${page}&limit=${limit}`,
    cookieStore,
    headerStore,
    resolvedUrl,
  );

  const profileResponse = await Api.serverside(
    `/profiles?rel=talent&limit=15&age=18..any`,
    cookieStore,
    headerStore,
    resolvedUrl,
  );

  const countResponse = await Api.serverside(
    `/testimonials?page=1&limit=1`,
    cookieStore,
    headerStore,
    resolvedUrl,
  );

  const maximumPage = Math.ceil(countResponse.count / limit);

  if (maximumPage && page > maximumPage) {
    redirect(maximumPage === 1 ? `/reviews` : `/reviews/page-${maximumPage}`);
  }

  if (page <= 0) {
    notFound();
  }

  if (resolvedUrl.includes('page-1') && page === 1) {
    redirect(resolvedUrl.replace(`page-1`, ``));
  }

  const clientStatus = await Api.serverside(
    '/clientstatus/info',
    cookieStore,
    headerStore,
    resolvedUrl,
  );

  return (
    <ReviewsClient
      initialReviews={reviewResponse.items}
      reviewCount={reviewResponse.count}
      profiles={profileResponse.items}
      initialReviewQuery={{ page, limit }}
      canContactTalent={
        clientStatus.info?.allowed_to?.hasOwnProperty('contact_talent') || false
      }
    />
  );
}
