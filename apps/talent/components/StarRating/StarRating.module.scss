/* stylelint-disable */

.star-container {
  display: flex;
  gap: 5px;

  .star {
    filter: invert(100%) sepia(0%) saturate(7400%) hue-rotate(276deg)
      brightness(91%) contrast(72%);

    &.selected {
      filter: invert(71%) sepia(97%) saturate(1417%) hue-rotate(359deg)
        brightness(101%) contrast(104%);
    }

    &.dynamic:hover {
      cursor: pointer;
      filter: invert(71%) sepia(97%) saturate(1417%) hue-rotate(359deg)
        brightness(101%) contrast(104%);
    }
  }
}

.star-container-image-rating {
  gap: 2px;

  &.rating-5 {
    .star.selected {
      filter: invert(84%) sepia(15%) saturate(6429%) hue-rotate(1deg)
        brightness(98%) contrast(100%);
    }
  }

  &.rating-1 {
    .star.selected {
      filter: invert(24%) sepia(51%) saturate(4722%) hue-rotate(347deg)
        brightness(106%) contrast(89%);
    }
  }

  &.rating-2 {
    .star.selected {
      filter: invert(24%) sepia(51%) saturate(4722%) hue-rotate(347deg)
        brightness(106%) contrast(89%);

      &:nth-of-type(1) {
        opacity: 0.7;
      }
    }
  }

  &.rating-3 {
    .star.selected {
      filter: invert(74%) sepia(62%) saturate(4458%) hue-rotate(346deg)
        brightness(101%) contrast(93%);

      &:nth-of-type(1) {
        opacity: 0.7;
      }

      &:nth-of-type(2) {
        opacity: 0.8;
      }
    }
  }

  &.rating-4 {
    .star.selected {
      filter: invert(74%) sepia(62%) saturate(4458%) hue-rotate(346deg)
        brightness(101%) contrast(93%);

      &:nth-of-type(1) {
        opacity: 0.6;
      }

      &:nth-of-type(2) {
        opacity: 0.7;
      }

      &:nth-of-type(3) {
        opacity: 0.8;
      }
    }
  }
}

.hint {
  position: absolute;
  right: -7px;
  top: -7px;
}
