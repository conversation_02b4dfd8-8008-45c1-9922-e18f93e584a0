'use client';
import React, { memo, useEffect, useState } from 'react';
import styles from './BoostForm.module.scss';
import Link from 'next/link';
import { Button, Checkbox, Input, Loading, Select } from '@components';
import { useFormik } from 'formik';
import Api from '@services/api';
import * as Yup from 'yup';
import { useNotifications } from '@contexts/NotificationContext';
import { TYPE } from '@constants/castingCalls';
import { ErrorMessage } from '@constants/form';

const BoostForm = ({
  onShowSuccessMessage,
  onClose,
  options,
  profileId,
  isInBoostPage,
  showWarningDefault,
  defaultSelectedOptionValue,
}) => {
  const [showWarning, setShowWarning] = useState(showWarningDefault);
  const [loading, setLoading] = useState(false);
  const { setNotification } = useNotifications();

  const formik = useFormik({
    initialValues: {
      id: defaultSelectedOptionValue || '',
      comment: '',
      acceptCondition: false,
    },
    onSubmit: async (values) => {
      setLoading(true);
      const body = new FormData();

      body.append('comment', values.comment);
      body.append('profile', profileId);

      const response = await Api.clientside(
        `/casting-calls/${values.id}/boost`,
        {
          method: 'POST',
          body,
        },
      );

      if (response.status !== 'ok') {
        setNotification({
          type: 'error',
          timeout: '5000',
          message: response.message || ErrorMessage.Unexpected,
        });
        onClose();
      } else {
        onShowSuccessMessage();
      }

      setLoading(false);
    },
    validationSchema: Yup.object({
      id: Yup.string().required(ErrorMessage.CastingCallRequired),
      comment: Yup.string().max(
        1000,
        ErrorMessage.MaxCharacters.replace('{X}', '1000'),
      ),
      acceptCondition: Yup.bool().test('acceptCondition', '', async (value) => {
        return showWarning ? !!value : true;
      }),
    }),
  });

  useEffect(() => {
    setShowWarning(showWarningDefault);
  }, [showWarningDefault]);

  useEffect(() => {
    formik.validateField('acceptCondition');
    formik.setFieldTouched('acceptCondition');
  }, [showWarning]);

  const onCastingCallSelect = (value) => {
    const option = options.find((option) => option.value === value);

    formik.setFieldValue('id', value);

    switch (true) {
      case option.type !== TYPE.Web && !showWarning:
        setShowWarning(true);
        break;
      case option.type === TYPE.Web && showWarning:
        setShowWarning(false);
        break;
      default:
        break;
    }
  };

  return (
    <div className={styles.container}>
      <span className={styles.title}>Boost your Casting Call</span>
      <div>
        <p className={styles.description}>
          Boost your casting call for free! You&apos;ll get a lot more attention
          to your project.{' '}
          {isInBoostPage ? (
            <span className={styles.link} onClick={onClose}>
              Learn more
            </span>
          ) : (
            <Link className={styles.link} href="/boost">
              Learn more
            </Link>
          )}
        </p>
        <div className={styles['form-container']}>
          <Select
            name="id"
            placeholder="Select Casting Call"
            options={options}
            value={formik.values.id}
            isTouched={formik.touched.id}
            setFormFieldTouched={() => formik.setFieldTouched('id')}
            onChange={onCastingCallSelect}
          />
          <Input
            name="comment"
            placeholder="Additional Requests"
            hint="(optional)"
            charCounter
            charCounterMax={1000}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            value={formik.values.comment}
            isTouched={formik.touched.comment}
            error={formik.errors.comment}
          />
        </div>
        {showWarning && (
          <div className={styles['condition-container']}>
            <Checkbox
              name="acceptCondition"
              onChange={formik.handleChange}
              value={formik.values.acceptCondition}
              error={formik.errors.acceptCondition}
              onBlur={formik.handleBlur}
              isTouched={formik.touched.acceptCondition}
            >
              <span className={styles.condition}>
                By choosing to continue, <b>you agree</b> your casting call will
                be <b>converted into an Easy Apply one</b>.<br />
                Warning: talent who already viewed your casting call will not be
                notified about this.
              </span>
            </Checkbox>
          </div>
        )}
        <div className={styles['btn-container']}>
          {loading ? (
            <Loading minHeight="40px" padding="0" />
          ) : (
            <Button
              label="Request now"
              minWidth="200px"
              shadow={false}
              color="green-gradient"
              onClick={formik.submitForm}
              disabled={!formik.isValid}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default memo(BoostForm);
