@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.profile-feature-checkmark-basic {
  filter: invert(94%) sepia(0%) saturate(4%) hue-rotate(246deg) brightness(77%)
    contrast(89%);
}

.profile-feature-checkmark-premium {
  filter: invert(100%) sepia(22%) saturate(6584%) hue-rotate(74deg)
    brightness(98%) contrast(74%);
}

.profile-features-header-premium {
  font-weight: 300;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;

  @include desktop {
    font-weight: 700;
    font-size: 24px;
  }
}

.profile-features-table {
  width: 100%;

  @include desktop {
    max-width: 685px;
  }

  &.full-width {
    @include desktop {
      max-width: 100%;
    }

    .profile-features-header-premium {
      justify-content: flex-start;
    }
  }
}

.profile-features-header-row {
  background-color: $grey-10;
  margin-bottom: $space-10;

  @include desktop {
    border-bottom: 1px solid $grey-20;
  }

  &:first-of-type {
    padding: $space-5 $space-20;

    @include desktop {
      padding: $space-10 $space-20 $space-10 $space-40;
    }
  }
}

.profile-features-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  font-size: 10px;
  font-weight: 300;
  padding: $space-15 $space-20;

  @include desktop {
    font-size: 16px;
    grid-template-columns: 2.5fr 1fr 1fr;
    padding: $space-10 $space-20 $space-10 $space-40;
  }

  div:first-child {
    text-align: left;
  }
}

.profile-feature-checkmark-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.profile-features-header-mobile {
  font-size: 30px;
  padding: 0;
  font-weight: 700;
  text-align: center;
  margin-top: 0;

  @include desktop {
    display: none;
  }
}

.profile-features-header-members {
  font-weight: 300;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;

  @include desktop {
    font-size: 16px;
    font-weight: 700;
  }
}

.profile-features-header-subscribers {
  color: transparent;
  opacity: 1;
  text-transform: uppercase;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 700;

  span {
    background: $gradient-green-blue;
    background-clip: text;
    background-size: 100%;
  }

  @include desktop {
    font-size: 18px;
    font-weight: 900;
  }
}
