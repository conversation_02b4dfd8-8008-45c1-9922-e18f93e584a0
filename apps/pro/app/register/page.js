import RegisterClient from './_client';
import { getResolvedUrl } from '@utils/apiHelper';
import { cookies, headers } from 'next/headers';
import Api from '@services/api';
import React from 'react';
import { isUserAgentBot } from '@utils/isUserAgentBot';
import * as Sentry from '@sentry/nextjs';
import { redirect } from 'next/navigation';

export const metadata = {
  title: 'Register',
  index: false,
  follow: false,
};

export default async function RegisterPage(req) {
  const searchParams = await req.searchParams;
  const resolvedUrl = getResolvedUrl('/register', searchParams);
  const cookieStore = await cookies();
  const headerStore = await headers();
  const userAgent = headerStore.get('user-agent');

  if (isUserAgentBot(userAgent)) {
    return <div>Bot detected. No action taken.</div>;
  }

  const { token } = searchParams;

  if (!token) {
    Sentry.captureException(new Error('Pro email registration missing params'));

    redirect(`${process.env.publicUrl}/register/professional`);
  }

  const response = await Api.serverside(
    `/auth/registration-token/validate`,
    cookieStore,
    headerStore,
    resolvedUrl,
    'POST',
    new URLSearchParams({ token }),
  );

  const { status, email, login_url, message } = response || {};

  if (status !== 'ok') {
    Sentry.captureException(new Error('Pro email registration request error'), {
      extra: {
        message,
      },
    });

    redirect(`${process.env.publicUrl}/register/professional`);
  }

  if (login_url) {
    const trackingQuery = { ...searchParams };

    delete trackingQuery.token;

    const trackingParams = new URLSearchParams(message).toString();

    redirect(`${login_url}${trackingParams ? `?${trackingParams}` : ''}`);
  }

  return <RegisterClient email={email} />;
}
