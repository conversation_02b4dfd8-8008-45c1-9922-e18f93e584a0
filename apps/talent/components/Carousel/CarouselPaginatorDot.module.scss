@use '@styles/mixins' as *;
@use '@styles/variables' as *;

.dot {
  position: relative;
  width: 10px;
  height: 10px;
  margin: 0 3px;
  border: none;
  padding: 0;
  background-color: transparent;

  &::after {
    content: '';
    position: absolute;
    inset: 1px;
    border-radius: 50%;
    background-color: $grey-40;
    transition: all ease-in-out 0.2s;
    cursor: pointer;
  }

  &:hover {
    &::after {
      inset: 0;
    }
  }

  &.selected {
    &::after {
      inset: 0;
    }
  }
}

.violet {
  &:hover {
    &::after {
      background-color: $violet-60;
    }
  }

  &.selected {
    &::after {
      background-color: $violet-60;
    }
  }
}

.white {
  &::after {
    background-color: $white;
    opacity: 0.4;
  }

  &:hover {
    &::after {
      background-color: $white;
      opacity: 1;
    }
  }

  &.selected {
    &::after {
      background-color: $white;
      opacity: 1;
    }
  }
}
