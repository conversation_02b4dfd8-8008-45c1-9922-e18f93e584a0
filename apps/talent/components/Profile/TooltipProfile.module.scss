@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.tooltip-title {
  position: relative;
  display: flex;
  padding-top: 16px;
  align-items: center;
  justify-content: center;
  color: $black;
  font-weight: 600;
}

.tooltip-icon {
  position: relative;
  width: 15px;
  height: 15px;
  margin: 0 $space-5;
  cursor: pointer;
  opacity: 0.5;
  transition: all 0.3s ease-in-out;
  z-index: 2;

  &:hover {
    opacity: 1;
  }
}

.tooltip-box {
  visibility: hidden;
  position: absolute;
  top: 80px;
  z-index: 1;
  color: $black;
  font-weight: 400;
  font-size: 12px;
  max-width: 100%;
  background-color: white;
  border-radius: 10px;
  line-height: 1.4;
  padding: $space-30;
  box-shadow: $shadow-tooltip;
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);

  &::before {
    content: '';
    position: absolute;
    top: -25px;
    width: 0;
    left: 0;
    right: 0;
    margin: auto;
    display: flex;
    justify-content: center;
    border: 13px solid transparent;
    border-bottom: 13px solid white;
  }

  & a {
    color: $blue-80;
    font-weight: 600;
  }
}

.tooltip-title.is-open {
  .tooltip-box {
    top: calc(100% + 15px);
    visibility: visible;
  }
}
