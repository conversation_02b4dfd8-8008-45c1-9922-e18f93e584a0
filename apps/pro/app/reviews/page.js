import ReviewsClient from './_client';
import { cookies, headers } from 'next/headers';
import Api from '@services/api';
import { getResolvedUrl } from '@utils/apiHelper';

export const metadata = {
  title: 'Reviews',
};

export default async function ReviewsPage(req) {
  const searchParams = await req.searchParams;
  const resolvedUrl = getResolvedUrl('/reviews', searchParams);
  const cookieStore = await cookies();
  const headerStore = await headers();
  const page = 1;
  const limit = searchParams.limit || 8;

  const reviewResponse = await Api.serverside(
    `/testimonials?page=${page}&limit=${limit}`,
    cookieStore,
    headerStore,
    resolvedUrl,
  );

  const profileResponse = await Api.serverside(
    `/profiles?rel=talent&limit=15&age=18..any`,
    cookieStore,
    headerStore,
    resolvedUrl,
  );

  const clientStatus = await Api.serverside(
    '/clientstatus/info',
    cookieStore,
    headerStore,
    resolvedUrl,
  );

  return (
    <ReviewsClient
      initialReviews={reviewResponse.items}
      reviewCount={reviewResponse.count}
      profiles={profileResponse.items}
      initialReviewQuery={{ page, limit }}
      canContactTalent={
        clientStatus.info?.allowed_to?.hasOwnProperty('contact_talent') || false
      }
    />
  );
}
