'use client';
import { <PERSON><PERSON>, <PERSON>erM<PERSON>ile, Input, Modal, Select } from '@components';
import styles from './ModalReportMessage.module.scss';
import React, { memo } from 'react';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import cn from 'classnames';
import Api from '@services/api';
import { useNotifications } from '@contexts/NotificationContext';
import { ErrorMessage, MESSAGE_LINK_REGEX } from '@constants/form';
import Link from 'next/link';

const ModalReportMessage = ({
  message: { content, id } = {},
  profileId,
  onClose,
}) => {
  const { setNotification } = useNotifications();

  const messageFragments = content.split(MESSAGE_LINK_REGEX);
  const reasons = [
    { title: `It's harassing me`, value: "It's harassing me" },
    { title: `Spam or scam`, value: 'Spam or scam' },
    { title: `Hate speech`, value: 'Hate speech' },
    { title: `Harmful behavior`, value: 'Harmful behavior' },
    { title: `Sexual context`, value: 'Sexual context' },
  ];

  const formik = useFormik({
    initialValues: {
      reason: '',
      comment: '',
    },
    onSubmit: async ({ reason, comment }) => {
      const body = new FormData();

      body.append('reason', reason);
      body.append('comment', comment);

      const { status, message } = await Api.clientside(
        `/messages/${id}/complaints`,
        {
          method: 'POST',
          body,
        },
        {
          'x-profile': profileId,
        },
      );

      if (status === 'error') {
        setNotification({
          type: 'error',
          message: message || ErrorMessage.Unexpected,
          timeout: '5000',
        });
        onClose();
      } else {
        setNotification({
          type: 'info',
          message: 'Message reported',
          timeout: '3000',
        });
        onClose(id);
      }
    },
    validationSchema: Yup.object({
      reason: Yup.string().required(ErrorMessage.ReasonRequired),
      comment: Yup.string().max(
        500,
        ErrorMessage.MaxCharactersLatin.replace('{X}', '500'),
      ),
    }),
  });

  const onReasonSelect = (value) => {
    formik.setFieldValue('reason', value);
  };

  return (
    <Modal
      classNameContainer={styles['modal-container']}
      classNameOverlay={styles['modal-overlay']}
      backdropClose
      onClose={onClose}
      contentOverflowHidden={false}
      showDefaultLayout={false}
      hideMobileCloseButton
      showMobileBorderRadius={false}
    >
      <HeaderMobile onClose={onClose} title="Report message" hideOnTablet />
      <form onSubmit={formik.handleSubmit}>
        <div className={styles.header}>
          <h1 className={styles.title}>Report message</h1>
          <div className={styles['message-wrap']}>
            <div className={styles.message}>
              {messageFragments.map((fragment, index) =>
                MESSAGE_LINK_REGEX.test(fragment) ? (
                  <Link
                    key={index}
                    href={fragment}
                    target="_blank"
                    rel="noreferrer"
                    className={styles['inline-link']}
                  >
                    {fragment}
                  </Link>
                ) : (
                  <span key={index}>{fragment}</span>
                ),
              )}
            </div>
          </div>
        </div>
        <div className={styles.content}>
          <Select
            name="reason"
            options={reasons}
            value={formik.values.reason}
            isTouched={formik.touched.reason}
            setFormFieldTouched={() => formik.setFieldTouched('reason')}
            onChange={onReasonSelect}
            placeholder="Reason"
          />
          <div className={styles['form-field']}>
            <Input
              name="comment"
              placeholder="Comment (optional)"
              hint="Max 500 characters"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.comment}
              isTouched={formik.touched.comment}
              error={formik.errors.comment}
              charCounter
            />
            {formik.errors.reason && formik.touched.reason && (
              <div className={styles.error}>{formik.errors.reason}</div>
            )}
          </div>
        </div>
        <div className={styles['mobile-submit-button']}>
          <Button
            type="submit"
            className={styles['mobile-submit-button']}
            minWidth="220px"
            kind="secondary"
            disabled={!(formik.isValid && formik.dirty)}
            label="Send report"
          />
        </div>
        <div className={styles.buttons}>
          <button
            className={cn(styles.button, styles.cancel)}
            type="cancel"
            onClick={onClose}
          >
            Cancel
          </button>
          <button
            className={cn(styles.button, styles.submit)}
            type="submit"
            disabled={!(formik.isValid && formik.dirty)}
          >
            Send
          </button>
        </div>
      </form>
    </Modal>
  );
};

export default memo(ModalReportMessage);
