'use client';
import { ModalDeleteConversation, Tooltip } from '@components';
import styles from './ConversationActions.module.scss';
import Image from 'next/image';
import React, { useState } from 'react';
import { createPortal } from 'react-dom';

const ConversationActions = ({ id, partnerProfileId }) => {
  const [showDeleteConversationModal, setShowDeleteConversationModal] =
    useState(false);

  const toggleShowDeleteConversationModal = () => {
    setShowDeleteConversationModal(!showDeleteConversationModal);
  };

  return (
    <>
      <Tooltip
        positions={['bottom', 'top']}
        backdropEnabled={false}
        showCloseButton={false}
        content={
          <div className={styles.tooltip}>
            <button
              onClick={toggleShowDeleteConversationModal}
              className={styles['tooltip-action']}
            >
              <Image
                src="/assets/icons/icon-trash-round-red.svg"
                width={25}
                height={25}
                alt="icon"
              />
              <span className={styles['remove-text']}>Delete conversation</span>
            </button>
          </div>
        }
      >
        <div className={styles['tooltip-container']}>
          <Image
            className={styles.icon}
            src="/assets/icons/icon-dots-2.svg"
            width={30}
            height={30}
            alt="icon dots"
          />
        </div>
      </Tooltip>
      {showDeleteConversationModal &&
        createPortal(
          <ModalDeleteConversation
            onClose={toggleShowDeleteConversationModal}
            id={id}
            partnerProfileId={partnerProfileId}
          />,
          document.body,
        )}
    </>
  );
};

export default ConversationActions;
