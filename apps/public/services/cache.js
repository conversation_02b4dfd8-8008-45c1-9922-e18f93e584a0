import { redis } from './redis';

const generateFetcher = (url, options) => {
  return async () => {
    return await fetch(url, options)
      .then(async (response) => {
        let json;

        // let's catch some serialization errors
        try {
          json = await response.json();
        } catch (e) {
          return {
            data: e,
            success: false,
          };
        }

        // let's check status field only if it exists in json response
        if (json.status && json.status !== 'ok') {
          return {
            data: json,
            success: false,
          };
        }

        // due to issues with gateway checking
        // response status code only will not help
        // so...
        // let's check response status
        return {
          data: json,
          success: response.status < 300,
        };
      })
      .catch((error) => {
        return {
          data: error,
          success: false,
        };
      });
  };
};

const cached = async (url, expires, options) => {
  const fetcher = generateFetcher(url, options);

  if (process.env.REDIS_ENABLED !== 'enabled') {
    return (await fetcher()).data;
  }

  /*
   * In order to be able to reset all cache
   */
  const key = `${process.env.CACHE_PREFIX}:::${expires}:::${url}`;

  const existing = await get(key);

  if (existing !== null) return existing;

  return set(key, fetcher, expires);
};

const get = async (key) => {
  if (process.env.REDIS_ENABLED !== 'enabled') {
    return null;
  }

  const value = await (await redis).get(key);

  if (value === null) return null;

  return JSON.parse(value);
};

const set = async (key, fetcher, expires) => {
  if (process.env.REDIS_ENABLED !== 'enabled') {
    return (await fetcher()).data;
  }

  const value = await fetcher();

  if (value.success) {
    await (await redis).set(key, JSON.stringify(value.data), 'EX', expires);

    return value.data;
  }

  return { success: false, original: value.data ?? {} };
};

const del = async (key) => {
  if (process.env.REDIS_ENABLED !== 'enabled') {
    return null;
  }

  await (await redis).del(key);
};

const clear = async (prefix, pattern) => {
  if (prefix !== process.env.CACHE_PREFIX) {
    return;
  }

  await del(
    await (await redis).keys(`${process.env.CACHE_PREFIX}:::*:::${pattern}`),
  );
};

const update = async (prefix, pattern) => {
  if (prefix !== process.env.CACHE_PREFIX) {
    return;
  }

  const r = await redis;
  const keys = await r.keys(`${process.env.CACHE_PREFIX}:::*:::${pattern}`);

  keys.map(async (key) => {
    const [prefix, expires, url] = key.split(':::');

    const fetcher = generateFetcher(url);

    await set(key, fetcher, expires);
  });
};

const save = async (key, data, expires) => {
  if (process.env.REDIS_ENABLED !== 'enabled') {
    return null;
  }

  await (await redis).set(key, JSON.stringify(data), 'EX', expires);
};

export default { cached, set, get, del, clear, update, save };
