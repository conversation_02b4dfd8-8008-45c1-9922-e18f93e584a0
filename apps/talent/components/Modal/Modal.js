'use client';
import styles from './Modal.module.scss';
import React, { memo, useEffect, useRef } from 'react';
import cn from 'classnames';
import { useModalContext } from '@contexts/ModalContext';

const Modal = ({
  backdropClose, // close on background click
  contentClose = false, // close on background/container/content click
  containerClose = false, // close on background/container click
  showDefaultLayout = true, // set false for full mobile layout or custom width/height
  children,
  onClose,
  showAnimation = true,
  showCloseButton = true,
  hideMobileCloseButton = false,
  classNameContainer = '',
  classNameOverlay = '',
  classNameContent = '',
  classNameCloseButton = '',
  showMobileBorderRadius = true, // set false for full mobile layout
  floatCloseButton = false, // set true to remove close button space
  contentOverflowHidden = true, // set false to show modal overflow
  disableBackgroundScroll = true, // set false for modal on modal
}) => {
  const overlayRef = useRef(null);
  const containerRef = useRef(null);
  const contentRef = useRef(null);
  const { setModalOpened } = useModalContext();

  useEffect(() => {
    setModalOpened(true);

    return () => {
      setModalOpened(false);
    };
  }, []);

  useEffect(() => {
    const currentRef = overlayRef.current;
    const callback = (event) => {
      if (onClose) {
        onClose(event);
      }
    };

    if (currentRef && backdropClose) {
      currentRef.addEventListener('mousedown', callback);
    } else {
      currentRef.removeEventListener('mousedown', callback);
    }

    return () => currentRef.removeEventListener('mousedown', callback);
  }, [overlayRef, onClose, backdropClose]);

  useEffect(() => {
    const currentRef = containerRef.current;
    const callback = (event) => {
      event.stopPropagation();
    };

    if (!containerClose && currentRef && !contentClose) {
      currentRef.addEventListener('mousedown', callback);
    } else {
      currentRef.removeEventListener('mousedown', callback);
    }

    return () => currentRef.removeEventListener('mousedown', callback);
  }, [containerClose, containerRef, contentClose]);

  useEffect(() => {
    const currentRef = contentRef.current;
    const callback = (event) => {
      event.stopPropagation();
    };

    if (containerClose && currentRef) {
      currentRef.addEventListener('mousedown', callback);
    } else {
      currentRef.removeEventListener('mousedown', callback);
    }

    return () => currentRef.removeEventListener('mousedown', callback);
  }, [containerClose, contentRef]);

  useEffect(() => {
    if (disableBackgroundScroll) {
      document.body.style.overflow = 'hidden';

      return () => {
        document.body.style.overflow = 'unset';
      };
    }
  }, [disableBackgroundScroll]);

  return (
    <div
      ref={overlayRef}
      className={cn(styles.overlay, classNameOverlay, {
        [styles['default-overlay']]: !!showDefaultLayout,
      })}
    >
      <div
        className={cn(styles.container, classNameContainer, {
          [styles.animation]: !!showAnimation,
          [styles['show-mobile-border-radius']]: showMobileBorderRadius,
          [styles['default-container']]: !!showDefaultLayout,
        })}
        ref={containerRef}
      >
        {onClose && showCloseButton && (
          <div
            className={cn(styles['close-button-container'], {
              [styles['hide-mobile-close-button']]: !!hideMobileCloseButton,
              [styles['float-close-button']]: !!floatCloseButton,
            })}
          >
            <button
              onClick={onClose}
              className={cn(styles['close-button'], classNameCloseButton)}
              type="button"
            />
          </div>
        )}
        <div
          ref={contentRef}
          className={cn(styles.content, classNameContent, {
            [styles['default-content']]: !!showDefaultLayout,
            [styles['content-overflow-hidden']]: contentOverflowHidden,
          })}
        >
          {children}
        </div>
      </div>
    </div>
  );
};

export default memo(Modal);
