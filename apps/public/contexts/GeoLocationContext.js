import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
} from 'react';
import { useAuth } from './AuthContext';
import Api from '../services/api';
import { useRouter } from 'next/router';
import { isUserAgentBot } from '../utils/isUserAgentBot';

const GeoLocationContext = createContext({});

export const GeoLocationProvider = ({ children }) => {
  const [geoLocation, setGeoLocation] = useState(null);
  const { isAuthenticated } = useAuth();
  const router = useRouter();

  const clearGeoLocation = useCallback(() => {
    if (geoLocation) {
      setGeoLocation(null);
      sessionStorage.removeItem('geoLocation');
    }
  }, [geoLocation]);

  const refreshGeoLocation = useCallback(() => {
    if (!isAuthenticated && navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(onSuccess, onError);
    } else {
      clearGeoLocation();
    }
  }, [clearGeoLocation, isAuthenticated]);

  useEffect(() => {
    if (
      router.asPath === '/castingcalls' &&
      !isUserAgentBot(navigator.userAgent)
    ) {
      const geoLocationCache = sessionStorage.getItem('geoLocation');

      if (geoLocationCache) {
        setGeoLocation(geoLocationCache);
      } else {
        refreshGeoLocation();
      }
    }
  }, [refreshGeoLocation, router.asPath]);

  useEffect(() => {
    if (geoLocation && router.asPath === '/castingcalls') {
      router.push('/castingcalls?city=' + geoLocation);
    }
  }, [geoLocation, router.asPath]);

  const onSuccess = async (position) => {
    if (position.coords?.latitude && position.coords?.latitude) {
      await getGeoLocation(position.coords.latitude, position.coords.longitude);
    }
  };

  const onError = (error) => {
    console.error('No location found.', error);
  };

  const getGeoLocation = async (latitude, longitude) => {
    try {
      const response = await Api.clientGateway(
        `/locations/city/coordinates?latitude=${latitude}&longitude=${longitude}`,
      );

      if (response.links?.city?.slug) {
        saveGeoLocation(response.links.city.slug);
      }
    } catch (error) {
      console.error('Unable to retrieve geolocation.', error);
    }
  };

  const saveGeoLocation = (value) => {
    setGeoLocation(value);
    sessionStorage.setItem('geoLocation', value);
  };

  return (
    <GeoLocationContext.Provider value={{ geoLocation }}>
      {children}
    </GeoLocationContext.Provider>
  );
};

export const useGeoLocation = () => useContext(GeoLocationContext);
