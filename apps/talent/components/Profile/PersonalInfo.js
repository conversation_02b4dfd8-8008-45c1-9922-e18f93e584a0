'use client';
import React, { memo, useEffect, useState } from 'react';
import styles from './ProfileInfo.module.scss';
import Api from '@services/api';
import dayjs from 'dayjs';
import { useNotifications } from '@contexts/NotificationContext';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { getAgeFromBirthdayDate } from '@utils/ageRangeHelpers';
import { DayPickerTooltip, Input, Select } from '@components';
import { hasFormValueChanged } from '@utils/hasFormValueChanged';
import cn from 'classnames';
import { Amp } from '@services/amp';
import { EditToggle } from './EditToggle';
import { BIRTHDAY_REGEX, ErrorMessage } from '@constants/form';

const PersonalInfo = ({
  profileId,
  accountId,
  gender,
  initialLocation,
  ethnicity,
  birthday,
  zipCode,
  genderOptions,
  ethnicitiesOptions,
  refreshUserProfiles,
  refreshProfileDetails,
}) => {
  const { setNotification } = useNotifications();
  const [isPersonalInfoContainerEditing, setPersonalInfoContainerEditing] =
    useState(false);
  const [isPersonalInfoNotEmpty, setIsPersonalInfoNotEmpty] = useState(false);
  const [location, setLocation] = useState(initialLocation || '');

  const formik = useFormik({
    initialValues: {
      gender: gender?.id || '',
      ethnicity: ethnicity?.id || '',
      birthday: dayjs(birthday).format('MM/DD/YYYY') || '',
      zip: zipCode || '',
    },
    onSubmit: async (values) => {
      if (hasFormValueChanged(values, formik.initialValues)) {
        const locationBody = new FormData();
        const profileBody = new FormData();
        const ethnicitiesBody = new FormData();

        locationBody.append('zip', values.zip.replaceAll(' ', ''));

        profileBody.append(
          'birthday',
          dayjs(values.birthday).format('YYYY-MM-DD'),
        );
        profileBody.append('gender', values.gender);

        ethnicitiesBody.append('items[]', values.ethnicity);

        const locationResponse = await Api.clientside(
          `/accounts/${accountId}/location`,
          {
            body: locationBody,
            method: 'PUT',
          },
        );

        const profileResponse = await Api.clientside(`/profiles/${profileId}`, {
          body: profileBody,
          method: 'PATCH',
        });

        const ethnicitiesResponse = await Api.clientside(
          `/profiles/${profileId}/ethnicities`,
          {
            body: ethnicitiesBody,
            method: 'PUT',
          },
        );

        const isError = [
          locationResponse.status !== 'ok',
          profileResponse.status !== 'ok',
          ethnicitiesResponse.status !== 'ok',
        ].some((value) => value);

        if (isError) {
          setNotification({
            type: 'error',
            timeout: '5000',
            message:
              locationResponse.message ||
              profileResponse.message ||
              ethnicitiesResponse.message ||
              ErrorMessage.Unexpected,
          });
        } else {
          formik.resetForm({ values });
          setPersonalInfoContainerEditing(false);
          await refreshUserProfiles();
          await refreshProfileDetails();
        }
      } else {
        setPersonalInfoContainerEditing(false);
      }
    },
    validationSchema: Yup.object({
      gender: Yup.string().required(ErrorMessage.GenderRequired),
      ethnicity: Yup.string().required(ErrorMessage.EthnicityRequired),
      birthday: Yup.string()
        .nullable()
        .required(ErrorMessage.BirthdayRequired)
        .matches(BIRTHDAY_REGEX, ErrorMessage.BirthdayMatch)
        .test('isValidDate', ErrorMessage.BirthdayValid, (value) => {
          const [month, day, year] = value.split('/').map(Number);
          const dateObj = dayjs(`${year}-${month}-${day}`);

          return (
            dateObj.date() === day &&
            dateObj.month() + 1 === month &&
            dateObj.year() === year
          );
        })
        .test('birthdayMinValid', ErrorMessage.AgeMin, async (value) => {
          if (value && String(value) !== 'Invalid Date') {
            const minValidAge = dayjs().subtract(18, 'year').unix();

            const date = dayjs(value).unix();

            return date <= minValidAge;
          }

          return false;
        })
        .test('birthdayMaxValid', ErrorMessage.AgeMax, async (value) => {
          if (value && String(value) !== 'Invalid Date') {
            const maxValidAge = dayjs().subtract(98, 'year').unix();

            const date = dayjs(value).unix();

            return date >= maxValidAge;
          }

          return false;
        }),
      zip: Yup.string()
        .transform((value) => value?.replaceAll(' ', ''))
        .required(ErrorMessage.ZipRequired)
        .min(5, ErrorMessage.ZipPattern)
        .test('zipIsValid', ErrorMessage.ZipPattern, async (value) => {
          if (value?.length >= 4 && value !== formik.values.zip) {
            const response = await getLocation(value);

            setLocation(
              response.count > 0
                ? `${response.items[0]?.links?.city?.title}, ${response.items[0]?.links?.state?.code}`
                : '',
            );

            return response.count > 0;
          } else if (!value) {
            setLocation('');
          }

          return !!location;
        }),
    }),
  });

  useEffect(() => {
    setIsPersonalInfoNotEmpty(
      Object.values(formik.initialValues).some((value) => value),
    );
  }, [formik.initialValues]);

  const editPersonalInfo = async () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'edit personal info',
      scope: Amp.element.scope.profile,
      section: Amp.element.section.personalInformation,
      type: Amp.element.type.button,
    });
    setPersonalInfoContainerEditing(true);
  };

  const triggerSubmit = async () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'save personal info',
      scope: Amp.element.scope.profile,
      section: Amp.element.section.personalInformation,
      type: Amp.element.type.button,
    });
    await formik.submitForm();
  };

  const getLocation = async (zip) => {
    return await Api.clientside(`/locations?query=${zip}`);
  };

  const setGenderValue = (e) => {
    formik.setFieldValue('gender', e);
  };

  const setEthnicityValue = (e) => {
    formik.setFieldValue('ethnicity', e);
  };

  const setGenderTouched = () => {
    formik.setFieldTouched('gender');
  };

  const setEthnicityTouched = () => {
    formik.setFieldTouched('ethnicity');
  };

  const setBirthdayValue = (value) => {
    formik.setFieldValue('birthday', dayjs(value).format('MM/DD/YYYY'));
  };

  const handleInputChange = (e) => {
    const { value, selectionStart, selectionEnd } = e.target;
    const input = value.replace(/\D/g, '');

    let formattedDate = '';

    const dashDiff = [3, 6].some(
      (pos) => pos === selectionStart && pos === value.length,
    )
      ? 1
      : 0;

    if (input.length <= 2) {
      formattedDate = input;
    } else if (input.length <= 4) {
      formattedDate = `${input.slice(0, 2)}/${input.slice(2)}`;
    } else {
      formattedDate = `${input.slice(0, 2)}/${input.slice(2, 4)}/${input.slice(4, 8)}`;
    }
    e.target.value = formattedDate;
    e.target.selectionStart = selectionStart + dashDiff;
    e.target.selectionEnd = selectionEnd + dashDiff;
    formik.setFieldValue('birthday', formattedDate);
  };

  return (
    <>
      <section>
        <div className={styles['profile-section-title']}>
          Personal:
          <EditToggle
            isEditing={isPersonalInfoContainerEditing}
            onEdit={editPersonalInfo}
            onSave={triggerSubmit}
            disabled={formik.isSubmitting || !formik.isValid}
          />
        </div>
        {!isPersonalInfoContainerEditing ? (
          <>
            {isPersonalInfoNotEmpty ? (
              <div
                className={cn(
                  styles['profile-properties-container'],
                  styles.clickable,
                )}
                onClick={editPersonalInfo}
              >
                <div className={styles['profile-property']}>
                  <div className={styles['profile-property-title']}>Gender</div>
                  <div
                    className={
                      styles[
                        formik.values.gender
                          ? 'profile-property-value'
                          : 'profile-property-value-empty'
                      ]
                    }
                  >
                    {genderOptions.find(
                      (option) => option.value === formik.values.gender,
                    )?.title || 'n/a'}
                  </div>
                </div>
                <div className={styles['profile-property']}>
                  <div className={styles['profile-property-title']}>Age</div>
                  <div
                    className={cn(
                      styles.age,
                      styles[
                        formik.values.birthday
                          ? 'profile-property-value'
                          : 'profile-property-value-empty'
                      ],
                    )}
                  >
                    {formik.values.birthday
                      ? `${getAgeFromBirthdayDate(formik.values.birthday)} y.o.`
                      : 'n/a'}
                  </div>
                </div>
                <div className={styles['profile-property']}>
                  <div className={styles['profile-property-title']}>
                    Location
                  </div>
                  <div
                    className={
                      styles[
                        formik.values.zip
                          ? 'profile-property-value'
                          : 'profile-property-value-empty'
                      ]
                    }
                  >
                    {location || 'n/a'}
                  </div>
                </div>
                <div className={styles['profile-property']}>
                  <div className={styles['profile-property-title']}>
                    Ethnicity
                  </div>
                  <div
                    className={
                      styles[
                        formik.values.ethnicity
                          ? 'profile-property-value'
                          : 'profile-property-value-empty'
                      ]
                    }
                  >
                    {ethnicitiesOptions.find(
                      (option) => option.value === formik.values.ethnicity,
                    )?.title || 'n/a'}
                  </div>
                </div>
              </div>
            ) : (
              <div
                className={styles['profile-section-empty']}
                onClick={editPersonalInfo}
              >
                List all the skills to increase your chances of getting noticed.
                <span className={styles['profile-section-empty-add-button']}>
                  Add
                </span>
              </div>
            )}
          </>
        ) : (
          <form
            className={cn(
              styles['profile-properties-container'],
              styles['profile-properties-container-form'],
            )}
            onSubmit={formik.handleSubmit}
          >
            <div className={styles['profile-property']}>
              <div className={styles['profile-property-value']}>
                <Select
                  name="gender"
                  placeholder="Gender"
                  onChange={setGenderValue}
                  value={formik.values.gender}
                  isTouched={formik.touched.gender}
                  error={formik.errors.gender}
                  options={genderOptions}
                  setFormFieldTouched={setGenderTouched}
                />
              </div>
            </div>

            <div className={styles['profile-property']}>
              <div className={styles['profile-property-value']}>
                <Input
                  name="birthday"
                  placeholder="Date Of Birth"
                  hint="MM/DD/YYYY"
                  inputMode="numeric"
                  value={formik.values.birthday}
                  error={formik.errors.birthday}
                  isTouched={formik.touched.birthday}
                  onChange={handleInputChange}
                  onBlur={formik.handleBlur}
                  icon={
                    <DayPickerTooltip
                      selected={formik.values.birthday}
                      initialValue={formik.initialValues.birthday}
                      onSelect={setBirthdayValue}
                      endMonth={dayjs().subtract(18, 'year').toISOString()}
                      startMonth={dayjs().subtract(98, 'year').toISOString()}
                    />
                  }
                />
              </div>
            </div>

            <div className={styles['profile-property']}>
              <div className={styles['profile-property-value']}>
                <Input
                  name="zip"
                  placeholder="Location"
                  hint="ZIP / Postal Code"
                  value={formik.values.zip}
                  error={formik.errors.zip}
                  isTouched={formik.touched.zip}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                />
                {location && (
                  <span className={styles['location-hint']}>{location}</span>
                )}
              </div>
            </div>

            <div className={styles['profile-property']}>
              <div className={styles['profile-property-value']}>
                <Select
                  name="ethnicity"
                  onChange={setEthnicityValue}
                  value={formik.values.ethnicity}
                  isTouched={formik.touched.ethnicity}
                  error={formik.errors.ethnicity}
                  placeholder="Ethnicity"
                  options={ethnicitiesOptions}
                  setFormFieldTouched={setEthnicityTouched}
                />
              </div>
            </div>
          </form>
        )}
      </section>
    </>
  );
};

export default memo(PersonalInfo);
