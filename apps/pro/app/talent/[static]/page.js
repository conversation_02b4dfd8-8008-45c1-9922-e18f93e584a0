import TalentClient from '../_client';
import Api from '@services/api';
import { getResolvedUrl } from '@utils/apiHelper';
import { cookies, headers } from 'next/headers';
import { CookieServiceServer } from '@services/cookieServiceServer';
import { getContent, getFilters } from '@utils/talentFilterHelper';
import { ELASTIC_PAGE_LIMIT, TALENTS_LIMIT } from '@constants/pagination';
import { formatSearchedTalents } from '@utils/submissionsHelper';
import FilterService from '@entertech/filter-service';
import { notFound, redirect } from 'next/navigation';
import { SelectedTalentsProvider } from '@contexts/SelectedTalentsContext';

export const metadata = {
  title: 'Talent Database',
};

export default async function TalentPage(req) {
  const params = await req.params;
  const searchParams = await req.searchParams;
  const resolvedUrl = getResolvedUrl(`/talent/${params.static}`, searchParams);
  const cookieStore = await cookies();
  const headerStore = await headers();
  const accountId = CookieServiceServer.getAccountCookie(cookieStore);
  const content = await getContent(
    { ...searchParams, ...params },
    resolvedUrl,
    cookieStore,
    headerStore,
    accountId,
  );
  const talentsTotal = content.talents?.count || 0;
  const pagesTotal = Math.ceil(talentsTotal / TALENTS_LIMIT);
  const page = parseInt(FilterService.getPage(content.filters));

  // elasticsearch issue with pagination
  const maximumPage =
    ELASTIC_PAGE_LIMIT > pagesTotal ? pagesTotal : ELASTIC_PAGE_LIMIT;

  if (maximumPage && page > maximumPage) {
    redirect(
      resolvedUrl
        .replace(
          `-page-${page}`,
          maximumPage === 1 ? `` : `-page-${maximumPage}`,
        )
        .replace(
          `/page-${page}`,
          maximumPage === 1 ? `` : `/page-${maximumPage}`,
        ),
    );
  }

  if (page <= 0 || isNaN(page)) {
    notFound();
  }

  if (page === 1 && resolvedUrl.includes(`page-1`)) {
    redirect(
      resolvedUrl.replace(`-page-${page}`, ``).replace(`/page-${page}`, ``),
    );
  }

  const clientStatus = await Api.serverside(
    '/clientstatus/info',
    cookieStore,
    headerStore,
    resolvedUrl,
  );

  const defaultFilters = await getFilters(
    resolvedUrl,
    cookieStore,
    headerStore,
    accountId,
  );

  return (
    <SelectedTalentsProvider>
      <TalentClient
        defaultFilters={defaultFilters}
        talents={formatSearchedTalents(content.talents?.items || [])}
        talentsTotal={talentsTotal}
        talentsTotalLimit={maximumPage * TALENTS_LIMIT}
        cities={content.cities}
        activeFilters={content.filters}
        canContactTalent={
          clientStatus.info?.allowed_to?.hasOwnProperty('contact_talent') ||
          false
        }
        page={page}
      />
    </SelectedTalentsProvider>
  );
}
