import CastingCallCreateClient from './_client';
import { cookies, headers } from 'next/headers';
import { CookieServiceServer } from '@services/cookieServiceServer';
import Api from '@services/api';
import { TYPE } from '@constants/castingCalls';
import { formatPostParameters } from '@utils/postCastingCallHelpers';
import {
  getDayOptionsByMonth,
  getMonthOptionsWithFullTitles,
  getYearOptions,
  timeFormat,
  timeOptions,
} from '@utils/getTimeOptions';
import dayjs from 'dayjs';
import { getResolvedUrl } from '@utils/apiHelper';

export const metadata = {
  title: 'Create casting call',
};

export default async function CastingCallCreatePage(req) {
  const searchParams = await req.searchParams;
  const resolvedUrl = getResolvedUrl('/castingcall/create', searchParams);
  const cookieStore = await cookies();
  const headerStore = await headers();

  const castingCallType = searchParams?.type || TYPE.Web;
  const profileId = CookieServiceServer.getProfileCookie(cookieStore);

  const parameters = await Api.serverside(
    '/calls/init_post',
    cookieStore,
    headerStore,
    resolvedUrl,
  );

  const profile = await Api.serverside(
    `/profiles/${profileId}?expand=touches`,
    cookieStore,
    headerStore,
    resolvedUrl,
  );

  return (
    <CastingCallCreateClient
      castingCallType={castingCallType}
      profile={profile}
      castingCallParameters={formatPostParameters(parameters.data)}
      monthOptions={getMonthOptionsWithFullTitles()}
      dayOptions={getDayOptionsByMonth(dayjs().year(), 1)}
      yearOptions={getYearOptions()}
      timeOptions={timeOptions}
      timeFormat={timeFormat}
    />
  );
}
