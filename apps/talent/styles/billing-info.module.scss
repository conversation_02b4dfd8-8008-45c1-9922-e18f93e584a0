@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.billing-info-container {
  text-align: center;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-image: $gradient-port-gore;
  padding-bottom: $space-20;

  @include tablet {
    background-image: none;
    gap: $space-60;
    padding: $space-40 $space-20;
  }
}

.billing-info-title {
  display: none;
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  line-height: 1.2;

  @include tablet {
    display: initial;
  }
}

.billing-info-card-container {
  display: flex;
  flex-direction: column;
  max-width: 814px;
  width: 100%;

  @include tablet {
    border: 1px solid $grey-60;
    border-radius: 10px;
  }
}

.billing-info-row {
  display: flex;
  gap: $space-10;
  justify-content: flex-start;
  text-align: left;
  flex-wrap: wrap;
  width: 100%;

  &.is-visible-desktop {
    display: none;

    @include desktop {
      display: flex;
    }
  }

  &.is-hidden-desktop {
    @include desktop {
      display: none;
    }
  }
}

.billing-info-row-title {
  font-weight: 700;
  flex-basis: 50%;
}

.billing-info-card {
  display: flex;
  flex-direction: column;

  @include tablet {
    flex-direction: row;
    position: relative;
  }
}

.billing-info-payment-method-container {
  flex-grow: 1;
  order: 2;
  padding: 0 $space-20;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  @include tablet {
    padding: 0;
    justify-content: flex-start;
    order: 1;
  }
}

.billing-info-payment-method {
  display: flex;
  gap: $space-20;
  align-items: flex-start;
  padding: $space-15 $space-20;
  flex-direction: column;
  width: 100%;
  max-width: 420px;
  min-width: 240px;
  border-radius: 10px;
  box-shadow: $shadow-card-container;
  background-color: $white;

  @include tablet {
    box-shadow: none;
    max-width: unset;
    padding: $space-30 0 $space-15 $space-10;
    align-items: initial;
  }

  @include desktop {
    padding: $space-35 $space-40 $space-20;
  }

  &.invoice {
    padding: $space-30 $space-50 $space-30 $space-40;
    gap: 12px;

    .billing-info-row {
      justify-content: space-between;

      .billing-info-row-title {
        flex-basis: auto;
      }
    }
  }
}

.billing-info-column-right {
  flex-grow: 1;
  order: 1;
  width: 100%;
  margin: 0;

  @include tablet {
    max-width: 320px;
    order: 2;
    margin: 0 $space-10 -70px 0;
  }

  @include desktop {
    margin: 0 $space-40 -70px 0;
  }
}

.billing-info-plan {
  background-image: $gradient-port-gore;
  width: 100%;
  min-height: 100%;
  color: $white;
  display: flex;
  flex-direction: column;
  gap: $space-20;
  padding: $space-30 $space-20;
  justify-content: center;
  align-items: center;

  @include tablet {
    justify-content: initial;
    align-items: initial;
    margin: (-$space-35) 0;
    padding: $space-30 0;
    max-width: 320px;
    border-radius: 10px;
    box-shadow: $shadow-card-container;
    overflow: hidden;
  }
}

.billing-info-plan-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-bottom: $space-10;
  gap: $space-10;
  color: $white;
}

.upgrade-button-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 0 $space-10;

  .upgrade-button {
    margin-top: $space-10;
    color: $white;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: $space-10 $space-20;
    border-radius: 30px;
    border: 1px solid $white;
    font-weight: 700;
    font-size: 14px;
    text-transform: uppercase;
    max-width: 280px;
    width: 100%;
    cursor: pointer;

    &:hover {
      text-decoration: none;
      border-color: $white;
    }
  }
}

.billing-info-plan-description {
  color: $yellow-60;
  font-weight: 700;
  font-size: 30px;
  line-height: 1;
}

.billing-info-plan-description-discontinued {
  color: $white;
  font-weight: 700;
  font-size: 24px;
  line-height: 1.5;
  text-transform: capitalize;

  @include tablet {
    max-width: 170px;
  }

  span {
    color: $green-80;
  }
}

.billing-info-plan-content {
  background-color: $white;
  color: $black;
  padding: $space-20 $space-30;
  text-align: left;
  border-radius: 10px;
  max-width: 320px;
  width: 100%;

  @include tablet {
    border-radius: initial;
  }
}

.billing-info-actions {
  display: flex;
  justify-content: space-around;
  align-items: center;
  gap: 20px;
  padding: $space-20 $space-20 0;
  width: 100%;
  max-width: 420px;

  @include tablet {
    border-top: 1px solid $grey-60;
    padding: $space-20 0 $space-20 $space-10;
    justify-content: flex-start;
    max-width: unset;

    &:empty {
      border: none;
      padding-top: 0;
    }
  }

  @include desktop {
    padding: 20px 40px;
  }
}

.billing-info-button {
  display: flex;
  align-items: center;
  gap: 5px;
  color: $blue-100;
  font-weight: 400;
  border: none;
  background-color: transparent;
  font-size: 16px;
  cursor: pointer;

  &:disabled {
    opacity: 0.3;
    cursor: default;
  }

  &.delete {
    color: $red-80;
  }
}

.seperator {
  border: 0.5px solid $grey-60;
  height: 25px;
}

.billing-discontinue-button-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 $space-10;
}

.billing-discontinue-button {
  border: 1px solid $white;
  color: $white;
  background-color: transparent;
  border-radius: 38px;
  font-size: 14px;
  opacity: 0.3;
  text-transform: lowercase;
  font-weight: 300;
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 280px;
  width: 100%;
  padding: $space-5 33px $space-5 $space-5;
  gap: $space-10;

  &:hover {
    opacity: 1;
    text-decoration: none;
  }
}

.billing-info-plan-container {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 3px;
}

.billing-info-plan-title {
  font-weight: 700;
  flex-basis: 30%;
}

.billing-info-plan-title-discontinued {
  font-weight: 700;
  flex-basis: 40%;
}

.billing-info-plan-status {
  font-weight: 300;
  text-transform: capitalize;

  &.active,
  &.complimentary {
    background-image: $gradient-green-blue;
    color: transparent;
    background-clip: text;
  }

  &.discontinued {
    color: $red-100;
  }
}

.billing-info-plan-header-title {
  color: $white;
  font-size: 24px;
  font-weight: 700;
}

.subscribe-button-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: $space-20;
}

div.edit-payment-modal-overlay {
  top: 55px;
  bottom: $mobile-menu-height;

  @include tablet {
    align-items: center;
    padding: 0 $space-20;
    inset: 0;
  }
}

.edit-payment-modal {
  width: 100vw;
  height: 100%;

  @include tablet {
    max-width: 830px;
    width: 100%;
    height: fit-content;
    border-radius: 10px;
    min-height: auto;
  }
}

div.edit-payment-modal-content {
  height: initial;
}

.invoice-title-mobile {
  margin: $space-30 0 $space-15;
  line-height: 1;
  color: $white;

  @include desktop {
    display: none;
  }
}

.plan-description {
  background: $white;
  margin-top: $space-20;

  @include tablet {
    max-width: 830px;
    width: 100%;
  }
}

.upgrade-level-start {
  strong {
    font-weight: 700;
  }

  &.mobile {
    color: $white;

    @include tablet {
      display: none;
    }
  }

  &.desktop {
    display: none;

    @include tablet {
      display: block;
    }
  }
}
