import cache from './cache';

const TTL_TYPE = {
  medium: 1,
  long: 2,
} as const;

type ApiResponse = {
  status?: string | 'error';
};

type TtlType = (typeof TTL_TYPE)[keyof typeof TTL_TYPE];

const parseCookieHeader = (cookieHeader: string): Record<string, string> => {
  const items = cookieHeader.split(';').filter((value) => value);
  const parsed = {};

  for (let i = 0; i < items.length; i++) {
    const nameValue = items[i].split('=');

    parsed[nameValue[0].trim()] = nameValue[1];
  }

  return parsed;
};

const getExpiration = (expiration: TtlType) => {
  switch (expiration) {
    case TTL_TYPE.long:
      return Number(process.env.cacheLongTerm);
    case TTL_TYPE.medium:
    default:
      return Number(process.env.cacheMediumTerm);
  }
};

const cached = <T extends ApiResponse>(
  hostname: string,
  url: string,
  term: TtlType,
  options: RequestInit,
): Promise<T | ApiResponse> => {
  return cache.cached(`${hostname}${url}`, getExpiration(term), options);
};

const serverAPIRoute = <T extends ApiResponse>(
  url: string,
  term: TtlType,
  options: RequestInit = {},
) => {
  return cached<T>(process.env.INTERNAL_NEXT_URL, url, term, options);
};

const clientAPIRoute = <T extends ApiResponse>(
  url: string,
  term: TtlType = TTL_TYPE.medium,
  options: RequestInit = {},
): Promise<T | ApiResponse> | ApiResponse => {
  try {
    return cached<T>(process.env.externalApiUrl, url, term, options);
  } catch (error) {
    console.error('Failed to fetch clientAPIRoute. Url: ', url, error);

    return { status: 'error' };
  }
};

const clientGateway = <T extends ApiResponse>(
  url: string,
  headers: Record<string, string> = {},
  term: TtlType,
  options: RequestInit = {},
): Promise<T | ApiResponse> | ApiResponse => {
  let processedHeaders = options;

  if (headers.cookie) {
    const cookies = parseCookieHeader(headers.cookie);

    processedHeaders = { headers: { ...cookies }, ...options };
  }

  try {
    return cached<T>(process.env.publicGateway, url, term, processedHeaders);
  } catch (error) {
    console.error('Failed to fetch clientGateway. Url: ', url, error);

    return { status: 'error' };
  }
};

const serverGateway = <T extends ApiResponse>(
  url: string,
  headers: Record<string, string> = {},
  term: TtlType,
  options: RequestInit = {},
) => {
  let processedHeaders = options;
  const ip = headers['x-forwarded-for'];
  const blackFire = headers['x-blackfire-query'];
  const referer = headers['referer'];

  if (headers.cookie) {
    const cookies = parseCookieHeader(headers.cookie);

    processedHeaders = { headers: { ...cookies }, ...options };
  }

  if (ip) {
    processedHeaders.headers = {
      ...processedHeaders.headers,
      'x-forwarded-for': ip,
    };
  }

  if (blackFire) {
    processedHeaders.headers = {
      ...processedHeaders.headers,
      'x-blackfire-query': blackFire,
    };
  }

  if (referer) {
    processedHeaders.headers = {
      ...processedHeaders.headers,
      referer: referer,
    };
  }

  return cached<T>(process.env.internalGateway, url, term, processedHeaders);
};

const clearCache = async (prefix: string, pattern: string) => {
  await cache.clear(prefix, pattern);
};

const updateCache = async (prefix: string, pattern: string) => {
  await cache.update(prefix, pattern);
};

export default {
  clearCache,
  updateCache,
  serverAPIRoute,
  clientAPIRoute,
  clientGateway,
  serverGateway,
  TTL_TYPE_MEDIUM: TTL_TYPE.medium,
  TTL_TYPE_LONG: TTL_TYPE.long,
};
