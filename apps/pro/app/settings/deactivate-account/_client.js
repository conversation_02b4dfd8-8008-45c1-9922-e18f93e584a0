'use client';
import React, { useCallback, useEffect, useState } from 'react';
import styles from '@styles/deactivate-account.module.scss';
import { useRouter } from 'next/navigation';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import {
  Button,
  Checkbox,
  HeaderMobile,
  Input,
  Loading,
  MainLayout,
  ModalContentProtection,
} from '@components';
import { useNotifications } from '@contexts/NotificationContext';
import { useAuth } from '@contexts/AuthContext';
import { ErrorMessage } from '@constants/form';
import Api from '@services/api';
import { Amp } from '@services/amp';

const DeactivateAccountClient = ({ identifier, isPasswordSet }) => {
  const [showContentProtectionForm, setContentProtectionForm] = useState(false);
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const { setNotification } = useNotifications();
  const { accountId, authLogoutWithTracking } = useAuth();

  useEffect(() => {
    setContentProtectionForm(isPasswordSet);
  }, [isPasswordSet]);

  const formik = useFormik({
    initialValues: {
      reason: '',
      agree: false,
    },
    onSubmit: async (values) => {
      setLoading(true);

      const discontinueSubscriptionResponse = await Api.clientside(
        `/accounts/${accountId}?reason=${values.reason}`,
        {
          method: 'DELETE',
        },
      );

      if (discontinueSubscriptionResponse.status !== 'ok') {
        setNotification({
          type: 'error',
          timeout: '5000',
          message:
            discontinueSubscriptionResponse.message || ErrorMessage.Unexpected,
        });
      } else {
        Amp.trackAsync(Amp.events.deactivateAccount)
          .catch((error) => console.error('Tracking error:', error))
          .finally(() => {
            authLogoutWithTracking();
          });
      }

      setLoading(false);
    },
    validationSchema: Yup.object({
      agree: Yup.bool().oneOf([true]),
      reason: Yup.string().required(ErrorMessage.FieldRequired),
    }),
  });

  const navigateBack = useCallback(() => {
    router.back();
  }, [router]);

  const navigateBackToSettings = useCallback(() => {
    router.push('/settings');
  }, [router]);

  const closeContentProtectionForm = useCallback(() => {
    setContentProtectionForm(false);
  }, []);

  useEffect(() => {
    Amp.track(Amp.events.viewSettings, {
      name: 'deactivate account',
    });
  }, []);

  return (
    <MainLayout
      isDefaultHeaderVisible
      isSaleHeaderVisible
      isMobileMenuVisible
      isUserMenuVisible
    >
      <HeaderMobile
        onClose={navigateBack}
        title="Deactivate your account"
        hideOnTablet
      />
      <section className={styles['deactivate-account-section']}>
        <div className={styles['deactivate-account-container']}>
          <h1 className={styles['deactivate-account-title']}>
            Deactivate your account
          </h1>
          <div className={styles['deactivate-account-description']}>
            <p>
              Keep in mind that this <b>action is irreversible</b> and
              information, including your photo, video and audio files will be
              lost.
            </p>
          </div>
          <form
            className={styles['deactivate-account-form']}
            onSubmit={formik.handleSubmit}
          >
            <Input
              name="reason"
              placeholder="Please, provide the reason for deactivation"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.reason}
              isTouched={formik.touched.reason}
              error={formik.errors.reason}
            />
            <div className={styles['deactivate-account-form-row']}>
              <Checkbox
                name="agree"
                onChange={formik.handleChange}
                value={formik.values.agree}
                error={formik.errors.agree}
                onBlur={formik.handleBlur}
                isTouched={formik.touched.agree}
              >
                <span className={styles['deactivate-account-form-label']}>
                  I understand the consequences
                </span>
              </Checkbox>
            </div>
            {loading ? (
              <Loading minHeight="40px" padding="0" />
            ) : (
              <>
                <Button
                  onClick={navigateBackToSettings}
                  label="Give another try"
                  color="green-gradient"
                  minWidth="280px"
                />
                <button
                  disabled={!(formik.isValid && formik.dirty)}
                  type="submit"
                  className={styles['deactivate-button']}
                >
                  Deactivate my account
                </button>
              </>
            )}
          </form>
        </div>
        {showContentProtectionForm && (
          <ModalContentProtection
            identifier={identifier}
            onClose={closeContentProtectionForm}
          />
        )}
      </section>
    </MainLayout>
  );
};

export default DeactivateAccountClient;
