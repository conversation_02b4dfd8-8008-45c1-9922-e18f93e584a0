'use client';
import { memo } from 'react';
import styles from './CarouselPaginatorDot.module.scss';
import cn from 'classnames';

export const CarouselPaginatorDot = ({
  selected,
  onClick,
  paginationColor = 'violet',
}) => {
  return (
    <button
      className={cn(styles.dot, styles[paginationColor], {
        [styles.selected]: selected,
      })}
      type="button"
      onClick={onClick}
    />
  );
};

export default memo(CarouselPaginatorDot);
