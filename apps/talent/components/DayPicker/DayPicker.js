'use client';
import React, { useState, memo, useEffect } from 'react';
import 'react-day-picker/style.css';
import styles from './DayPicker.module.scss';
import {
  DayPicker as DatePicker,
  getDefaultClassNames,
} from 'react-day-picker';
import dayjs from 'dayjs';
import cn from 'classnames';
import Select from '../FormFields/Select/Select';
import Footer from './Footer';

const DayPicker = ({
  onSelect,
  onClose,
  initialValue,
  selected,
  endMonth,
  startMonth,
}) => {
  const [date, setDate] = useState();
  const defaultClassNames = getDefaultClassNames();

  useEffect(() => {
    if (
      dayjs(selected).isAfter(dayjs(startMonth), 'day') &&
      dayjs(selected).isBefore(dayjs(endMonth), 'day')
    ) {
      setDate(selected);
    } else {
      setDate(endMonth);
    }
  }, [selected]);

  const setNextMonth = () => {
    const nextMonth = dayjs(date).add(1, 'month').toDate();

    onSelect(nextMonth);
  };

  const setPrevMonth = () => {
    const prevMonth = dayjs(date).subtract(1, 'month').toDate();

    onSelect(prevMonth);
  };

  return (
    <DatePicker
      selected={date}
      onSelect={onSelect}
      month={date}
      endMonth={endMonth}
      startMonth={startMonth}
      captionLayout="dropdown"
      mode="single"
      onNextClick={() => setNextMonth()}
      onPrevClick={() => setPrevMonth()}
      classNames={{
        root: cn(defaultClassNames.root, styles['rdp-root']),
        selected: cn(defaultClassNames.selected, styles['rdp-selected']),
        month_grid: cn(defaultClassNames.month_grid, styles['month-grid']),
        months: cn(defaultClassNames.months, styles.months),
        weeks: cn(defaultClassNames.weeks, styles.weeks),
        day: cn(defaultClassNames.day, styles.day),
        chevron: cn(defaultClassNames.chevron, styles.chevron),
        nav: cn(defaultClassNames.nav, styles.nav),
        dropdowns: cn(defaultClassNames.dropdowns, styles.dropdowns),
        footer: cn(defaultClassNames.footer, styles.footer),
      }}
      formatters={{
        formatWeekdayName: (date) => dayjs(date).format('ddd'),
      }}
      onDayClick={onSelect}
      disabled={{ after: endMonth }}
      footer={<Footer onClose={onClose} />}
      components={{
        YearsDropdown: (props) => {
          return (
            <Select
              name={'year'}
              style="rdp-year"
              value={props.value}
              options={props.options}
              setFormFieldTouched={() => {}}
              onChange={(e) => {
                props.onChange({ target: { value: e } });
                onSelect(dayjs(selected).year(e));
              }}
            />
          );
        },
        MonthsDropdown: (props) => {
          return (
            <Select
              name={'month'}
              style="rdp-month"
              value={props.value}
              options={props.options}
              setFormFieldTouched={() => {}}
              onChange={(e) => {
                props.onChange({ target: { value: e } });
                onSelect(dayjs(selected).month(e));
              }}
            />
          );
        },
      }}
    />
  );
};

export default memo(DayPicker);
