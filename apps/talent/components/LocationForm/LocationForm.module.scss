@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.location-form {
  max-width: 814px;
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: $space-30 $space-20;

  @include tablet {
    padding: $space-40 $space-30 0;
    border: 1px solid $grey-60;
    border-radius: 10px;
  }

  @include desktop {
    gap: $space-20;
  }
}

.location-form-row {
  display: flex;
  flex-direction: column;

  @include desktop {
    justify-content: space-between;
    flex-direction: row;
  }
}

.location-form-field {
  padding: $space-10 0;

  @include desktop {
    flex-grow: 1;
    padding: 0 $space-20;
  }
}

.location {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  height: 100%;
}

.location-form-actions {
  display: none;

  @include tablet {
    border-top: 1px solid $grey-60;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: $space-20;
    margin: $space-40 (-$space-30) 0;
  }

  @include desktop {
    margin: $space-20 (-$space-30) 0;
  }
}

.location-form-button {
  color: $blue-100;
  font-weight: 400;
  border: none;
  background-color: $white;
  font-size: 16px;
  cursor: pointer;

  &:disabled {
    opacity: 0.3;
    cursor: default;
  }
}
