'use client';
import React, { memo, useState } from 'react';
import styles from './ProfileInfo.module.scss';
import { useNotifications } from '@contexts/NotificationContext';
import { useFormik } from 'formik';
import { Select } from '@components';
import Api from '@services/api';
import cn from 'classnames';
import AppearanceValue from './FieldComponents/AppearanceValue';
import { multipleProfileOptions } from '@constants/multipleProfileOptions';
import { EditToggle } from './EditToggle';
import { ErrorMessage } from '@constants/form';

const MultipleProfile = ({ profileId, multiples, refreshProfileDetails }) => {
  const [isEditing, setIsEditing] = useState(false);
  const { setNotification } = useNotifications();
  const edit = () => setIsEditing(true);
  const label =
    multipleProfileOptions.find((option) => Number(option.value) === multiples)
      ?.title || 'n/a';

  const onSubmit = async (values) => {
    if (!formik.dirty) {
      setIsEditing(false);
      formik.resetForm({ values });

      return;
    }

    const body = new FormData();

    body.append('multiples', values.multiples);

    const response = await Api.clientside(`/profiles/${profileId}`, {
      body: body,
      method: 'PATCH',
    });

    if (response.status !== 'ok') {
      setNotification({
        type: 'error',
        timeout: '5000',
        message: response.message || ErrorMessage.Unexpected,
      });
    } else {
      await refreshProfileDetails();
    }

    formik.resetForm({ values });
    setIsEditing(false);
  };

  const formik = useFormik({
    initialValues: { multiples: multiples ? String(multiples) : '0' },
    onSubmit,
  });

  return (
    <section>
      <div className={styles['profile-section-title']}>
        Profile for Twins, Triplets, Quadruplets:
        <EditToggle
          isEditing={isEditing}
          onEdit={edit}
          onSave={formik.handleSubmit}
          disabled={formik.isSubmitting}
        />
      </div>

      {!isEditing ? (
        <div
          className={cn(styles['profile-section-row'], styles.clickable)}
          onClick={edit}
        >
          <AppearanceValue
            label="Type of identical siblings"
            hasValue={!!multiples}
          >
            {label}
          </AppearanceValue>
        </div>
      ) : (
        <form
          className={cn(
            styles['profile-section-row'],
            styles['profile-multiples-form'],
          )}
        >
          <div>
            <div className={styles['profile-property']}>
              <div className={styles['profile-property-value']}>
                <Select
                  name="multiples"
                  onChange={(value) => formik.setFieldValue('multiples', value)}
                  value={formik.values.multiples}
                  isTouched={formik.touched.multiples}
                  error={formik.errors.multiples}
                  placeholder="Type of identical siblings"
                  options={multipleProfileOptions}
                  setFormFieldTouched={() =>
                    formik.setFieldTouched('multiples')
                  }
                />
              </div>
            </div>
          </div>
        </form>
      )}
    </section>
  );
};

export default memo(MultipleProfile);
