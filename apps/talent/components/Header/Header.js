'use client';
import React, { memo } from 'react';
import Logo from '/public/assets/logo/logo-4.svg';
import styles from './Header.module.scss';
import MenuItem from './MenuItem';
import Link from 'next/link';
import { desktopLinks } from '@constants/menuLinks/menu-links';
import { Amp } from '@services/amp';
import { useFeature } from '@contexts/FeatureContext';
import { usePathname, useRouter } from 'next/navigation';
import { Button, PremiumActions } from '@components';

const Header = ({
  isAuthenticated,
  isPaidOrDelayed,
  canUpgradeExistingSubscription,
}) => {
  const path = usePathname();
  const router = useRouter();
  const { premiumActionsButtonEnabled } = useFeature();

  const navigateToUpgradePage = () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'subscribe',
      scope: Amp.element.scope.global,
      section: Amp.element.section.header,
      type: Amp.element.type.button,
      upgrade_to_12: !!canUpgradeExistingSubscription,
    });

    router.push(`/upgrade${canUpgradeExistingSubscription ? '-12' : ''}`);
  };

  return (
    <div className={styles.header}>
      <Link
        href={`${process.env.publicUrl}/castingcalls`}
        passHref
        aria-label="allcasting"
        className={styles['logo-link']}
      >
        <Logo className={styles.logo} />
      </Link>
      <div className={styles.navbar}>
        {desktopLinks.map(({ label, href }) => (
          <MenuItem key={label} label={label} href={href} />
        ))}
        {isAuthenticated &&
          (!isPaidOrDelayed || canUpgradeExistingSubscription) &&
          !path.includes('checkout') && (
            <div className={styles['header-subscribe-container']}>
              <div className={styles['header-divider-dot']} />
              <Button
                label={
                  canUpgradeExistingSubscription
                    ? 'Upgrade your plan'
                    : 'Subscribe now'
                }
                color="green-gradient"
                minWidth="220px"
                disabled={path.includes('upgrade')}
                onClick={navigateToUpgradePage}
                className={styles['subscribe-btn-animation-highlight']}
              />
            </div>
          )}
        {premiumActionsButtonEnabled && (
          <>
            <span className={styles.divider}></span>
            <PremiumActions />
          </>
        )}
      </div>
    </div>
  );
};

export default memo(Header);
