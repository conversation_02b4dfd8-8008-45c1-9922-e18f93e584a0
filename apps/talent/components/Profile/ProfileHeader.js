'use client';
import React, { memo, useState } from 'react';
import { useProfileContext } from '@contexts/ProfileContext';
import Api from '@services/api';
import styles from './Profile.module.scss';
import { useAuth } from '@contexts/AuthContext';
import { useNotifications } from '@contexts/NotificationContext';
import cn from 'classnames';
import StarIcon from '../../public/assets/icons/icon-star-filled-slim.svg';
import EditIcon from '../../public/assets/icons/icon-edit.svg';
import { maxProfileRating } from '@constants/profile';
import { Amp } from '@services/amp';
import { SaveButton } from './EditToggle';
import { ErrorMessage, NAME_REGEX } from '@constants/form';
import { InputFormik } from '@components';
import { Form, Formik } from 'formik';
import * as Yup from 'yup';

const ProfileHeader = ({
  refreshUserProfiles,
  refreshProfileDetails,
  openRatingProgress,
}) => {
  const [isFormActive, setIsFormActive] = useState(false);

  const { firstName, lastName, fullName, rating, statusProgress } =
    useProfileContext();
  const { setNotification } = useNotifications();
  const { profileId } = useAuth();

  const initialValues = {
    firstName: NAME_REGEX.test(firstName) ? firstName : '',
    lastName: NAME_REGEX.test(lastName) ? lastName : '',
  };

  const validationSchema = Yup.object({
    firstName: Yup.string()
      .required(ErrorMessage.NameRequired)
      .max(35, ErrorMessage.NamePattern)
      .matches(NAME_REGEX, ErrorMessage.NamePattern),
    lastName: Yup.string()
      .required(ErrorMessage.LastNameRequired)
      .max(35, ErrorMessage.NamePattern)
      .matches(NAME_REGEX, ErrorMessage.NamePattern),
  });

  const onEdit = () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'edit name',
      scope: Amp.element.scope.profile,
      section: Amp.element.section.name,
      type: Amp.element.type.button,
    });

    setIsFormActive(true);
  };

  const onRatingClick = () => {
    openRatingProgress();
  };

  const toggleFormActive = () => {
    setIsFormActive(false);
  };

  const onSubmit = async (values) => {
    Amp.track(Amp.events.elementClicked, {
      name: 'save name',
      scope: Amp.element.scope.profile,
      section: Amp.element.section.name,
      type: Amp.element.type.button,
    });

    const body = new FormData();

    body.append('firstname', values.firstName);
    body.append('lastname', values.lastName);

    const { status, message } = await Api.clientside(`/profiles/${profileId}`, {
      method: 'PATCH',
      body: body,
    });

    if (status !== 'ok') {
      setNotification({
        type: 'error',
        message: message,
        timeout: '5000',
      });
    } else {
      await refreshUserProfiles();
      await refreshProfileDetails();
      toggleFormActive();
    }
  };

  return (
    <div id="name" className={styles['profile-overview-header']}>
      {!isFormActive ? (
        <>
          <h1
            className={cn(styles['profile-name'], {
              [styles.smaller]: lastName.length > 13 || firstName.length > 13,
            })}
          >
            {fullName}
            <button onClick={onEdit} className={styles['edit-button-header']}>
              <EditIcon />
            </button>
          </h1>
          <div
            className={cn(
              styles['profile-rating'],
              styles[`profile-rating-${statusProgress}`],
            )}
            onClick={onRatingClick}
          >
            <StarIcon className={styles['profile-rating-star']} />
            <span className={styles['profile-rating-count']}>
              {rating?.toLocaleString('en-EN')}{' '}
              <span>/ {maxProfileRating.toLocaleString('en-EN')}</span>
            </span>
          </div>
        </>
      ) : (
        <Formik
          initialValues={initialValues}
          onSubmit={onSubmit}
          validationSchema={validationSchema}
        >
          {({ isSubmitting }) => (
            <>
              <div
                onClick={toggleFormActive}
                className={cn(styles['edit-name-layer'])}
              />
              <Form className={styles['profile-section-title-edit']}>
                <div className={styles['profile-section-title']}>
                  First and last name:
                  <SaveButton type="submit" disabled={isSubmitting} />
                </div>
                <div className={styles['name-field-change']}>
                  <InputFormik name="firstName" placeholder="First name" />
                  <InputFormik name="lastName" placeholder="Last name" />
                  <SaveButton
                    type="submit"
                    disabled={isSubmitting}
                    className={styles['save-button-name-mobile']}
                  />
                  <div
                    onClick={toggleFormActive}
                    className={cn(
                      styles['profile-panel-info-close'],
                      styles['edit-name-close'],
                    )}
                  />
                </div>
              </Form>
            </>
          )}
        </Formik>
      )}
    </div>
  );
};

export default memo(ProfileHeader);
