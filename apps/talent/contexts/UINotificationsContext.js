'use client';
import { usePathname } from 'next/navigation';
import { createContext, useContext, useEffect, useRef, useState } from 'react';
import dayjs from 'dayjs';
import { useAuth } from './AuthContext';
import Api from '../services/api';
import { ALLOWED_PATHS, UIEvent } from '@constants/uiEvents';

const UINotificationsContext = createContext({});

export const useUINotificationsContext = () =>
  useContext(UINotificationsContext);

const MINUTE = 1_000 * 60;

const getEvents = (events) => {
  const params = new URLSearchParams();

  events.forEach((event) => {
    params.append('events[]', event);
  });

  return Api.clientside(`/tracking/events?${params.toString()}`);
};

const calculateDayDiff = (lastEvent) => {
  const last = lastEvent ? dayjs(lastEvent.created_at) : dayjs(0);
  const now = dayjs();

  return now.diff(last, 'day');
};

export const UINotificationsProvider = ({ children }) => {
  const [phoneNumberModal, setPhoneNumberModal] = useState({
    enabled: false,
    show: false,
    occurrence: 0,
  });

  const path = usePathname();
  const { userProfiles, refreshUserProfiles } = useAuth();
  const isFetching = useRef(false);

  useEffect(() => {
    if (isFetching.current || phoneNumberModal.enabled) return;

    const isPhoneSet = 'phone' in (userProfiles?.[0] || {});
    const phone = userProfiles?.[0]?.phone;
    const isUrlAllowed = ALLOWED_PATHS.some((allowedPath) =>
      path.includes(allowedPath),
    );

    if (!isUrlAllowed) return;
    if (!isPhoneSet) {
      // Authenticated users might not have userProfiles refreshed
      refreshUserProfiles();

      return;
    }
    if (phone) return;

    isFetching.current = true;

    getEvents([UIEvent.PhoneNumberModalViewed]).then((data) => {
      const events = data.items || [];
      const phoneModalEvents = events.filter(
        ({ event_name }) => event_name === UIEvent.PhoneNumberModalViewed,
      );
      const diff = calculateDayDiff(phoneModalEvents[0]);

      if (phoneModalEvents.length < 2 && diff >= 2) {
        setPhoneNumberModal((prev) => ({
          ...prev,
          enabled: true,
          occurrence: phoneModalEvents.length + 1,
        }));
      }
      isFetching.current = false;
    });
  }, [userProfiles, path, refreshUserProfiles, phoneNumberModal.enabled]);

  useEffect(() => {
    if (phoneNumberModal.enabled) {
      const timeoutId = setTimeout(() => setShowPhoneNumberModal(true), MINUTE);

      return () => clearTimeout(timeoutId);
    }
  }, [phoneNumberModal.enabled]);

  const setShowPhoneNumberModal = (val) =>
    setPhoneNumberModal((prev) => ({ ...prev, show: val }));

  return (
    <UINotificationsContext.Provider
      value={{
        showPhoneNumberModal: phoneNumberModal.show,
        setShowPhoneNumberModal,
        phoneNumberModalOccurrence: phoneNumberModal.occurrence,
      }}
    >
      {children}
    </UINotificationsContext.Provider>
  );
};
