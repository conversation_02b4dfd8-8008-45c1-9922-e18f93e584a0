@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.decline-reason-container {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 2px;
  padding: 0 $space-10;
  background-color: rgb($black, 0.7);
  color: $white;
  font-weight: 700;
  font-size: 12px;
  text-align: center;
  cursor: default;
  line-height: 1.2;
}

.decline-icon {
  flex-shrink: 0;
}

.declined-image-container {
  position: relative;
  padding-top: 133.124%;
}

.action-button {
  margin-top: $space-10;
  padding: 6px $space-20;
  text-transform: capitalize;
  font-size: 12px;
  background: $blue-100 none;

  @include tablet {
    padding: $space-5 16px;
  }
}

.icon-hint {
  position: absolute;

  @include tablet {
    display: none;
  }
}

.decline-reason-icon-container {
  position: relative;
  display: flex;
  cursor: pointer;
  margin-bottom: $space-10;

  @include tablet {
    cursor: default;
  }
}

.declined-image-inner-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: $grey-10;

  img {
    width: 100%;
  }

  &.small {
    .decline-reason-icon-container {
      .icon-blocked {
        width: 18px;
        height: 18px;
      }

      .icon-hint {
        top: -3px;
        right: -3px;
        width: 10px;
        height: 10px;
      }
    }
  }

  &.medium {
    .decline-reason-icon-container {
      margin-bottom: $space-5;

      .icon-blocked {
        width: 25px;
        height: 25px;
      }

      .icon-hint {
        top: -4px;
        right: -4px;
        width: 13px;
        height: 13px;
      }
    }
  }

  &.large {
    .decline-reason-icon-container {
      .icon-blocked {
        width: 40px;
        height: 40px;
      }

      .icon-hint {
        top: -5px;
        right: -5px;
        width: 20px;
        height: 20px;
      }
    }

    .decline-reason-container {
      font-size: 25px;

      @include tablet {
        font-size: 16px;
      }
    }

    .action-button {
      margin-top: $space-20;
      font-size: 14px;

      @include tablet {
        margin-top: $space-30;
        font-size: 12px;
      }
    }
  }
}

.desktop {
  display: none;

  @include tablet {
    display: block;
  }
}

.mobile {
  @include tablet {
    display: none;
  }
}

.modal-overlay {
  padding: $space-20;
}

.modal-container {
  border-radius: 15px;
}
