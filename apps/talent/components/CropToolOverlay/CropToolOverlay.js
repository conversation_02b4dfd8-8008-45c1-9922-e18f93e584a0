'use client';
import React, { memo, useEffect, useRef, useState } from 'react';
import styles from './CropToolOverlay.module.scss';
import { useProfileContext } from '@contexts/ProfileContext';
import { CropTool } from '@components';

const CropToolOverlay = ({
  onClose,
  image,
  type,
  skipDefaultCrop = false,
  isCurrentTitlePhoto = false,
}) => {
  const overlayRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [previewImage, setPreviewImage] = useState(null);
  const { isTitleImageRelatedToExistingPhoto, uploadImage, uploadTitleImage } =
    useProfileContext();
  const [showTitleCropTool, setShowTitleCropTool] = useState(false);

  useEffect(() => {
    if (skipDefaultCrop) {
      setPreviewImage(image);
    }
    setShowTitleCropTool(skipDefaultCrop);
  }, [image, skipDefaultCrop]);

  useEffect(() => {
    const currentRef = overlayRef.current;
    const callback = (event) => {
      if (onClose && event.target.id === 'overlay') {
        onClose(event);
      }
    };

    if (currentRef && !loading) {
      currentRef.addEventListener('mousedown', callback);
    } else {
      currentRef.removeEventListener('mousedown', callback);
    }

    return () => currentRef.removeEventListener('mousedown', callback);
  }, [overlayRef, onClose, loading]);

  const uploadPhoto = async (image) => {
    setLoading(true);
    const imageUploadResponse = await uploadImage(
      image,
      type,
      isCurrentTitlePhoto,
    );

    setLoading(false);

    if (
      imageUploadResponse &&
      (!isTitleImageRelatedToExistingPhoto || isCurrentTitlePhoto)
    ) {
      setPreviewImage(imageUploadResponse);
      setShowTitleCropTool(true);
    } else {
      onClose();
    }
  };

  const uploadTitlePhoto = async (image) => {
    setLoading(true);

    await uploadTitleImage(image, previewImage.id);

    setLoading(false);
    setShowTitleCropTool(false);

    onClose();
  };

  return (
    <div id="overlay" ref={overlayRef} className={styles.overlay}>
      <div className={styles.content}>
        {showTitleCropTool ? (
          <CropTool
            title="Adjust Profile Picture"
            cropShape="round"
            image={previewImage.url}
            onCrop={uploadTitlePhoto}
            onClose={onClose}
            aspectRatio={1}
            loading={loading}
          />
        ) : (
          <CropTool
            image={image}
            onCrop={uploadPhoto}
            onClose={onClose}
            loading={loading}
          />
        )}
      </div>
    </div>
  );
};

export default memo(CropToolOverlay);
