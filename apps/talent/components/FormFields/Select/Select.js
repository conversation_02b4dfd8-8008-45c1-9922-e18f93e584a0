'use client';
import React, { memo, useCallback, useEffect, useRef, useState } from 'react';
import styles from './Select.module.scss';
import cn from 'classnames';

const Select = ({
  name,
  options,
  placeholder,
  hideFloatingPlaceholder = false,
  onChange,
  value,
  isTouched,
  error,
  setFormFieldTouched,
  style = '',
  classNameOptions = '',
}) => {
  const [showOptions, setShowOptions] = useState(false);
  const showError = isTouched && error;
  const dropdownRef = useRef(null);
  const optionsRef = useRef([]);
  const inputRef = useRef(null);

  const focusOption = useCallback(
    (index, isInitialFocus = false) => {
      const currentOption = optionsRef.current.find(
        (node) => node.id === `${name}-option-${index}`,
      );

      dropdownRef.current.scrollTop = isInitialFocus
        ? currentOption?.offsetTop
        : currentOption?.scrollHeight + currentOption.scrollTop;
      currentOption.focus();
    },
    [name],
  );

  useEffect(() => {
    if (showOptions) {
      focusOption(getSelectedOptionIndex(), true);
    }
  }, [focusOption, options, showOptions, value]);

  useEffect(() => {
    const closeOptions = (e) => {
      if (!String(e.target.id).includes(name)) {
        if (showOptions) {
          setFormFieldTouched();
        }
        setShowOptions(false);
      }
    };

    if (showOptions) {
      document.addEventListener('click', closeOptions);
    } else {
      document.removeEventListener('click', closeOptions);
    }

    return () => document.removeEventListener('click', closeOptions);
  }, [name, setFormFieldTouched, showOptions]);

  const toggleShowOptions = () => {
    setShowOptions(!showOptions);
  };

  const onTriggerClick = (event) => {
    event.preventDefault();
    setShowOptions(!showOptions);

    if (showOptions) {
      setFormFieldTouched();
    }
  };

  const focusInput = () => {
    inputRef?.current?.focus();
  };

  const getDisplayValue = (value, options) => {
    const option = options.find((option) => option.value === value);

    return option ? option.title || option.label : '';
  };

  const getSelectedOptionIndex = () => {
    const index = options.findIndex((option) => option.value === value);

    return index !== -1 ? index : 0;
  };

  const onOptionKeyDown = (event, index, value) => {
    event.preventDefault();

    switch (event.code) {
      case 'ArrowUp':
        if (index !== 0) {
          focusOption(index - 1);
        } else if (index === 0) {
          focusInput();
          setShowOptions(false);
          setFormFieldTouched();
        }
        break;
      case 'ArrowDown':
        if (index !== options.length - 1) {
          focusOption(index + 1);
        }
        break;
      case 'Enter':
      case 'Space':
        setFormFieldTouched();
        onChange(value);
        toggleShowOptions();
        focusInput();
        break;
      case 'Tab':
        setFormFieldTouched();
        toggleShowOptions();
        focusInput();
        break;
      default:
        break;
    }
  };

  const onOptionClick = (event, value) => {
    event.preventDefault();
    setFormFieldTouched();
    onChange(value);
    toggleShowOptions();
    focusInput();
  };

  const onInputKeyDown = (event) => {
    switch (event.code) {
      case 'ArrowUp':
        event.preventDefault();

        if (showOptions) {
          const index = getSelectedOptionIndex();

          if (index === 0) {
            setFormFieldTouched();
            toggleShowOptions();
          }
        }
        break;
      case 'Enter':
      case 'Space':
        event.preventDefault();
        toggleShowOptions();
        focusOption(getSelectedOptionIndex());

        if (showOptions) {
          setFormFieldTouched();
        }
        break;
      case 'ArrowDown':
        event.preventDefault();

        if (showOptions) {
          focusOption(getSelectedOptionIndex());
        } else {
          toggleShowOptions();
        }
        break;
      case 'Tab':
        setFormFieldTouched();

        if (showOptions) {
          setShowOptions(false);
        }
        break;
      default:
        break;
    }
  };

  return (
    <div
      className={cn(styles['select-container'], styles[style], {
        [styles['select-error']]: showError,
      })}
    >
      <div
        id={`${name}-container`}
        onClick={onTriggerClick}
        className={cn(styles['select-field'], {
          [styles.open]: showOptions,
          [styles.error]: showError,
        })}
      >
        <input
          id={name}
          className={styles.select}
          type="text"
          value={getDisplayValue(value, options)}
          onKeyDown={onInputKeyDown}
          ref={inputRef}
          readOnly
        />
        <div
          id={`${name}-select-trigger`}
          className={cn(styles['select-trigger'], {
            [styles.open]: showOptions,
          })}
        ></div>
        <div
          id={`${name}-select-focus-border`}
          className={styles['focus-border']}
        ></div>
        <label
          id={`${name}-label`}
          className={cn(styles.label, {
            [styles['floating-label']]: value || showOptions,
            [styles['has-value']]: value,
            [styles['hide-label']]: hideFloatingPlaceholder,
          })}
          htmlFor={name}
        >
          {placeholder}
        </label>
      </div>
      {showOptions && (
        <div ref={dropdownRef} className={cn(styles.options, classNameOptions)}>
          {options.map((option, i) => (
            <div
              tabIndex={i}
              ref={(element) => (optionsRef.current[i] = element)}
              id={`${name}-option-${i}`}
              onClick={(e) => onOptionClick(e, option.value)}
              onKeyDown={(e) => onOptionKeyDown(e, i, option.value)}
              key={i}
              className={cn(styles.option, {
                [styles['selected-option']]:
                  option.value === value || option.selected,
              })}
            >
              {option.title || option.label}
            </div>
          ))}
        </div>
      )}
      {showError && typeof error !== 'boolean' && (
        <div className={styles['error-message']}>{error}</div>
      )}
    </div>
  );
};

export default memo(Select);
