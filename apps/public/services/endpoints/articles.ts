import type {
  IdSlugTitle,
  IdTitleName,
  IdSlugTitleName,
  RelHref,
  PhpOrEmptyList,
} from './types';
import type { Nullable } from '../../types/utility';
import { removeEmptyList } from './types';
import Api from '../api';
import {
  ARTICLE_CATEGORIES,
  ARTICLE_CATEGORIES_LABELS,
  ARTICLE_LESSONS_CATEGORIES,
  ARTICLE_NEWS_CATEGORIES,
  ARTICLE_PAGE_LIMIT,
  ARTICLE_STATUS,
  mapCategorySlug,
} from '../../constants/articles';

// ToDo: Remove this after migration
const mapCategoryTemporary = ({ id, slug: oldSlug }: IdSlugTitleName) => {
  const slug = mapCategorySlug(oldSlug);

  return {
    id,
    slug,
    title: ARTICLE_CATEGORIES_LABELS[slug] || slug,
  };
};

const mapArticle = (resource: ArticleListResource) => ({
  id: resource.id,
  title: resource.title,
  slug: resource.slug,
  // ToDo: use updated_on for sitemap, seo
  time: resource.created_on || resource.time,
  summary: resource.summary,
  categories: resource.categories.map(mapCategoryTemporary),
  author: resource.author || null,
  thumbnailImage: removeEmptyList(
    resource['thumbnail-image'],
    defaultThumbnailImage,
  ),
});

const mapFullArticle = (response: ArticleResponse) => ({
  status: 'ok' as const,
  ...mapArticle(response),
  content: response.content,
  headlineImage: removeEmptyList(response['headline-image']),
  // ToDo: modify Article component to always show headline image
  // atleast for non info pages
  showHeadlineImage: true,
  meta: response.meta,
  redirectUrl: response.redirect_url,
  articleStatus: response.article_status,
  howTo: response.how_to,
  questionsAndAnswers: response.questions_and_answers,
  tldrSummary: response.tldr_summary,
});

// ToDo: fetch needs headers?
async function fetchArticles({
  page = 1,
  limit = ARTICLE_PAGE_LIMIT,
  category = '',
  showOnHomepage = false,
}) {
  const params = new URLSearchParams({
    limit: limit.toString(),
    page: page.toString(),
  });

  // ToDo: remove after migration
  if (ARTICLE_LESSONS_CATEGORIES.includes(category)) {
    params.set('categories', ARTICLE_LESSONS_CATEGORIES.join(','));
  } else if (ARTICLE_NEWS_CATEGORIES.includes(category)) {
    params.set('categories', ARTICLE_NEWS_CATEGORIES.join(','));
  } else if (category) {
    params.set('categories', category);
  } else {
    // ToDo: backend task to filter out info category
    params.set('categories', ARTICLE_CATEGORIES.join(','));
  }
  if (showOnHomepage) {
    params.set('show_on_homepage', '1');
  }

  const url = `/articles?${params.toString()}`;
  const response: ArticleListResponse | ErrorResponse =
    await Api.serverGateway(url);

  if (response.status === 'error') {
    return response;
  }

  const articles = response.items.map(mapArticle);

  return {
    ...response,
    items: articles,
  };
}

async function fetchArticle(idOrSlug: number | string) {
  const response: ArticleResponse | ErrorResponse = await Api.serverGateway(
    `/articles/${idOrSlug}`,
  );

  if (response.status === 'error') {
    return response;
  }

  const result = mapFullArticle(response);

  return result;
}

type Article = ReturnType<typeof mapFullArticle>;
type ListArticle = ReturnType<typeof mapArticle>;
type FetchArticleResponse = Awaited<ReturnType<typeof fetchArticle>>;
type FetchArticlesResponse = Awaited<ReturnType<typeof fetchArticles>>;
type ArticleList = Extract<FetchArticlesResponse, { status: 'ok' }>;

export { fetchArticles, fetchArticle, DEFAULT_IMAGE };
export type {
  Article,
  ListArticle,
  FetchArticleResponse,
  FetchArticlesResponse,
  ArticleList,
  HowTo,
};

// generated with cursor from
// https://gitlab.dyninno.net/entertech/services/article-service/-/blob/1.2.5/src/Controller/ArticleApiController.php
// modified, tested and verified

type ArticleListResponse = {
  href: string;
  status: 'ok';
  total: number;
  items: ArticleListResource[];
  pagination: PaginationLinks;
  filters: Record<string, RelHref & { total: number }>;
  http_status: number;
};

type ArticleResponse = ArticleListResource & {
  status: 'ok';
  content: string; // From PHP getBody()
  'headline-image': PhpOrEmptyList<{
    reference: ImageSize & Nullable<{ width: number; height: number }>;
    size4: ImageSize;
    size5: ImageSize;
  }>;
  meta: {
    keywords: string | null;
    description: string | null;
  } | null;
  tags: RelHref;
  links: Record<string, RelHref>;
  http_status: number;
};

type ErrorResponse = {
  status: 'error';
  message: string;
};

const DEFAULT_IMAGE = {
  href: '/assets/lessons/article-background.webp',
};
const defaultThumbnailImage = {
  size1: DEFAULT_IMAGE,
  size2: DEFAULT_IMAGE,
  size3: DEFAULT_IMAGE,
  size4: DEFAULT_IMAGE,
  size5: DEFAULT_IMAGE,
};

type ArticleListResource = IdSlugTitle & {
  // ToDo: deprecate time in favor of created_on
  time: string; // 2025-08-12T09:29:56-07:00
  created_on: string;
  updated_on: string;
  featured: 0 | 1;
  size: number;
  article_status: ArticleStatus;
  redirect_url: string | null;
  skill_level: 'Beginner' | 'Intermediate' | 'Pro';
  type: IdTitleName;
  category: IdTitleName;
  categories: IdSlugTitleName[];
  comments: RelHref & { total: number };
  summary: string | null;
  tldr_summary: string | null;
  'story-pic': PhpOrEmptyList<{
    sizeStory: ImageSize;
  }>;
  'thumbnail-image': PhpOrEmptyList<{
    size1: ImageSize;
    size2: ImageSize;
    size3: ImageSize;
    size4: ImageSize;
    size5: ImageSize;
  }>;
  questions_and_answers: QuestionAndAnswer[];
  how_to: HowTo | null;
  author?: Author;
  intro: string | null;
};

type ArticleStatus = (typeof ARTICLE_STATUS)[keyof typeof ARTICLE_STATUS];

type Author = {
  id: number;
  name: string;
};

type QuestionAndAnswer = {
  id: number;
  question: string;
  answer: string;
};

type HowTo = {
  name: string;
  description: string;
  cta: {
    link: string | null;
    text: string | null;
  };
  items: HowToItem[];
};

type HowToItem = {
  id: number;
  title: string;
  description: string;
  pro_tip: string | null;
};

type ImageSize = RelHref & {
  name?: string | null;
};

type PaginationLinks = {
  first: RelHref;
  last: RelHref;
  next: RelHref | null;
  prev: RelHref | null;
};
