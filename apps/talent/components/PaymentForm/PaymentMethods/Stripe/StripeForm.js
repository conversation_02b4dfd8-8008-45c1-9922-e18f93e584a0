'use client';
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState,
} from 'react';
import * as Sentry from '@sentry/nextjs';
import {
  useElements,
  useStripe,
  PaymentElement,
} from '@stripe/react-stripe-js';
import { useNotifications } from '@contexts/NotificationContext';
import { useLiveChat } from '@contexts/LiveChatContext';
import { GTM_ACTIONS, GTM_CATEGORIES, GTM_EVENTS } from '@constants/analytics';
import { useAnalytics } from 'use-analytics';
import { Loading } from '@components';
import { LIVE_CHAT_GROUP } from '@constants/liveChat';
import { CookieService } from '@services/cookieService';
import { ErrorMessage } from '@constants/form';
import PaymentFormDescription from '../../PaymentFormDescription';
import PaymentFormFooter from '../../PaymentFormFooter';
import styles from '../../../../styles/checkout.module.scss';

const StripeForm = forwardRef(
  (
    {
      clientSecret,
      invoiceHash,
      updatePaymentLoading,
      isLifetime,
      price,
      isProfileOutsideUSA,
      isUpgradeForm,
      isPaymentLoading,
    },
    ref,
  ) => {
    const [isPaymentElementLoading, setIsPaymentElementLoading] =
      useState(true);
    const [isError, setIsError] = useState(false);
    const { setNotification } = useNotifications();
    const { initiateLiveChat } = useLiveChat();
    const { track } = useAnalytics();

    const stripe = useStripe();
    const elements = useElements();
    const options = {
      terms: { card: 'never', applePay: 'never', googlePay: 'never' },
      paymentMethodOrder: ['card'],
      wallets: { applePay: 'never', googlePay: 'never' },
    };

    useEffect(() => {
      if (!stripe || !clientSecret) {
        return;
      }

      const storedInvoiceHash = window.sessionStorage.getItem('invoiceHash');

      if (storedInvoiceHash !== invoiceHash) {
        window.sessionStorage.setItem('invoiceHash', invoiceHash);
      }

      const storedCookie = CookieService.getShowStripeCheckoutSuccess();

      if (!storedCookie) {
        CookieService.setShowStripeCheckoutSuccess(true);
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [stripe]);

    useImperativeHandle(ref, () => ({
      async triggerSubmit() {
        await handleSubmit();
      },
    }));

    const handleSubmit = async () => {
      if (!stripe || !elements) {
        Sentry.captureException(new Error('Stripe or elements not found'), {
          tags: {
            feature: 'stripe',
          },
          extra: {
            stripe: !!stripe,
            elements: !!elements,
          },
        });

        return;
      }

      updatePaymentLoading(true);

      const { error } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: `${process.env.baseUrl}/checkout/success`,
        },
      });

      const { type, code, decline_code, message } = error;

      Sentry.captureException(
        new Error(
          `Type: ${type};${code ? ` Code: ${code};` : ''}${decline_code ? ` Decline code: ${decline_code};` : ''}`,
        ),
        {
          tags: {
            feature: 'stripe',
          },
          extra: {
            message,
          },
        },
      );

      if (code === 'payment_intent_unexpected_state') {
        showErrorMessage(ErrorMessage.SalesClosed, false);
      } else if (
        error.type === 'card_error' ||
        error.type === 'validation_error'
      ) {
        showErrorMessage(error.message);
      } else {
        showErrorMessage(ErrorMessage.Unexpected);
      }

      initiateLiveChat(true, LIVE_CHAT_GROUP.AllcastingPayments);

      track(GTM_EVENTS.interaction, {
        target: GTM_CATEGORIES.payment,
        action: GTM_ACTIONS.submit,
        label:
          error.type === 'card_error' ? error.message : ErrorMessage.Unexpected,
      });

      updatePaymentLoading(false);
    };

    const showErrorMessage = (message, timeout = true) => {
      setNotification({
        type: 'error',
        message: message || ErrorMessage.Unexpected,
        timeout: timeout ? '15000' : null,
      });
    };

    const onReady = () => {
      setIsPaymentElementLoading(false);
    };

    const onError = (event) => {
      const { error } = event || {};
      const { type, code, setup_intent, message } = error || {};

      Sentry.captureException(new Error('PaymentElement failed to load'), {
        tags: {
          feature: 'stripe',
        },
        extra: {
          type,
          code,
          setup_intent,
          message,
        },
      });

      setIsPaymentElementLoading(false);
      setIsError(true);
    };

    return (
      <div className={styles['stripe-container']}>
        {isPaymentElementLoading && (
          <Loading minHeight="40px" padding="10px 0 20px" />
        )}
        <PaymentElement
          onLoadError={onError}
          onReady={onReady}
          id="payment-element"
          options={options}
        />
        {isError && (
          <div className={styles['error-container']}>
            <h1>Sales have closed for today.</h1>
            <p>Please try again tomorrow.</p>
          </div>
        )}
        {!isPaymentElementLoading && !isError && (
          <>
            <PaymentFormDescription
              isLifetime={isLifetime}
              price={price}
              isProfileOutsideUSA={isProfileOutsideUSA}
            />
            <PaymentFormFooter
              isStripe
              isUpgradeForm={isUpgradeForm}
              loading={isPaymentLoading}
            />
          </>
        )}
      </div>
    );
  },
);

StripeForm.displayName = 'StripeForm';

export default StripeForm;
