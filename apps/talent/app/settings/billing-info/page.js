import BillingInfoClient from './_client';
import { cookies, headers } from 'next/headers';
import { formatAccountLevelsResponse } from '@utils/formatAccountLevelsResponse';
import { formatBillingInfo } from '@utils/formatBillingInfo';
import { CookieServiceServer } from '@services/cookieServiceServer';
import Api from '@services/api';
import { extractResult } from '@utils/extractPromiseResult';

export const metadata = {
  title: 'Billing Info',
};

export default async function BillingInfoPage() {
  const cookieStore = await cookies();
  const headerStore = await headers();
  const accountId = CookieServiceServer.getAccountCookie(cookieStore);
  const resolvedUrl = '/settings/billing-info';

  const paths = [
    '/payment/billing',
    `/accounts/${accountId}/levels/last`,
    `/accounts/${accountId}/levels/upgrade`,
  ];

  const results = await Promise.allSettled(
    paths.map((path) =>
      Api.serverside(path, cookieStore, headerStore, resolvedUrl),
    ),
  );

  const [billing, levelsLast, levelsUpgrade] = results.map((result) =>
    extractResult(result, {}),
  );

  const initialBillingInfo = formatBillingInfo(billing);
  const initialSubscription = formatAccountLevelsResponse(levelsLast);
  const upgradeLevel = levelsUpgrade?.start ? levelsUpgrade : null;

  return (
    <BillingInfoClient
      initialSubscription={initialSubscription}
      initialBillingInfo={initialBillingInfo}
      upgradeLevel={upgradeLevel}
    />
  );
}
