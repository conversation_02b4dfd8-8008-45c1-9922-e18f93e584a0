import { CookieService } from '@services/cookieService';
import Api from '@services/api';
import { formatAccountLevel } from './formatAccountLevel';
import { getGatewayHeaders } from './headerHelper';

export function extractAccountIdFromToken(token) {
  return extractFromToken(token, 'Account');
}

export function extractExpiresFromAuthenticationToken(token) {
  return extractFromToken(token, 'Expires');
}

export function responseExtractProfiles(response) {
  const isTalent = responseExtractType(response) === 'talent';
  const profileUrl = isTalent ? '/profile' : '/director';

  return response.links?.profiles?.items.map((el) => {
    const baseUser = {
      titlePhotoUrl: el.title_photo_url,
      profileUrl: el.links?.personal_url?.path
        ? `${profileUrl}/${el.links.personal_url.path}`
        : `${profileUrl}/${el.id}`,
      id: el.id,
      firstName: el.firstname,
      lastName: el.lastname,
      fullName: `${el.firstname} ${el.lastname}`,
      href: el?.href || '',
      zipCode: el.links?.location?.links?.zip?.code,
      city: el.links?.location?.links?.city?.slug,
      clientId: el.client_id,
      isEmailValid:
        !!el?.links?.touches?.links?.email?.value &&
        !el.links.touches.links.email.value.endsWith(
          '@privaterelay.appleid.com',
        ),
    };

    if (isTalent) {
      return {
        ...baseUser,
        birthday: el.birthday,
        gender: el?.gender?.title?.toLowerCase() || '',
        rating: el?.rating || 0,
      };
    } else {
      return {
        ...baseUser,
        company: el.company_name,
      };
    }
  });
}

export function responseExtractType(response) {
  return response.links?.profiles?.items[0]?.rel || 'talent';
}

export function responseExtractProfileId(response) {
  return response.links?.profiles?.items[0]?.id || 0;
}

export const checkIsPasswordSet = (token) => {
  const identities = extractFromToken(token, 'Identities');

  return identities ? String(identities).includes('standard') : true;
};

export function responseAccountLevel(response) {
  const status = response.links?.account_level?.links?.level?.status || null;

  return formatAccountLevel(status);
}

export async function callbackAccount(
  accountId,
  decodedAuthenticationToken,
  volatileToken,
  path,
) {
  const req = {
    headers: {
      [CookieService.cookie.authentication]: decodedAuthenticationToken,
      [CookieService.cookie.volatile]: volatileToken,
      ...getGatewayHeaders(path).headers,
    },
  };

  return await Api.serverside(
    `/accounts/${accountId}?expand=profiles,personal_url,level,location,touches`,
    req,
    null,
    req,
  );
}

export const extractFromToken = (token = '', property = null) => {
  const tokenValues = token ? token.replaceAll(';', '').split(' ') : [];
  const extractedValue = tokenValues.find((it) => it.includes(property));
  const extractedValuePair = extractedValue ? extractedValue.split('=') : null;

  return extractedValuePair?.length === 2 ? extractedValuePair[1] || 0 : 0;
};
