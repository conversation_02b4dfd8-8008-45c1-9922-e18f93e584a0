import cache from './cache';
import * as https from 'https';

const SEO_TYPE_PAGE = 'page';
const SEO_TYPE_CROSS_LINKING = 'cross-linking';
const SEO_TYPE_REDIRECTS = 'redirects';

const seo = async (type, key, cacheEnabled = true) => {
  const cacheKey = seoCacheKey(key, type);

  if (cacheEnabled) {
    const cached = await cache.get(cacheKey);

    if (cached) {
      return cached;
    }
  }

  let url = `${process.env.seoAPIUrl}/${type}`;

  switch (type) {
    case SEO_TYPE_CROSS_LINKING:
    case SEO_TYPE_PAGE:
      url += `?key=${key}`;
      break;
  }

  try {
    const response = await fetchWithTimeout(url);

    let data = {
      data: null,
      meta: null,
    };

    if (response.status === 200) {
      data = await response.json();
    }

    if (cacheEnabled) {
      const ttl = data.meta?.ttl || 60; // cache empty response too

      await cache.save(cacheKey, data, ttl);
    }

    return data;
  } catch (error) {
    console.error(error);

    return {
      data: null,
      meta: null,
    };
  }
};

async function fetchWithTimeout(resource, options = {}) {
  const { timeout = 3000 } = options;

  const agent = new https.Agent({ rejectUnauthorized: false });
  const controller = new AbortController();
  const id = setTimeout(() => controller.abort(), timeout);
  const response = await fetch(resource, {
    ...options,
    signal: controller.signal,
    agent: agent,
  });

  clearTimeout(id);

  return response;
}

const seoCacheKey = (key, type) => {
  key = key.replace(/-?page-.*/, '');
  if (key.length > 1 && key.charAt(key.length - 1) === '/') {
    key = key.slice(0, -1);
  }

  return `seo-${type}-${key}`;
};

export default {
  seo,
  SEO_TYPE_PAGE,
  SEO_TYPE_CROSS_LINKING,
  SEO_TYPE_REDIRECTS,
};
