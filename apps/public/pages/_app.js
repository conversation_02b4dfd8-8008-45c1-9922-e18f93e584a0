import NextNProgress from 'nextjs-progressbar';
import '../styles/typography.scss';
import '../styles/globals.scss';
import { useRouter } from 'next/router';
import { useEffect } from 'react';
import { AnalyticsProvider } from 'use-analytics';
import { AuthProvider } from '../contexts/AuthContext';
import { LiveChatProvider } from '../contexts/LiveChatContext';
import { analytics } from '../utils/analytics';
import {
  xTracking,
  pixelTracking,
  parseQueryString,
} from '../services/tracking';
import { NotificationProvider } from '../contexts/NotificationContext';
import { SaleProvider } from '../contexts/SaleContext';
import { AlertProvider } from '../contexts/AlertContext';
import { OneTrustService } from '../services/onetrust';
import { CookieService } from '../services/cookieService';
import { authGuard } from '../redirects/clientSide';
import { getRandomId } from '../utils/sessionHelpers';
import { ABTestProvider } from '../contexts/ABTestContext';
import { FeatureProvider } from '../contexts/FeatureContext';
import { GeoLocationProvider } from '../contexts/GeoLocationContext';
import Head from 'next/head';
import { CTAProvider } from '../contexts/CTAContext';
import { Plus_Jakarta_Sans } from 'next/font/google';
import { ThemeProvider, createTheme } from '@mui/material/styles';

const jakarta = Plus_Jakarta_Sans({
  subsets: ['latin'],
  fallback: [
    'system-ui',
    'Segoe UI',
    'Roboto',
    'Helvetica',
    'Arial',
    'sans-serif',
  ],
  variable: '--font-jakarta',
});

const theme = createTheme({
  typography: {
    fontFamily: jakarta.style.fontFamily,
  },
});

function Allcasting({ Component, pageProps }) {
  const router = useRouter();

  useEffect(() => {
    // Set initial referrer values on first load so the first page uses document.referrer
    const docReferrer = document.referrer;

    if (docReferrer && URL.canParse(docReferrer)) {
      sessionStorage.setItem('amplitudeReferrer', docReferrer);
      sessionStorage.setItem(
        'amplitudeReferringDomain',
        new URL(docReferrer).hostname,
      );
    }
  }, []);

  useEffect(() => {
    xTracking(parseQueryString(window.location.search));
    pixelTracking(router.asPath);
    analytics.page();
    OneTrustService.saveVisitedPath(router.asPath);
    OneTrustService.checkTracking();

    const sessionId = CookieService.getSessionIdCookie();

    if (!sessionId) {
      CookieService.setSessionIdCookie(getRandomId());
    }
  }, [router]);

  // Update referrer for SPA navigations: store the previous URL before route change
  useEffect(() => {
    const handleRouteChangeStart = () => {
      try {
        sessionStorage.setItem('amplitudeReferrer', window.location.href);
        sessionStorage.setItem(
          'amplitudeReferringDomain',
          window.location.hostname,
        );
      } catch (e) {
        // Ignore sessionStorage errors (e.g., disabled storage)
      }
    };

    router.events.on('routeChangeStart', handleRouteChangeStart);

    return () => {
      router.events.off('routeChangeStart', handleRouteChangeStart);
    };
  }, [router.events]);

  useEffect(() => {
    OneTrustService.listener();

    // Browser back button click from me.allcasting.com or social login
    window.addEventListener('pageshow', (event) => {
      if (event.persisted) {
        const isTalent = CookieService.getUserTypeCookie() === 'talent';
        const isAuthenticated = !!CookieService.getAuthenticationCookie();
        const baseUrl = process.env.baseUrl;

        switch (true) {
          case isAuthenticated &&
            !isTalent &&
            authGuard.includes(router.asPath):
            window.location.href = `${process.env.redirectProUrl}/castingcalls`;
            break;
          case isAuthenticated && isTalent:
            window.location.href = `${baseUrl}/castingcalls`;
            break;
          default:
            window.location.href = `${baseUrl}${router.asPath}`;
        }
      }
    });
  }, []);

  return (
    <>
      <Head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>
      <AnalyticsProvider instance={analytics}>
        <FeatureProvider initialValues={pageProps.initialFeatures}>
          <NotificationProvider>
            <AuthProvider defaultAuthValues={pageProps.defaultAuthValues || []}>
              <GeoLocationProvider>
                <AlertProvider>
                  <SaleProvider>
                    <CTAProvider>
                      <ABTestProvider
                        defaultABGroupValues={
                          pageProps.defaultABGroupValues || []
                        }
                      >
                        <LiveChatProvider>
                          <NextNProgress
                            color="#641971"
                            height={4}
                            options={{ showSpinner: false }}
                          />
                          <style jsx global>
                            {`
                              :root {
                                --font-jakarta: ${jakarta.style.fontFamily};
                              }
                            `}
                          </style>
                          <ThemeProvider theme={theme}>
                            <Component {...pageProps} />
                          </ThemeProvider>
                        </LiveChatProvider>
                      </ABTestProvider>
                    </CTAProvider>
                  </SaleProvider>
                </AlertProvider>
              </GeoLocationProvider>
            </AuthProvider>
          </NotificationProvider>
        </FeatureProvider>
      </AnalyticsProvider>
    </>
  );
}

export default Allcasting;
