'use client';
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import styles from './ChangePasswordForm.module.scss';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { PasswordInput } from '@components';
import Api from '@services/api';
import { useNotifications } from '@contexts/NotificationContext';
import { CookieService } from '@services/cookieService';
import { extractFromToken } from '@utils/authHelper';
import { ErrorMessage, PASSWORD_REGEX } from '@constants/form';

const ChangePasswordForm = forwardRef(
  ({ toggleSaveButtonDisabled, isPasswordSet, accountId }, ref) => {
    const [showNewPasswordForm, setShowNewPasswordForm] =
      useState(!isPasswordSet);
    const [error, setError] = useState('');
    const { setNotification } = useNotifications();

    useEffect(() => {
      setShowNewPasswordForm(!isPasswordSet);
    }, [isPasswordSet]);

    const triggerSubmit = async () => {
      await formik.submitForm();
    };

    useImperativeHandle(ref, () => ({
      async triggerSubmit() {
        await triggerSubmit();
      },
    }));

    const formik = useFormik({
      initialValues: {
        password: '',
        newPassword: '',
        newPasswordConfirmation: '',
      },
      onSubmit: async (values) => {
        if (showNewPasswordForm) {
          await submitNewPasswordForm(values);
        } else {
          await submitChangePasswordForm(values);
        }
      },
      validationSchema: Yup.object({
        password: Yup.string().test(
          'Required',
          ErrorMessage.PasswordRequired,
          async (value) => {
            if (showNewPasswordForm) {
              return true;
            } else {
              return value?.length;
            }
          },
        ),
        newPassword: Yup.string()
          .required(ErrorMessage.NewPasswordRequired)
          .matches(PASSWORD_REGEX, ErrorMessage.PasswordPattern)
          .min(8, ErrorMessage.PasswordPattern),
        newPasswordConfirmation: Yup.string()
          .required(ErrorMessage.PasswordConfirmationRequired)
          .matches(PASSWORD_REGEX, ErrorMessage.PasswordPattern)
          .min(8, ErrorMessage.PasswordPattern)
          .test('passwordMismatch', '', async (value) => {
            if (value?.length > 7 && value !== formik.values.newPassword) {
              setError(ErrorMessage.PasswordMatch);

              return false;
            } else {
              setError('');

              return true;
            }
          }),
      }),
    });

    useEffect(() => {
      if (toggleSaveButtonDisabled) {
        toggleSaveButtonDisabled(!(formik.isValid && formik.dirty));
      }
    }, [formik.dirty, formik.isValid, formik.values, toggleSaveButtonDisabled]);

    const submitChangePasswordForm = async (values) => {
      const body = new FormData();

      body.append('old', values.password);
      body.append('new', values.newPassword);

      const changePasswordResponse = await Api.clientside(`/auth/change`, {
        body,
        method: 'POST',
      });

      if (changePasswordResponse.status !== 'ok') {
        setNotification({
          type: 'error',
          timeout: '5000',
          message: changePasswordResponse.message || ErrorMessage.Unexpected,
        });
      } else {
        setNotification({
          type: 'success',
          timeout: '5000',
          message: 'Password successfully changed',
        });
        formik.resetForm();
      }
    };

    const submitNewPasswordForm = async (values) => {
      const body = new FormData();

      body.append('password', values.newPassword);

      const response = await Api.clientside(
        `/accounts/${accountId}/passwords`,
        {
          body,
          method: 'PUT',
        },
      );

      if (response.status !== 'ok') {
        setNotification({
          type: 'error',
          timeout: '5000',
          message: response.message || ErrorMessage.Unexpected,
        });
      } else {
        setNotification({
          type: 'success',
          timeout: '5000',
          message: 'Password set successfully',
        });
        setShowNewPasswordForm(false);
        formik.resetForm();
        await refreshToken();
      }
    };

    const refreshToken = async () => {
      const token = CookieService.getAuthenticationCookie();
      const body = new FormData();

      body.append('account', accountId);
      body.append('series', String(extractFromToken(token, 'Series')));
      body.append('token', String(extractFromToken(token, 'Token')));

      await Api.clientside(`/auth/token`, {
        body,
        method: 'POST',
      });
    };

    return (
      <div className={styles['change-password-form-container']}>
        <h1 className={styles['change-password-form-title']}>Password</h1>
        <form
          className={styles['change-password-form']}
          onSubmit={formik.handleSubmit}
        >
          {!showNewPasswordForm && (
            <div className={styles['change-password-form-row']}>
              <div className={styles['change-password-form-field']}>
                <PasswordInput
                  name="password"
                  placeholder="Old Password"
                  value={formik.values.password}
                  error={formik.errors.password}
                  isTouched={formik.touched.password}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                />
              </div>
            </div>
          )}
          <div className={styles['change-password-form-row']}>
            <div className={styles['change-password-form-field']}>
              <PasswordInput
                name="newPassword"
                placeholder="New Password"
                hint="Min 8 characters"
                value={formik.values.newPassword}
                error={formik.errors.newPassword}
                isTouched={formik.touched.newPassword}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
              />
            </div>
            <div className={styles['change-password-form-field']}>
              <PasswordInput
                name="newPasswordConfirmation"
                placeholder="Confirm Password"
                hint="Min 8 characters"
                value={formik.values.newPasswordConfirmation}
                error={formik.errors.newPasswordConfirmation}
                isTouched={formik.touched.newPasswordConfirmation}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
              />
            </div>
          </div>
          {error && (
            <div className={styles['change-password-form-error']}>{error}</div>
          )}
          <div className={styles['change-password-form-actions']}>
            <button
              className={styles['change-password-form-button']}
              type="submit"
              disabled={!(formik.isValid && formik.dirty)}
            >
              Save changes
            </button>
          </div>
        </form>
      </div>
    );
  },
);

ChangePasswordForm.displayName = 'ChangePasswordForm';

export default ChangePasswordForm;
