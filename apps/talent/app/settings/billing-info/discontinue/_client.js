'use client';
import React, { useCallback, useEffect, useState } from 'react';
import styles from '@styles/discontinue.module.scss';
import { useRouter } from 'next/navigation';
import { useFormik } from 'formik';
import Link from 'next/link';
import dayjs from 'dayjs';
import * as Yup from 'yup';
import {
  Button,
  Checkbox,
  HeaderMobile,
  Input,
  Loading,
  MainLayout,
  ModalContentProtection,
} from '@components';
import { useNotifications } from '@contexts/NotificationContext';
import { useAuth } from '@contexts/AuthContext';
import Api from '@services/api';
import { ErrorMessage } from '@constants/form';
import { Amp } from '@services/amp';

const DiscontinueClient = ({ identifier, endDate, isPasswordSet }) => {
  const [showContentProtectionForm, setContentProtectionForm] = useState(false);
  const [discontinued, setDiscontinued] = useState(false);
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const { setNotification } = useNotifications();
  const { accountId } = useAuth();

  useEffect(() => {
    setContentProtectionForm(isPasswordSet);
  }, [isPasswordSet]);

  const formik = useFormik({
    initialValues: {
      reason: '',
      agree: false,
    },
    onSubmit: async () => {
      setLoading(true);

      const discontinueSubscriptionResponse = await Api.clientside(
        `/accounts/${accountId}/subscription?reason=${formik.values.reason}`,
        {
          method: 'DELETE',
        },
      );

      if (discontinueSubscriptionResponse.status !== 'ok') {
        setNotification({
          type: 'error',
          timeout: '5000',
          message:
            discontinueSubscriptionResponse.message || ErrorMessage.Unexpected,
        });
      } else {
        setDiscontinued(true);
      }

      setLoading(false);
    },
    validationSchema: Yup.object({
      agree: Yup.bool().oneOf([true]),
    }),
  });

  const navigateBack = useCallback(() => {
    router.back();
  }, [router]);

  const closeContentProtectionForm = useCallback(() => {
    setContentProtectionForm(false);
  }, []);

  const goToCastingCalls = () => {
    window.location.href = `${process.env.publicUrl}/castingcalls`;
  };

  useEffect(() => {
    Amp.track(Amp.events.viewSettings, {
      name: 'discontinue subscription',
    });
  }, []);

  return (
    <MainLayout
      isDefaultHeaderVisible
      isSaleHeaderVisible
      isMobileMenuVisible
      isUserMenuVisible
    >
      <HeaderMobile
        title={discontinued ? 'Confirmation' : 'Discontinue your subscription'}
        onClose={navigateBack}
        hideOnTablet
      />
      <section className={styles['discontinue-subscription-section']}>
        {discontinued && (
          <div className={styles['discontinue-subscription-container-success']}>
            <h1 className={styles['discontinue-subscription-title-success']}>
              Your subscription has been discontinued
            </h1>
            <p>
              The last day of your subscription will be{' '}
              <b>{dayjs(endDate).format('M/D/YY')}</b>. We encourage you to use
              the remaining {dayjs(endDate).diff(dayjs(), 'days')} days to
              submit to as many casting calls on allcasting.com as you can. You
              never know when your dream comes true! You will receive an email
              with confirmation and detailed information soon.
            </p>
            <div>
              <Button
                onClick={goToCastingCalls}
                label="Visit casting calls"
                kind="secondary"
                minWidth="280px"
              />
            </div>
            <Link className={styles.link} href={'/settings'} passHref>
              Back to settings
            </Link>
          </div>
        )}
        {!discontinued && (
          <div className={styles['discontinue-subscription-container']}>
            <h1 className={styles['discontinue-subscription-title']}>
              Discontinue your subscription
            </h1>
            <p className={styles['discontinue-subscription-description']}>
              After your subscription expires, you will no longer be able to
              send and receive messages and apply to casting calls. Your current
              subscription is valid until {dayjs(endDate).format('M/D/YY')}
            </p>
            <form
              className={styles['discontinue-subscription-form']}
              onSubmit={formik.handleSubmit}
            >
              <Input
                name="reason"
                placeholder="Reason for subscription cancellation"
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.reason}
                isTouched={formik.touched.reason}
                error={formik.errors.reason}
              />
              <Checkbox
                name="agree"
                onChange={formik.handleChange}
                value={formik.values.agree}
                error={formik.errors.agree}
                onBlur={formik.handleBlur}
                isTouched={formik.touched.agree}
              >
                <span className={styles['discontinue-subscription-form-label']}>
                  I understand the consequences
                </span>
              </Checkbox>
              {loading ? (
                <Loading minHeight="40px" padding="0" />
              ) : (
                <>
                  <Button
                    type="submit"
                    label="Discontinue my subscription"
                    disabled={!(formik.isValid && formik.dirty)}
                    color="red-gradient"
                    minWidth="280px"
                  />
                  <Link className={styles.link} href={'/settings'} passHref>
                    Give another try
                  </Link>
                </>
              )}
            </form>
          </div>
        )}

        {showContentProtectionForm && (
          <ModalContentProtection
            identifier={identifier}
            onClose={closeContentProtectionForm}
          />
        )}
      </section>
    </MainLayout>
  );
};

export default DiscontinueClient;
