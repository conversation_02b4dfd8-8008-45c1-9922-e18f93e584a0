import { useRouter } from 'next/router';
import { createContext, useContext, useEffect, useRef, useState } from 'react';
import dayjs from 'dayjs';
import { useAuth } from './AuthContext';
import { CookieService } from '../services/cookieService';
import ApiNoCache from '../services/apiNoCache';
import { UIEvent } from '../constants/uiEvents';
import { ALLOWED_PATHS } from './CTAModalContext';

const UINotificationsContext = createContext({});

export const useUINotificationsContext = () =>
  useContext(UINotificationsContext);

const MINUTE = 1_000 * 60;

const getEvents = (events) => {
  const params = new URLSearchParams();

  events.forEach((event) => {
    params.append('events[]', event);
  });

  return ApiNoCache.clientGateway(`/tracking/events?${params.toString()}`);
};

const calculateDayDiff = (lastEvent) => {
  const last = lastEvent ? dayjs(lastEvent.created_at) : dayjs(0);
  const now = dayjs();

  return now.diff(last, 'day');
};

export const UINotificationsProvider = ({ children }) => {
  const [phoneNumberModal, setPhoneNumberModal] = useState({
    enabled: false,
    show: false,
    occurrence: 0,
  });
  const router = useRouter();
  const { userProfiles, refreshUserProfiles, accountLevel } = useAuth();
  const isFetching = useRef(false);

  useEffect(() => {
    if (isFetching.current || phoneNumberModal.enabled) return;

    const guideSlots = CookieService.getGuideSlotsData();
    const isPhoneSet = 'phone' in (userProfiles?.[0] || {});
    const phone = userProfiles?.[0]?.phone;
    const isUrlAllowed = ALLOWED_PATHS.some((path) =>
      router.asPath.includes(path),
    );
    const guideSlotsShown =
      guideSlots?.slots?.every(({ seen }) => seen) ||
      accountLevel?.isPaidOrDelayed;

    if (!(isUrlAllowed && guideSlotsShown)) return;
    if (!isPhoneSet) {
      // Authenticated users might not have userProfiles refreshed
      refreshUserProfiles();

      return;
    }
    if (phone) return;

    isFetching.current = true;

    getEvents([UIEvent.PhoneNumberModalViewed]).then((response) => {
      const events = response.data.items || [];
      const phoneModalEvents = events.filter(
        ({ event_name }) => event_name === UIEvent.PhoneNumberModalViewed,
      );
      const diff = calculateDayDiff(phoneModalEvents[0]);

      if (phoneModalEvents.length < 2 && diff >= 2) {
        setPhoneNumberModal((prev) => ({
          ...prev,
          enabled: true,
          occurrence: phoneModalEvents.length + 1,
        }));
      }
      isFetching.current = false;
    });
  }, [
    userProfiles,
    router.asPath,
    refreshUserProfiles,
    accountLevel,
    phoneNumberModal.enabled,
  ]);

  useEffect(() => {
    if (phoneNumberModal.enabled) {
      const timeoutId = setTimeout(() => setShowPhoneNumberModal(true), MINUTE);

      return () => clearTimeout(timeoutId);
    }
  }, [phoneNumberModal.enabled]);

  const setShowPhoneNumberModal = (val) =>
    setPhoneNumberModal((prev) => ({ ...prev, show: val }));

  return (
    <UINotificationsContext.Provider
      value={{
        showPhoneNumberModal: phoneNumberModal.show,
        setShowPhoneNumberModal,
        phoneNumberModalOccurrence: phoneNumberModal.occurrence,
      }}
    >
      {children}
    </UINotificationsContext.Provider>
  );
};
