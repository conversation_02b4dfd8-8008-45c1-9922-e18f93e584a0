import CastingCallEditClient from './_client';
import { cookies, headers } from 'next/headers';
import { CookieServiceServer } from '@services/cookieServiceServer';
import Api from '@services/api';
import { getResolvedUrl } from '@utils/apiHelper';
import dayjs from 'dayjs';
import { notFound } from 'next/navigation';
import {
  formatCastingCallData,
  formatPostParameters,
} from '@utils/postCastingCallHelpers';
import {
  getDayOptionsByMonth,
  getMonthOptionsWithFullTitles,
  getYearOptions,
  timeFormat,
  timeOptions,
} from '@utils/getTimeOptions';

export const metadata = {
  title: 'Casting call',
};

export default async function CastingCallEditPage(req) {
  const params = await req.params;
  const searchParams = await req.searchParams;
  const { role } = searchParams;
  const { id } = params;
  const resolvedUrl = getResolvedUrl(`/castingcall/${id}/edit`, searchParams);
  const cookieStore = await cookies();
  const headerStore = await headers();
  const editRoleId = role ? Number(role) : null;
  const profileId = CookieServiceServer.getProfileCookie(cookieStore);

  const castingCallResponse = await Api.serverside(
    `/calls/get?id=${id}`,
    cookieStore,
    headerStore,
    resolvedUrl,
  );

  if (castingCallResponse.status !== 'ok') {
    notFound();
  }

  const castingCallParameters = await Api.serverside(
    '/calls/init_post',
    cookieStore,
    headerStore,
    resolvedUrl,
  );

  const profile = await Api.serverside(
    `/profiles/${profileId}?expand=touches`,
    cookieStore,
    headerStore,
    resolvedUrl,
  );

  return (
    <CastingCallEditClient
      castingCallType={castingCallResponse.data.type}
      castingCallData={formatCastingCallData(
        castingCallResponse.data,
        castingCallParameters.data,
      )}
      profile={profile}
      castingCallParameters={formatPostParameters(castingCallParameters.data)}
      monthOptions={getMonthOptionsWithFullTitles()}
      dayOptions={getDayOptionsByMonth(dayjs().year(), 1)}
      yearOptions={getYearOptions()}
      timeOptions={timeOptions}
      timeFormat={timeFormat}
      editRoleId={editRoleId}
    />
  );
}
