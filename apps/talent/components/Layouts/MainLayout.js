'use client';
import { useEffect, useState } from 'react';
import styles from './MainLayout.module.scss';
import cn from 'classnames';
import {
  CheckoutHeader,
  <PERSON>er,
  Header,
  LiveChat,
  LogoHeader,
  MobileSidebarMenu,
  Modal,
  ModalPremium,
  Notification,
  PlanSelectHeader,
  PremiumActions,
  SaleHeader,
  TalentMobileMenu,
  UINotificationsModals,
  UserMenu,
} from '@components';
import { useNotifications } from '@contexts/NotificationContext';
import { useSale } from '@contexts/SaleContext';
import { useAuth } from '@contexts/AuthContext';
import { usePremium } from '@contexts/PremiumContext';
import { useFeature } from '@contexts/FeatureContext';
import { UINotificationsProvider } from '@contexts/UINotificationsContext';

export default function MainLayout({
  children,
  referer,
  isDefaultHeaderVisible = false,
  isUserMenuVisible = false,
  isSaleHeaderVisible = false,
  isMobileSaleHeaderHidden = false,
  isFooterVisible = false,
  isMobileMenuVisible = false,
  isLogoHeaderVisible = false,
  isPlanSelectHeaderVisible = false,
  isCheckoutHeaderVisible = false,
  isPremiumCheckout = false,
  isLifetimeCheckout = false,
}) {
  const {
    isAuthenticated,
    accountLevel,
    authLogoutWithTracking,
    userProfiles,
  } = useAuth();
  const { saleExpirationTime, showSale, stopSale } = useSale();
  const { showNotification, setNotification } = useNotifications();
  const [showSideBar, setShowSideBar] = useState(false);
  const [showHelpCenter, setShowHelpCenter] = useState(false);
  const { showPremiumModal, closePremiumModal, premiumTrackingName } =
    usePremium();
  const { premiumActionsButtonEnabled } = useFeature();

  useEffect(() => {
    document.body.style.overflow = showSideBar ? 'hidden' : 'unset';
  }, [showSideBar]);

  useEffect(() => {
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, []);

  const toggleHelpCenter = () => setShowHelpCenter(!showHelpCenter);

  useEffect(() => {
    const currenRef = document;

    const callback = () => setShowHelpCenter(false);

    if (showHelpCenter) {
      currenRef.addEventListener('mouseup', callback);

      return () => currenRef.removeEventListener('mouseup', callback);
    }
  }, [showHelpCenter]);

  const toggleShowBarVisible = () => setShowSideBar(!showSideBar);

  return (
    <>
      <div className={styles['main-wrapper']}>
        <section className={styles['header-section']}>
          {isSaleHeaderVisible && showSale && (
            <SaleHeader
              saleExpirationTime={saleExpirationTime}
              onStopSale={stopSale}
              isMobileSaleHeaderHidden={isMobileSaleHeaderHidden}
            />
          )}
          <div className={styles['header-section-headers']}>
            {isDefaultHeaderVisible && (
              <Header
                isAuthenticated={isAuthenticated}
                isPaidOrDelayed={!!accountLevel?.isPaidOrDelayed}
                canUpgradeExistingSubscription={
                  !!accountLevel?.canUpgradeExistingSubscription
                }
              />
            )}
          </div>
          {isLogoHeaderVisible && <LogoHeader />}
          {isPlanSelectHeaderVisible && (
            <PlanSelectHeader
              referer={referer}
              profileUrl={
                userProfiles.length ? userProfiles[0].profileUrl : '/'
              }
            />
          )}
          {isCheckoutHeaderVisible && (
            <CheckoutHeader
              isPremiumCheckout={isPremiumCheckout}
              isLifetimeCheckout={isLifetimeCheckout}
            />
          )}
          {showNotification?.message && (
            <Notification
              type={showNotification?.type}
              onClose={() => setNotification({ message: null })}
              timeout={showNotification?.timeout}
              message={showNotification?.message}
            />
          )}
        </section>
        {isUserMenuVisible && (
          <UserMenu
            stopSale={stopSale}
            showSale={showSale && isSaleHeaderVisible}
            showHelpCenter={showHelpCenter}
            toggleHelpCenter={toggleHelpCenter}
            authLogout={authLogoutWithTracking}
            userProfiles={userProfiles}
          />
        )}
        <div className={styles['content-wrapper']}>
          <div
            className={cn(styles['content'], {
              [styles['content-without-sidebar']]: !isUserMenuVisible,
            })}
          >
            {children}
            {isFooterVisible && <Footer />}
          </div>
          {isMobileMenuVisible && (
            <TalentMobileMenu
              profile={userProfiles[0]}
              className={styles['mobile-menu']}
              onSideBarOpen={toggleShowBarVisible}
            />
          )}
          {showSideBar && (
            <Modal
              backdropClose
              onClose={toggleShowBarVisible}
              showDefaultLayout={false}
              showCloseButton={false}
              showAnimation={false}
            >
              <MobileSidebarMenu
                open={showSideBar}
                onSideBarClose={toggleShowBarVisible}
                isAuthenticated={isAuthenticated}
                profile={userProfiles[0]}
                isPaidOrDelayed={!!accountLevel?.isPaidOrDelayed}
                canUpgradeExistingSubscription={
                  !!accountLevel?.canUpgradeExistingSubscription
                }
                accountLevel={accountLevel}
                authLogout={authLogoutWithTracking}
                showSale={showSale}
                stopSale={stopSale}
                saleExpirationTime={saleExpirationTime}
              />
            </Modal>
          )}
          {showPremiumModal && (
            <ModalPremium
              onClose={closePremiumModal}
              premiumTrackingName={premiumTrackingName}
            />
          )}
        </div>
        <LiveChat />
      </div>

      {premiumActionsButtonEnabled && <PremiumActions isMobile />}

      {isAuthenticated && (
        <UINotificationsProvider>
          <UINotificationsModals />
        </UINotificationsProvider>
      )}
    </>
  );
}
