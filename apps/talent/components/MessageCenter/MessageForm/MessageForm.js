'use client';
import React, { memo, useEffect, useRef } from 'react';
import cn from 'classnames';
import styles from './MessageForm.module.scss';
import { Button } from '@components';
import { useFormik } from 'formik';
import Api from '@services/api';
import { useNotifications } from '@contexts/NotificationContext';
import { Amp } from '@services/amp';
import * as Yup from 'yup';
import FileListUpload from '../FileListUpload/FileListUpload';
import Image from 'next/image';
import { ErrorMessage } from '@constants/form';

const MessageForm = ({
  conversationId,
  onMessageSuccess,
  openImageAttachment = () => {},
  onSetActiveConversation = () => {},
  isPartnerActive = true,
}) => {
  const { setNotification } = useNotifications();
  const fileAttachRef = useRef(null);

  const formik = useFormik({
    initialValues: {
      message: '',
      attachments: [],
    },
    onSubmit: async (values) => {
      const body = new FormData();

      body.append('content', values.message || 'File:');

      values.attachments.forEach((file) => {
        body.append('files[]', file.id);
      });

      const response = await Api.clientside(
        `/profiles/${conversationId}/messages`,
        {
          body,
          method: 'POST',
        },
      );

      if (response.status !== 'ok') {
        setNotification({
          type: 'error',
          message: response.message || ErrorMessage.MessageFailed,
          timeout: '5000',
        });
      } else {
        Amp.track(Amp.events.elementClicked, {
          name: 'message submit',
          scope: Amp.element.scope.messages,
          section: Amp.element.section.messageForm,
          type: Amp.element.type.button,
        });

        onMessageSuccess(response);
        formik.resetForm({
          values: { message: '', attachments: [] },
        });
        fileAttachRef.current.clear();
      }
    },
    validationSchema: Yup.object({
      message: Yup.string().test(
        'Required',
        ErrorMessage.MessageRequired,
        async (value) => {
          return formik.values.attachments.length ? true : !!value?.length;
        },
      ),
    }),
  });

  useEffect(() => {
    formik.setFieldTouched('message');
    formik.validateField('message');
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formik.values.attachments]);

  const onFileAttach = () => {
    fileAttachRef.current.attach();
  };

  const onUploadEnd = (files) => {
    formik.setFieldValue('attachments', files);
  };

  return (
    <>
      {isPartnerActive ? (
        <div onClick={onSetActiveConversation} className={styles.body}>
          <div className={cn(styles['form-wrap'], styles['is-focused'])}>
            <form className={styles['form']} onSubmit={formik.handleSubmit}>
              <div className={styles['input-box']}>
                <div className={styles['input-area']}>
                  <input
                    autoComplete="off"
                    className={styles['message-input']}
                    name="message"
                    type="text"
                    onChange={formik.handleChange}
                    value={formik.values.message}
                    placeholder="Message"
                  />
                </div>
                <div className={styles['input-controls']}>
                  <div className={styles['attachment-controls']}>
                    <Image
                      className={styles['attach-btn']}
                      onClick={onFileAttach}
                      src="/assets/icons/icon-attachment.svg"
                      alt="icon"
                      width={30}
                      height={30}
                    />
                  </div>
                  <div className={styles['input-submit']}>
                    <span className={styles['input-counter']}>
                      {formik.values.message.length} / 1000
                    </span>
                    <Button
                      type="button"
                      label="SEND"
                      minWidth="80px"
                      disabled={!formik.isValid}
                      onClick={formik.handleSubmit}
                    />
                  </div>
                </div>
              </div>
            </form>
            <div className={styles['file-list']}>
              <FileListUpload
                ref={fileAttachRef}
                onUploadEnd={onUploadEnd}
                openImageAttachment={openImageAttachment}
              />
            </div>
          </div>
        </div>
      ) : (
        <div className={styles.inactive}>
          This profile is no longer active and will not be receiving or
          responding to messages
        </div>
      )}
    </>
  );
};

export default memo(MessageForm);
