import { memo, useEffect, useRef, useState } from 'react';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import styles from './RegistrationForm.module.scss';
import {
  Button,
  Checkbox,
  SocialButtons,
  Input,
  Loading,
  Modal,
  PasswordInput,
  PrivacyPolicy,
  TermsOfUse,
} from '../index';
import cn from 'classnames';
import { maskPhoneNumber } from '../../utils/maskPhoneNumber';
import Api from '../../services/api';
import { useRouter } from 'next/router';
import { useAnalytics } from 'use-analytics';
import {
  GTM_EVENTS,
  GTM_CATEGORIES,
  GTM_ACTIONS,
} from '../../constants/analytics';
import { useAuth } from '../../contexts/AuthContext';
import ApiNoCache from '../../services/apiNoCache';
import { CookieService } from '../../services/cookieService';
import { cacheTest, nodeListsEqualTest } from '../../utils/formikHelpers';
import { useSale } from '../../contexts/SaleContext';
import { Amp } from '../../services/amp';
import {
  EMAIL_REGEX,
  ErrorMessage,
  NAME_REGEX,
  PASSWORD_REGEX,
  PHONE_NUMBER_REGEX,
} from '../../constants/form';
import * as Sentry from '@sentry/nextjs';

const RegistrationForm = ({
  isTalent,
  isModal,
  onRedirecting,
  styleTheme = 'light',
}) => {
  const [showTerms, setShowTerms] = useState(false);
  const [showPrivacyPolicy, setShowPrivacyPolicy] = useState(false);
  const [autofilledFields, setAutofilledFields] = useState(null);
  const [loading, setLoading] = useState(false);
  const [locationLoading, setLocationLoading] = useState(false);
  const [location, setLocation] = useState('');
  const [country, setCountry] = useState('');
  const [error, setError] = useState('');
  const [isFormStarted, setIsFormStarted] = useState(false);
  const [inputStarted, setInputStarted] = useState(false);
  const router = useRouter();
  const { track } = useAnalytics();
  const { refreshSale } = useSale();
  const { refreshUserProfiles, accountLevelVerify } = useAuth();

  const validateZip = async (value, isTalent) => {
    const length = value?.length || 0;

    if (length >= 4 && length <= 6) {
      setLocationLoading(true);

      const response = await getLocation(value);
      const isZipValid = response.count > 0;
      const {
        country = null,
        city = null,
        state = null,
      } = response?.items?.[0]?.links || {};

      setCountry(country?.code || '');
      setLocation(isZipValid ? `${city?.title}, ${state?.code}` : '');
      setLocationLoading(false);

      return isZipValid;
    } else {
      return isTalent ? !!location : true;
    }
  };

  const validateZipRef = useRef(cacheTest(validateZip, () => isTalent));

  const formik = useFormik({
    initialValues: {
      firstName: '',
      lastName: '',
      zip: '',
      email: '',
      password: '',
      agree: false,
      phoneNumber: '',
    },
    onSubmit: async (values) => {
      setError('');
      setLoading(true);

      RegErrors.current = {};
      RegValues.current = { ...values };

      const type = isTalent ? 'talent' : 'agent';
      const registrationResponse = await register({ ...values, type });
      const isError = registrationResponse.status === 'error';

      setLoading(false);

      if (!isError) {
        onRedirecting(true);
        formik.resetForm();
        setError('');

        const loginResponse = await login(
          registrationResponse.identifier,
          registrationResponse.key,
        );

        if (loginResponse.status !== 'error') {
          const generateRandomPhone = `+1555${Math.floor(
            Math.random() * 10000000,
          )}`;

          track(GTM_EVENTS.interaction, {
            target: GTM_CATEGORIES.successReg,
            action: GTM_ACTIONS.registration,
            label: type,
            client: {
              first_name: values.firstName,
              last_name: values.lastName,
              email: values.email,
              phone: values.phoneNumber || generateRandomPhone,
              postal_code: values.zip,
              city: location.split(', ')[0],
              region: location.split(', ')[1],
              country: country,
            },
          });

          const authenticationToken = loginResponse.authenticationToken;
          const volatileToken = loginResponse.volatileToken;
          const tokenExpires = loginResponse.tokenExpires;
          const accountId = loginResponse.id;
          const profileId = loginResponse.links?.profiles?.items[0]?.id;
          const userType =
            loginResponse.links?.profiles?.items[0]?.rel || 'talent';

          CookieService.setAuthenticationCookie(authenticationToken);
          CookieService.setVolatileCookie(volatileToken);
          CookieService.setExpiresCookie(tokenExpires);
          CookieService.setAccountCookie(accountId);
          CookieService.setUserTypeCookie(userType);
          CookieService.setProfileCookie(profileId);
          Sentry.setUser({ id: accountId, type: userType });

          await refreshSale();
          await accountLevelVerify();
          await refreshUserProfiles(accountId);

          Amp.track(Amp.events.signUp, {
            type: isTalent ? 'talent' : 'agent',
            provider: 'native',
          });

          if (values.phoneNumber) {
            sendSubmitPhoneNumberToAmplitude(
              accountId,
              values.phoneNumber.replace(/[\D]/g, ''),
              country,
              isError,
              registrationResponse.message,
            );
          }

          if (isTalent) {
            if (router.asPath.includes('/premium')) {
              CookieService.setRedirectCookie(`/checkout?action=329`);
            }
            if (router.asPath.includes('/lifetime')) {
              CookieService.setRedirectCookie(`/checkout?action=343`);
            }
            window.location.href = `${process.env.redirectTalentUrl}/wizard`;
          } else {
            window.location.href = `${process.env.redirectProUrl}/welcome`;
          }
        } else {
          Amp.track(Amp.events.invalidEventOccurred, {
            name: `login failed`,
          });

          await router.push('/login');
        }
      } else {
        setError(registrationResponse.message);
        RegErrors.current = {
          registrationResponse: registrationResponse.message,
        };

        sendFormSubmittedErrorsToAmplitude(registrationResponse.message);
        if (values.phoneNumber) {
          sendSubmitPhoneNumberToAmplitude(
            null,
            values.phoneNumber.replace(/[\D]/g, ''),
            country,
            isError,
            registrationResponse.message,
          );
        }
      }
    },
    validationSchema: Yup.object({
      firstName: Yup.string()
        .required(ErrorMessage.NameRequired)
        .max(35, ErrorMessage.NamePattern)
        .matches(NAME_REGEX, ErrorMessage.NamePattern),
      lastName: Yup.string()
        .required(ErrorMessage.LastNameRequired)
        .max(35, ErrorMessage.NamePattern)
        .matches(NAME_REGEX, ErrorMessage.NamePattern),
      zip: !isTalent
        ? Yup.string()
            .transform((value) => value?.replaceAll(' ', ''))
            .min(5, ErrorMessage.ZipPattern)
            .max(6, ErrorMessage.ZipPattern)
            .test('zipIsValid', ErrorMessage.ZipPattern, (value) =>
              validateZipRef.current(value),
            )
        : Yup.string()
            .transform((value) => value?.replaceAll(' ', ''))
            .required(ErrorMessage.ZipRequired)
            .min(5, ErrorMessage.ZipPattern)
            .max(6, ErrorMessage.ZipPattern)
            .test('zipIsValid', ErrorMessage.ZipPattern, (value) =>
              validateZipRef.current(value),
            ),
      email: Yup.string()
        .required(ErrorMessage.EmailRequired)
        .matches(EMAIL_REGEX, ErrorMessage.EmailPattern),
      password: Yup.string()
        .required(ErrorMessage.PasswordRequired)
        .matches(PASSWORD_REGEX, ErrorMessage.PasswordPattern)
        .min(8, ErrorMessage.PasswordPattern),
      agree: Yup.bool().isTrue(),
      phoneNumber: Yup.string().matches(
        PHONE_NUMBER_REGEX,
        ErrorMessage.PhonePattern,
      ),
    }),
  });

  const RegErrors = useRef({});
  const RegValues = useRef({});

  const beforeunload = () => {
    sendFormAbandonedErrorsToAmplitude();
  };

  const sendFormStartedEventToAmplitude = () => {
    Amp.track(Amp.events.formStarted, {
      name: 'registration',
    });
  };

  const sendFormSubmittedErrorsToAmplitude = (message) => {
    Amp.track(Amp.events.formSubmitted, {
      name: 'registration errors',
      result: Amp.element.result.fail,
      message: message,
    });
  };

  const sendSubmitPhoneNumberToAmplitude = (
    accountId,
    phone,
    country,
    isError = false,
    errorMessage = null,
  ) => {
    Amp.track(Amp.events.submitPhoneNumber, {
      scope: Amp.element.scope.global,
      phone_number_last_4_digits: phone.slice(-4),
      country_code: country,
      status: isError ? 'failure' : 'success',
      error_message: isError ? errorMessage : null,
    });
  };

  const sendFormAbandonedErrorsToAmplitude = () => {
    if (Object.keys(RegErrors.current).length) {
      Amp.track(Amp.events.formAbandoned, {
        name: 'registration errors',
        errors: RegErrors.current,
        values: RegValues.current,
      });

      RegErrors.current = {};
    }
  };

  useEffect(() => {
    RegErrors.current = {};
    RegValues.current = {};

    if (!formik.isValid && formik.dirty) {
      RegErrors.current = { ...formik.errors };
      RegValues.current = { ...formik.values };
    }
  }, [
    formik.isValid,
    formik.isValidating,
    formik.dirty,
    formik.errors,
    formik.values,
  ]);

  useEffect(() => {
    if (!isFormStarted && formik.dirty) {
      sendFormStartedEventToAmplitude();
      setIsFormStarted(true);
    }
  }, [formik.dirty, isFormStarted]);

  useEffect(() => {
    window.addEventListener('beforeunload', beforeunload);

    return () => {
      window.removeEventListener('beforeunload', beforeunload);
      sendFormAbandonedErrorsToAmplitude();
    };
  }, []);

  useEffect(() => {
    formik.validateField('zip');
  }, [location]);

  useEffect(() => {
    if (formik.values.zip.length < 4) {
      setLocation('');
      setCountry('');
    }
  }, [formik.values.zip]);

  useEffect(() => {
    if (error) {
      setError('');
    }

    const autofilledFieldsQuery = document.querySelectorAll(
      'input:-webkit-autofill',
    );

    if (!nodeListsEqualTest(autofilledFieldsQuery, autofilledFields)) {
      setAutofilledFields(autofilledFieldsQuery);
    }
  }, [formik.values]);

  useEffect(() => {
    if (autofilledFields) {
      autofilledFields.forEach(({ id }) => {
        if (!formik.touched[id]) {
          setFieldTouched(id);
        }
      });
    }
  }, [autofilledFields]);

  useEffect(() => {
    if (!formik.values.zip) {
      formik.setFieldError('zip', '');
    }

    validateZipRef.current = cacheTest(validateZip, () => isTalent);
    formik.validateForm();
  }, [isTalent]);

  const toggleShowTerms = (e) => {
    e?.preventDefault();
    setShowTerms(!showTerms);
  };

  const toggleShowPrivacyPolicy = (e) => {
    e?.preventDefault();
    setShowPrivacyPolicy(!showPrivacyPolicy);
  };

  const formatPhoneNumber = (e) => {
    formik.setFieldValue('phoneNumber', maskPhoneNumber(e.target.value));
  };

  const register = async (values) => {
    const body = { ...values };

    if (body.phoneNumber) {
      body.phoneNumber = body.phoneNumber.replace(/[^A-Z0-9]+/gi, '');
    }

    if (body.zip) {
      body.zip = body.zip.replaceAll(' ', '');
    }

    return (
      await ApiNoCache.clientAPIRoute(`/register`, {
        method: 'POST',
        body: JSON.stringify(body),
      })
    ).data;
  };

  const login = async (identifier, key) => {
    return (
      await ApiNoCache.clientAPIRoute(`/login/nonce`, {
        method: 'POST',
        body: JSON.stringify({
          identifier,
          key,
        }),
      })
    ).data;
  };

  const getLocation = async (zip) => {
    return Api.clientAPIRoute(`/register/location/${zip}`);
  };

  const setFieldTouched = (field) => {
    formik.setFieldTouched(field);
  };

  const handleFormChange = (event) => {
    formik.handleChange(event);

    if (!formik.isSubmitting && !inputStarted) {
      setInputStarted(true);

      Amp.track(Amp.events.formStarted, {
        name: `${isTalent ? 'talent' : 'casting director'} signup`,
        scope: Amp.element.scope.modal,
      });
    }
  };

  return (
    <form
      className={cn(styles['registration-form'], styles[`theme-${styleTheme}`])}
      onSubmit={formik.handleSubmit}
      onChange={handleFormChange}
      data-cy={`${isTalent ? 'talent' : 'director'}-registration-form`}
    >
      <div className={styles['socials-container']}>
        <SocialButtons
          isModal={isModal}
          isTalent={isTalent}
          styleTheme={styleTheme}
        />
        <span className={styles['socials-or']}>or</span>
      </div>
      <div className={styles['form-row']}>
        <Input
          name="firstName"
          placeholder="First Name"
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          value={formik.values.firstName}
          isTouched={formik.touched.firstName}
          error={formik.errors.firstName}
          styleTheme={styleTheme}
        />
        <Input
          name="lastName"
          placeholder="Last Name"
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          value={formik.values.lastName}
          isTouched={formik.touched.lastName}
          error={formik.errors.lastName}
          styleTheme={styleTheme}
        />
      </div>
      <div className={styles[isTalent ? 'zip-form-row' : 'form-row']}>
        <Input
          name="zip"
          placeholder="Zip Code"
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          value={formik.values.zip}
          isTouched={formik.touched.zip}
          error={formik.errors.zip}
          hint={!isTalent && '(optional)'}
          styleTheme={styleTheme}
        />
        {location && isTalent && !locationLoading && (
          <div className={styles.location}>{location}</div>
        )}
        {locationLoading && isTalent && (
          <Loading minHeight="40px" padding="0" />
        )}
        {!isTalent && (
          <Input
            name="phoneNumber"
            placeholder="Phone number"
            onChange={formatPhoneNumber}
            onBlur={formik.handleBlur}
            value={formik.values.phoneNumber}
            isTouched={formik.touched.phoneNumber}
            error={formik.errors.phoneNumber}
            hint="(optional)"
            styleTheme={styleTheme}
          />
        )}
      </div>
      <Input
        name="email"
        placeholder="Email"
        onChange={formik.handleChange}
        onBlur={formik.handleBlur}
        value={formik.values.email}
        isTouched={formik.touched.email}
        error={formik.errors.email}
        styleTheme={styleTheme}
      />
      <PasswordInput
        name="password"
        value={formik.values.password}
        error={formik.errors.password}
        isTouched={formik.touched.password}
        onChange={formik.handleChange}
        onBlur={formik.handleBlur}
        placeholder={'Create Password'}
        hint={'Min 8 characters'}
        styleTheme={styleTheme}
      />
      <div
        className={cn(styles['agreement-container'], {
          [styles['agreement-error']]:
            formik.touched.agree && formik.errors.agree,
        })}
      >
        <Checkbox
          name="agree"
          onChange={formik.handleChange}
          value={formik.values.agree}
          error={formik.errors.agree}
          onBlur={formik.handleBlur}
          isTouched={formik.touched.agree}
          styleTheme={styleTheme}
        >
          <div className={styles['agreement-text']}>
            <span>
              By choosing to join, I certify I am at least 18 years old and have
              read and agree to the AllCasting.com
            </span>
            <span> </span>
            <span className={styles.link} onClick={toggleShowPrivacyPolicy}>
              privacy policy
            </span>
            <span> and </span>
            <span className={styles.link} onClick={toggleShowTerms}>
              terms of use
            </span>
            <span> . </span>
            <span>
              I agree to receive welcome email, newsletter, SMS &amp; occasional
              account updates from AllCasting.com
            </span>
          </div>
        </Checkbox>
      </div>
      {error && <small className={styles['error-message']}>{error}</small>}
      <div
        className={cn(styles['action-container'], {
          [styles['loading']]: loading,
        })}
      >
        {loading ? (
          <Loading minHeight="40px" padding="0" />
        ) : (
          <div className={styles['btn-box']}>
            {styleTheme === 'dark' ? (
              <Button
                className={styles.button}
                color={'blue'}
                disabled={!(formik.isValid && formik.dirty)}
                type="submit"
                label="Join Now"
                minWidth={'220px'}
              />
            ) : (
              <Button
                className={styles.button}
                color={isTalent ? 'orange' : 'green-gradient'}
                disabled={!(formik.isValid && formik.dirty)}
                type="submit"
                label="Sign Up"
                minWidth={'220px'}
              />
            )}
          </div>
        )}
      </div>
      {showTerms && (
        <Modal
          backdropClose
          onClose={toggleShowTerms}
          disableBackgroundScroll={!isModal}
        >
          <TermsOfUse />
        </Modal>
      )}
      {showPrivacyPolicy && (
        <Modal
          backdropClose
          onClose={toggleShowPrivacyPolicy}
          disableBackgroundScroll={!isModal}
        >
          <PrivacyPolicy onNavigation={toggleShowPrivacyPolicy} />
        </Modal>
      )}
    </form>
  );
};

export default memo(RegistrationForm);
