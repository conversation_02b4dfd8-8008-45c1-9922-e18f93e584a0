@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.overlay {
  backdrop-filter: blur(3px);
  display: flex;
  align-items: center;
  overflow: auto;
  position: fixed;
  inset: 0;
  z-index: 1000;
  background-color: $black-100-opacity-30;
  justify-content: center;
}

.content {
  width: 100%;
  overflow-x: hidden;
  position: relative;
  height: 100%;
  max-width: 850px;

  @include tablet {
    height: unset;
  }
}
