'use client';
import React, { memo, useCallback, useState } from 'react';
import styles from './ProfileInfo.module.scss';
import Categories from './Categories';
import Skills from './Skills';
import PersonalInfo from './PersonalInfo';
import Appearance from './Appearance';
import { CookieService } from '@services/cookieService';
import UnionAffiliation from './FieldComponents/UnionAffiliation';
import Advanced from './Advanced';
import { Hint } from '@components';
import MultipleProfile from './MultipleProfile';
import UnlockSkills from './UnlockFeature/UnlockSkills';

const ProfileInfo = ({
  hideProfileProgressInfoBlock,
  hideAttributeLevelInfoBlock,
  profileId,
  accountId,
  categories,
  skills,
  gender,
  location,
  ethnicity,
  birthday,
  zipCode,
  height,
  weight,
  eyeColor,
  hairColor,
  hipSize,
  dressSize,
  bust,
  cupSize,
  genderOptions,
  ethnicitiesOptions,
  heightOptions,
  weightOptions,
  eyeColorOptions,
  hairColorOptions,
  hipSizeOptions,
  dressSizeOptions,
  bustOptions,
  cupSizeOptions,
  genderTitle,
  refreshUserProfiles,
  categoryOptions,
  refreshSkills,
  refreshCategories,
  refreshProfileDetails,
  updateProfileRating,
  attributes,
  saveAttribute,
  refreshProfileAttributes,
  profileAttributes,
  isPaidOrDelayed = false,
  multiples,
}) => {
  const [isProfileProgressInfoBlockShow, setProfileProgressInfoBlockShow] =
    useState(!hideProfileProgressInfoBlock);

  const closeProgressInfoBlock = useCallback(() => {
    CookieService.setProfileProgressInfoBlockHidden(true);
    setProfileProgressInfoBlockShow(false);
  }, []);

  return (
    <>
      {isProfileProgressInfoBlockShow && (
        <div className={styles['hint-container']}>
          <Hint
            title="Your Profile Progress"
            canClose
            onClose={closeProgressInfoBlock}
            classNameAdditional={'gray'}
          >
            Get points for completing your profile to get noticed by the casting
            directors. See your progress in a special section and increase your
            chances of getting an audition.
            <br />
            <br />
            <a
              target="_blank"
              rel="noreferrer"
              href={`${process.env.publicUrl}/blog/beginner-s-guide/perfect-allcasting-profile-guide`}
            >
              Learn more about Profile Progress
            </a>
          </Hint>
        </div>
      )}
      <div className={styles['profile-section-content']}>
        <div className={styles['profile-section-row']}>
          <Categories
            profileId={profileId}
            categories={categories}
            categoryOptions={categoryOptions}
            refreshCategories={refreshCategories}
          />
          <Skills
            profileId={profileId}
            skills={skills}
            refreshSkills={refreshSkills}
            updateProfileRating={updateProfileRating}
          />
        </div>
        <PersonalInfo
          profileId={profileId}
          accountId={accountId}
          ethnicity={ethnicity}
          initialLocation={location}
          birthday={birthday}
          gender={gender}
          zipCode={zipCode}
          genderOptions={genderOptions}
          ethnicitiesOptions={ethnicitiesOptions}
          refreshUserProfiles={refreshUserProfiles}
          refreshProfileDetails={refreshProfileDetails}
        />
        {attributes.unions && (
          <div className={styles['profile-section-row']}>
            <UnionAffiliation
              attribute={attributes.unions}
              selectedOptions={profileAttributes.unions?.items || []}
              saveAttribute={saveAttribute}
              refreshProfileAttributes={refreshProfileAttributes}
            />
          </div>
        )}
        <Appearance
          profileId={profileId}
          genderTitle={genderTitle}
          height={height}
          bust={bust}
          weight={weight}
          cupSize={cupSize}
          dressSize={dressSize}
          hipSize={hipSize}
          eyeColor={eyeColor}
          hairColor={hairColor}
          heightOptions={heightOptions}
          weightOptions={weightOptions}
          eyeColorOptions={eyeColorOptions}
          hairColorOptions={hairColorOptions}
          hipSizeOptions={hipSizeOptions}
          dressSizeOptions={dressSizeOptions}
          bustOptions={bustOptions}
          cupSizeOptions={cupSizeOptions}
          refreshProfileDetails={refreshProfileDetails}
          attributes={attributes}
          profileAttributes={profileAttributes}
          saveAttribute={saveAttribute}
          refreshProfileAttributes={refreshProfileAttributes}
        />
        <MultipleProfile
          profileId={profileId}
          multiples={multiples}
          refreshProfileDetails={refreshProfileDetails}
        />
        {!isPaidOrDelayed ? (
          <UnlockSkills genderTitle={genderTitle} />
        ) : (
          <Advanced
            attributes={attributes}
            hideAttributeLevelInfoBlock={hideAttributeLevelInfoBlock}
            refreshProfileAttributes={refreshProfileAttributes}
            saveAttribute={saveAttribute}
            profileAttributes={profileAttributes}
          />
        )}
      </div>
    </>
  );
};

export default memo(ProfileInfo);
