'use client';
import React, { memo, useCallback, useEffect, useRef, useState } from 'react';
import styles from './MultiSelect.module.scss';
import cn from 'classnames';
import { Checkbox } from '@components';

const MultiSelect = ({
  name,
  options,
  placeholder,
  onChange,
  isTouched,
  error,
  onBlur,
  setFormFieldTouched,
  selectedOptions = [],
  hint,
}) => {
  const [showOptions, setShowOptions] = useState(false);
  const showError = isTouched && error;
  const dropdownRef = useRef(null);
  const optionsRef = useRef([]);
  const inputRef = useRef(null);

  const focusOption = useCallback(
    (index) => {
      const currentOption = optionsRef.current.find(
        (node) => node.id === `${name}-option-${index}`,
      );

      currentOption.focus();
    },
    [name],
  );

  useEffect(() => {
    const closeOptions = (event) => {
      if (!String(event.target.id).includes(name)) {
        setShowOptions(false);
      }
    };

    if (showOptions) {
      document.addEventListener('click', closeOptions);
    } else {
      document.removeEventListener('click', closeOptions);
    }

    return () => document.removeEventListener('click', closeOptions);
  }, [name, setFormFieldTouched, showOptions]);

  const toggleShowOptions = () => {
    setShowOptions(!showOptions);
  };

  const onTriggerClick = (event) => {
    event.preventDefault();
    focusInput();
    setShowOptions(!showOptions);
  };

  const onValueChange = (checked, option) => {
    const newOptions = checked
      ? [...selectedOptions, option]
      : selectedOptions.filter((selectedOption) => selectedOption !== option);

    onChange(newOptions);
  };

  const focusInput = () => {
    inputRef?.current?.focus();
  };

  const onInputKeyDown = (event) => {
    switch (event.code) {
      case 'ArrowUp':
      case 'Enter':
      case 'Space':
        event.preventDefault();
        toggleShowOptions();
        break;
      case 'ArrowDown':
        event.preventDefault();

        if (showOptions) {
          focusOption(0);
        } else {
          toggleShowOptions();
        }
        break;
      default:
        break;
    }
  };

  const onOptionKeyDown = (event, index, value) => {
    event.preventDefault();

    switch (event.code) {
      case 'ArrowUp':
        if (index !== 0) {
          focusOption(index - 1);
        } else if (index === 0) {
          focusInput();
          setShowOptions(false);
        }
        break;
      case 'ArrowDown':
        if (index !== options.length - 1) {
          focusOption(index + 1);
        }
        break;
      case 'Enter':
      case 'Space':
        onValueChange(!selectedOptions.includes(value), value);
        break;
      case 'Tab':
        toggleShowOptions();
        focusInput();
        break;
      default:
        break;
    }
  };

  return (
    <div
      className={cn(styles['select-container'], {
        [styles['select-error']]: showError,
      })}
    >
      <div
        id={`${name}-container`}
        onClick={onTriggerClick}
        className={cn(styles['select-field'], {
          [styles.open]: showOptions,
        })}
      >
        <input
          id={name}
          className={styles.select}
          type="text"
          onBlur={onBlur}
          value={selectedOptions
            .map(
              (selectedOption) =>
                options.find((option) => option.value === selectedOption).title,
            )
            .join(', ')}
          onKeyDown={onInputKeyDown}
          ref={inputRef}
          readOnly
        />
        <div
          id={`${name}-select-trigger`}
          className={cn(styles['select-trigger'], {
            [styles.open]: showOptions,
          })}
        ></div>
        <div
          id={`${name}-select-focus-border`}
          className={styles['focus-border']}
        ></div>
        <label
          id={`${name}-label`}
          className={cn(styles.label, {
            [styles['floating-label']]: selectedOptions.length || showOptions,
          })}
          htmlFor={name}
        >
          {placeholder}
        </label>
      </div>
      {showOptions && (
        <div className={styles.options} ref={dropdownRef}>
          {options.map((option, i) => (
            <div
              tabIndex={i}
              ref={(element) => (optionsRef.current[i] = element)}
              id={`${name}-option-${i}`}
              key={i}
              onKeyDown={(e) => onOptionKeyDown(e, i, option.value)}
              className={cn(styles.option, {
                [styles['selected-option']]: selectedOptions.some(
                  (selectedOption) => selectedOption === option.value,
                ),
              })}
            >
              <div
                className={styles['checkbox-container']}
                onClick={(e) => {
                  e.stopPropagation();
                }}
              >
                <Checkbox
                  name={`${name}-option-checkbox-${i}`}
                  value={!!selectedOptions.includes(option.value)}
                  onChange={(e) =>
                    onValueChange(e.target.checked, option.value)
                  }
                >
                  <div
                    id={`${name}-option-title-${i}`}
                    className={styles['option-title']}
                  >
                    {option.title}
                  </div>
                </Checkbox>
              </div>
            </div>
          ))}
        </div>
      )}
      {showError && typeof error !== 'boolean' && (
        <div className={styles['error-message']}>{error}</div>
      )}
      {!showError && hint && <div className={styles.hint}>{hint}</div>}
    </div>
  );
};

export default memo(MultiSelect);
