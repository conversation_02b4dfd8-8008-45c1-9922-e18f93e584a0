import { memo, useCallback, useEffect, useState } from 'react';
import { Loading, Modal, ProfilePreview } from '../../index';
import styles from './ModalProfile.module.scss';
import { ProfileProvider } from '../../../contexts/ProfileContext';
import {
  formatProProfile,
  formatTalentProfile,
} from '../../../utils/formatProfile';
import Api from '../../../services/api';
import { useNotifications } from '../../../contexts/NotificationContext';
import { ErrorMessage } from '../../../constants/form';

const ModalProfile = ({ id, onClose, onOpenContactTalentModal }) => {
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const { setNotification } = useNotifications();

  const onError = useCallback(
    (message) => {
      setNotification({
        type: 'error',
        message: message || ErrorMessage.Unexpected,
        timeout: '5000',
      });

      onClose();
    },
    [onClose, setNotification],
  );

  useEffect(() => {
    const getProfile = async () => {
      const selectedProfile = await fetchProfile(id);
      const selectedProfileImages = await fetchProfileImages(id);
      const selectedProfileEthnicities = await fetchProfileEthnicities(id);

      if (
        [
          selectedProfile.status !== 'ok',
          selectedProfileImages.status !== 'ok',
          selectedProfileEthnicities.status !== 'ok',
        ].some((value) => value)
      ) {
        onError(
          selectedProfile.message ||
            selectedProfileImages.message ||
            selectedProfileEthnicities.message,
        );
      } else {
        setProfile(
          selectedProfile.rel === 'agent'
            ? formatProProfile(selectedProfile, selectedProfileImages.items)
            : formatTalentProfile(
                selectedProfile,
                selectedProfileImages.items,
                selectedProfileEthnicities.items,
              ),
        );
      }
    };

    if (id) {
      getProfile()
        .then(() => setLoading(false))
        .catch(() => onError(''));
    }
  }, [id, onError]);

  const fetchProfile = async (id) => {
    return await Api.clientAPIRoute(`/profiles/${id}`);
  };

  const fetchProfileImages = async (id) => {
    return await Api.clientAPIRoute(`/profiles/images/${id}`);
  };

  const fetchProfileEthnicities = async (id) => {
    return await Api.clientAPIRoute(`/profiles/ethnicities/${id}`);
  };

  const openContactTalentModal = () => {
    onClose();
    onOpenContactTalentModal();
  };

  return (
    <ProfileProvider value={profile}>
      <Modal
        backdropClose
        onClose={onClose}
        floatCloseButton
        showDefaultLayout={false}
        classNameContainer={styles['profile-modal']}
        showMobileBorderRadius={false}
        hideMobileCloseButton
        classNameContent={styles['profile-modal-content']}
      >
        {loading ? (
          <Loading minHeight="80px" padding="20px" />
        ) : (
          <ProfilePreview
            onContactTalent={openContactTalentModal}
            onClose={onClose}
          />
        )}
      </Modal>
    </ProfileProvider>
  );
};

export default memo(ModalProfile);
