'use client';
import React, { memo, useEffect, useRef, useState } from 'react';
import {
  Carousel,
  CropToolOverlay,
  DeclinedImage,
  ImageActions,
  Modal,
  ModalDeletePhoto,
  ModalEditPhoto,
  PhotoUploadTool,
  TooltipPhotoDeclined,
  UploadImage,
} from '@components';
import styles from './MainImageMobileCarousel.module.scss';
import ImageAnalyzer from '../ImageAnalyzer/ImageAnalyzer';
import { getImageStarRating, isImageDeclined } from '@utils/imageHelpers';
import ProfileHeader from '../../Profile/ProfileHeader';
import { useProfileContext } from '@contexts/ProfileContext';
import { useViewport } from '@utils/useViewport';
import { isInViewPort } from '@utils/isInViewPort';
import { Amp } from '@services/amp';

const defaultMainPhotos = [
  { title: 'Headshot', type: 'close_up' },
  { title: 'Side view', type: '3_4' },
  { title: 'Full height', type: 'height' },
];

const MainImageMobileCarousel = ({
  closeUpImage,
  sideViewImage,
  titleImage,
  genderTitle,
  isPaidOrDelayed,
  fullHeightImage,
  refreshUserProfiles,
  refreshProfileDetails,
  openRatingProgress,
}) => {
  const [activeImage, setActiveImage] = useState(closeUpImage);
  const [activeImageDefault, setActiveImageDefault] = useState(
    defaultMainPhotos[0],
  );
  const [selectedImageUrl, setSelectedImageUrl] = useState(null);
  const [showCropTool, setShowCropTool] = useState(false);
  const [showTitleCropTool, setShowTitleCropTool] = useState(false);
  const [showDeletePhotoModal, setShowDeletePhotoModal] = useState(false);
  const [showEditPhotoModal, setShowEditPhotoModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const photoUploadToolRef = useRef(null);
  const [showDeclinedModal, setShowDeclinedModal] = useState(false);

  const { deleteImage } = useProfileContext();
  const { width } = useViewport();

  useEffect(() => {
    if (isInViewPort(width, 'tablet', 'min') && showDeclinedModal) {
      closeDeclinedModal();
    }
  }, [width]);

  useEffect(() => {
    if (activeImageDefault?.type === 'close_up') {
      setActiveImage(closeUpImage);
    } else if (activeImageDefault?.type === '3_4') {
      setActiveImage(sideViewImage);
    } else {
      setActiveImage(fullHeightImage);
    }
  }, [closeUpImage, sideViewImage, fullHeightImage]);

  const getAmpSectionName = () => {
    switch (activeImageDefault?.type) {
      case 'close_up':
        return Amp.element.section.headshot;
      case '3_4':
        return Amp.element.section.sideView;
      case 'height':
        return Amp.element.section.fullHeight;
      default:
        return Amp.element.section.additionalPhotos;
    }
  };

  const onSlideChange = (index) => {
    if (index === 0) {
      setActiveImage(closeUpImage);
      setActiveImageDefault(defaultMainPhotos[0]);
    } else if (index === 1) {
      setActiveImage(sideViewImage);
      setActiveImageDefault(defaultMainPhotos[1]);
    } else {
      setActiveImage(fullHeightImage);
      setActiveImageDefault(defaultMainPhotos[2]);
    }
  };

  const onFileInputChange = (imageUrl) => {
    setSelectedImageUrl(imageUrl);
    setShowCropTool(true);
  };

  const openDeletePhotoModal = () => {
    const section = getAmpSectionName();

    Amp.track(Amp.events.elementClicked, {
      name: `remove ${section}`,
      scope: Amp.element.scope.profile,
      section: section,
      type: Amp.element.type.button,
    });
    setShowDeletePhotoModal(true);
  };

  const closeDeletePhotoModal = () => {
    setShowDeletePhotoModal(false);
  };

  const openEditPhotoModal = () => {
    setShowEditPhotoModal(true);
  };

  const closeEditPhotoModal = () => {
    setShowEditPhotoModal(false);
  };

  const closeCropTool = () => {
    setShowCropTool(false);
    setShowTitleCropTool(false);
  };

  const deletePhoto = async () => {
    setLoading(true);

    await deleteImage(activeImage.id);

    setLoading(false);
    setShowDeletePhotoModal(false);
  };

  const onMakeTitleClick = () => {
    const section = getAmpSectionName();

    Amp.track(Amp.events.elementClicked, {
      name: 'make photo title',
      scope: Amp.element.scope.profile,
      section: section,
      type: Amp.element.type.button,
    });
    setSelectedImageUrl(activeImage.url);
    setShowTitleCropTool(true);
  };

  const selectFile = () => {
    const section = getAmpSectionName();

    Amp.track(Amp.events.elementClicked, {
      name: `upload ${section}`,
      scope: Amp.element.scope.profile,
      section: section,
      type: Amp.element.type.button,
    });

    photoUploadToolRef.current.selectFile();
  };

  const openDeclinedModal = () => {
    setShowDeclinedModal(true);
  };

  const closeDeclinedModal = () => {
    setShowDeclinedModal(false);
  };

  const getImageComponent = (image, imageType, imageTitle) => {
    switch (true) {
      case !image:
        return (
          <UploadImage
            onFileInputChange={onFileInputChange}
            imageType={imageType}
            title={imageTitle}
            genderTitle={genderTitle}
            selectFile={selectFile}
            isMainImage={true}
          />
        );
      case isImageDeclined(image):
        return (
          <DeclinedImage
            onClick={selectFile}
            src={image.url}
            contentSize="large"
            actionLabel="Upload New Photo"
            declineReason={image.decline_reason}
            isTooltip={false}
            openModal={openDeclinedModal}
          />
        );
      default:
        return (
          <ImageActions
            showUploadIcon={true}
            image={image}
            onDelete={openDeletePhotoModal}
            onMakeTitleClick={onMakeTitleClick}
            selectFile={selectFile}
            actionIconSize="large"
            isCurrentTitlePhoto={
              titleImage && image.id === titleImage?.source_id
            }
            isPaidOrDelayed={isPaidOrDelayed}
            showTooltip={false}
            showRating={true}
            iconSize="large"
            showRatingHint={false}
            genderTitle={genderTitle}
            showActions={false}
            openEditModal={openEditPhotoModal}
          />
        );
    }
  };

  return (
    <>
      <PhotoUploadTool
        ref={photoUploadToolRef}
        onFileInputChange={onFileInputChange}
      >
        <div style={{ position: 'relative' }}>
          <Carousel
            enablePagination
            slidesToScroll={1}
            className="carousel-main-images"
            onSlideChange={onSlideChange}
            paginationClassName="carousel-main-images-pagination"
            paginationColor="white"
            enableArrowNavigation={!isImageDeclined(activeImage)}
            stopOnMouseEnter
            stopOnInteraction
          >
            <div className={styles['profile-image']}>
              {getImageComponent(closeUpImage, 'close_up', 'Headshot')}
            </div>
            <div className={styles['profile-image']}>
              {getImageComponent(sideViewImage, '3_4', 'Side view')}
            </div>
            <div className={styles['profile-image']}>
              {getImageComponent(fullHeightImage, 'height', 'Full height')}
            </div>
          </Carousel>
          <ProfileHeader
            refreshUserProfiles={refreshUserProfiles}
            refreshProfileDetails={refreshProfileDetails}
            openRatingProgress={openRatingProgress}
          />
        </div>
        <div className={styles['analyzer-container']}>
          <ImageAnalyzer
            characteristics={
              activeImage ? activeImage.links?.characteristics?.items : []
            }
            starRating={getImageStarRating(
              activeImage?.links?.characteristics?.items || [],
            )}
            isTooltip={false}
            isReviewPending={
              activeImage && !activeImage.links?.characteristics?.items?.length
            }
            title={activeImageDefault.title}
            isPaidOrDelayed={isPaidOrDelayed}
            isBlocked={isImageDeclined(activeImage)}
            genderTitle={genderTitle}
          />
        </div>
      </PhotoUploadTool>
      {(showCropTool || showTitleCropTool) && (
        <CropToolOverlay
          onClose={closeCropTool}
          image={showCropTool ? selectedImageUrl : activeImage}
          type={activeImageDefault.type}
          isCurrentTitlePhoto={
            titleImage && activeImage?.id === titleImage?.source_id
          }
          skipDefaultCrop={showTitleCropTool}
        />
      )}
      {showDeletePhotoModal && (
        <ModalDeletePhoto
          onClose={closeDeletePhotoModal}
          onDeletePhoto={deletePhoto}
          loading={loading}
        />
      )}
      {showDeclinedModal && (
        <Modal
          onClose={closeDeclinedModal}
          backdropClose
          classNameOverlay={styles['modal-overlay']}
          classNameContainer={styles['modal-container']}
          showDefaultLayout={false}
        >
          <TooltipPhotoDeclined
            declineReason={activeImage.decline_reason}
            label="Upload new photo"
            onClick={() => {
              closeDeclinedModal();
              selectFile();
            }}
          />
        </Modal>
      )}
      {showEditPhotoModal && (
        <ModalEditPhoto
          onClose={closeEditPhotoModal}
          title={activeImageDefault.title}
          onDelete={() => {
            closeEditPhotoModal();
            openDeletePhotoModal();
          }}
          onMakeTitle={() => {
            closeEditPhotoModal();
            onMakeTitleClick();
          }}
          onUpload={() => {
            closeEditPhotoModal();
            selectFile();
          }}
          isCurrentTitlePhoto={
            titleImage && activeImage?.id === titleImage?.source_id
          }
        />
      )}
    </>
  );
};

export default memo(MainImageMobileCarousel);
