import { useRouter } from 'next/router';
import Image from 'next/image';
import Link from 'next/link';
import React, { useEffect, useRef, useState } from 'react';
import dayjs from 'dayjs';
import Api from '../../services/api';
import {
  Accordion,
  Attachment,
  Back,
  Breadcrumbs,
  Button,
  CastingCallList,
  CategoryList,
  Modal,
  ModalReviewExtended,
  PaymentLabel,
  ModalReportCastingCall,
  Role,
  Switch,
} from '../../components';
import styles from '../../styles/castingcall.module.scss';
import Seo from '../../components/Seo/Seo';
import { CC_CATEGORY, TYPE, VIEW_MODE } from '../../constants/castingCalls';
import { MainLayout, PageLayout } from '../../components/Layouts';
import cn from 'classnames';
import { useAuth } from '../../contexts/AuthContext';
import ApiNoCache from '../../services/apiNoCache';
import { CookieService } from '../../services/cookieService';
import { getDaysFromDate } from '../../utils/getDaysFromDate';
import Carousel from '../../components/Carousel/Carousel';
import { formatJobPostData } from '../../utils/seoSchema/jobPosting';
import { Amp } from '../../services/amp';
import { getGatewayHeaders, getHeaders } from '../../utils/headerHelper';
import { sharedServerSideProps } from '../../utils/sharedServerSideProps';
import FlagIcon from '../../public/assets/icons/icon-flag.svg';
import { formatTalentProfile } from '../../utils/formatProfile';

export async function getServerSideProps({ query, req, resolvedUrl, res }) {
  const isTalent = CookieService.getUserTypeCookie(req, res) === 'talent';

  const castingCallResponse =
    isTalent && !query.cypress
      ? (
          await ApiNoCache.serverGateway(
            `/calls/get?id=${query.static}`,
            getGatewayHeaders(resolvedUrl),
            req,
            res,
          )
        ).data
      : await Api.serverAPIRoute(
          `/castingcalls/item?slug=${query.static}`,
          null,
          getHeaders(req, resolvedUrl),
        );

  let isUnavailable = false,
    isExpired = false;

  const isBonusCc = !!castingCallResponse.data?.tags?.filter(
    (tag) => tag.slug === 'bonus_cc',
  ).length;

  /**
   * First we take from original.data then from data.
   *
   * Reason:
   *
   * backward compatibility of returning
   * statuses of casting call from gateway.
   */
  const status =
    castingCallResponse.original?.data?.status ||
    castingCallResponse.data?.status ||
    '';

  // SEO: https://confluence.dyninno.net/display/casting/Casting+Call+Page
  switch (status) {
    case 'approved':
      // do nothing
      break;
    case 'edited':
    case 'hold':
      return {
        redirect: {
          destination: `/castingcalls`,
          permanent: false,
        },
      };
    case 'declined':
      return {
        redirect: {
          destination: `/castingcalls`,
          permanent: true,
        },
      };
    case 'deactivated':
    case 'deleted':
      res.statusCode = 410;
      isUnavailable = true;
      break;
    case 'expired':
      if (isBonusCc) {
        return {
          redirect: {
            destination: `/castingcalls`,
            permanent: true,
          },
        };
      }
      isExpired = true;
      break;
    default:
      return {
        notFound: true,
      };
  }

  const hasCategory = castingCallResponse?.data?.category?.id;

  const relatedCastingCallsResponse = isTalent
    ? (
        await ApiNoCache.serverGateway(
          !hasCategory
            ? `/calls/search?limit=5`
            : `/calls/search?limit=5&categories[]=${castingCallResponse?.data?.category?.id}`,
          getGatewayHeaders(resolvedUrl),
          req,
          res,
        )
      ).data
    : await Api.serverAPIRoute(
        !hasCategory
          ? `/castingcalls/related`
          : `/castingcalls/related?category=${castingCallResponse?.data?.category?.id}`,
        Api.TTL_TYPE_MEDIUM,
        getHeaders(req, resolvedUrl),
      );

  const agentInfoResponse = castingCallResponse.data.agent_id
    ? await Api.serverGateway(
        `/clients/${castingCallResponse.data.agent_id}?expand=profiles,personal_url`,
        {
          ...req.headers,
          ...getGatewayHeaders(resolvedUrl).headers,
        },
      )
    : null;

  const sideMenuItems = [
    ...(await Api.serverAPIRoute(
      `/sidemenu/castingcall`,
      null,
      getHeaders(req, resolvedUrl),
    )),
    {
      title: `ROLES (${castingCallResponse.data.roles?.length || 0})`,
      items:
        castingCallResponse.data.roles?.map(({ id, title }) => ({
          title,
          selector: `#role-${id}`,
        })) || [],
    },
  ];

  let requestReviewFromTalent = false;

  let profile = {};

  let reportOptions = [];

  if (isTalent) {
    if (castingCallResponse.data?.type === TYPE.Email) {
      const profileId = CookieService.getProfileCookie(req, res);
      const paths = [
        `/profiles/${profileId}?expand=personal_url,location,touches,ethnicities`,
        `/profiles/${profileId}/images?expand=profile_image_characteristics&width=600&height=800`,
        `/profiles/${profileId}/socialities`,
      ];

      const requests = await Promise.all(
        paths.map((path) =>
          ApiNoCache.serverGateway(
            path,
            getGatewayHeaders(resolvedUrl),
            req,
            res,
          ),
        ),
      );

      const [profileResponse, imageResponse, socialNetworkResponse] =
        requests.map(({ data }) => data);

      profile = formatTalentProfile(
        profileResponse,
        imageResponse.items,
        (profileResponse.links?.ethnicities?.items || [])[0],
        socialNetworkResponse.items || [],
      );
    }

    reportOptions =
      (
        await ApiNoCache.serverGateway(
          `/casting-calls/complaint/types`,
          getGatewayHeaders(resolvedUrl),
          req,
          res,
        )
      ).data?.data || [];

    await ApiNoCache.serverGateway(
      `/casting-calls/${query.static}/checked`,
      {
        method: 'POST',
        body: {},
        ...getGatewayHeaders(resolvedUrl),
      },
      req,
      res,
    );

    const reviewReminded = CookieService.getReviewRemindedCookie(req, res);

    if (!reviewReminded) {
      const accountLevel = CookieService.getAccountLevelCookie(req, res);
      const status = (
        await ApiNoCache.serverGateway(
          `/clientstatus/info`,
          getGatewayHeaders(resolvedUrl),
          req,
          res,
        )
      ).data;

      requestReviewFromTalent = [
        status?.info?.show_review_popup,
        getDaysFromDate(status?.info?.registration_date) > 2,
        accountLevel?.isPaidOrDelayed,
      ].every((value) => value);
    }
  }

  const agentUrl = agentInfoResponse
    ? agentInfoResponse.links?.profiles?.items[0]?.links?.personal_url?.path ||
      agentInfoResponse.links?.profiles?.items[0]?.id
    : null;

  const sharedProps = await sharedServerSideProps(req, res);

  return {
    props: {
      ...sharedProps,
      relatedCastingCalls:
        relatedCastingCallsResponse.data?.calls
          .filter(
            (call) =>
              call.id !== castingCallResponse.data.id &&
              !call.roles.find((role) => role.attended),
          )
          .slice(0, 4) || [],
      castingCall: castingCallResponse.data,
      agentURL: agentUrl ?? null,
      referer: req.headers.referer || '',
      sideMenuItems,
      isExpired: isExpired,
      isUnavailable: isUnavailable,
      requestReviewFromTalent,
      jobPostData:
        !isExpired && !isUnavailable
          ? formatJobPostData(castingCallResponse.data)
          : null,
      reportOptions,
      profile,
    },
  };
}

function Content({
  relatedCastingCalls,
  castingCall,
  agentURL,
  referer,
  sideMenuItems,
  isExpired,
  requestReviewFromTalent,
  isUnavailable,
  jobPostData,
  reportOptions,
  profile,
}) {
  const [showMatchingRolesOnly, setShowMatchingRolesOnly] = useState(false);
  const [showAvoidScamsTooltip, setShowAvoidScamsTooltip] = useState(false);
  const [showReviewModal, setShowReviewModal] = useState(false);
  const [showReportModal, setShowReportModal] = useState(false);
  const [reportSubmitted, setReportSubmitted] = useState(false);
  const [showAttachmentGallery, setShowAttachmentGallery] = useState(false);
  const [clickedImageIndex, setClickedImageIndex] = useState(null);
  const [visibleRoles, setVisibleRoles] = useState(castingCall.roles || []);
  const [previousPage, setPreviousPage] = useState(referer || '');
  const [loading, setLoading] = useState(false);

  const { userType, isAuthenticated, accountLevel } = useAuth();
  const router = useRouter();
  const timerRef = useRef(null);
  const rolesContainerRef = useRef(null);
  const rolesRef = useRef([]);

  const isReported = castingCall.is_complaint_applied || reportSubmitted;
  const isSingleRole = castingCall.roles?.length === 1;
  const ogImageUrl = `${process.env.baseUrl}/assets/meta/casting-call.webp`;

  useEffect(() => {
    if (requestReviewFromTalent) {
      timerRef.current = setTimeout(() => {
        setShowReviewModal(true);

        Amp.track(Amp.events.modalViewed, {
          name: 'ask for review',
          scope: Amp.element.scope.castingCallItemPage,
          section: Amp.element.section.reviews,
        });
      }, 5000);
    }

    return () => clearTimeout(timerRef.current);
  }, [requestReviewFromTalent]);

  useEffect(() => {
    Amp.track(Amp.events.viewCastingCall, {
      casting_call_id: castingCall.id,
      casting_call_category: castingCall.category?.name,
    });
  }, [castingCall]);

  useEffect(() => {
    setShowAvoidScamsTooltip(!sessionStorage.getItem('scam-warning-seen'));
    const refererCache = JSON.parse(sessionStorage.getItem('refererData'));

    if (refererCache?.referer) {
      setPreviousPage(refererCache.referer || '');
    }

    if (refererCache?.page !== router.asPath) {
      sessionStorage.setItem(
        'refererData',
        JSON.stringify({ referer, page: router.asPath }),
      );
    }

    return () => {
      const refererCache = JSON.parse(sessionStorage.getItem('refererData'));

      if (location.pathname !== refererCache?.page) {
        sessionStorage.removeItem('refererData');
      }
    };
  }, []);

  useEffect(() => {
    if (castingCall.roles) {
      setVisibleRoles(
        showMatchingRolesOnly
          ? castingCall.roles.filter((role) => role.full_match)
          : castingCall.roles,
      );
    }
  }, [castingCall.roles, showMatchingRolesOnly]);

  const handleNavigateBack = async () => {
    let backURL = previousPage;

    if (backURL && !backURL.includes(window.location.hostname)) {
      backURL = '';
    }
    await router.push(backURL || '/castingcalls');
  };

  const toggleShowMatchingRolesOnly = () => {
    setShowMatchingRolesOnly(!showMatchingRolesOnly);
  };

  const hideAvoidScamsTooltip = () => {
    sessionStorage.setItem('scam-warning-seen', 'true');
    setShowAvoidScamsTooltip(false);
  };

  const closeReviewModal = () => {
    setShowReviewModal(false);
    setReminderToRequestReview();
  };

  const closeReportModal = () => {
    setShowReportModal(false);
  };

  const setReminderToRequestReview = () => {
    const expires = new Date();

    expires.setDate(expires.getDate() + 10);
    CookieService.setReviewRemindedCookie(true, null, null, expires);
    setShowReviewModal(false);
  };

  const toggleShowGallery = () => {
    setShowAttachmentGallery(!showAttachmentGallery);
  };

  const onImageClick = (imageId) => {
    if (imageId) {
      const index = castingCall.files
        .filter(({ content_type }) => content_type.split('/')[0] === 'image')
        .findIndex(({ id }) => id === imageId);

      setClickedImageIndex(index);
    }

    toggleShowGallery();
  };

  const onApplyButtonClick = async () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'apply for casting call',
      section: Amp.element.section.castingCall,
      scope: Amp.element.scope.castingCallItemPage,
      type: Amp.element.type.button,
      casting_call_id: castingCall.id,
    });

    switch (true) {
      case !isAuthenticated:
        await router.push('/register');
        break;
      case isSingleRole:
        setLoading(true);
        await rolesRef.current[0].applyToRole();
        setLoading(false);
        break;
      default:
        rolesContainerRef.current.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
        });
    }
  };

  const handleReportClick = () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'submit a casting call complaint',
      section: Amp.element.section.castingCall,
      scope: Amp.element.scope.castingCallItemPage,
      type: Amp.element.type.button,
      casting_call_id: castingCall.id,
    });
    setShowReportModal(true);
  };

  const onEasyApplySuccess = () => {
    if (isSingleRole && castingCall.agent_id) {
      castingCall.roles[0].attended = true;
    }
  };

  const messageUnavailable = () => {
    return (
      <>
        <div className={styles['casting-call-unavailable-logo']}></div>
        <div className={styles['casting-call-unavailable-title']}>
          This casting call is
          <br />
          currently unavailable
        </div>
        <div className={styles['casting-call-unavailable-body']}>
          You can still browse{' '}
          <Link href={'/castingcalls'}>other casting calls</Link> from our
          database.
        </div>
      </>
    );
  };

  const messageExpired = () => {
    return (
      <>
        <div className={styles['casting-call-expired-column']}>
          <div className={styles['casting-call-expired-title']}>
            This casting call has expired.
          </div>
          <div className={styles['casting-call-expired-text']}>
            Not accepting new submissions.{' '}
          </div>
        </div>
        <Link
          href={`/castingcalls/${
            CC_CATEGORY[castingCall.category?.name?.toLowerCase()] || ''
          }`}
          className={styles['casting-call-expired-link']}
        >
          View related casting calls
        </Link>
      </>
    );
  };

  const contentUnavailable = () => {
    return (
      <section id="intro" className={styles['casting-call-section']}>
        <div
          className={cn(
            styles['casting-call-container'],
            styles['is-unavailable-casting-call'],
          )}
        >
          <div className={styles['casting-call-content']}>
            <div className={styles['casting-call-content-header']}>
              <Back
                shortLabel={'Casting Calls'}
                fullLabel={'Back to Casting Calls'}
                onClick={handleNavigateBack}
              />
              <div className={styles['casting-call-content-menu']}>
                <Link href="#description" scroll={false}>
                  <span className={styles['casting-call-content-menu-link']}>
                    Description
                  </span>
                </Link>
                <Link href="#roles" scroll={false}>
                  <span className={styles['casting-call-content-menu-link']}>
                    Roles
                  </span>
                </Link>
              </div>
              <Seo
                seoPage={{
                  title: 'Casting Calls for everyone • allcasting',
                  description:
                    '《 allcasting 》 ★ Auditions for everyone: we need talented people. Apply for castings and get hired. ★ Start To Be a Star Today!',
                  ogImageUrl,
                }}
              />
              <div className={styles['casting-call-unavailable-block']}>
                {messageUnavailable()}
              </div>
            </div>
            <div className={styles['casting-call-details-container']}>
              <div
                className={cn(
                  styles['casting-call-unavailable-block'],
                  styles['second'],
                )}
              >
                {messageUnavailable()}
              </div>
            </div>
          </div>
        </div>
      </section>
    );
  };

  const renderButton = () => {
    if (castingCall.roles?.length === 1 && castingCall.roles[0].attended) {
      return (
        <button className={styles['applied-btn']} disabled>
          <Image
            src="/assets/icons/icon-checkmark-2.svg"
            alt="icon"
            width={19}
            height={16}
          />
          Applied
        </button>
      );
    }

    return (
      <Button
        onClick={onApplyButtonClick}
        label="Apply"
        minWidth="100px"
        className={styles['apply-btn']}
        disabled={loading}
      />
    );
  };

  const contentAvailable = () => {
    const crumbs = [
      {
        text: castingCall.category?.name + ' Casting Calls',
        href: `/castingcalls/${
          CC_CATEGORY[castingCall.category?.name?.toLowerCase()] || ''
        }`,
      },
      { text: castingCall.title },
    ];

    return (
      <section id="intro" className={styles['casting-call-section']}>
        <div className={styles['casting-call-container']}>
          <div className={styles['casting-call-content']}>
            <div className={styles['casting-call-content-header']}>
              <Back
                fullLabel="Back to Casting Calls"
                shortLabel="Casting Calls"
                onClick={handleNavigateBack}
              />
              <Breadcrumbs crumbs={crumbs} />
              <div className={styles['casting-call-content-menu']}>
                <Link href="#description" scroll={false}>
                  <span className={styles['casting-call-content-menu-link']}>
                    Description
                  </span>
                </Link>
                <Link href="#roles" scroll={false}>
                  <span className={styles['casting-call-content-menu-link']}>
                    Roles
                  </span>
                </Link>
              </div>
              <Seo
                seoPage={{
                  title: `${
                    castingCall.title || 'Casting Call'
                  } | Casting Calls at allcasting`,
                  description: `Apply Now for ${crumbs[0].text} - ${
                    castingCall.title || 'Casting Call'
                  } at allcasting.com`,
                  ogImageUrl,
                }}
                jobPostData={jobPostData}
              />

              {isExpired && (
                <div className={styles['casting-call-expired-block']}>
                  {messageExpired()}
                </div>
              )}

              <h1 id="description">
                {castingCall.title || 'Casting Call'}
                {process.env.showCastingCallType === 'true' && (
                  <span> ({castingCall.type})</span>
                )}
              </h1>
              <div className={styles['casting-call-contacts-container']}>
                <div
                  className={styles['casting-call-contacts-inner-container']}
                >
                  <div className={styles['info-section']}>
                    <span className={cn(styles.info, styles['info-divider'])}>
                      {castingCall.location}
                    </span>
                    {isAuthenticated && (
                      <span
                        className={cn(
                          styles['report-button-container'],
                          styles['info-divider-left'],
                        )}
                      >
                        <button
                          className={styles['report-button']}
                          onClick={handleReportClick}
                          disabled={isReported}
                        >
                          <span className={styles['report-button-content']}>
                            <FlagIcon />
                            <span>{isReported ? 'Reported' : 'Report'}</span>
                          </span>
                        </button>
                      </span>
                    )}
                  </div>
                  {isAuthenticated && isReported && (
                    <div
                      className={cn(styles['reported-tooltip'], styles.mobile)}
                    >
                      <span>
                        You reported this casting call on{' '}
                        {castingCall.complaint_applied_date ||
                          dayjs(new Date()).format('MM/DD/YYYY')}
                      </span>
                    </div>
                  )}
                  <div className={styles.section}>
                    <span
                      className={cn(styles.identifier, styles['info-divider'])}
                    >
                      ID: {castingCall.id}
                    </span>
                    <span className={styles.date}>
                      Exp:{' '}
                      {new Date(castingCall.expiration).toLocaleDateString(
                        'en-US',
                      )}
                    </span>
                  </div>
                </div>
                {!isExpired && (
                  <div
                    className={cn(
                      styles['apply-btn-container'],
                      styles.desktop,
                    )}
                  >
                    {castingCall.roles?.length === 1 &&
                    castingCall.roles[0].attended ? (
                      <button
                        className={styles['applied-btn']}
                        disabled
                        data-cy="castingcall-apply-btn"
                      >
                        <Image
                          src="/assets/icons/icon-checkmark-2.svg"
                          alt="icon"
                          width={19}
                          height={16}
                        />
                        Applied
                      </button>
                    ) : (
                      <Button
                        onClick={onApplyButtonClick}
                        label="Apply"
                        minWidth="100px"
                        className={styles['apply-btn']}
                        dataCy="castingcall-apply-btn"
                        disabled={loading}
                      />
                    )}
                  </div>
                )}
                {isAuthenticated && isReported && (
                  <div
                    className={cn(styles['reported-tooltip'], styles.desktop)}
                  >
                    <span>
                      You reported this casting call on{' '}
                      {castingCall.complaint_applied_date ||
                        dayjs(new Date()).format('MM/DD/YYYY')}
                    </span>
                  </div>
                )}
              </div>
            </div>
            <div className={styles['casting-call-details-container']}>
              {(castingCall.payment_amount ||
                castingCall.payment_period === 'TFP') && (
                <span>
                  <PaymentLabel
                    paymentAmount={castingCall.payment_amount}
                    paymentPeriod={castingCall.payment_period}
                    paymentCurrency={castingCall.payment_currency}
                    inline
                  />
                </span>
              )}
              {castingCall.type === TYPE.Web && (
                <>
                  <div className={styles['divider-dot']} />
                  <span
                    className={cn(styles.badge, styles['easy-apply-badge'])}
                  >
                    easy apply
                  </span>
                </>
              )}
              {castingCall.online_audition && (
                <>
                  <div className={styles['divider-dot']} />
                  <span className={cn(styles.badge, styles['online-badge'])}>
                    online audition
                  </span>
                </>
              )}
            </div>
            <div className={styles['category-list']}>
              <CategoryList
                mainCategory={castingCall.category}
                additionalCategories={castingCall.additional_categories}
                collapse={false}
                clickable
              />
            </div>
            <div
              id="description"
              className={styles['casting-call-description-container']}
            >
              <span className={styles['casting-call-description-title']}>
                <strong>Description:</strong>
              </span>
              <p
                style={{ whiteSpace: 'pre-wrap' }}
                dangerouslySetInnerHTML={{ __html: castingCall.description }}
              ></p>
              {castingCall.agent_name && (
                <div>
                  <span>— </span>
                  {agentURL ? (
                    <Link href={`/director/${agentURL}`}>
                      {castingCall.agent_name}
                    </Link>
                  ) : (
                    <span>{castingCall.agent_name}</span>
                  )}
                  <span className={styles.star}></span>
                </div>
              )}
              {castingCall.files && (
                <div className={styles['attachment-container']}>
                  {castingCall.files.map((attachment, index) => (
                    <div
                      className={styles.attachment}
                      key={`attachment-${index}`}
                    >
                      <Attachment
                        attachment={attachment}
                        openImageAttachment={onImageClick}
                      />
                    </div>
                  ))}
                </div>
              )}
            </div>

            {isExpired && (
              <div
                className={cn(
                  styles['casting-call-expired-block'],
                  styles['second'],
                )}
              >
                {messageExpired()}
              </div>
            )}
            {showAvoidScamsTooltip && accountLevel?.isPaidOrDelayed && (
              <div className={styles['avoid-scams-tooltip']}>
                <span>Do not send money to anyone. Avoid scams and fraud!</span>
                <button className={styles['close-button']}>
                  <Image
                    onClick={hideAvoidScamsTooltip}
                    width={12}
                    height={12}
                    src={'/assets/icons/icon-close-1.svg'}
                    alt="icon-close-1.svg"
                  />
                </button>
              </div>
            )}
            {!isExpired && (
              <div className={cn(styles['apply-btn-container'], styles.mobile)}>
                {renderButton()}
              </div>
            )}
            <div className={styles['casting-call-roles-container']}>
              <div className={styles['casting-call-roles-title-container']}>
                <h2
                  ref={rolesContainerRef}
                  className={styles['casting-call-roles-title']}
                  id="roles"
                >
                  {visibleRoles.length} role
                  {visibleRoles.length === 1 ? '' : 's'}
                </h2>
                {userType === 'talent' && (
                  <div
                    className={styles['casting-call-roles-switch-container']}
                  >
                    <Switch
                      label="Matching roles only"
                      value={showMatchingRolesOnly}
                      onChange={toggleShowMatchingRolesOnly}
                    />
                  </div>
                )}
              </div>
              {visibleRoles.length ? (
                visibleRoles.map((role, i) => (
                  <Role
                    ref={(element) => (rolesRef.current[i] = element)}
                    isExpired={isExpired}
                    key={role.id}
                    id={role.id}
                    title={role.title}
                    gender={role.gender.name}
                    age={role.age_from + '-' + role.age_to}
                    ethnicities={role.ethnicities}
                    allEthnicitiesAccepted={role.all_ethnicities}
                    description={role.description}
                    isFullMatch={role.full_match}
                    criteria={role.criteria}
                    attended={role.attended}
                    dates={role.dates}
                    email={castingCall.agent_email}
                    emailSubject={role.email_subject}
                    castingCallId={castingCall.id}
                    castingCallType={castingCall.type}
                    castingCallWebsite={
                      role.application_link || castingCall.website || ''
                    }
                    castingCallLocation={castingCall.location}
                    castingCallAddress={castingCall.address?.address}
                    castingCallPhone={castingCall.phone}
                    castingCallTitle={castingCall.title}
                    isProRegistered={!!castingCall.agent_id}
                    profile={profile}
                    onEasyApplySuccess={onEasyApplySuccess}
                  />
                ))
              ) : (
                <span>No matching roles</span>
              )}
            </div>
          </div>
        </div>
        <div className={styles['casting-call-side-menu-container']}>
          <Accordion accordionItems={sideMenuItems} showButton={false} />
        </div>
      </section>
    );
  };

  return (
    <MainLayout
      isDefaultHeaderVisible
      isUserMenuVisible
      isSaleHeaderVisible
      isMobileSubscribeHeaderVisible
      isMobileMenuVisible
    >
      <PageLayout>
        {isUnavailable ? contentUnavailable() : contentAvailable()}
        <div className={styles['casting-call-related-container']}>
          <h3>RELATED CASTING CALLS</h3>
          <CastingCallList
            mode={VIEW_MODE.Grid}
            items={relatedCastingCalls}
            related
          />
        </div>
        {showReviewModal && <ModalReviewExtended onClose={closeReviewModal} />}
        {showReportModal && (
          <ModalReportCastingCall
            callId={castingCall.id}
            onClose={closeReportModal}
            reportOptions={reportOptions}
            setReportSubmitted={setReportSubmitted}
          />
        )}
        {showAttachmentGallery && (
          <Modal
            backdropClose
            onClose={toggleShowGallery}
            showCloseButton={false}
            showDefaultLayout={false}
            classNameContainer={styles['gallery-modal']}
            classNameContent={styles['gallery-modal-content']}
            containerClose
          >
            <Carousel
              enableArrowNavigation
              startIndex={clickedImageIndex}
              className="carousel-attachment-gallery"
              draggable={false}
              loop
            >
              {castingCall.files
                .filter(
                  ({ content_type }) => content_type.split('/')[0] === 'image',
                )
                .map(({ path }, index) => (
                  <img
                    key={index}
                    src={path}
                    style={{ width: '100%' }}
                    alt={index}
                  />
                ))}
            </Carousel>
          </Modal>
        )}
      </PageLayout>
    </MainLayout>
  );
}

export default Content;
