@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.payment-form {
  height: 100%;
  padding-top: $space-30;

  @include tablet {
    padding-bottom: 0;
    min-height: unset;
    height: fit-content;
  }
}

.payment-form-content {
  padding-bottom: 100px;
  background-color: $white;
  overflow: hidden;

  &.overflow-visible {
    overflow: visible;
  }

  @include tablet {
    padding-bottom: 0;
    border-radius: 10px;
  }
}

.payment-information-container {
  display: flex;
  flex-direction: column;
  gap: $space-20;
  padding: 0 $space-45;
}

.payment-information-title-container {
  text-align: center;
}

.payment-information-title {
  font-size: 24px;
  line-height: 1.2;
  font-weight: 700;
  text-align: center;
  display: none;

  @include tablet {
    display: initial;
  }
}

.form-row {
  display: flex;
  align-items: center;
  gap: $space-40;
  flex-direction: column;

  @include tablet {
    flex-direction: row;
  }
}

.form-inner-row {
  width: 100%;
  display: flex;
  align-items: center;
  gap: $space-20;
}

.form-row-title {
  font-size: 14px;
  line-height: 30px;
  color: $grey-80;
  text-transform: uppercase;
  display: flex;
  align-items: center;
  font-weight: 300;
  width: 100%;

  &.extra-padding {
    padding-top: $space-20;
  }
}

.form-row-column {
  width: 100%;
}

.payment-form-actions {
  border-top: 1px solid $grey-60;
  display: none;
  justify-content: center;
  align-items: center;
  padding: $space-20;
  margin: $space-20 (-$space-50) 0;

  @include tablet {
    display: flex;
  }
}

.payment-form-button {
  color: $blue-100;
  font-weight: 400;
  border: none;
  background-color: transparent;
  font-size: 16px;
  cursor: pointer;

  &:disabled {
    opacity: 0.3;
    cursor: default;
  }
}

.payment-form-error-container {
  display: flex;
  justify-content: center;
  text-align: center;
  width: 100%;
  margin-bottom: -$space-20;
}

.payment-form-error {
  color: $red-80;
  font-size: 14px;
  font-weight: 300;
}

.form-field {
  display: flex;
  width: 100%;
  align-items: flex-end;
  gap: $space-20;
}

.cvv-icon-container {
  width: 100%;
  display: flex;
  align-items: center;

  @include desktop {
    width: initial;
  }
}

.cvv-icon {
  filter: invert(73%) sepia(2%) saturate(436%) hue-rotate(260deg)
    brightness(90%) contrast(90%);
  margin-bottom: 2px;
}

div.select-options {
  max-height: 210px;
}
