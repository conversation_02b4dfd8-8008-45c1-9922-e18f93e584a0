'use client';
import styles from './PasswordInput.module.scss';
import cn from 'classnames';
import { Input } from '@components';
import { memo, useState } from 'react';

const PasswordInput = ({
  name,
  onChange,
  onBlur,
  value,
  isTouched,
  error,
  placeholder = 'Password',
  hint,
}) => {
  const [showPassword, setShowPassword] = useState(false);

  const toggleShowPassword = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div className={styles['password-field']}>
      <div className={styles['password-input-container']}>
        <Input
          name={name}
          placeholder={placeholder}
          onChange={onChange}
          onBlur={onBlur}
          value={value}
          isTouched={isTouched}
          type={showPassword ? 'text' : 'password'}
          hint={hint}
          error={error}
        />
      </div>
      <div className={styles['password-field-image']}>
        <div
          onClick={toggleShowPassword}
          className={cn(styles['password-icon-container'], {
            [styles['show-password']]: showPassword,
          })}
        ></div>
      </div>
    </div>
  );
};

export default memo(PasswordInput);
