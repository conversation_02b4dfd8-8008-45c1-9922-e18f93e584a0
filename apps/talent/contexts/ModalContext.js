import { createContext, useContext, useState } from 'react';

const ModalContext = createContext(null);

export const useModalContext = () => {
  return useContext(ModalContext);
};

export const ModalProvider = ({ children, value }) => {
  const [isModalOpened, setModalOpened] = useState(false);

  return (
    <ModalContext.Provider
      value={{
        ...value,
        isModalOpened: isModalOpened,
        setModalOpened: setModalOpened,
      }}
    >
      {children}
    </ModalContext.Provider>
  );
};
