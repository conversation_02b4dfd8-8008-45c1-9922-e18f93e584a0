@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.crop-tool-outer-container {
  height: 100%;

  @include tablet {
    padding: 0 $space-20;
  }
}

.crop-tool-container {
  width: 100%;
  position: relative;
  height: 100%;
  background-color: $white;
  display: flex;
  flex-direction: column;

  @include tablet {
    border-radius: 10px;
    min-height: unset;
    max-width: 850px;
    max-height: calc(100vh - 20px);
    max-height: calc(100dvh - 20px);
  }
}

.crop-tool {
  height: calc(100vh - 49px);
  height: calc(100dvh - 49px);
  width: 100%;
  background-color: $black;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;

  @include tablet {
    height: 70vh;
    max-height: 600px;
  }
}

.crop-tool-actions {
  display: none;
  grid-template-columns: 1fr 1fr;
  justify-content: center;
  align-items: stretch;
  width: 100%;
  border: 1px solid $grey-60;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  background-color: $white;

  @include tablet {
    display: grid;
  }
}

.crop-tool-action-button {
  background-color: transparent;
  padding: $space-20;
  border: none;
  font-weight: 300;
  font-size: 16px;
  cursor: pointer;

  &:first-of-type {
    border-right: 1px solid $grey-60;
  }

  &:hover {
    text-decoration: underline;
  }

  &:disabled {
    cursor: not-allowed;
    color: $grey-80;
    text-decoration: none;
  }
}

.crop-tool-action-button-cancel {
  color: $red-60;
  border-bottom-left-radius: 10px;
}

.zoom-slider-container {
  position: absolute;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  width: 100%;
  top: 0;
  padding: $space-20 $space-40 $space-10;
  gap: $space-5;
  background-color: $grey-100-opacity-20;

  @include tablet {
    gap: $space-20;
    height: 100%;
    right: 10px;
    width: 100px;
    padding: 0;
    background-color: transparent;
  }
}

.zoom-slider-inner-container {
  overflow: hidden;
  display: flex;
  justify-content: center;
  flex-direction: row;
  width: 100%;

  @include tablet {
    align-items: center;
    max-height: 60vh;
    height: 100%;
    flex-direction: column;
  }
}

.zoom-slider {
  accent-color: $blue-100;
  width: 100%;

  @include tablet {
    min-width: 60vh;
    height: 20px;
    transform: rotate(270deg);
    justify-self: flex-end;
  }
}

.zoom-slider-progress {
  color: $white;
}

.crop-tool-rotate-container {
  position: absolute;
  color: $blue-100;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: $space-10;
  cursor: pointer;
  user-select: none;
  font-weight: 400;
  border-radius: 50%;
  padding: $space-10;
  background-color: $grey-100-opacity-20;
  bottom: 20px;
  left: 20px;

  @include tablet {
    bottom: 40px;
    left: 40px;
  }
}

.crop-tool-rotate-icon {
  filter: invert(65%) sepia(100%) saturate(3200%) hue-rotate(160deg)
    brightness(104%) contrast(99%);
}

.crop-tool-header {
  display: grid;
  grid-template-columns: 0.2fr 1fr 0.2fr;
  background-color: $white;
  align-items: center;
  justify-content: space-between;
  text-align: center;

  &.disabled {
    color: $grey-80;
  }

  @include tablet {
    grid-template-columns: 1fr;
    padding: $space-15 0;
    border-top-right-radius: 10px;
    border-top-left-radius: 10px;
    border-bottom: 1px solid $grey-60;
  }
}

.crop-tool-mobile-action-button {
  padding: $space-15 $space-20;
  background-color: transparent;
  border: none;
  font-weight: 300;
  font-size: 16px;
  cursor: pointer;

  &:disabled {
    cursor: not-allowed;
    color: $grey-80;
    text-decoration: none;
  }

  @include tablet {
    display: none;
  }
}

.crop-tool-action-button-crop {
  color: $blue-100;
  font-weight: 400;
  border-bottom-right-radius: 10px;
}
