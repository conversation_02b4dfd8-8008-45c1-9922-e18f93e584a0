import React, { useState } from 'react';
import styles from './CastingCalls.module.scss';
import Api from '../../services/api';
import { Button, CastingCall, Loading } from '../index';
import { TYPE, VIEW_MODE } from '../../constants/castingCalls';

const PageConstructorCastingCalls = ({ data }) => {
  const [castingCalls, setCastingCalls] = useState(
    data?.response?.data?.calls || [],
  );
  const [page, setPage] = useState(2);
  const [isLoading, setLoading] = useState(false);

  const filterParams = data?.filterParams.replace('page=1', '');

  const loadMore = async () => {
    setLoading(true);
    const requestMore = await Api.clientGateway(
      `/calls/search${filterParams}&limit=5&page=${page}`,
    );

    setCastingCalls(castingCalls.concat(requestMore.data?.calls || []));
    setPage(page + 1);
    setLoading(false);
  };

  return (
    <div className={styles.container}>
      {castingCalls?.map((item, i) => (
        <CastingCall
          key={i}
          mode={VIEW_MODE.Widget}
          type={item.type}
          mainCategory={item.category}
          location={item.location}
          title={item.title}
          description={false}
          rolesCount={item.roles.length}
          expires={item.expiration_date || item.expiration}
          isOnline={item.online_audition}
          paymentAmount={item.payment_amount}
          paymentPeriod={item.payment_period}
          paymentCurrency={item.payment_currency}
          isEasyToApply={item.type === TYPE.Web}
          hot={item.hot}
          id={item.id}
        />
      ))}
      <div className={styles.controls}>
        {isLoading ? (
          <Loading />
        ) : (
          <Button onClick={loadMore} label="LOAD MORE" minWidth="250px" />
        )}
      </div>
    </div>
  );
};

export default PageConstructorCastingCalls;
