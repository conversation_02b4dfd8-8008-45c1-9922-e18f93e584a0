@use '@styles/mixins' as *;

.countdown {
  display: flex;
  align-items: flex-start;
  margin: 10px 0;

  @include tablet {
    margin: 0;
  }
}

.time-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.time-digit {
  font-size: 30px;
  font-weight: 700;
  line-height: 30px;
  padding: 0 14px;
}

.time-title {
  font-size: 14px;
  line-height: 18px;
  font-weight: 300;
  opacity: 0.8;
}

.time-separator {
  font-size: 16px;
  font-weight: 700;
  line-height: 30px;
}

.countdown-horizontal {
  gap: 5px;

  .time-container {
    flex-direction: row;
    align-items: flex-end;
    gap: 5px;
  }

  .time-title {
    text-transform: uppercase;
    font-size: 10px;
    font-weight: 400;
    opacity: 1;
  }

  .time-digit {
    font-weight: 900;
    font-size: 24px;
    padding: 0;
  }
}
