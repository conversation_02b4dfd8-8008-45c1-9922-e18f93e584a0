'use client';
import styles from './Footer.module.scss';
import React, { createRef } from 'react';
import { Button } from '@components';

const Footer = ({ value, handleInputChange, submitQuestion }) => {
  let input;

  return (
    <div className={styles.wrapper}>
      <div className={styles['form-wrap']}>
        <form
          className={styles.form}
          ref={createRef()}
          onSubmit={(e) => {
            e.preventDefault();
            submitQuestion({ displayText: input.value });
            input.value = '';
          }}
        >
          <div className={styles['input-box']}>
            <div className={styles['input-area']}>
              <input
                autoComplete="off"
                className={styles['message-input']}
                ref={(node) => {
                  input = node;
                }}
                onChange={handleInputChange}
                value={value}
                placeholder="Ask a question..."
              />
            </div>
            <div className={styles['input-controls']}>
              <div className={styles['input-submit']}>
                <span className={styles['input-counter']}>
                  {value.length} / 1000
                </span>
                <Button
                  type="submit"
                  label="SEND"
                  minWidth="80px"
                  disabled={value.length < 1}
                >
                  SEND
                </Button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default Footer;
