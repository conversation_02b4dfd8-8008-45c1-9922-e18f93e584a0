'use client';
import { memo } from 'react';
import styles from './ScrollTopButton.module.scss';
import Image from 'next/image';
import cn from 'classnames';

const ScrollTopButton = ({ containerRef, type = 'text' }) => {
  const scrollToTop = () => {
    if (containerRef) {
      containerRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
    } else {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  return (
    <button
      type="button"
      onClick={scrollToTop}
      className={cn({
        [styles['scroll-top-icon-btn']]: type === 'icon',
        [styles['scroll-top-text-btn']]: type === 'text',
      })}
    >
      {type === 'icon' && (
        <Image
          src="/assets/icons/icon-arrow-up.svg"
          width={9}
          height={10}
          alt=""
        />
      )}
      {type === 'text' && <span className={styles.text}>back to top</span>}
    </button>
  );
};

export default memo(ScrollTopButton);
