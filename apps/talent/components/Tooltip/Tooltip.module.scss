@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.tooltip-trigger {
  cursor: pointer;
  position: relative;
  height: fit-content;

  &.active {
    z-index: $z-index-tooltip;
  }
}

.tooltip-content {
  cursor: default;
  box-shadow: $shadow-tooltip;
  background-color: $white;
  padding: $space-10;
  max-width: 400px;
  border-radius: 10px;

  &.clickable {
    cursor: inherit;
  }
}

.tooltip-arrow-container {
  padding: 0;
}

.icon-close-container {
  position: relative;
}

.icon-close {
  position: absolute;
  right: 10px;
  top: 10px;
  cursor: pointer;
}

.tooltip {
  z-index: $z-index-tooltip;
}

.tooltip-overlay {
  position: fixed;
  inset: 0;
  height: 100%;
  width: 100%;
  z-index: $z-index-tooltip-overlay;
  display: flex;
  justify-content: center;
  align-items: center;

  &.backdrop {
    backdrop-filter: blur(3px);
    background-color: $black-100-opacity-30;
  }
}
