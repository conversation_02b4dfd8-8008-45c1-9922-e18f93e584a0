'use client';
import { memo } from 'react';
import { Button, Input, Modal } from '@components';
import styles from './ModalShareAudioLink.module.scss';
import { useNotifications } from '@contexts/NotificationContext';

const ModalShareAudioLink = ({ link, onClose, title }) => {
  const { setNotification } = useNotifications();
  const copy = async () => {
    try {
      await navigator.clipboard.writeText(link);
      setNotification({
        type: 'success',
        message: `Link to audio "${title}" is copied to clipboard`,
        timeout: '5000',
      });
      onClose();
    } catch (err) {
      setNotification({
        type: 'error',
        message: `Failed to copy audio "${title}" link to clipboard`,
        timeout: '5000',
      });
    }
  };

  return (
    <Modal
      classNameContainer={styles['modal-share-audio']}
      onClose={onClose}
      backdropClose
    >
      <div className={styles['modal-share-audio-content']}>
        <Input placeholder="Link" value={link} onChange={() => {}} />
        <div>
          <Button
            label="Copy link"
            minWidth="130px"
            color="plain-blue"
            onClick={copy}
          />
        </div>
      </div>
    </Modal>
  );
};

export default memo(ModalShareAudioLink);
