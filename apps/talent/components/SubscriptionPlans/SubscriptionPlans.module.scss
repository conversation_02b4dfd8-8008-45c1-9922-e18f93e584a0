@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.container {
  display: flex;
  width: 100%;
  justify-content: center;
  margin-top: -78px;
  padding: 0 var(--container-padding);

  --container-padding: 10px;
  --color: #{$green-80};
  --promo-color: #{$green-100};
  --price-color: #{$red-60};
  --price-selected-color: #{$red-60};
  --price-bg-color: #{$grey-10};
  --price-bg-selected-color: #{$grey-10};
  --border-color: #{$grey-20};
  --border-radius: 10px;
  --border-radius-large: 16px;
  --border-width: 1px;
  --shadow-width: 2px;
  --period-badge-color: #{$white};
  --period-badge-bg-color: #{$black};

  @include desktop {
    margin-top: -117px;
  }
}

.bottom-container {
  display: flex;
  width: 100%;
  justify-content: center;

  @include desktop {
    margin-top: -20px;
  }
}

.bottom {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: $space-20;
  max-width: 600px;
  width: 100%;

  @include desktop {
    gap: $space-15;

    &.extra-gap {
      gap: $space-25;
    }
  }
}

.container-sale {
  margin-top: -80px;

  @include desktop {
    margin-top: -120px;
  }
}

.plans {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: $space-10;
  max-width: 600px;
  width: 100%;

  @include desktop {
    gap: $space-15;

    &.extra-gap {
      gap: $space-25;
    }
  }
}

.plan {
  width: 100%;
  font-weight: 400;
  height: 80px;
  color: $black;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  background-color: $white;
  border-radius: var(--border-radius);
  transition: all 0.2s ease-in-out;
  border: var(--border-width) solid var(--border-color);
  cursor: pointer;
  position: relative;
  user-select: none;

  @include desktop {
    height: 98px;
    grid-template-columns: 1fr 0.8fr 1fr;
    border-radius: var(--border-radius-large);
  }

  @media (hover: hover) {
    &:hover {
      border-color: var(--color);
      box-shadow: inset 0 0 0 var(--shadow-width) var(--color);
    }
  }

  &.selected {
    border-color: var(--color);
    box-shadow: inset 0 0 0 var(--shadow-width) var(--color);
  }

  img {
    height: auto;
  }
}

.plan.promo {
  &::before {
    position: absolute;
    content: 'Promo';
    top: 0;
    left: 0;
    height: 20px;
    line-height: 20px;
    width: 55px;
    border-radius: calc(var(--border-radius) - var(--border-width)) 0 13px;
    font-size: 12px;
    text-align: center;
    font-weight: 700;
    background-color: $grey-10;
    color: var(--promo-color);
    transition: all 0.2s ease-in-out;

    @include desktop {
      height: 31px;
      line-height: 31px;
      border-radius: calc(var(--border-radius-large) - var(--border-width)) 0
        13px;
      width: 86px;
      border-bottom-right-radius: 16px;
      font-size: 16px;
    }
  }

  &:hover::before {
    box-shadow: inset var(--shadow-width) var(--shadow-width) var(--color);
  }

  &.selected::before {
    background-color: var(--color);
    color: $white;
  }
}

.period-container {
  position: relative;
  display: flex;
  align-items: center;
  gap: $space-5;
  justify-content: center;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  padding-right: $space-5;

  @include xsmall {
    font-size: 14px;
    padding-right: 0;
  }

  @include desktop {
    font-weight: 400;
    font-size: 18px;
    gap: $space-20;
  }
}

.plan.selected .period-container {
  font-weight: 500;
}

.period-container.period-badge-enabled {
  justify-content: initial;
  gap: $space-10;

  @include desktop {
    gap: $space-20;
  }

  .radio {
    margin-left: $space-15;

    @include desktop {
      margin-left: $space-35;
    }
  }

  .period-badge-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }

  .period-badge {
    font-size: 8px;
    font-weight: 700;
    background-color: var(--period-badge-bg-color);
    color: var(--period-badge-color);
    padding: 0 3px;
    border-radius: 2px;

    @include desktop {
      font-size: 14px;
      padding: 0 $space-5;
      border-radius: 3.25px;
    }
  }
}

.price-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: var(--price-bg-color);
  height: 100%;
  clip-path: inset(0 0 0 0);
  transition: all 0.2s ease-in-out;
}

.plan:hover > .price-container,
.plan.selected > .price-container {
  clip-path: inset(var(--shadow-width) 0 var(--shadow-width) 0);
}

.plan.selected > .price-container {
  background-color: var(--price-bg-selected-color);
}

.price-base {
  font-size: 12px;
  font-weight: 300;
  text-decoration: line-through;
  line-height: 1;

  @include desktop {
    font-size: 14px;
  }
}

.plan:not(.plan.selected) .price-base {
  color: $grey-100;
}

.price {
  color: var(--price-color);
  font-size: 18px;
  font-weight: 700;
  line-height: 1.2;

  @include desktop {
    font-size: 30px;
  }

  .currency {
    font-size: 0.6em;
  }
}

.plan.selected .price {
  color: var(--price-selected-color);
}

.price-period {
  font-size: 12px;
  font-weight: 500;
  line-height: 1;
  text-transform: lowercase;

  @include desktop {
    font-weight: 600;
    font-size: 14px;
  }
}

.savings-container {
  color: $grey-80;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  line-height: 1;

  @include desktop {
    font-size: 20px;
  }
}

.plan.selected > .savings-container {
  color: var(--color);
  font-weight: 500;
}

.savings {
  font-size: 18px;

  @include desktop {
    font-size: 30px;
  }

  & sup {
    font-size: 14px;

    @include desktop {
      font-size: 20px;
    }
  }
}

.plan.selected .savings {
  font-weight: 700;

  & sup {
    font-weight: 500;
  }
}

.button-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 0;
}

.footer {
  font-size: 14px;
  font-weight: 300;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-top: $space-10;
  gap: $space-20;

  @include desktop {
    margin-top: $space-20;
    font-size: 14px;
  }
}

.text-accent {
  font-weight: 400;
}

/* stylelint-disable */
.theme-dark {
  &.container {
    margin-top: 0;
  }

  .label-container {
    display: none;
  }

  .icon {
    max-width: 36px;

    @include desktop {
      max-width: 50px;
    }
  }

  .plans {
    gap: $space-25;
    width: 100%;

    @include tablet {
      gap: $space-40;
    }
  }

  .plan {
    background-color: $violet-80;
    border: var(--border-width) solid $violet-70;
    outline: 0 solid $blue-100;
    color: $white;

    &:hover {
      box-shadow: none;
      outline: 3px solid $blue-100;

      .price-container {
        box-shadow: none;
      }
    }

    &.selected {
      border-color: $blue-100;
      box-shadow: inset 0 0 0 var(--shadow-width) $blue-100;

      .price-container {
        background: $violet-70;
      }

      .savings-container {
        color: $white;
        font-weight: 300;

        span {
          opacity: 0.7;
        }

        .savings {
          font-weight: 700;
          opacity: 1;
        }
      }
    }
  }

  .price-container {
    background: $violet-70;
  }

  .price {
    color: $white;
  }

  button {
    color: $black;
  }
}

.label-container {
  position: absolute;
  width: 100%;
  display: flex;
  justify-content: flex-start;
  top: 0;

  .before {
    background-color: var(--color);
    color: $white;
    font-style: normal;
    font-size: 12px;
    font-weight: 600;
    position: relative;
    top: -10px;
    left: 6px;
    margin: 0;
    padding: 4px 6px;
    border-radius: 5px;
    white-space: nowrap;
    z-index: 1;
    line-height: 1;

    @include desktop {
      top: -18px;
      left: -15px;
      line-height: 36px;
      padding: 0 $space-10;
      border-radius: 10px;
      font-size: 21px;
    }
  }
}

.gift {
  margin-top: 7px;
  font-size: 8px;
  color: $white;
  background-color: $red-80;
  padding: 4px 3px;
  border-radius: 3px;
  width: fit-content;

  @include desktop {
    margin-top: 7px;
    font-size: 14px;
    padding: 7px;
  }
}

.description {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  font-size: 12px;
  padding: 0 $space-5;

  @include desktop {
    padding: $space-10 $space-20;
    font-size: 18px;
  }
}

.icon {
  position: absolute;
  right: -10px;
  top: -12px;
  width: 36px;
  height: 36px;
  z-index: 1;

  @include desktop {
    width: 50px;
    height: 50px;
    right: -10px;
    top: -20px;
  }
}

.refund-info {
  width: calc(100% + var(--container-padding) * 2);
  background-color: #{$blue-100};
  color: $white;
  padding: 14px 15%;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  @include desktop {
    width: 100%;
    padding: 18px 14%;
    border-radius: 16px;
    align-items: center;
    font-size: 16px;
  }

  svg {
    position: absolute;
    margin-right: 10px;
    vertical-align: text-bottom;
    right: 100%;
  }
}

.refund-info-first {
  position: relative;
}
