'use client';
import { memo } from 'react';
import styles from './AudioPlayerProgressBar.module.scss';

const AudioPlayerProgressBar = ({ progressBarRef, audioRef }) => {
  const handleProgressChange = () => {
    audioRef.current.currentTime = progressBarRef.current.value;
  };

  return (
    <div className={styles['volume-slider-container']}>
      <input
        className={styles['volume-slider']}
        type="range"
        defaultValue="0"
        step="0.001"
        ref={progressBarRef}
        onChange={handleProgressChange}
      />
    </div>
  );
};

export default memo(AudioPlayerProgressBar);
