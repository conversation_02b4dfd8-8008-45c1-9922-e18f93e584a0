import { MainLayout } from '@components/Layouts';
import React from 'react';
import styles from './ErrorLayout.module.scss';
import { Button } from '@components';

const ErrorLayout = ({ reset }) => {
  return (
    <MainLayout
      isDefaultHeaderVisible
      isSaleHeaderVisible
      isUserMenuVisible
      isMobileMenuVisible
      isFooterVisible
    >
      <div className={styles.container}>
        <h1 className={styles.title}>Oops... Something went wrong</h1>
        <div className={styles.actions}>
          <Button label="Try again" onClick={reset} shadow={false} />
          <span>or</span>
          <a className={styles.link} href={process.env.publicUrl}>
            Go to homepage
          </a>
        </div>
      </div>
    </MainLayout>
  );
};

export default ErrorLayout;
