export const ErrorMessage = {
  Unexpected: 'Unexpected error occurred',
  UnexpectedBilling: 'Unexpected error occurred while fetching billing',
  AgeMin: 'You must be at least 18 y.o.',
  AgeMax: 'You have exceeded the maximum age limit.',
  AgeMinWizard:
    'You have to be at least 18 y. o. to complete your registration.',
  ZipMax: 'Maximum zip code length reached!',
  NamePattern:
    "Must contain 1-35 characters, Latin letters, spaces, - and/or '",
  PhonePattern: 'Please provide a valid phone number',
  PhonePatternToll:
    'Sorry, we do not accept toll-free and other non-geographic numbers. Please use your home or mobile phone number.',
  ZipPattern: 'Please provide a valid zip code',
  ZipPatternBilling: 'Please enter a valid billing zip/postal code',
  CardNumberPattern:
    'The credit card entered is invalid. Please check the number and try again.',
  CVVPattern: 'The CVV entered is invalid. Please check the CVV and try again.',
  PasswordPattern:
    'Password must be minimum 8 characters long and have at least one letter',
  NetworkPattern: 'Please, provide correct link',
  EmailPattern: 'Please provide a valid email',
  LastNameRequired: 'Please enter your last name',
  NameRequired: 'Please enter your first name',
  NameRequiredBilling: 'Please enter your name on card',
  GenderRequired: 'Please choose a gender',
  EthnicityRequired: 'Please choose an ethnicity',
  MonthRequired: 'Please provide a month',
  DayRequired: 'Please provide a day',
  YearRequired: 'Please provide a year',
  ZipRequired: 'Please provide a zip code',
  BirthdayRequired: 'Please provide your birthday',
  BirthdayMatch: 'Please provide your full date of birth',
  BirthdayValid: 'Please provide a valid date',
  CardNumberRequired: 'Please enter your credit card number',
  CVVRequired: 'Please enter a 3 digit CVV number',
  ExpirationMonthRequired: 'Please provide an Expiration Month',
  ExpirationYearRequired: 'Please provide an Expiration Year',
  EmailRequired: 'Please enter your e-mail',
  PasswordRequired: "Password can't be empty",
  NewPasswordRequired: 'Please enter your new password',
  PasswordConfirmationRequired: 'Please enter password confirmation',
  JobTitleRequired: 'Please enter Job title',
  DescriptionsRequired: 'Please enter description',
  PasswordMatch: 'Passwords should match',
  PasswordInvalid: 'Password is incorrect. Please, try again.',
  MaxCharacters: 'Max X characters',
  MinCharacters: 'Min X characters',
  ReasonRequired: 'Please provide a reason',
  URLRequired: 'Please provide a valid personal URL',
  MessageRequired: 'Please enter a message',
  TitleRequired: 'Please choose a title',
  MaxCharactersLatin:
    'Text should contain latin letters only and not exceed X characters',
  MinCharactersLatin:
    'Text should contain latin letters only and not less than X characters',
  MessageFailed: 'Could not send message',
  YoutubeLinkRequired: 'Please provide a valid Youtube link',
  SalesClosed: 'Sales have closed for today. Please try again tomorrow.',
};

export const PHONE_NUMBER_REGEX =
  /^(\([0-9]{3}\) |[0-9]{3}-)[0-9]{3}-[0-9]{4}$/;

export const NAME_REGEX = /^[a-zA-Z]+[a-zA-Z '`\-]{0,19}$/i;

export const EMAIL_REGEX = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

export const PASSWORD_REGEX = /\p{L}/giu;

export const BIRTHDAY_REGEX =
  /^(0[1-9]|1[0-2])\/(0[1-9]|[12][0-9]|3[01])\/(19|20)\d{2}$|^(0[1-9]|1[0-2]) (0[1-9]|[12][0-9]|3[01]) (19|20)\d{2}$/;

export const NETWORK_REGEX =
  /(http(s)?:\/\/.)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}(\/[-a-zA-Z0-9@:%_\+.~#?&=]*)*$/;

export const JOB_TITLE_REGEX = /^[a-zA-Z][a-zA-Z &.,'`-]{2,129}$/;

export const PERSONAL_URL_REGEX = /^[a-zA-Z][a-zA-Z0-9-_]{5,99}$/;

export const MASTER_CARD_REGEX =
  /^5[1-5][0-9]{14}$|^2(?:2(?:2[1-9]|[3-9][0-9])|[3-6][0-9][0-9]|7(?:[01][0-9]|20))[0-9]{12}$/;

export const VISA_REGEX = /^4[0-9]{12}(?:[0-9]{3})?$/;

export const ABSOLUTE_PATH_REGEX = /^(?:f|ht)tps?\:\/\//;

export const MESSAGE_LINK_REGEX = /(https?:\/\/[^\s]+)/g;

export const FULL_NAME_REGEX = /^[a-zA-Z]+[a-zA-Z '`\-]{0,34}$/i;

export const INTERNAL_URL_REGEX =
  /^https:\/\/(talent\.)?(allcasting\.(com|test)|frontend\.ac\.stg\.entertech\.art)(\/[^?]*)?(\?.*)?$/;
