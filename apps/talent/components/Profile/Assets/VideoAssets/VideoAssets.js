'use client';
import React, { memo, useState } from 'react';
import styles from './VideoAssets.module.scss';
import { ModalEditVideo, VideoPlaceholder } from '@components';
import Video from '../Video/Video';
import { usePremium } from '@contexts/PremiumContext';
import { Amp } from '@services/amp';

const VideoAssets = ({
  profileId,
  videos,
  isPaidOrDelayed,
  demoReel,
  slate,
  ugcDemoReel,
}) => {
  const [selectedVideo, setSelectedVideo] = useState(null);

  const { openPremiumModal, setPremiumTrackingName } = usePremium();

  const editVideo = (id) => {
    Amp.track(Amp.events.elementClicked, {
      name: `edit additional video`,
      scope: Amp.element.scope.profile,
      section: Amp.element.section.video,
      type: Amp.element.type.button,
    });
    setSelectedVideo(videos.find((video) => video.id === id));
  };

  const editSlate = () => {
    Amp.track(Amp.events.elementClicked, {
      name: `edit slate video`,
      scope: Amp.element.scope.profile,
      section: Amp.element.section.video,
      type: Amp.element.type.button,
    });
    setSelectedVideo(slate);
  };

  const editDemoReel = () => {
    Amp.track(Amp.events.elementClicked, {
      name: `edit demo_reel video`,
      scope: Amp.element.scope.profile,
      section: Amp.element.section.video,
      type: Amp.element.type.button,
    });
    setSelectedVideo(demoReel);
  };

  const editUgcDemoReel = () => {
    Amp.track(Amp.events.elementClicked, {
      name: `edit ugc_demo_reel video`,
      scope: Amp.element.scope.profile,
      section: Amp.element.section.video,
      type: Amp.element.type.button,
    });
    setSelectedVideo(ugcDemoReel);
  };

  const addVideo = (type = 'default') => {
    Amp.track(Amp.events.elementClicked, {
      name: `upload ${type === 'default' ? 'additional' : type} video`,
      scope: Amp.element.scope.profile,
      section: Amp.element.section.video,
      type: Amp.element.type.button,
    });

    if (isPaidOrDelayed) {
      setSelectedVideo({ type });
    } else {
      setPremiumTrackingName(
        `Unlock upload ${type === 'default' ? 'additional' : type} video popup`,
      );
      openPremiumModal();
    }
  };

  const onClose = () => {
    setSelectedVideo(null);
  };

  return (
    <div className={styles['video-assets']}>
      {demoReel ? (
        <Video
          type={demoReel.type}
          id={demoReel.id}
          link_id={demoReel.link_id}
          onEdit={editDemoReel}
          declineReason={demoReel.decline_reason}
        />
      ) : (
        <VideoPlaceholder
          onClick={() => addVideo('demo_reel')}
          type="demo-reel"
          text="Upload your demo reel"
          tooltipText="A demo reel is a short video compilation of your best work. It should be concise, exciting, and focused on YOU. Upload your demo reel here and dazzle casting directors."
          tooltipTitle="Demo reel"
          showTooltip
        />
      )}
      {slate ? (
        <Video
          type={slate.type}
          id={slate.id}
          link_id={slate.link_id}
          onEdit={editSlate}
          declineReason={slate.decline_reason}
        />
      ) : (
        <VideoPlaceholder
          onClick={() => addVideo('slate')}
          type="slate"
          text="Upload your slate"
          tooltipText="Upload your slate (only one allowed). A slate is an introduction when you audition for a project -
 before going into a scene, slate your name for the camera, e.g., 'Hi, my name is Christy Bowan'."
          tooltipTitle="Slate"
          showTooltip
        />
      )}
      {ugcDemoReel ? (
        <Video
          type={ugcDemoReel.type}
          id={ugcDemoReel.id}
          link_id={ugcDemoReel.link_id}
          onEdit={editUgcDemoReel}
          declineReason={ugcDemoReel.decline_reason}
        />
      ) : (
        <VideoPlaceholder
          onClick={() => addVideo('ugc_demo_reel')}
          type="ugc-demo-reel"
          text="Upload your UGC demo reel"
          tooltipText="UGC reel is a short video showcasing your skills while promoting a product, service, sharing a testimonial, or providing a tutorial. 
          Make sure to have great lighting, sound, and background. Upload your UGC reel and show brands your authentic touch while promoting their products!"
          tooltipTitle="UGC demo reel"
          showTooltip
        />
      )}
      <>
        {videos.map(({ id, link_id, title, type, decline_reason }, index) => (
          <Video
            key={index}
            type={type}
            id={id}
            link_id={link_id}
            title={title}
            onEdit={() => editVideo(id)}
            declineReason={decline_reason}
          />
        ))}
        <VideoPlaceholder onClick={() => addVideo()} />
      </>
      {!!selectedVideo && (
        <ModalEditVideo
          profileId={profileId}
          onClose={onClose}
          video={selectedVideo}
        />
      )}
    </div>
  );
};

export default memo(VideoAssets);
