import styles from './ProfileDesktop.module.scss';
import React, { memo, useEffect, useState } from 'react';
import {
  Accordion,
  Audio,
  Breadcrumbs,
  Button,
  CastingCall,
  Loading,
  Modal,
  ModalContactTalent,
  ProfileCreditCard,
  ProfileOverview,
  YoutubePlayer,
  ZoomImage,
} from '../index';
import Image from 'next/image';
import { PageLayout } from '../Layouts';
import { useProfileContext } from '../../contexts/ProfileContext';
import cn from 'classnames';
import Api from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';
import Carousel from '../Carousel/Carousel';
import { sortProfilePhotos } from '../../utils/imageHelpers';
import { TYPE } from '../../constants/castingCalls';

const ProfileDesktop = ({ sideMenuItems, isMobileFromUserAgent }) => {
  const {
    id,
    name,
    images,
    additionalImages,
    rating,
    videos,
    credits,
    closeUpImage,
    sideViewImage,
    fullHeightImage,
    gender,
    castingCalls,
    isAgent,
    clientId,
    castingCallTotal,
    audios,
    directorImage,
  } = useProfileContext();

  const [visibleImages, setVisibleImages] = useState([]);
  const [sliderImages, setSliderImages] = useState([]);
  const [visibleVideos, setVisibleVideos] = useState([]);
  const [visibleCastingCalls, setVisibleCastingCalls] = useState(castingCalls);
  const [visibleCastingCallsPage, setVisibleCastingCallsPage] = useState(1);
  const [visibleCastingCallsLoading, setVisibleCastingCallsLoading] =
    useState(false);
  const [visibleAudios, setVisibleAudios] = useState([]);
  const [showGallery, setShowGallery] = useState(false);
  const [clickedImageIndex, setClickedImageIndex] = useState(null);
  const [showContactTalentPopup, setShowContactTalentPopup] = useState(false);
  const { userType } = useAuth();

  useEffect(() => {
    loadImages();
    sortSliderImages();
  }, [images]);

  useEffect(() => {
    loadVideos();
  }, [videos]);

  useEffect(() => {
    loadAudios();
  }, [audios]);

  const loadImages = () => {
    if (additionalImages) {
      const startIndex = visibleImages.length;

      setVisibleImages([
        ...visibleImages,
        ...additionalImages.slice(startIndex, startIndex + 8),
      ]);
    }
  };

  const sortSliderImages = () => {
    let sortedImages = [...images];

    if (sortedImages?.length) {
      sortedImages = sortProfilePhotos(sortedImages);
    }
    setSliderImages(sortedImages);
  };

  const loadVideos = () => {
    if (videos) {
      const startIndex = visibleVideos.length;

      setVisibleVideos([
        ...visibleVideos,
        ...videos.slice(startIndex, startIndex + 4),
      ]);
    }
  };

  const loadAudios = () => {
    if (audios) {
      const startIndex = visibleAudios.length;

      setVisibleAudios([
        ...visibleAudios,
        ...audios.slice(startIndex, startIndex + 4),
      ]);
    }
  };

  const toggleShowGallery = () => {
    setShowGallery(!showGallery);
  };

  const onImageClick = (imageId) => {
    if (imageId) {
      const index = sliderImages.findIndex(({ id }) => id === imageId);

      setClickedImageIndex(index);
    }

    toggleShowGallery();
  };

  const loadMoreCastingCalls = async () => {
    setVisibleCastingCallsLoading(true);

    const profileCastingCalls = await Api.clientAPIRoute(
      `/profiles/casting-calls/${clientId}?limit=3&page=${
        visibleCastingCallsPage + 1
      }`,
    );

    setVisibleCastingCallsLoading(false);
    setVisibleCastingCallsPage(visibleCastingCallsPage + 1);
    setVisibleCastingCalls([
      ...visibleCastingCalls,
      ...profileCastingCalls.data?.calls,
    ]);
  };

  const renderTalentProfile = () => {
    return (
      <>
        <div className={styles['profile-container']}>
          <div id="main-photos" className={styles.profile}>
            <div className={styles['profile-main-images']}>
              {closeUpImage && (
                <ZoomImage
                  onClick={() => onImageClick(closeUpImage.id)}
                  src={closeUpImage.proxy_url}
                />
              )}

              {!closeUpImage && (
                <div className={styles['placeholder-image-container']}>
                  <div
                    className={cn(styles['placeholder-image'], {
                      [styles['placeholder-image-female-head']]:
                        gender.toLowerCase() === 'female',
                      [styles['placeholder-image-male-head']]:
                        gender.toLowerCase() === 'male',
                    })}
                  ></div>
                </div>
              )}

              <div className={styles['profile-additional-images']}>
                {sideViewImage && (
                  <ZoomImage
                    onClick={() => onImageClick(sideViewImage.id)}
                    src={sideViewImage.proxy_url}
                  />
                )}

                {!sideViewImage && (
                  <div className={styles['placeholder-image-container']}>
                    <div
                      className={cn(styles['placeholder-image'], {
                        [styles['placeholder-image-female-side']]:
                          gender.toLowerCase() === 'female',
                        [styles['placeholder-image-male-side']]:
                          gender.toLowerCase() === 'male',
                      })}
                    ></div>
                  </div>
                )}

                {fullHeightImage && (
                  <ZoomImage
                    onClick={() => onImageClick(fullHeightImage.id)}
                    src={fullHeightImage.proxy_url}
                  />
                )}

                {!fullHeightImage && (
                  <div className={styles['placeholder-image-container']}>
                    <div
                      className={cn(styles['placeholder-image'], {
                        [styles['placeholder-image-female-full']]:
                          gender.toLowerCase() === 'female',
                        [styles['placeholder-image-male-full']]:
                          gender.toLowerCase() === 'male',
                      })}
                    ></div>
                  </div>
                )}
              </div>
            </div>
            <div className={styles['profile-overview']}>
              <div id="name" className={styles['profile-overview-header']}>
                <h1 className={styles['profile-name']}>{name}</h1>
              </div>
              <div className={styles['profile-overview-details']}>
                <div className={styles['profile-rating']}>
                  <Image
                    className={styles['star']}
                    src={'/assets/icons/icon-star-2.svg'}
                    width={17}
                    height={17}
                    alt="star icon"
                    priority
                  />
                  <span className={styles['profile-rating-count']}>
                    {rating}
                  </span>
                </div>

                <div className={styles['profile-id']}>
                  <span>Profile ID: </span>
                  <span>{id}</span>
                </div>
              </div>
              <hr className={styles['separator']} />
              <ProfileOverview
                contactTalentCallback={(event) => {
                  event.preventDefault();
                  event.stopPropagation();
                  setShowContactTalentPopup(true);
                }}
                fontSize={'16px'}
              />
            </div>
          </div>

          {(visibleVideos?.length > 0 || visibleAudios?.length > 0) && (
            <>
              <hr className={styles['separator']} />
              <h2 id="videos" className={styles['profile-title']}>
                Assets
              </h2>
            </>
          )}

          {visibleVideos.length > 0 && (
            <div className={styles['profile-content-section']}>
              <h2 id="videos" className={styles['profile-subtitle']}>
                Videos:
              </h2>
              <div className={styles['profile-video-container']}>
                {visibleVideos.map(({ id, link_id, title, type }) => (
                  <div key={id} className={styles['video-card']}>
                    <div className={styles['video-container']}>
                      <YoutubePlayer
                        videoId={link_id}
                        videoClassName={styles.video}
                      />
                    </div>

                    {type === 'default' && (
                      <p className={styles['profile-video-title']}>{title}</p>
                    )}

                    {type === 'demo_reel' && (
                      <p className={styles['profile-video-title']}>
                        <strong>Demo Reel</strong>
                      </p>
                    )}

                    {type === 'slate' && (
                      <p className={styles['profile-video-title']}>
                        <strong>Slate</strong>
                      </p>
                    )}

                    {type === 'ugc_demo_reel' && (
                      <p className={styles['profile-video-title']}>
                        <strong>UGC demo reel</strong>
                      </p>
                    )}
                  </div>
                ))}
              </div>

              {visibleVideos.length !== videos.length && (
                <div className={styles['profile-btn-container']}>
                  <Button
                    onClick={loadVideos}
                    label={'Load more'}
                    kind={'secondary'}
                    minWidth={'320px'}
                  />
                </div>
              )}
            </div>
          )}

          {visibleAudios.length > 0 && (
            <div className={styles['profile-content-section']}>
              <h2 id="audios" className={styles['profile-subtitle']}>
                Audio Files:
              </h2>
              <div className={styles['profile-audio-container']}>
                {visibleAudios.map(({ id, title, full_path }) => (
                  <Audio
                    key={id}
                    title={title}
                    url={full_path}
                    isMobileFromUserAgent={isMobileFromUserAgent}
                  />
                ))}
              </div>

              {visibleAudios.length !== audios.length && (
                <div className={styles['profile-btn-container']}>
                  <Button
                    onClick={loadAudios}
                    label={'Load more'}
                    kind={'secondary'}
                    minWidth={'320px'}
                  />
                </div>
              )}
            </div>
          )}

          {visibleImages.length > 0 && (
            <div className={styles['profile-content-section']}>
              <hr className={styles['separator']} />
              <h2 id="photos" className={styles['profile-title']}>
                Photos
              </h2>
              <div className={styles['profile-photo-container']}>
                {visibleImages.map(({ id, proxy_url }) => (
                  <ZoomImage
                    key={id}
                    onClick={() => onImageClick(id)}
                    src={proxy_url}
                  />
                ))}
              </div>
              {visibleImages.length !== additionalImages.length && (
                <div className={styles['profile-btn-container']}>
                  <Button
                    onClick={loadImages}
                    label={'Load more'}
                    kind={'secondary'}
                    minWidth={'320px'}
                  />
                </div>
              )}
            </div>
          )}

          {credits.length > 0 && (
            <>
              <hr className={styles['separator']} />
              <h2 id="credits" className={styles['profile-title']}>
                Credits
              </h2>
              {credits.map(
                ({ id, title, description, month, year, company }) => (
                  <ProfileCreditCard
                    key={id}
                    title={title}
                    description={description}
                    month={month}
                    year={year}
                    company={company.title}
                  />
                ),
              )}
            </>
          )}
        </div>
      </>
    );
  };

  const renderAgentProfile = () => {
    return (
      <div className={styles['profile-container']}>
        <div id="main-photos" className={styles.profile}>
          <div className={styles['profile-main-images']}>
            {directorImage ? (
              <ZoomImage
                onClick={() => onImageClick(directorImage.id)}
                src={directorImage.proxy_url}
              />
            ) : (
              <div className={styles['placeholder-image-container']}>
                <div
                  className={cn(
                    styles['placeholder-image'],
                    styles['placeholder-image-casting-director'],
                  )}
                ></div>
              </div>
            )}
          </div>
          <div className={styles['profile-overview']}>
            <div id="name" className={styles['profile-overview-header']}>
              <h1 className={styles['profile-name']}>{name}</h1>
            </div>
            <div className={styles['profile-overview-details']}>
              <div className={styles['profile-id']}>
                <span>Profile ID: </span>
                <span>{id}</span>
              </div>
            </div>
            <hr className={styles['separator']} />
            <ProfileOverview fontSize={'16px'} />
          </div>
        </div>

        {visibleCastingCalls?.length > 0 && (
          <>
            <hr className={styles['separator']} />
            <h2 id="casting-calls" className={styles['profile-title']}>
              Casting Calls
            </h2>
            <div className={styles['castingcall-list']}>
              {visibleCastingCalls.map((item, index) => (
                <CastingCall
                  key={index}
                  type={item.type}
                  mainCategory={item.category}
                  additionalCategories={item.additional_categories}
                  description={item.description.slice(0, 300)}
                  location={item.location}
                  title={item.title}
                  rolesCount={item.roles.length}
                  expires={item.expiration_date || item.expiration}
                  isOnline={item.online_audition}
                  isEasyToApply={item.type === TYPE.Web}
                  hot={item.hot}
                  id={item.id}
                  paymentAmount={item.payment_amount}
                  paymentPeriod={item.payment_period}
                  paymentCurrency={item.payment_currency}
                />
              ))}
            </div>

            {visibleCastingCalls.length < castingCallTotal &&
              !visibleCastingCallsLoading && (
                <div className={styles['profile-btn-container']}>
                  <Button
                    onClick={loadMoreCastingCalls}
                    label={'Load more'}
                    kind={'secondary'}
                    minWidth={'320px'}
                  />
                </div>
              )}

            {visibleCastingCallsLoading && (
              <Loading minHeight="40px" padding="0" />
            )}
          </>
        )}

        {visibleImages.length > 0 && (
          <div className={styles['profile-content-section']}>
            <hr className={styles['separator']} />
            <h2 id="photos" className={styles['profile-title']}>
              Photos
            </h2>
            <div className={styles['profile-photo-container']}>
              {visibleImages.map(({ id, proxy_url }) => (
                <ZoomImage
                  key={id}
                  onClick={() => onImageClick(id)}
                  src={proxy_url}
                />
              ))}
            </div>

            {visibleImages.length !== additionalImages.length && (
              <div className={styles['profile-btn-container']}>
                <Button
                  onClick={loadImages}
                  label={'Load more'}
                  kind={'secondary'}
                  minWidth={'320px'}
                />
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  return (
    <PageLayout>
      {!isAgent && (
        <div className={styles['breadcrumbs']}>
          <Breadcrumbs
            crumbs={[
              { text: 'Talent Database', href: '/talent' },
              { text: name },
            ]}
          />
        </div>
      )}
      {isAgent && (
        <div className={styles['breadcrumbs']}>
          <Breadcrumbs crumbs={[{ text: name }]} />
        </div>
      )}
      <section className={styles['profile-section']}>
        {isAgent ? renderAgentProfile() : renderTalentProfile()}
        <div className={styles['profile-sidemenu']}>
          <Accordion
            accordionItems={sideMenuItems}
            buttonLabel={'Contact Talent'}
            onButtonClick={() => {
              setShowContactTalentPopup(true);
            }}
            buttonMinWidth={'220px'}
            showButton={!isAgent && userType !== 'talent'}
          />
        </div>
        {showGallery && (
          <Modal
            backdropClose
            onClose={toggleShowGallery}
            showCloseButton={false}
            showDefaultLayout={false}
            classNameContainer={styles['gallery-modal']}
            classNameContent={styles['gallery-modal-content']}
            containerClose
          >
            <Carousel
              enableArrowNavigation
              startIndex={clickedImageIndex}
              className="carousel-profile-gallery"
              draggable={false}
              loop
            >
              {sliderImages.map(({ proxy_url }, index) => (
                <img
                  key={index}
                  src={proxy_url}
                  style={{ width: '100%' }}
                  alt={index}
                />
              ))}
            </Carousel>
          </Modal>
        )}
      </section>
      {showContactTalentPopup && (
        <ModalContactTalent
          onClose={() => {
            setShowContactTalentPopup(false);
          }}
        />
      )}
    </PageLayout>
  );
};

export default memo(ProfileDesktop);
