'use client';
import React, { memo, useEffect, useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import styles from './MobileSidebarMenu.module.scss';
import { companyLinks, exploreLinks } from '@constants/menuLinks/sidebar-links';
import cn from 'classnames';
import {
  Button,
  NumberBadge,
  ProfileCloseupImage,
  SaleMobileHeader,
} from '@components';
import { useRouter } from 'next/navigation';
import { Amp } from '@services/amp';
import { useAlert } from '@contexts/AlertContext';

const MobileSidebarMenu = ({
  onSideBarClose,
  open,
  isAuthenticated,
  profile,
  isPaidOrDelayed,
  canUpgradeExistingSubscription,
  authLogout,
  saleExpirationTime,
  stopSale,
  showSale,
}) => {
  const [showSideBar, setShowSideBar] = useState(false);

  const router = useRouter();
  const { newReviewCount } = useAlert();

  useEffect(() => {
    setShowSideBar(open);
  }, [open]);

  const logout = () => {
    onSideBarClose();
    authLogout();
  };

  const onNavigateToSettings = () => {
    onSideBarClose();
    router.push('/settings');
  };

  const onNavigateToUpgrade = () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'subscribe',
      scope: Amp.element.scope.burger,
      section: Amp.element.section.navigation,
      type: Amp.element.type.button,
      upgrade_to_12: !!canUpgradeExistingSubscription,
    });
    onSideBarClose();
    router.push(`/upgrade${canUpgradeExistingSubscription ? '-12' : ''}`);
  };

  return (
    <div
      className={cn(
        styles['sidebar-container'],
        showSideBar && styles['sidebar-open'],
      )}
    >
      <button className={styles['close-btn']} onClick={onSideBarClose}>
        <Image
          width={16}
          height={16}
          src={'/assets/icons/icon-close-1.svg'}
          alt="icon-close.svg"
        />
      </button>
      <div className={styles['sidebar-header']}>
        <div className={styles['rating-container']}>
          <a
            className={styles.button}
            href={`${profile.profileUrl}/info`}
            onClick={onSideBarClose}
          >
            <ProfileCloseupImage
              className={styles['profile-img']}
              gender={profile?.gender}
              src={profile?.titlePhotoUrl}
              alt=""
              image="closeup"
            />
          </a>
          <div className={styles.rating}>
            <Image
              className={styles['star']}
              src={'/assets/icons/icon-star-filled.svg'}
              width={17}
              height={17}
              alt="star"
            />
            <span>{profile.rating.toLocaleString('en-EN')}</span>
          </div>
        </div>
        <a
          href={`${profile.profileUrl}/info`}
          onClick={onSideBarClose}
          className={styles['profile-name']}
        >
          <span>{profile.fullName}</span>
        </a>
        {showSale && (
          <SaleMobileHeader
            saleExpirationTime={saleExpirationTime}
            stopSale={stopSale}
            className={styles['sale-header']}
            loweredOpacity
            loweredFontWeight
            onNavigate={onSideBarClose}
          />
        )}
        {!showSale && (!isPaidOrDelayed || canUpgradeExistingSubscription) && (
          <div className={styles['subscribe-button-container']}>
            <Button
              onClick={onNavigateToUpgrade}
              label={
                canUpgradeExistingSubscription
                  ? 'Upgrade your plan'
                  : 'Subscribe'
              }
              color="green-gradient"
              minWidth="220px"
              shadow={false}
            />
          </div>
        )}
      </div>
      <div className={styles.sidebar}>
        <div className={styles.container}>
          <div className={styles.title}>Explore</div>
          <div className={styles['menu-links']}>
            {exploreLinks.map(({ title, id, routerLink }) => (
              <Link
                className={styles['menu-link']}
                key={id}
                href={routerLink}
                onClick={onSideBarClose}
              >
                {title}
                {id === 'castingcalls' && <NumberBadge number={99} />}
                {id === 'reviews' && newReviewCount > 0 && (
                  <NumberBadge number={newReviewCount} />
                )}
              </Link>
            ))}
          </div>
        </div>
        <div className={styles.container}>
          <div className={styles.title}>Company</div>
          <div className={styles['menu-links']}>
            {companyLinks.map(({ title, id, routerLink }) => (
              <Link
                className={styles['menu-link']}
                key={id}
                href={routerLink}
                onClick={onSideBarClose}
              >
                {title}
              </Link>
            ))}
          </div>
        </div>
        {isAuthenticated && (
          <div className={styles['action-buttons']}>
            <Button
              className={cn(
                styles['action-button'],
                styles['action-button-logout'],
              )}
              color="grey"
              label="Log Out"
              minWidth="130px"
              shadow={false}
              onClick={logout}
            />
            <Button
              className={cn(
                styles['action-button'],
                styles['action-button-settings'],
              )}
              color="grey"
              label="Settings"
              minWidth="130px"
              shadow={false}
              onClick={onNavigateToSettings}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default memo(MobileSidebarMenu);
