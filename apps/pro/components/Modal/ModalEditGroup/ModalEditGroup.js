'use client';
import React, { memo, useState } from 'react';
import { useNotifications } from '@contexts/NotificationContext';
import Api from '@services/api';
import { Input, Loading, Modal } from '@components';
import styles from './ModalEditGroup.module.scss';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { ErrorMessage } from '@constants/form';

const ModalEditGroup = ({
  onClose,
  group,
  toggleShowModalRemoveGroup = () => {},
  onRenameGroup,
}) => {
  const [loading, setLoading] = useState(false);
  const { setNotification } = useNotifications();

  const formik = useFormik({
    initialValues: {
      title: group.title || '',
    },
    onSubmit: async (values) => {
      setLoading(true);

      const body = new FormData();

      body.append('title', values.title);

      const response = await Api.clientside(`/contact-groups/${group.id}`, {
        method: 'PATCH',
        body,
      });

      if (response.status !== 'ok') {
        setNotification({
          type: 'error',
          timeout: '5000',
          message: response.message || ErrorMessage.Unexpected,
        });
      } else {
        await onRenameGroup(group.id, values.title);

        setNotification({
          type: 'success',
          timeout: '5000',
          message: 'Group saved',
        });
      }

      onClose();
      setLoading(false);
    },
    validationSchema: Yup.object({
      title: Yup.string().required(ErrorMessage.FieldRequired),
    }),
  });

  const onRemove = () => {
    toggleShowModalRemoveGroup();
    onClose();
  };

  return (
    <Modal
      backdropClose={!loading}
      onClose={onClose}
      classNameContainer={styles['modal-container']}
      showDefaultLayout={false}
      floatCloseButton
      hideMobileCloseButton
    >
      {loading ? (
        <div className={styles['loading-container']}>
          <div>Saving group...</div>
          <Loading minHeight="40px" padding="0" />
        </div>
      ) : (
        <>
          <div className={styles.container}>
            <span className={styles['remove-button']} onClick={onRemove}>
              Remove
            </span>
            <span className={styles.title}>Edit group</span>
            <Input
              name="title"
              placeholder="Group title"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.title}
              isTouched={formik.touched.title}
              error={formik.errors.title}
            />
          </div>
          <div className={styles.actions}>
            <button onClick={onClose} className={styles.action}>
              Cancel
            </button>
            <button onClick={formik.submitForm} className={styles.action}>
              Confirm
            </button>
          </div>
        </>
      )}
    </Modal>
  );
};

export default memo(ModalEditGroup);
