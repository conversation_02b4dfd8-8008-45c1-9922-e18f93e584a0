import FilterService from '@entertech/filter-service';
import Api from '../../../services/api';
import { getHeaders } from '../../../utils/headerHelper';

export default async function handler(req, res) {
  const filter = {
    data: await Api.serverAPIRoute(`/talent/filters`, null, getHeaders(req)),
    seo: await Api.serverAPIRoute(`/seo/talent`, null, getHeaders(req)),
    cities: await Api.serverAPIRoute(
      `/location/search/popular-cities`,
      null,
      getHeaders(req),
    ),
  };

  const queryParameters = !Object.keys(req.query.filter || {}).length
    ? {}
    : JSON.parse(req.query.filter);

  /**
   * Dirty hack, sorry.
   */
  if (
    queryParameters.city &&
    (!queryParameters.longitude ||
      !queryParameters.latitude ||
      !queryParameters.location)
  ) {
    const city = await Api.serverAPIRoute(
      `/location/resource/city?slug=${queryParameters.city}`,
      null,
      getHeaders(req),
    );

    queryParameters.longitude = `${city.longitude}`;
    queryParameters.latitude = `${city.latitude}`;
    queryParameters.location = `${city.title}, ${city.links.state.code}`;
  }

  filter.data = FilterService.generateFilterFromUrl(
    filter.data,
    queryParameters,
    filter.seo,
  );

  const filterParameters = FilterService.generateBCUrlFromFilter(
    filter.data,
    filter.seo,
  );

  const list = {
    list: await Api.serverGateway(
      `/profiles/search${filterParameters}&rel=talent&limit=35&tf=1`,
      req.headers,
    ),
    filter: filter,
  };

  const data = {
    data: list,
    filter: filter,
  };

  res.status(200).json(data);
}
