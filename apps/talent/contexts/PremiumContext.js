import React, { createContext, useContext, useState } from 'react';

const PremiumContext = createContext({});

export const PremiumProvider = ({ children }) => {
  const [showPremiumModal, setShowPremiumModal] = useState(false);
  const [premiumTrackingName, setPremiumTrackingName] = useState(null);

  const closePremiumModal = () => {
    setShowPremiumModal(false);
  };

  const openPremiumModal = () => {
    setShowPremiumModal(true);
  };

  return (
    <PremiumContext.Provider
      value={{
        showPremiumModal,
        setShowPremiumModal,
        premiumTrackingName,
        setPremiumTrackingName,
        closePremiumModal,
        openPremiumModal,
      }}
    >
      {children}
    </PremiumContext.Provider>
  );
};

export const usePremium = () => useContext(PremiumContext);
