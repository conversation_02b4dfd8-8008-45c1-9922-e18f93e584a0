/**
 * Custom image loader for Next.js that leverages assetPrefix for dynamic images
 * and handles SVG files differently by serving them directly from CDN /assets/ directory
 * @param {Object} params - The parameters for the image loader
 * @param {string} params.src - The source path of the image
 * @param {number} params.width - The requested width of the image
 * @param {number} params.quality - The requested quality of the image
 * @returns {string} - The complete URL for the image
 */
module.exports = function imageLoader({ src, width, quality }) {
  const assetPrefix = process.env.NEXT_PUBLIC_CDN_URL;
  const version = process.env.NEXT_PUBLIC_VERSION;
  const q = quality || 75;

  // Check if the image is an SVG under /assets/
  if (src.startsWith('/assets/') && src.toLowerCase().endsWith('.svg')) {
    return `${assetPrefix}/${version}${src}`;
  }

  // static image imports
  if (src.startsWith(`${assetPrefix}/_next/static/media`)) {
    const url = src.slice(assetPrefix.length);

    return `${assetPrefix}/_next/image?url=${encodeURIComponent(url)}&w=${width}&q=${q}&version=${version}`;
  }

  // Check if src is an external URL (starts with http:// or https://)
  if (src.startsWith('http://') || src.startsWith('https://')) {
    return src;
  }

  // For internal images, use assetPrefix with Next.js image optimization path
  return `${assetPrefix}/_next/image?url=${encodeURIComponent(src)}&w=${width}&q=${q}&version=${version}`;
};
