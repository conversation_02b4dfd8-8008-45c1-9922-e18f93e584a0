'use client';
import React, { memo, useEffect, useState } from 'react';
import { Loading, Modal } from '@components';
import styles from './ModalRoleDescription.module.scss';
import Api from '@services/api';
import { useNotifications } from '@contexts/NotificationContext';
import { ErrorMessage } from '@constants/form';

const ModalRoleDescription = ({
  onClose = () => {},
  roleId,
  castingCallId,
}) => {
  const [loading, setLoading] = useState(true);
  const [role, setRole] = useState({});
  const { setNotification } = useNotifications();

  useEffect(() => {
    const getRole = async () => {
      const response = await Api.clientside(`/calls/get?id=${castingCallId}`);

      if (response.status !== 'ok') {
        setNotification({
          type: 'error',
          message: response.message || ErrorMessage.Unexpected,
          timeout: '5000',
        });
        onClose();
      } else {
        setRole(response.data.roles.find((it) => it.id === roleId) || {});
      }

      setLoading(false);
    };

    getRole().catch();
  }, []);

  return (
    <Modal
      onClose={onClose}
      backdropClose
      showDefaultLayout={false}
      classNameContainer={styles['modal-container']}
      floatCloseButton
    >
      {loading ? (
        <div className={styles['loading-container']}>
          <Loading minHeight="40px" padding="0" />
        </div>
      ) : (
        <div className={styles.container}>
          <span className={styles.title}>{role.title}</span>
          <div className={styles.details}>
            <span>{role.gender?.name}</span>
            <span className={styles['dot-divider']}>
              {role.age_from} - {role.age_to} y.o.
            </span>
          </div>
          <div className={styles.details}>
            {role.ethnicities.map((it, i) => (
              <span className={i === 0 ? '' : styles['dot-divider']} key={i}>
                {it.name}
              </span>
            ))}
          </div>
          <p>{role.description}</p>
        </div>
      )}
    </Modal>
  );
};

export default memo(ModalRoleDescription);
