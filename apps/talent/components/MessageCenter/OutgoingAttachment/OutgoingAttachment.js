'use client';
import styles from './OutgoingAttachment.module.scss';
import IconVideo from '../../../public/assets/icons/icon-video.svg';
import IconAudio from '../../../public/assets/icons/icon-audio.svg';
import { memo } from 'react';

export const OutgoingAttachment = ({
  attachment,
  openImageAttachment,
  displaySize = 48,
}) => {
  const type =
    attachment.content_type.split('/')[1] === 'pdf'
      ? attachment.content_type.split('/')[1]
      : attachment.content_type.split('/')[0];

  switch (type) {
    case 'image':
      return (
        <button
          className={styles['image-opener']}
          onClick={(e) => {
            e.preventDefault();
            openImageAttachment(attachment.id);
          }}
        >
          <img
            className={styles.image}
            src={attachment.path}
            alt={attachment.name}
            style={{
              maxWidth: `${displaySize}px`,
              maxHeight: `${displaySize}px`,
            }}
          />
        </button>
      );
    case 'pdf':
      return (
        <a
          href={attachment.path}
          target="_blank"
          rel="noreferrer"
          className={styles['attachment-document']}
        >
          PDF
        </a>
      );
    case 'video':
      return (
        <a
          href={attachment.path}
          target="_blank"
          rel="noreferrer"
          className={styles['attachment-document']}
        >
          <IconVideo />
        </a>
      );
    case 'audio':
      return (
        <a
          href={attachment.path}
          target="_blank"
          rel="noreferrer"
          className={styles['attachment-document']}
        >
          <IconAudio />
        </a>
      );
    default:
      return (
        <a
          href={attachment.path}
          target="_blank"
          rel="noreferrer"
          className={styles['attachment-document']}
        >
          DOC
        </a>
      );
  }
};

export default memo(OutgoingAttachment);
