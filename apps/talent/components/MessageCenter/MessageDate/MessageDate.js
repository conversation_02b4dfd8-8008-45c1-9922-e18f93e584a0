'use client';
import React, { memo } from 'react';
import dayjs from 'dayjs';
import styles from './MessageDate.module.scss';

const MessageDate = ({ current, previous }) => {
  let generated = dayjs(current).format('MM/DD/YYYY');

  if (
    previous &&
    dayjs(current).format('MM/DD/YYYY') === dayjs(previous).format('MM/DD/YYYY')
  ) {
    return <></>;
  }

  if (dayjs(current).isToday()) generated = 'TODAY';
  if (dayjs(current).isYesterday()) generated = 'YESTERDAY';

  return <div className={styles.date}>{generated}</div>;
};

export default memo(MessageDate);
