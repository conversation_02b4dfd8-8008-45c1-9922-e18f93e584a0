@use '@styles/variables' as *;

.search-input-container {
  position: relative;
}

.search-input {
  border: 0;
  border-radius: 3px;
  height: 35px;
  font-size: 16px;
  padding: 0 $space-40 0 $space-10;
  width: 100%;
  background-color: $white;
  color: $black;
  outline: none;
  font-weight: 300;
}

.search-input-label {
  position: relative;
}

.search-input-clear-button {
  position: absolute;
  top: 0;
  right: 0;
  border: none;
  cursor: pointer;
  background-color: transparent;
  padding: 0 $space-10;
  height: 100%;
}

.clear-icon {
  filter: invert(50%) sepia(94%) saturate(2261%) hue-rotate(325deg)
    brightness(98%) contrast(97%);
}
