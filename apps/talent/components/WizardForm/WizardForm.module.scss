@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.container {
  --vertical-space: 2rem;
  --horizontal-padding: 1.25rem;

  max-width: 850px;
  width: 100%;
  background-color: $white;
  border-bottom-left-radius: inherit;
  border-bottom-right-radius: inherit;
  padding: var(--vertical-space) var(--horizontal-padding);
  display: flex;
  flex-direction: column;
  gap: var(--vertical-space);

  @include desktop {
    --vertical-space: 3rem;
    --horizontal-padding: 4rem;
  }
}

.heading {
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.title {
  margin: 0;
}

.subtitle {
  font-size: 1rem;
  font-weight: 500;
}

.fields {
  display: grid;
  grid-template-columns: 1fr;
  grid-auto-flow: dense;
  gap: 3rem;

  @include desktop {
    display: grid;
    grid-template-columns: 1fr 1fr;
    align-items: start;
    gap: 2rem 4rem;
  }
}

.gender {
  display: contents;

  @include desktop {
    display: initial;
    position: relative;
  }
}

.alert {
  @include desktop {
    inset-inline-start: 195px;
    inset-block-start: -10px;
  }
}

.ethnicity {
  @include desktop {
    grid-column-start: 2;
    grid-row: span 3;
  }
}

.actions {
  grid-column: 1 / -1;
  margin: 1rem auto;
}

button.button {
  padding: 0;
  height: 40px;
}
