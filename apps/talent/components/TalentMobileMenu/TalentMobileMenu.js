'use client';
import { memo } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import styles from './TalentMobileMenu.module.scss';
import { mobileTalentLinks } from '@constants/menuLinks/menu-links';
import { usePathname } from 'next/navigation';
import { useAlert } from '@contexts/AlertContext';
import cn from 'classnames';
import NumberBadge from '../NumberBadge/NumberBadge';

const TalentMobileMenu = ({ onSideBarOpen, profile }) => {
  const profileLink = `${profile?.profileUrl}/info`;
  const path = usePathname();
  const { newConversationCount } = useAlert();

  return (
    <div className={styles['mobile-menu-container']}>
      <ul className={styles['mobile-menu']}>
        {mobileTalentLinks.map(
          ({ id, icon, title, routerLink, isMainAction }) => (
            <li
              key={id}
              className={cn(styles['mobile-menu-item'], {
                [styles['main-mobile-menu-item']]: isMainAction,
              })}
            >
              <Link
                prefetch={false}
                href={id === 'profile' ? profileLink : routerLink}
                className={cn(styles['mobile-menu-link'], {
                  [styles.active]: path.includes(routerLink || profileLink),
                })}
              >
                {isMainAction ? (
                  <div className={styles['mobile-menu-icon-container']}>
                    <Image
                      className={styles['mobile-menu-icon']}
                      src={`/assets/icons/icon-${icon}.svg`}
                      alt="icon"
                      width={20}
                      height={20}
                    />
                  </div>
                ) : (
                  <div className={styles['mobile-menu-icon-container']}>
                    <Image
                      className={styles['mobile-menu-icon']}
                      src={`/assets/icons/icon-${icon}.svg`}
                      alt="icon"
                      width={40}
                      height={20}
                    />
                    {id === 'inbox' && newConversationCount > 0 && (
                      <NumberBadge
                        className={styles.badge}
                        number={newConversationCount}
                      />
                    )}
                  </div>
                )}
                <span className={styles['mobile-menu-txt']}>{title}</span>
              </Link>
            </li>
          ),
        )}
        <li className={styles['mobile-menu-item']}>
          <div className={styles['mobile-menu-link']} onClick={onSideBarOpen}>
            <Image
              className={styles['mobile-menu-icon']}
              src={'/assets/icons/icon-menu-burger.svg'}
              alt="icon"
              width={40}
              height={20}
            />
            <span className={styles['mobile-menu-txt']}>menu</span>
          </div>
        </li>
      </ul>
    </div>
  );
};

export default memo(TalentMobileMenu);
