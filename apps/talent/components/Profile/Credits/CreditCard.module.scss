@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.profile-credit-card {
  display: flex;
  flex-flow: column nowrap;
  position: relative;
  padding: 32px $space-30;
  margin-bottom: $space-30;
  background: $white;
  box-shadow: $shadow-credit-container;
  border-radius: 10.193px;
  scroll-margin-top: 80px;
  word-break: word-break;

  @include tablet {
    flex-direction: row;
    justify-content: space-between;
    padding: $space-20 $space-30;
  }
}

.card-title {
  color: $black;
  font-size: 16px;
  font-weight: 700;
  line-height: 1.3;
  display: block;
}

.card-subtitle {
  font-size: 14px;
  color: $grey-100;
  font-weight: 400;
}

.card-date {
  font-weight: 400;
  display: flex;
  align-items: center;
  font-size: 12px;
  padding-top: $space-20;

  @include tablet {
    width: 18%;
    padding-top: 0;
    padding-left: $space-5;
    font-size: 14px;
  }
}

.card-description {
  position: relative;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.6;
  justify-self: stretch;
  padding: 12px 0;

  @include tablet {
    width: 44%;
    padding: 0 $space-5;
    align-self: center;
    font-size: 12px;
  }

  & > div {
    display: flex;
    align-items: center;
  }
}

.separator {
  display: block;
  width: 1px;
  border: none;
  border-top: 1px dashed $grey-40;
  margin: 0;
  min-width: 100%;

  @include tablet {
    min-height: 100%;
    min-width: 0;
    max-width: 0;
    border-top: none;
    border-left: 1px dashed $grey-40;
  }
}

.profile-credit-card-header {
  display: flex;
  flex-flow: row nowrap;
  padding-bottom: $space-20;
  justify-content: space-between;

  @include tablet {
    width: 32%;
    padding-right: $space-10;
    padding-bottom: 0;
    align-items: center;
    justify-content: flex-start;
  }
}

.edit-button {
  margin-left: 0;
  order: 2;

  @include tablet {
    margin-right: $space-15;
    order: 0;
  }
}

.empty-block {
  justify-content: center;
}

.add-btn {
  display: flex;
  align-items: center;
}

.icon-add-new {
  width: 25px;
  height: 25px;
}

.clickable {
  cursor: pointer;
}
