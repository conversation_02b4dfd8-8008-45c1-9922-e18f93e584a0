'use client';
import { memo, useEffect } from 'react';
import styles from './Notification.module.scss';
import IconClose from '../../public/assets/icons/icon-close-1.svg';
import cn from 'classnames';

const Notification = ({
  type = 'info',
  message,
  children,
  onClose,
  timeout = false,
}) => {
  useEffect(() => {
    if (timeout) {
      const timer = setTimeout(() => {
        onClose();
      }, timeout);

      return () => clearTimeout(timer);
    }
  }, [onClose, timeout]);

  return (
    <div className={cn(styles.container, styles[type])}>
      <div className={styles.content}>{message || children}</div>
      {onClose && (
        <div className={styles.close} onClick={onClose}>
          <IconClose />
        </div>
      )}
    </div>
  );
};

export default memo(Notification);
