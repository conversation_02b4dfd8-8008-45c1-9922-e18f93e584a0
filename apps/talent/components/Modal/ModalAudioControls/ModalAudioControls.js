'use client';
import React, { memo } from 'react';
import styles from './ModalAudioControls.module.scss';
import Image from 'next/image';
import { Modal } from '@components';

const ModalAudioControls = ({
  onClose,
  onEdit,
  onRemove,
  onDownload,
  onShare,
}) => {
  return (
    <Modal
      onClose={onClose}
      backdropClose
      showDefaultLayout={false}
      showCloseButton={false}
      classNameContainer={styles['audio-control-modal']}
    >
      <div className={styles['audio-preview-actions-mobile']}>
        <div className={styles['audio-preview-action']} onClick={onEdit}>
          <Image
            className={styles.icon}
            src="/assets/icons/icon-edit.svg"
            alt="icon"
            width={20}
            height={19}
            title="Edit"
          />
          <span>Edit</span>
        </div>
        <div className={styles['audio-preview-action']} onClick={onRemove}>
          <Image
            className={styles.icon}
            src="/assets/icons/icon-trash-black.svg"
            alt="icon"
            width={20}
            height={15}
            title="Delete"
          />
          <span>Delete</span>
        </div>
        <div
          className={styles['audio-preview-action']}
          onClick={onDownload}
          title="Download"
        >
          <Image
            className={styles.icon}
            src="/assets/icons/icon-download.svg"
            alt="icon"
            width={20}
            height={22}
            title="Download"
          />
          <span>Download</span>
        </div>
        <div className={styles['audio-preview-action']} onClick={onShare}>
          <Image
            className={styles.icon}
            src="/assets/icons/icon-share.svg"
            alt="icon"
            width={19}
            height={19}
            title="Share link"
          />
          <span>Share link</span>
        </div>
      </div>
    </Modal>
  );
};

export default memo(ModalAudioControls);
