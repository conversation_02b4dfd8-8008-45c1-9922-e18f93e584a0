'use client';
import React, { memo, useState } from 'react';
import styles from '../ProfileConnections.module.scss';
import Image from 'next/image';
import cn from 'classnames';
import { useFormik } from 'formik';
import Api from '@services/api';
import * as Yup from 'yup';
import { useNotifications } from '@contexts/NotificationContext';
import { Checkbox, Input } from '@components';
import { usePremium } from '@contexts/PremiumContext';
import { useRouter } from 'next/navigation';
import { hasFormValueChanged } from '@utils/hasFormValueChanged';
import { Amp } from '@services/amp';
import { EditToggle } from '../EditToggle';
import { ErrorMessage, PERSONAL_URL_REGEX } from '@constants/form';

const PersonalLink = ({
  isPaidOrDelayed,
  profileId,
  personalUsername,
  allowIndexingProfile,
  refreshProfileDetails,
  refreshPersonalUrl,
  clearPersonalUrl,
  refreshUserProfiles,
}) => {
  const [isEditing, setEditing] = useState(false);
  const [isSaving, setSaving] = useState(false);
  const { setNotification } = useNotifications();
  const { openPremiumModal, setPremiumTrackingName } = usePremium();
  const publicRedirectUrl = process.env.publicUrl;
  const router = useRouter();

  const edit = () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'edit personal link',
      scope: Amp.element.scope.profile,
      section: Amp.element.section.personalLink,
      type: Amp.element.type.button,
    });
    if (isPaidOrDelayed) {
      setEditing(true);
    } else {
      setPremiumTrackingName('Unlock personal link popup');
      openPremiumModal();
    }
  };

  const triggerSubmit = async () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'save personal link',
      scope: Amp.element.scope.profile,
      section: Amp.element.section.personalLink,
      type: Amp.element.type.button,
    });
    await formik.submitForm();
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(
        `${publicRedirectUrl}/profile/${personalUsername}`,
      );
    } catch (err) {
      setNotification({
        type: 'error',
        message: 'Failed to copy personal link!',
        timeout: '5000',
      });
    } finally {
      setNotification({
        type: 'success',
        message: `Personal link copied successfully!`,
        timeout: '3000',
      });
    }
  };

  const formik = useFormik({
    enableReinitialize: true,
    initialValues: {
      personalUrl: personalUsername || '',
      allowIndexing: allowIndexingProfile || false,
    },
    onSubmit: async (values) => {
      if (!hasFormValueChanged(values, formik.initialValues)) {
        setEditing(false);

        return;
      }

      setSaving(true);

      let isError = false;

      if (values.allowIndexing !== allowIndexingProfile) {
        const body = new FormData();

        body.append('index', values.allowIndexing ? '1' : '0');

        const response = await Api.clientside(`/profiles/${profileId}`, {
          method: 'PATCH',
          body: body,
        });

        if (response.status !== 'ok') {
          isError = true;
          setNotification({
            type: 'error',
            message: response.message || ErrorMessage.Unexpected,
            timeout: '5000',
          });
        } else {
          await refreshProfileDetails();
        }
      }

      if (!isError && values.personalUrl !== personalUsername) {
        const body = new FormData();

        body.append('path', values.personalUrl);

        const response = await Api.clientside(
          `/profiles/${profileId}/personal-urls`,
          {
            body,
            method: 'POST',
          },
        );

        if (response.status !== 'ok') {
          isError = true;
          setNotification({
            type: 'error',
            message: response.message || ErrorMessage.Unexpected,
            timeout: '5000',
          });
        } else {
          if (values.personalUrl) {
            await refreshPersonalUrl();
          } else {
            await clearPersonalUrl();
          }
          await refreshUserProfiles();
          await router.replace(
            `/profile/${values.personalUrl || profileId}`,
            undefined,
            { shallow: true },
          );
        }
      }
      setSaving(false);
      if (!isError) {
        setEditing(false);
      }
    },
    validationSchema: Yup.object({
      personalUrl: Yup.string().matches(
        PERSONAL_URL_REGEX,
        ErrorMessage.URLRequired,
      ),
    }),
  });

  return (
    <>
      <section id="personal-link" className={styles['profile-info-block']}>
        <div className={styles['profile-section-title']}>
          Personal link:
          <EditToggle
            isEditing={isEditing}
            onEdit={edit}
            onSave={triggerSubmit}
            disabled={isSaving || formik.isSubmitting || !formik.isValid}
          />
        </div>

        {!isEditing ? (
          <>
            {personalUsername ? (
              <div className={styles['profile-info-row']}>
                <Image
                  src={'/assets/icons/icon-pes-link.svg'}
                  width={27}
                  height={27}
                  alt="phone profile"
                  onClick={copyToClipboard}
                  priority
                  className={cn(
                    styles['profile-info-icon'],
                    styles['personal-link'],
                  )}
                />
                <a
                  className={cn(
                    styles['text-ellipsis'],
                    styles['profile-section-link'],
                  )}
                  href={`${publicRedirectUrl}/profile/${personalUsername}`}
                  target="_blank"
                >
                  /{personalUsername}
                </a>
              </div>
            ) : (
              <div className={styles['profile-section-empty']} onClick={edit}>
                Add your custom URL.
                <span className={styles['profile-section-empty-add-button']}>
                  Add
                </span>
              </div>
            )}
          </>
        ) : (
          <form className={styles['form']} onSubmit={formik.handleSubmit}>
            <div className={styles['form-personal-url-input']}>
              <Input
                name="personalUrl"
                placeholder="Custom URL"
                onBlur={formik.handleBlur}
                onChange={formik.handleChange}
                value={formik.values.personalUrl}
                isTouched={formik.touched.personalUrl}
                error={formik.errors.personalUrl}
                hint="Your personalized web address must be between 6 and 100 characters long and only contain letters, numbers, “_” or “-” and must start with a letter."
              />
            </div>
            <div className={styles['form-indexing-container']}>
              <Checkbox
                className={styles['form-indexing-input']}
                name="allowIndexing"
                onChange={formik.handleChange}
                value={formik.values.allowIndexing}
                error={formik.errors.allowIndexing}
                onBlur={formik.handleBlur}
                isTouched={formik.touched.allowIndexing}
              >
                <span className={styles['form-field-label']}>
                  Allow indexing by search engines
                </span>
              </Checkbox>
              <span className={styles.hint}>
                Indexing means that your profile may appear on Google, Bing, and
                other search engines. We recommend keeping this turned on so you
                can get discovered easier.
              </span>
            </div>
          </form>
        )}
      </section>
    </>
  );
};

export default memo(PersonalLink);
