'use client';
import React, { memo, useEffect, useState } from 'react';
import { SearchSelect } from '@components';
import Image from 'next/image';
import Api from '@services/api';
import styles from './SearchSelectFilter.module.scss';
import * as Sentry from '@sentry/nextjs';

const SearchSelectFilter = ({
  placeholder = '',
  skills = [],
  onChange = () => {},
  selectedIds = [],
  onClick = () => {},
}) => {
  const [skillOptions, setSkillOptions] = useState([]);
  const [selectedSkills, setSelectedSkills] = useState(skills);

  let skillsRequestAbortController = new AbortController();

  const fetchSkills = async (title = '') => {
    if (title.length > 1) {
      try {
        skillsRequestAbortController?.abort();
        skillsRequestAbortController = new AbortController();

        const response = await Api.clientside(
          `/skills/search?limit=10&title=${title}`,
          { signal: skillsRequestAbortController.signal },
        );

        setSkillOptions(
          response.items.map((skill) => ({ ...skill, value: skill.id })) || [],
        );
      } catch (error) {
        if (error.name !== 'AbortError') {
          Sentry.captureException(error);
        }
      }
    } else {
      setSkillOptions([]);
    }
  };

  const addSkill = async (value) => {
    const skill = skillOptions.find((option) => option.value === value) || {
      id: value,
      title: value,
    };
    const isSkillAlreadySelected = selectedSkills.every(
      (option) => option.title !== value,
    );

    if (isSkillAlreadySelected) {
      setSelectedSkills([...selectedSkills, skill]);
    }
  };

  const removeSkill = (id) => {
    setSelectedSkills(selectedSkills.filter((skill) => skill.id !== id));
  };

  useEffect(() => {
    setSelectedSkills(skills);
  }, [skills]);

  useEffect(() => {
    if (selectedSkills.length !== skills.length) {
      onChange(selectedSkills);
    }
  }, [selectedSkills]);

  useEffect(() => {
    if (selectedIds?.length) {
      getSkillsByIds(selectedIds).catch();
    }
  }, [selectedIds]);

  const getSkillsByIds = async (ids) => {
    if (ids.length && !selectedSkills.length) {
      const response = await Api.clientside(
        `/skills/search-by-ids?items=${ids.join(',')}`,
      );

      setSelectedSkills(
        response.items
          ? response.items.map((item) => ({ ...item, value: item.id }))
          : [],
      );
    }
  };

  return (
    <>
      <SearchSelect
        name="skills"
        placeholder={placeholder}
        onChange={fetchSkills}
        options={skillOptions.filter(
          (skill) =>
            !selectedSkills.some(
              (selectedSkill) => selectedSkill.id === skill.id,
            ),
        )}
        onAdd={addSkill}
        hint="Max 25 characters"
        charCounter
        maxChars={25}
        onClick={onClick}
      />
      {selectedSkills && selectedSkills.length > 0 && (
        <div className={styles.tags}>
          {selectedSkills.map((tag, i) => (
            <div
              key={i}
              className={styles.tag}
              onClick={() => removeSkill(tag.id)}
            >
              <span>{tag.title}</span>
              <Image
                src="/assets/icons/icon-close-3.svg"
                width={8}
                height={8}
                alt="icon"
              />
            </div>
          ))}
        </div>
      )}
    </>
  );
};

export default memo(SearchSelectFilter);
