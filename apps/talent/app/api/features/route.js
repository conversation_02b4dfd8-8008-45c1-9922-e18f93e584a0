import { cookies } from 'next/headers';
import dayjs from 'dayjs';
import { CookieServiceServer } from '@services/cookieServiceServer';
import { FeatureManager } from '@services/featureManager';

export async function GET(req) {
  const cookieStore = await cookies();
  const { searchParams } = new URL(req.url);
  const key = searchParams.get('key');
  const headers = { 'Content-Type': 'application/json' };
  const status = 200;
  const defaultResponse = new Response(JSON.stringify(false), {
    status,
    headers,
  });

  const controller = new AbortController();
  const timeout = setTimeout(() => controller.abort(), 3000);

  try {
    let features = CookieServiceServer.getFeaturesCookie(cookieStore) || [];

    if (!features?.length || !key) {
      const response = await fetch(
        `${process.env.featureManagerApiUrl}/configuration`,
        {
          method: 'GET',
          signal: controller.signal,
        },
      );

      clearTimeout(timeout);

      const data = await response.json();

      features = data.features;

      if (features?.length) {
        const expires = dayjs()
          .add(FeatureManager.cacheTimeInSeconds, 'seconds')
          .toDate();

        CookieServiceServer.setFeaturesCookie(features, cookieStore, expires);
      }
    }

    const feature = features?.find((feature) => feature.key === key);

    return new Response(JSON.stringify(!!feature?.enabled), {
      status,
      headers,
    });
  } catch (error) {
    return defaultResponse;
  } finally {
    clearTimeout(timeout);
  }
}
