import Api from '../../../services/api';

export default async function handler(req, res) {
  const config = await Api.serverGateway(
    `/calls/init`,
    req.headers,
    Api.TTL_TYPE_LONG,
  );

  const optionsCategories = [];

  config.data?.categories?.map((item) => {
    optionsCategories.push({
      label: item.name,
      value: item.name.toLowerCase().replace(/[\W_]+/g, ''),
      id: item.id,
      checked: false,
    });
  });

  const optionsEthnicities = [];

  config.data?.ethnicities?.map((item) => {
    optionsEthnicities.push({
      label: item.name,
      value: item.name.toLowerCase().replace(/[\W_]+/g, ''),
      id: item.id,
      checked: false,
    });
  });

  const response = {
    sections: [
      {
        title: 'Name',
        widgets: [
          {
            class: 'full',
            type: 'input',
            title: 'Name',
            slug: 'name',
            bc: 'name',
            value: '',
          },
        ],
      },
      {
        title: 'Location',
        widgets: [
          {
            class: 'full',
            type: 'select',
            subtype: 'disableClearable',
            title: 'Country',
            slug: 'country',
            bc: 'country',
            options: [
              { label: 'Canada', value: 'CA' },
              { label: 'United States', value: 'US' },
              { label: 'Worldwide', value: '' },
            ],
            selected: '',
          },
          {
            class: 'full',
            type: 'location',
            title: 'Location',
            slug: 'location',
            value: '',
            summary: '',
            filters: [
              {
                class: 'full',
                type: 'input',
                title: 'City',
                slug: 'city',
                bc: '',
                // bc: 'city_slug',
                value: '',
              },
              {
                class: 'full',
                type: 'input',
                title: 'Longitude',
                slug: 'longitude',
                bc: 'longitude',
                value: '',
              },
              {
                class: 'full',
                type: 'input',
                title: 'Latitude',
                slug: 'latitude',
                bc: 'latitude',
                value: '',
              },
            ],
          },
          {
            class: 'full',
            type: 'select',
            subtype: 'disableClearable',
            title: 'Radius',
            slug: 'radius',
            bc: 'distance',
            options: [
              {
                label: '25 Miles',
                value: '25',
              },
              {
                label: '50 Miles',
                value: '50',
              },
              {
                label: '100 Miles',
                value: '100',
              },
              {
                label: '150 Miles',
                value: '150',
              },
              {
                label: '200 Miles',
                value: '200',
              },
            ],
            selected: '200',
          },
        ],
      },
      {
        title: 'Details',
        widgets: [
          {
            class: 'left',
            type: 'select',
            subtype: 'disableClearable',
            title: 'Age min',
            slug: 'agemin',
            bc: 'age_range[]',
            options: [
              {
                label: '18',
                value: '18',
              },
              {
                label: '19',
                value: '19',
              },
              {
                label: '20',
                value: '20',
              },
              {
                label: '21',
                value: '21',
              },
              {
                label: '22',
                value: '22',
              },
              {
                label: '23',
                value: '23',
              },
              {
                label: '24',
                value: '24',
              },
              {
                label: '25',
                value: '25',
              },
              {
                label: '26',
                value: '26',
              },
              {
                label: '27',
                value: '27',
              },
              {
                label: '28',
                value: '28',
              },
              {
                label: '29',
                value: '29',
              },
              {
                label: '30',
                value: '30',
              },
              {
                label: '31',
                value: '31',
              },
              {
                label: '32',
                value: '32',
              },
              {
                label: '33',
                value: '33',
              },
              {
                label: '34',
                value: '34',
              },
              {
                label: '35',
                value: '35',
              },
              {
                label: '36',
                value: '36',
              },
              {
                label: '37',
                value: '37',
              },
              {
                label: '38',
                value: '38',
              },
              {
                label: '39',
                value: '39',
              },
              {
                label: '40',
                value: '40',
              },
              {
                label: '41',
                value: '41',
              },
              {
                label: '42',
                value: '42',
              },
              {
                label: '43',
                value: '43',
              },
              {
                label: '44',
                value: '44',
              },
              {
                label: '45',
                value: '45',
              },
              {
                label: '46',
                value: '46',
              },
              {
                label: '47',
                value: '47',
              },
              {
                label: '48',
                value: '48',
              },
              {
                label: '49',
                value: '49',
              },
              {
                label: '50',
                value: '50',
              },
              {
                label: '51',
                value: '51',
              },
              {
                label: '52',
                value: '52',
              },
              {
                label: '53',
                value: '53',
              },
              {
                label: '54',
                value: '54',
              },
              {
                label: '55',
                value: '55',
              },
              {
                label: '56+',
                value: '56',
              },
            ],
            selected: '18',
          },
          {
            class: 'right',
            type: 'select',
            subtype: 'disableClearable',
            title: 'Age max',
            slug: 'agemax',
            bc: 'age_range[]',
            options: [
              {
                label: '18',
                value: '18',
              },
              {
                label: '19',
                value: '19',
              },
              {
                label: '20',
                value: '20',
              },
              {
                label: '21',
                value: '21',
              },
              {
                label: '22',
                value: '22',
              },
              {
                label: '23',
                value: '23',
              },
              {
                label: '24',
                value: '24',
              },
              {
                label: '25',
                value: '25',
              },
              {
                label: '26',
                value: '26',
              },
              {
                label: '27',
                value: '27',
              },
              {
                label: '28',
                value: '28',
              },
              {
                label: '29',
                value: '29',
              },
              {
                label: '30',
                value: '30',
              },
              {
                label: '31',
                value: '31',
              },
              {
                label: '32',
                value: '32',
              },
              {
                label: '33',
                value: '33',
              },
              {
                label: '34',
                value: '34',
              },
              {
                label: '35',
                value: '35',
              },
              {
                label: '36',
                value: '36',
              },
              {
                label: '37',
                value: '37',
              },
              {
                label: '38',
                value: '38',
              },
              {
                label: '39',
                value: '39',
              },
              {
                label: '40',
                value: '40',
              },
              {
                label: '41',
                value: '41',
              },
              {
                label: '42',
                value: '42',
              },
              {
                label: '43',
                value: '43',
              },
              {
                label: '44',
                value: '44',
              },
              {
                label: '45',
                value: '45',
              },
              {
                label: '46',
                value: '46',
              },
              {
                label: '47',
                value: '47',
              },
              {
                label: '48',
                value: '48',
              },
              {
                label: '49',
                value: '49',
              },
              {
                label: '50',
                value: '50',
              },
              {
                label: '51',
                value: '51',
              },
              {
                label: '52',
                value: '52',
              },
              {
                label: '53',
                value: '53',
              },
              {
                label: '54',
                value: '54',
              },
              {
                label: '55',
                value: '55',
              },
              {
                label: '56+',
                value: '150',
              },
            ],
            selected: '150',
          },
          {
            class: 'full',
            type: 'select',
            subtype: 'disableClearable',
            title: 'Gender',
            slug: 'gender',
            bc: 'gender',
            options: [
              {
                label: 'Male',
                value: 'male',
                id: 2,
              },
              {
                label: 'Female',
                value: 'female',
                id: 4,
              },
              {
                label: 'Not Specified',
                value: null,
                id: null,
              },
            ],
            selected: null,
          },
        ],
      },
      {
        title: 'Categories',
        widgets: [
          {
            class: 'full',
            type: 'checkbox',
            title: '',
            slug: 'category',
            bc: 'categories[]',
            options: optionsCategories,
          },
        ],
      },
      {
        title: 'Ethnicities',
        widgets: [
          {
            class: 'full',
            type: 'checkbox',
            title: '',
            slug: 'ethnicity',
            bc: 'ethnicities[]',
            options: optionsEthnicities,
          },
        ],
      },
      {
        title: '',
        widgets: [
          {
            class: 'hidden',
            type: 'input',
            title: '',
            slug: 'page',
            bc: 'page',
            value: 1,
          },
        ],
      },
    ],
  };

  res.status(200).json(response);
}
