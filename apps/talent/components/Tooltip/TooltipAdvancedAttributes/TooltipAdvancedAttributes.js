'use client';
import React, { memo } from 'react';
import styles from './TooltipAdvancedAttributes.module.scss';
import { Button } from '@components';
import { useRouter } from 'next/navigation';
import { Amp } from '@services/amp';
import useTrackElementActions from '@utils/useTrackElementActions';
import imageAttributesMale from '../../../public/assets/tooltip/image-advanced-attributes-male.webp';
import imageAttributesFemale from '../../../public/assets/tooltip/image-advanced-attributes-female.webp';
import Image from 'next/image';

const TooltipAdvancedAttributes = ({ genderTitle = 'male' }) => {
  const router = useRouter();

  const { onTrackClick } = useTrackElementActions({
    name: 'Unlock advanced skills popup',
    type: Amp.element.type.popup,
    context: Amp.element.context.ctaPopup,
    autoTrackEnabled: true,
  });

  const onButtonClick = () => {
    onTrackClick();

    router.push('/upgrade');
  };

  return (
    <div className={styles['tooltip-container']}>
      <span className={styles.title}>Advanced Skills Feature</span>
      <p className={styles['description']}>
        Boost your profile&apos;s potential and unlock the advanced skills
        feature today to impress casting directors and land your dream
        opportunities!
      </p>
      <Image
        className={styles.image}
        src={
          genderTitle === 'male' ? imageAttributesMale : imageAttributesFemale
        }
        alt="attribute image"
      />
      <div className={styles['button-container']}>
        <Button
          className={styles['button-unblock']}
          label="Subscribe Now"
          shadow={false}
          color="solid-blue"
          minWidth="75px"
          onClick={onButtonClick}
        />
      </div>
    </div>
  );
};

export default memo(TooltipAdvancedAttributes);
