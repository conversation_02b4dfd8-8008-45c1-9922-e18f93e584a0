import { createContext, useContext, useEffect, useState } from 'react';
import Api from '../services/api';
import {
  formatAttributes,
  formatImages,
  formatProfileDetails,
  formatTouches,
  formatVideos,
} from '@utils/formatProfile';
import { useNotifications } from './NotificationContext';
import { useAuth } from './AuthContext';
import { CookieService } from '@services/cookieService';
import { getProfileProgress } from '@utils/profileProgressHelpers';
import { ErrorMessage } from '@constants/form';

const ProfileContext = createContext(null);

export const useProfileContext = () => {
  return useContext(ProfileContext);
};

export const ProfileProvider = ({ children, value }) => {
  const [profile, setProfile] = useState(value);
  const [progress, setProgress] = useState({});
  const { setNotification } = useNotifications();
  const { refreshUserProfiles, profileId, userProfiles } = useAuth();

  useEffect(() => {
    setProgress(getProfileProgress(profile));
  }, [profile]);

  const refreshSkills = async () => {
    const id = CookieService.getProfileCookie();
    const response = await Api.clientside(`/profiles/${id}/skills`);

    if (response.status !== 'ok') {
      showErrorMessage(response.message);
    } else {
      setProfile((current) => {
        return {
          ...current,
          skills: response.items?.length ? response.items : [],
        };
      });
    }
  };

  const refreshVideos = async () => {
    const id = CookieService.getProfileCookie();
    const response = await Api.clientside(`/profiles/${id}/videos/youtubes`);

    if (response.status !== 'ok') {
      showErrorMessage(response.message);
    } else {
      setProfile((current) => {
        return {
          ...current,
          ...formatVideos(response.items),
        };
      });
    }
  };

  const refreshCredits = async () => {
    const id = CookieService.getProfileCookie();
    const response = await Api.clientside(`/profiles/${id}/credits`);

    if (response.status !== 'ok') {
      showErrorMessage(response.message);
    } else {
      setProfile((current) => {
        return {
          ...current,
          credits: response.items?.length ? response.items : [],
        };
      });
    }
  };

  const refreshSocialNetworks = async () => {
    const id = CookieService.getProfileCookie();
    const response = await Api.clientside(`/profiles/${id}/socialities`);

    if (response.status !== 'ok') {
      showErrorMessage(response.message);
    } else {
      setProfile((current) => {
        return {
          ...current,
          socialNetworks: response.items?.length ? response.items : [],
        };
      });
    }
  };

  const refreshCategories = async () => {
    const id = CookieService.getProfileCookie();
    const response = await Api.clientside(`/profiles/${id}/categories`);

    if (response.status !== 'ok') {
      showErrorMessage(response.message);
    } else {
      setProfile((current) => {
        return {
          ...current,
          categories: response.items?.length ? response.items : [],
        };
      });
    }
  };

  const refreshTouches = async () => {
    const id = CookieService.getAccountCookie();
    const response = await Api.clientside(`/accounts/${id}/touches`);

    if (response.status !== 'ok') {
      showErrorMessage(response.message);
    } else {
      setProfile((current) => {
        return {
          ...current,
          ...formatTouches(response.links),
        };
      });
    }
  };

  const refreshPersonalUrl = async () => {
    const id = CookieService.getProfileCookie();
    const response = await Api.clientside(`/profiles/${id}/personal-urls/last`);

    if (response.status !== 'ok') {
      showErrorMessage(response.message);
    } else {
      setProfile((current) => {
        return {
          ...current,
          personalUsername: response.path || null,
        };
      });
    }
  };

  const clearPersonalUrl = async () => {
    setProfile((current) => {
      return {
        ...current,
        personalUsername: null,
      };
    });
  };

  const refreshProfileAttributes = async () => {
    const id = CookieService.getProfileCookie();
    const response = await Api.clientside(
      `/profiles/${id}/attributes?expand=ethnicities,location`,
    );

    if (response.status !== 'ok') {
      showErrorMessage(response.message);
    } else {
      setProfile((current) => {
        return {
          ...current,
          ...formatAttributes(response.items),
        };
      });
    }
  };

  const refreshProfileDetails = async () => {
    const id = CookieService.getProfileCookie();
    const response = await Api.clientside(
      `/profiles/${id}?expand=ethnicities,location`,
    );

    if (response.status !== 'ok') {
      showErrorMessage(response.message);
    } else {
      const newProfile = {
        ...profile,
        ...formatProfileDetails(response),
      };

      setProfile(newProfile);
    }
  };

  const refreshImages = async () => {
    const id = CookieService.getProfileCookie();
    const response = await Api.clientside(
      `/profiles/${id}/images?expand=profile_image_characteristics&width=600&height=800`,
    );

    if (response.status !== 'ok') {
      showErrorMessage(response.message);
    } else {
      setProfile((current) => {
        return {
          ...current,
          ...formatImages(response.items),
        };
      });
    }
  };

  const saveAttribute = async (attribute) => {
    const id = CookieService.getProfileCookie();
    const body = {
      id: attribute.id,
      name: attribute.name,
      items: [...attribute.items],
    };

    return await Api.clientside(`/profiles/${id}/attributes`, {
      method: 'PUT',
      body: JSON.stringify(body),
    });
  };

  const uploadImage = async (file, type, isCurrentTitlePhoto) => {
    const clientId = userProfiles[0].clientId;
    const body = new FormData();

    body.append('profile_id', profileId);
    body.append('client_id', clientId);
    body.append('file', file);
    body.append('type', type);

    const response = await Api.clientside('/profile-images-upload', {
      method: 'POST',
      body,
    });

    if (response.status === 'error') {
      showErrorMessage(response.message);

      return null;
    }

    if (isCurrentTitlePhoto && profile.titleImage?.id) {
      await deleteTitleImage(profile.titleImage.id);
    }

    updateProfileRating(response.profile_rating);

    await refreshImages();
    await refreshUserProfiles();

    return response;
  };

  const uploadTitleImage = async (file, id) => {
    const clientId = userProfiles[0].clientId;
    const body = new FormData();

    body.append('profile_id', profileId);
    body.append('client_id', clientId);
    body.append('file', file);
    body.append('type', 'pc');
    body.append('source_id', id);

    const response = await Api.clientside('/profile-images-upload', {
      method: 'POST',
      body,
    });

    if (response.status === 'error') {
      showErrorMessage(response.message);
    } else {
      await refreshImages();
      await refreshUserProfiles();
    }
  };

  const deleteImage = async (id) => {
    const body = new FormData();

    body.append('id', `${id}`);

    const response = await Api.clientside(
      `/photo/delete`,
      {
        body: body,
        method: 'POST',
      },
      {
        'x-profile': profileId,
      },
    );

    if (response.status !== 'ok') {
      showErrorMessage(response.message);

      return;
    }

    if (profile.titleImage?.source_id === id) {
      await deleteTitleImage(profile.titleImage.id);
    }

    if (response.profile_rating) {
      updateProfileRating(response.profile_rating);
    }

    await refreshUserProfiles();
    await refreshImages();
  };

  const deleteTitleImage = async (id) => {
    const body = new FormData();

    body.append('id', `${id}`);

    const response = await Api.clientside(
      `/photo/delete`,
      {
        body: body,
        method: 'POST',
      },
      {
        'x-profile': profileId,
      },
    );

    if (response.status !== 'ok') {
      showErrorMessage(response.message);
    }
  };

  const updateProfileRating = (newRating) => {
    if (newRating) {
      setProfile((current) => {
        return {
          ...current,
          rating: newRating,
        };
      });
    }
  };

  const uploadAudio = async (title, audio) => {
    const id = CookieService.getProfileCookie();
    const body = new FormData();

    body.append('title', title);
    body.append('file', audio);

    const response = await Api.clientside(`/profiles/${id}/audios`, {
      method: 'POST',
      body,
    });

    if (response.status !== 'ok') {
      showErrorMessage(response.message);
    } else {
      await refreshAudios();
    }
  };

  const updateAudio = async (title, audio, audioId) => {
    const id = CookieService.getProfileCookie();
    const body = new FormData();

    body.append('title', title);
    body.append('file', audio);

    const response = await Api.clientside(`/profiles/${id}/audios/${audioId}`, {
      method: 'POST',
      body,
    });

    if (response.status !== 'ok') {
      showErrorMessage(response.message);
    } else {
      await refreshAudios();
    }
  };

  const refreshAudios = async () => {
    const id = CookieService.getProfileCookie();
    const response = await Api.clientside(`/profiles/${id}/audios`);

    if (response.status !== 'ok') {
      showErrorMessage(response.message);
    } else {
      setProfile((current) => {
        return {
          ...current,
          audios: response.items || [],
        };
      });
    }
  };

  const deleteAudio = async (audioId) => {
    const id = CookieService.getProfileCookie();

    const response = await Api.clientside(`/profiles/${id}/audios/${audioId}`, {
      method: 'DELETE',
    });

    if (response.status !== 'removed') {
      showErrorMessage(response.message);
    } else {
      await refreshAudios();
    }
  };

  const showErrorMessage = (message) => {
    setNotification({
      type: 'error',
      message: message || ErrorMessage.Unexpected,
      timeout: '5000',
    });
  };

  return (
    <ProfileContext.Provider
      value={{
        ...profile,
        ...progress,
        refreshProfileDetails,
        refreshTouches,
        refreshSocialNetworks,
        refreshCategories,
        refreshPersonalUrl,
        clearPersonalUrl,
        refreshCredits,
        refreshSkills,
        refreshVideos,
        refreshImages,
        deleteImage,
        uploadTitleImage,
        uploadImage,
        updateProfileRating,
        saveAttribute,
        refreshProfileAttributes,
        uploadAudio,
        updateAudio,
        deleteAudio,
      }}
    >
      {children}
    </ProfileContext.Provider>
  );
};
