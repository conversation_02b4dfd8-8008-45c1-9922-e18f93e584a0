@use '@styles/mixins' as *;
@use '@styles/variables' as *;

@include keyframes(bouncer) {
  0% {
    transform: scaleY(1);
  }

  30% {
    transform: scaleY(1.8);
  }

  60% {
    transform: scaleY(1);
  }

  100% {
    transform: scaleY(1);
  }
}

.container {
  align-items: center;
  display: flex;
  justify-content: center;
  width: 100%;
}

.bouncer {
  height: 20px;
  margin-left: 3px;
  width: 4px;

  &.midnight {
    background-color: $violet-60;
  }

  &.white {
    background-color: $white;
  }

  @include animation(bouncer, 1.4s, ease-in-out, infinite);
}

.bouncer:nth-child(2) {
  animation-delay: 0.12s;
}

.bouncer:nth-child(3) {
  animation-delay: 0.24s;
}

.bouncer:nth-child(4) {
  animation-delay: 0.36s;
}

.bouncer:nth-child(5) {
  animation-delay: 0.48s;
}

.bouncer:nth-child(6) {
  animation-delay: 0.6s;
}
