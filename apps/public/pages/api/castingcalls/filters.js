// Next.js API route support: https://nextjs.org/docs/api-routes/introduction
import Api from '../../../services/api';
import { getHeaders } from '../../../utils/headerHelper';

export default async function handler(req, res) {
  const { isTalent } = req.query;

  const config = await Api.serverGateway(
    `/calls/init`,
    req.headers,
    Api.TTL_TYPE_LONG,
  );

  const optionsCategories = [];

  config.data?.categories?.map((item) => {
    optionsCategories.push({
      label: item.name,
      value: item.name.toLowerCase().replace(/[\W_]+/g, ''),
      id: item.id,
      checked: false,
    });
  });

  const optionsEthnicities = [];

  config.data?.ethnicities?.map((item) => {
    optionsEthnicities.push({
      label: item.name,
      value: item.name.toLowerCase().replace(/[\W_]+/g, ''),
      id: item.id,
      checked: false,
    });
  });

  const optionsGenders = [];

  config.data?.genders?.map((item) => {
    optionsGenders.push({
      label: item.name,
      value: item.name.toLowerCase().replace(/[\W_]+/g, ''),
      id: item.id,
    });
  });

  const suggestions = [
    'spanish',
    'tattoo',
    'blonde',
    'doctor',
    'sports',
    'fashion',
    'fitness',
    'ballet',
    'dancer',
    'bartender',
    'boxing',
    'archery',
    'baseball',
    'rumba',
    'french',
    'italian',
  ];

  function getMultipleRandom(arr, num) {
    const shuffled = [...arr].sort(() => 0.5 - Math.random());

    return shuffled.slice(0, num);
  }

  const response = {
    sections: [
      {
        title: 'Search',
        widgets: [
          {
            class: 'full',
            type: 'input',
            auto_submit: true,
            title: 'e.g., ' + getMultipleRandom(suggestions, 2).join(', '),
            slug: 'keyword',
            bc: 'keyword',
            value: '',
          },
        ],
      },
      {
        title: 'Location',
        widgets: [
          {
            class: 'full',
            type: 'select',
            subtype: 'disableClearable',
            title: 'Country',
            slug: 'country',
            bc: 'country',
            options: [
              { label: 'Canada', value: 'CA' },
              { label: 'United States', value: 'US' },
              { label: 'Worldwide', value: '' },
            ],
            selected: '',
          },
          {
            class: 'full',
            type: 'location',
            title: 'Location',
            slug: 'location',
            value: '',
            summary: '',
            filters: [
              {
                class: 'full',
                type: 'input',
                title: 'City',
                slug: 'city',
                bc: '',
                // bc: 'city_slug',
                value: '',
              },
              {
                class: 'full',
                type: 'input',
                title: 'Longitude',
                slug: 'longitude',
                bc: 'longitude',
                value: '',
              },
              {
                class: 'full',
                type: 'input',
                title: 'Latitude',
                slug: 'latitude',
                bc: 'latitude',
                value: '',
              },
            ],
          },
          {
            class: 'full',
            type: 'select',
            subtype: 'disableClearable',
            title: 'Radius',
            slug: 'radius',
            bc: 'distance',
            options: [
              { label: '25 Miles', value: '25' },
              { label: '50 Miles', value: '50' },
              {
                label: '100 Miles',
                value: '100',
              },
              { label: '150 Miles', value: '150' },
              { label: '200 Miles', value: '200' },
            ],
            selected: '200',
          },
        ],
      },
      {
        title: 'Details',
        widgets: [
          {
            class: 'left',
            type: 'select',
            subtype: 'disableClearable',
            title: 'Age min',
            slug: 'agemin',
            bc: 'age_range[]',
            options: [
              { label: '18', value: '18' },
              { label: '19', value: '19' },
              {
                label: '20',
                value: '20',
              },
              { label: '21', value: '21' },
              { label: '22', value: '22' },
              {
                label: '23',
                value: '23',
              },
              { label: '24', value: '24' },
              { label: '25', value: '25' },
              {
                label: '26',
                value: '26',
              },
              { label: '27', value: '27' },
              { label: '28', value: '28' },
              {
                label: '29',
                value: '29',
              },
              { label: '30', value: '30' },
              { label: '31', value: '31' },
              {
                label: '32',
                value: '32',
              },
              { label: '33', value: '33' },
              { label: '34', value: '34' },
              {
                label: '35',
                value: '35',
              },
              { label: '36', value: '36' },
              { label: '37', value: '37' },
              {
                label: '38',
                value: '38',
              },
              { label: '39', value: '39' },
              { label: '40', value: '40' },
              {
                label: '41',
                value: '41',
              },
              { label: '42', value: '42' },
              { label: '43', value: '43' },
              {
                label: '44',
                value: '44',
              },
              { label: '45', value: '45' },
              { label: '46', value: '46' },
              {
                label: '47',
                value: '47',
              },
              { label: '48', value: '48' },
              { label: '49', value: '49' },
              {
                label: '50',
                value: '50',
              },
              { label: '51', value: '51' },
              { label: '52', value: '52' },
              {
                label: '53',
                value: '53',
              },
              { label: '54', value: '54' },
              { label: '55', value: '55' },
              { label: '56+', value: '56' },
            ],
            selected: '18',
          },
          {
            class: 'right',
            type: 'select',
            subtype: 'disableClearable',
            title: 'Age max',
            slug: 'agemax',
            bc: 'age_range[]',
            options: [
              { label: '18', value: '18' },
              { label: '19', value: '19' },
              {
                label: '20',
                value: '20',
              },
              { label: '21', value: '21' },
              { label: '22', value: '22' },
              {
                label: '23',
                value: '23',
              },
              { label: '24', value: '24' },
              { label: '25', value: '25' },
              {
                label: '26',
                value: '26',
              },
              { label: '27', value: '27' },
              { label: '28', value: '28' },
              {
                label: '29',
                value: '29',
              },
              { label: '30', value: '30' },
              { label: '31', value: '31' },
              {
                label: '32',
                value: '32',
              },
              { label: '33', value: '33' },
              { label: '34', value: '34' },
              {
                label: '35',
                value: '35',
              },
              { label: '36', value: '36' },
              { label: '37', value: '37' },
              {
                label: '38',
                value: '38',
              },
              { label: '39', value: '39' },
              { label: '40', value: '40' },
              {
                label: '41',
                value: '41',
              },
              { label: '42', value: '42' },
              { label: '43', value: '43' },
              {
                label: '44',
                value: '44',
              },
              { label: '45', value: '45' },
              { label: '46', value: '46' },
              {
                label: '47',
                value: '47',
              },
              { label: '48', value: '48' },
              { label: '49', value: '49' },
              {
                label: '50',
                value: '50',
              },
              { label: '51', value: '51' },
              { label: '52', value: '52' },
              {
                label: '53',
                value: '53',
              },
              { label: '54', value: '54' },
              { label: '55', value: '55' },
              { label: '56+', value: '150' },
            ],
            selected: '150',
          },
          {
            class: 'full',
            type: 'select',
            subtype: 'disableClearable',
            title: 'Gender',
            slug: 'gender',
            bc: 'gender',
            options: optionsGenders,
            selected: 'bothgenders',
          },
        ],
      },
      {
        title: 'Categories',
        widgets: [
          {
            class: 'full',
            type: 'checkbox',
            title: '',
            slug: 'category',
            bc: 'categories[]',
            options: optionsCategories,
          },
        ],
      },
      {
        title: 'Ethnicities',
        widgets: [
          {
            class: 'full',
            type: 'checkbox',
            title: '',
            slug: 'ethnicity',
            bc: 'ethnicities[]',
            options: optionsEthnicities,
          },
        ],
      },
      {
        title: '',
        widgets: [
          {
            class: 'hidden',
            type: 'input',
            title: '',
            slug: 'page',
            bc: 'page',
            value: 1,
          },
        ],
      },
    ],
  };

  if (isTalent === 'true') {
    response.sections[0].widgets.splice(1, 0, {
      class: 'full',
      type: 'checkbox',
      title: '',
      slug: 'best',
      bc: 'best',
      options: [
        {
          value: 'true',
          label: 'Best Match',
          id: 'true',
          checked: false,
        },
      ],
    });
    response.sections[0].widgets.splice(2, 0, {
      class: 'full',
      type: 'checkbox',
      title: '',
      slug: 'viewed',
      bc: 'viewed',
      options: [
        {
          value: 'false',
          label: 'Hide viewed Casting Calls',
          id: 'false',
          checked: false,
        },
      ],
    });
    response.sections[0].widgets.splice(3, 0, {
      class: 'hidden',
      type: 'checkbox',
      title: '',
      slug: 'near',
      bc: '',
      options: [
        {
          value: 'false',
          label: 'Search all casting calls',
          id: 'false',
          checked: false,
        },
      ],
    });
  }

  const seo = await Api.serverAPIRoute(
    `/seo/castingcalls`,
    null,
    getHeaders(req),
  );

  response.sections.map((section) => {
    section.widgets.map((widget) => {
      if (undefined === seo.rules[widget.slug]) {
        return null;
      }

      if (undefined === seo.rules[widget.slug].overrides) {
        return null;
      }

      switch (widget.type) {
        case 'input':
          break;
        case 'select':
          widget.options.map((option) => {
            if (undefined !== seo.rules[widget.slug]) {
              if (
                undefined !== seo.rules[widget.slug].overrides[option.value]
              ) {
                option.value = seo.rules[widget.slug].overrides[option.value];
              }
            }
          });
          break;
        case 'checkbox':
          widget.options.map((option) => {
            if (undefined !== seo.rules[widget.slug]) {
              if (
                undefined !== seo.rules[widget.slug].overrides[option.value]
              ) {
                option.value = seo.rules[widget.slug].overrides[option.value];
              }
            }
          });
          break;
      }
    });
  });

  res.status(200).json(response);
}
