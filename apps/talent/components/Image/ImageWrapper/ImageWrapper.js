'use client';
import React, { memo, useEffect, useRef, useState } from 'react';
import {
  CropToolOverlay,
  DeclinedImage,
  ModalDeletePhoto,
  PhotoUploadTool,
  UploadImage,
  ImageActions,
} from '@components';
import { useProfileContext } from '@contexts/ProfileContext';
import { declineActionList } from '@constants/images';
import { Amp } from '@services/amp';
import { isImageDeclined } from '@utils/imageHelpers';
import UnlockPhotoAnalyzer from '../../Profile/UnlockFeature/UnlockPhotoAnalyzer';

const ImageWrapper = ({
  image,
  genderTitle = 'male',
  titlePhotoSourceId,
  onImageClick,
  showUploadIcon = false,
  imageType = 'additional',
  imageTitle = '',
  declineContentSize = 'small',
  declineActionLabel = declineActionList.upload,
  zoomActionIconSize = 'small', // small | large
  zoomIconSize,
  isPaidOrDelayed = false,
  showTooltip,
  ratingPosition,
  showRatingHint,
  isMainImage = false,
}) => {
  const [selectedImageUrl, setSelectedImageUrl] = useState(null);
  const [showCropTool, setShowCropTool] = useState(false);
  const [showTitleCropTool, setShowTitleCropTool] = useState(false);
  const [showDeletePhotoModal, setShowDeletePhotoModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isCurrentTitlePhoto, setIsCurrentTitlePhoto] = useState(false);

  const photoUploadToolRef = useRef(null);

  const { deleteImage } = useProfileContext();

  useEffect(() => {
    setIsCurrentTitlePhoto(titlePhotoSourceId === image?.id);
  }, [image, titlePhotoSourceId]);

  const onFileInputChange = (imageUrl) => {
    setSelectedImageUrl(imageUrl);
    setShowCropTool(true);
  };

  const openDeletePhotoModal = () => {
    const section = getAmpSectionName();

    Amp.track(Amp.events.elementClicked, {
      name: `remove ${section}`,
      scope: Amp.element.scope.profile,
      section: section,
      type: Amp.element.type.button,
    });
    setShowDeletePhotoModal(true);
  };

  const closeDeletePhotoModal = () => {
    setShowDeletePhotoModal(false);
  };

  const closeCropTool = () => {
    setShowCropTool(false);
    setShowTitleCropTool(false);
  };

  const deletePhoto = async () => {
    setLoading(true);

    await deleteImage(image.id);

    setLoading(false);
    setShowDeletePhotoModal(false);
  };

  const getAmpSectionName = () => {
    switch (imageType) {
      case 'close_up':
        return Amp.element.section.headshot;
      case '3_4':
        return Amp.element.section.sideView;
      case 'height':
        return Amp.element.section.fullHeight;
      default:
        return Amp.element.section.additionalPhotos;
    }
  };

  const onMakeTitleClick = () => {
    const section = getAmpSectionName();

    Amp.track(Amp.events.elementClicked, {
      name: 'make photo title',
      scope: Amp.element.scope.profile,
      section: section,
      type: Amp.element.type.button,
    });
    setSelectedImageUrl(image.url);
    setShowTitleCropTool(true);
  };

  const selectFile = () => {
    const section = getAmpSectionName();

    Amp.track(Amp.events.elementClicked, {
      name: `upload ${section}`,
      scope: Amp.element.scope.profile,
      section: section,
      type: Amp.element.type.button,
    });

    photoUploadToolRef.current.selectFile();
  };

  const getImageComponent = () => {
    switch (true) {
      case !image:
        return (
          <UploadImage
            onFileInputChange={onFileInputChange}
            imageType={imageType}
            title={imageTitle}
            genderTitle={genderTitle}
            selectFile={selectFile}
            isMainImage={isMainImage}
          />
        );
      case isImageDeclined(image):
        return (
          <DeclinedImage
            onClick={
              declineActionLabel === declineActionList.upload
                ? selectFile
                : openDeletePhotoModal
            }
            src={image.url}
            contentSize={declineContentSize}
            actionLabel={declineActionLabel}
            declineReason={image.decline_reason}
          />
        );
      default:
        return (
          <ImageActions
            onClick={() => onImageClick(image.id)}
            showUploadIcon={showUploadIcon}
            image={image}
            onDelete={openDeletePhotoModal}
            onMakeTitleClick={onMakeTitleClick}
            selectFile={selectFile}
            actionIconSize={zoomActionIconSize}
            isCurrentTitlePhoto={isCurrentTitlePhoto}
            isPaidOrDelayed={isPaidOrDelayed}
            showTooltip={showTooltip}
            showRating={isMainImage}
            ratingPosition={ratingPosition}
            iconSize={zoomIconSize}
            showRatingHint={showRatingHint}
            genderTitle={genderTitle}
          />
        );
    }
  };

  return (
    <PhotoUploadTool
      ref={photoUploadToolRef}
      onFileInputChange={onFileInputChange}
    >
      {getImageComponent()}
      {isMainImage && !showTooltip && !isPaidOrDelayed && (
        <UnlockPhotoAnalyzer genderTitle={genderTitle} />
      )}
      {(showCropTool || showTitleCropTool) && (
        <CropToolOverlay
          onClose={closeCropTool}
          image={showCropTool ? selectedImageUrl : image}
          type={imageType}
          isCurrentTitlePhoto={isCurrentTitlePhoto}
          skipDefaultCrop={showTitleCropTool}
        />
      )}
      {showDeletePhotoModal && (
        <ModalDeletePhoto
          onClose={closeDeletePhotoModal}
          onDeletePhoto={deletePhoto}
          loading={loading}
        />
      )}
    </PhotoUploadTool>
  );
};

export default memo(ImageWrapper);
