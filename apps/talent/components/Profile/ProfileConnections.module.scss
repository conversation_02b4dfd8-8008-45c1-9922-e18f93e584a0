@use '@styles/mixins' as *;
@use '@styles/variables' as *;

.profile-info-block {
  display: grid;
  gap: 8px;
  scroll-margin-top: 70px;
}

.save-button {
  background-color: $blue-20;
  border-radius: 10px;
  padding: 3px $space-10;
  color: $blue-100;
  font-weight: 600;
  line-height: 16px;
  margin-left: $space-10;
  text-transform: capitalize;
  cursor: pointer;
  border: none;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
}

.form-personal-url-input {
  margin-bottom: 5em;
}

.form-indexing-container {
  .form-indexing-input {
    margin-bottom: 7px;
  }

  .hint {
    display: flex;
    color: $grey-80;
    font-size: 12px;
    font-weight: 300;
    text-align: initial;
    line-height: 1.5;
  }
}

.text-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.form {
  display: flex;
  flex-direction: column;
  gap: $space-15;

  .form-field-label {
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    color: $grey-80;
  }

  .form-notifications-container {
    margin-top: $space-35;
    margin-bottom: $space-10;
  }
}

.profile-section-title {
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  text-transform: uppercase;
  color: $grey-80;
  line-height: 30px;
  font-size: 16px;
  font-weight: 700;
  margin-bottom: $space-5;
}

.edit-button {
  position: relative;
  display: flex;
  margin-left: 12px;
  width: 26px;
  height: 26px;
  background: $grey-20;
  border-radius: 12.9873px;
  transition: all 0.3s ease-in-out;
  cursor: pointer;

  svg {
    position: absolute;
    top: -1px;
    right: -1px;
    width: 24px;
    height: 25px;
    color: $grey-60;
    transition: all 0.3s ease-in-out;
  }

  @include tablet {
    width: 18px;
    height: 18px;
    border-radius: 9px;

    svg {
      width: 17px;
      height: 18px;
      top: -2px;
      right: -1px;
    }
  }

  &:hover {
    background: $grey-40;

    svg {
      color: $grey-80;
    }
  }
}

.profile-section-link {
  color: $black;
  font-size: 16px;
  font-weight: 500;
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
}

.profile-info-row {
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  overflow: hidden;
}

.profile-info-icon {
  width: 27px;
  height: 27px;
  margin-right: $space-15;
}

.personal-link {
  cursor: pointer;
}

.profile-section-empty {
  display: flex;
  flex-flow: column nowrap;
  padding: 23px $space-30;
  line-height: 1.6;
  border: 1px dashed $grey-60;
  border-radius: 10px;
  color: $black;
  cursor: pointer;

  .profile-section-empty-add-button {
    display: flex;
    align-self: flex-start;
    position: relative;
    color: $blue-100;
    font-weight: 700;
    text-decoration: none;

    &::after {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      height: 2px;
      background-color: $blue-100;
      opacity: 0;
    }

    &:hover::after {
      opacity: 1;
    }
  }
}

.soc-icon-link {
  display: flex;
  align-items: flex-end;
  gap: $space-15;

  & img {
    @include desktop {
      width: 28px;
      height: 28px;
    }
  }
}

.profile-soc-link {
  font-weight: 500;
  color: $blue-100;
  line-height: 1.8;
}
