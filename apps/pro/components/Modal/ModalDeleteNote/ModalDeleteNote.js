'use client';
import React, { memo, useState } from 'react';
import { Loading, Modal } from '@components';
import styles from './ModalDeleteNote.module.scss';
import Api from '@services/api';
import { useNotifications } from '@contexts/NotificationContext';
import { ErrorMessage } from '@constants/form';

const ModalDeleteNote = ({ onClose, refreshNotes, id }) => {
  const [loading, setLoading] = useState(false);
  const { setNotification } = useNotifications();

  const onDelete = async () => {
    setLoading(true);
    const response = await Api.clientside(`/notes/${id}`, {
      method: 'DELETE',
    });

    if (response.status !== 'removed') {
      setNotification({
        type: 'error',
        timeout: '5000',
        message: response.message || ErrorMessage.Unexpected,
      });
    } else {
      await refreshNotes();
      setNotification({
        type: 'success',
        timeout: '5000',
        message: 'Note removed',
      });
    }

    onClose();
    setLoading(false);
  };

  return (
    <Modal
      backdropClose={!loading}
      onClose={onClose}
      classNameContainer={styles['modal-container']}
      showDefaultLayout={false}
      floatCloseButton
      hideMobileCloseButton
    >
      {loading ? (
        <Loading minHeight="40px" padding="10px 0 50px" />
      ) : (
        <>
          <div className={styles.container}>
            <span>Do you really want to delete this note?</span>
          </div>
          <div className={styles.actions}>
            <button onClick={onClose} className={styles.action}>
              Cancel
            </button>
            <button onClick={onDelete} className={styles.action}>
              Delete
            </button>
          </div>
        </>
      )}
    </Modal>
  );
};

export default memo(ModalDeleteNote);
