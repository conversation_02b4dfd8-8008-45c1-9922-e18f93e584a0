// This file configures the initialization of Sen<PERSON> on the server.
// The config you add here will be used whenever the server handles a request.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import * as Sentry from '@sentry/nextjs';

Sentry.init({
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
  integrations: [
    Sentry.requestDataIntegration({
      include: {
        cookies: true,
        data: true,
        headers: true,
        ip: true, // If user is unauthenticated, <PERSON><PERSON> uses the IP address as a unique identifier
        query_string: true,
        url: true,
        user: {
          id: true,
          username: false,
          email: false,
        },
      },
      transactionNamingScheme: 'methodPath',
    }),
  ],

  // Sample rate for error events.
  sampleRate: 1.0,

  // Sample rate for other events.
  tracesSampleRate: 1.0,
});
