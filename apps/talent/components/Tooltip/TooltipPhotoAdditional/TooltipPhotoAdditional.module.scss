@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.tooltip-container {
  padding: $space-30 $space-20;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: $space-10;

  .button-unblock {
    padding: $space-5 $space-10;
    text-transform: capitalize;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 14px;
  }
}

.title {
  font-weight: 700;
  font-size: 20px;
  text-align: center;
}

.description {
  font-weight: 400;
  font-size: 16px;
  text-align: center;
}

.button-container {
  display: flex;
  justify-content: center;
  width: 100%;
}

.image {
  width: 100%;
  height: auto;
}
