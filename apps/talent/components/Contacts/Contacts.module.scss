@use '@styles/variables' as *;

.contact-section {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: $space-15;
  font-weight: 300;
}

.main-title {
  font-size: 24px;
  color: $black;
  font-weight: 600;
  line-height: 1.2;
}

.title-container {
  display: flex;
  width: 100%;
  justify-content: center;
  position: relative;
  align-items: center;
  text-align: center;
  padding: 0;

  &::before {
    border-bottom: 1px solid $grey-20;
    content: '';
    flex-grow: 1;
    height: 1px;
    align-content: baseline;
    margin-right: 7px;
    display: block;
  }

  &::after {
    border-bottom: 1px solid $grey-20;
    content: '';
    flex-grow: 1;
    height: 1px;
    align-content: baseline;
    margin-left: 7px;
    display: block;
  }
}

.title {
  font-size: 14px;
}

.contact-info-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.contact-link {
  color: $blue-100;
  font-weight: 400;
  cursor: pointer;
}
