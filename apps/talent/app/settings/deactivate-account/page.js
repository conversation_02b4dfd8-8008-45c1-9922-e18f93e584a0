import DeactivateAccountClient from './_client';
import { cookies, headers } from 'next/headers';
import { checkIsPasswordSet } from '@utils/authHelper';
import { CookieServiceServer } from '@services/cookieServiceServer';
import Api from '@services/api';
import { extractResult } from '@utils/extractPromiseResult';

export const metadata = {
  title: 'Deactivate Account',
};

export default async function DeactivateAccountPage() {
  const cookieStore = await cookies();
  const headerStore = await headers();
  const accountId = CookieServiceServer.getAccountCookie(cookieStore);
  const resolvedUrl = '/settings/deactivate-account';

  const paths = [
    `/accounts/${accountId}/identities`,
    `/accounts/${accountId}/levels/last`,
    `/accounts/${accountId}/levels/upgrade`,
  ];

  const results = await Promise.allSettled(
    paths.map((path) =>
      Api.serverside(path, cookieStore, headerStore, resolvedUrl),
    ),
  );

  const [identities, levelsLast, levelsUpgrade] = results.map((result) =>
    extractResult(result, {}),
  );

  const identifier = identities.items?.length
    ? identities.items[0].identifier
    : '';

  const isPasswordSet = checkIsPasswordSet(
    CookieServiceServer.getAuthenticationCookie(cookieStore),
  );

  const endDate = levelsUpgrade?.end || levelsLast.end;

  return (
    <DeactivateAccountClient
      identifier={identifier}
      isPasswordSet={isPasswordSet}
      endDate={endDate}
    />
  );
}
