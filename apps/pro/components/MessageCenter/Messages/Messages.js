'use client';
import React, { useState, useEffect, useRef } from 'react';
import styles from './Messages.module.scss';
import {
  HeaderMobile,
  Loading,
  MessageLocked,
  Modal,
  ModalReportMessage,
  TalentCard,
} from '../../';
import dayjs from 'dayjs';
import isToday from 'dayjs/plugin/isToday';
import isYesterday from 'dayjs/plugin/isYesterday';
import OutgoingMessage from '../OutgoingMessage/OutgoingMessage';
import IncomingMessage from '../IncomingMessage/IncomingMessage';
import MessageDate from '../MessageDate/MessageDate';
import MessageForm from '../MessageForm/MessageForm';
import Api from '@services/api';
import { extractResult } from '@utils/extractPromiseResult';
import { useNotifications } from '@contexts/NotificationContext';
import { useInterval } from '@utils/useInterval';
import { useMessage } from '@contexts/MessageContext';
import { formatTalentProfileBase } from '@utils/formatTalentProfile';
import useTabInactivity from '@utils/useTabInactivity';

const MESSAGE_POOLING_INTERVAL = 7000;

const Messages = ({ canContactTalent, profileId }) => {
  const [modalContent, setModalContent] = useState('');
  const [showReportMessage, setShowReportMessage] = useState(null);
  const [loadingHistory, setLoadingHistory] = useState(false);
  const [loading, setLoading] = useState(true);
  const [partner, setPartner] = useState({});
  const [messages, setMessages] = useState([]);

  const allMessagesLoaded = useRef(false);
  const historyFetchController = useRef(null);
  const messagesFetchController = useRef(null);
  const scrollToStart = useRef(true);
  const shouldFetchHistory = useRef(false);
  const messagesEndRef = useRef(null);

  const { setNotification } = useNotifications();
  const {
    conversations,
    conversation,
    showConversationSelect,
    toggleConversationSelect,
    conversationSettings,
  } = useMessage();
  const isTabInactive = useTabInactivity();

  const { status } = conversationSettings;

  const hasConversations = conversations?.length > 0;

  dayjs.extend(isToday);
  dayjs.extend(isYesterday);

  useInterval(() => {
    if (
      canContactTalent &&
      conversation.id &&
      !loading &&
      !loadingHistory &&
      !isTabInactive
    ) {
      fetchMessages(conversation.id);
    }
  }, MESSAGE_POOLING_INTERVAL);

  useEffect(() => {
    const profile = conversation?.links?.profile;

    if (!conversation.id || !profile || !canContactTalent) {
      setLoading(false);
      setPartner({});
      setMessages([]);

      return;
    }

    let controllers = [],
      isCancelled = false;

    const fetchConversationData = async (id) => {
      const isActive = profile.active;

      setLoading(true);

      if (isActive) {
        const paths = [
          `/profiles/${id}/messages?expand=message-complaints,casting-calls,video-calls`,
          `/profiles/${id}?expand=personal_url,location,categories,touches`,
        ];

        controllers = paths.map(() => new AbortController());

        const results = await Promise.allSettled(
          paths.map((path, index) =>
            Api.clientside(path, { signal: controllers[index].signal }),
          ),
        );

        const [messagesResponse, profileResponse] = results.map((result) =>
          extractResult(result, {}),
        );

        if (isCancelled) return;

        setMessages(messagesResponse.items || []);
        setPartner({
          ...formatTalentProfileBase(profileResponse),
          isActive,
        });
        setLoading(false);
      } else {
        controllers = [new AbortController()];

        Api.clientside(
          `/profiles/${id}/messages?expand=message-complaints,casting-calls,video-calls`,
          { signal: controllers[0].signal },
        )
          .then((response) => {
            if (isCancelled) return;

            setMessages(response.items || []);
            setPartner({
              ...formatTalentProfileBase(profile),
              isActive,
            });
          })
          .catch(() => {})
          .finally(() => {
            setLoading(false);
          });
      }
    };

    fetchConversationData(conversation.id);

    return () => {
      isCancelled = true;
      controllers.forEach((controller) => controller.abort());
      historyFetchController.current?.abort();
      messagesFetchController.current?.abort();
      scrollToStart.current = true;
      allMessagesLoaded.current = false;
    };
  }, [conversation, canContactTalent]);

  useEffect(() => {
    if (scrollToStart.current && messages.length) {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      scrollToStart.current = false;
    }
  }, [messages]);

  const onScrollToLastMessage = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const fetchMessageHistory = async (id) => {
    if (!id || !messages.length || allMessagesLoaded.current) return;

    const controller = new AbortController();

    historyFetchController.current = controller;
    setLoadingHistory(true);

    Api.clientside(
      `/profiles/${conversation.id}/messages?end=${messages[0].id}&expand=message-complaints,casting-calls,video-calls`,
      { signal: controller.signal },
    )
      .then((response) => {
        if (response.items?.length > 0) {
          setMessages([...response.items, ...messages]);
        } else {
          allMessagesLoaded.current = true;
        }
      })
      .catch(() => {})
      .finally(() => {
        historyFetchController.current = null;
        setLoadingHistory(false);
      });
  };

  const getLastMessageId = () => {
    const length = messages.length;
    const lastMessageId = length ? messages[messages.length - 1].id : null;

    return !lastMessageId && length > 1
      ? messages[messages.length - 2].id
      : lastMessageId;
  };

  const fetchMessages = async (id) => {
    if (!id) return;

    const lastMessageId = getLastMessageId();
    const query = lastMessageId ? `&start=${lastMessageId}` : '';
    const controller = new AbortController();

    messagesFetchController.current = controller;

    Api.clientside(
      `/profiles/${id}/messages?expand=message-complaints,casting-calls,video-calls${query}`,
      { signal: controller.signal },
    )
      .then((response) => {
        const newMessages = response.items || [];

        if (!response.items.length) return;

        if (lastMessageId) {
          setMessages((prev) => [
            ...prev,
            ...newMessages.filter((message) => message.id !== lastMessageId),
          ]);
        } else {
          setMessages(newMessages);
        }
      })
      .catch(() => {})
      .finally(() => {
        messagesFetchController.current = null;
      });
  };

  const deleteMessage = async (id) => {
    if (!id) return;

    const response = await Api.clientside(`/messages/${id}`, {
      method: 'DELETE',
    });

    if (response.status === 'removed') {
      setMessages((prev) => prev.filter((message) => message.id !== id));

      setNotification({
        type: 'info',
        message: 'Message deleted',
        timeout: '3000',
      });
    } else {
      setNotification({
        type: 'error',
        message: response.message || 'Could not delete message',
        timeout: '5000',
      });
    }
  };

  const handleScroll = (e) => {
    if (e.currentTarget.scrollTop === 0 && shouldFetchHistory.current) {
      shouldFetchHistory.current = false;
      fetchMessageHistory(conversation.id);
    }

    if (e.currentTarget.scrollTop > 200) {
      shouldFetchHistory.current = true;
    }
  };

  const handleSubmit = (message) => {
    scrollToStart.current = true;
    if (!messagesFetchController.current) {
      setMessages((prev) => [...prev, message]);
    }
  };

  const openImageAttachment = (src) => {
    setModalContent(src);
  };

  const closeReportMessageModal = (id) => {
    setShowReportMessage(null);

    if (id) {
      setMessages((prev) =>
        prev.map((message) =>
          message.id === id ? { ...message, isReported: true } : message,
        ),
      );
    }
  };

  const generateUnseenHeader = (current, previous) => {
    if (current || (!current && !previous)) {
      return;
    }

    return (
      <div className={styles['unread-separator']}>
        <div className={styles['text-wrap']}>
          <div className={styles['text']}>Unread</div>
        </div>
      </div>
    );
  };

  const isResponse = (item) => {
    return (
      profileId?.toString() !==
      item.links?.author?.href.split('/profiles/')[1].toString()
    );
  };

  const getContent = () => {
    switch (true) {
      case loading:
        return <Loading />;
      case !canContactTalent:
        return (
          <div className={styles['conversation-wrapper']}>
            {!showConversationSelect && hasConversations && (
              <HeaderMobile onClose={toggleConversationSelect} title="Back" />
            )}
            <MessageLocked />;
          </div>
        );
      case !hasConversations:
        return (
          <>
            {!showConversationSelect && (
              <HeaderMobile onClose={toggleConversationSelect} title="Back" />
            )}
            <div className={styles['no-messages']}>
              <p>
                {status
                  ? 'You have no unread conversations'
                  : 'You have no conversations'}
              </p>
            </div>
          </>
        );
      default:
        return (
          <>
            <div className={styles.container}>
              <div className={styles['conversation-wrapper']}>
                {!showConversationSelect && (
                  <HeaderMobile
                    onClose={toggleConversationSelect}
                    title={
                      partner
                        ? `${partner.firstName} ${partner.lastName}`
                        : 'Back'
                    }
                  />
                )}
                <div
                  className={styles['message-container']}
                  onScroll={handleScroll}
                >
                  {loadingHistory && <Loading />}
                  {messages?.map((message, index) => (
                    <div key={`message-${index}`}>
                      <MessageDate
                        current={message.created}
                        previous={index > 0 && messages[index - 1].created}
                      />
                      {isResponse(message) &&
                        generateUnseenHeader(
                          message.seen,
                          (index > 0 &&
                            isResponse(messages[index - 1]) &&
                            messages[index - 1].seen) ||
                            (index > 0 && !isResponse(messages[index - 1])),
                        )}
                      {!isResponse(message) ? (
                        <OutgoingMessage
                          message={message}
                          openImageAttachment={openImageAttachment}
                          onDeleteMessage={deleteMessage}
                        />
                      ) : (
                        <IncomingMessage
                          message={message}
                          partner={`${partner.firstName} ${partner.lastName}`}
                          reportMessage={() => setShowReportMessage(message)}
                          openImageAttachment={openImageAttachment}
                        />
                      )}
                    </div>
                  ))}
                  <div ref={messagesEndRef} />
                </div>
                {messages?.length > 0 && (
                  <MessageForm
                    conversationId={conversation.id}
                    onMessageSuccess={handleSubmit}
                    openImageAttachment={openImageAttachment}
                    isPartnerActive={partner.isActive}
                    onScrollToLastMessage={onScrollToLastMessage}
                  />
                )}
              </div>
              {messages?.length > 0 && (
                <div className={styles['container-right']}>
                  <TalentCard profile={partner} />
                </div>
              )}
              {modalContent && (
                <Modal backdropClose onClose={() => setModalContent('')}>
                  <img
                    className={styles['image-preview']}
                    src={modalContent}
                    alt=""
                  />
                </Modal>
              )}
              {showReportMessage && (
                <ModalReportMessage
                  onClose={closeReportMessageModal}
                  message={showReportMessage}
                  profileId={profileId}
                />
              )}
            </div>
          </>
        );
    }
  };

  return getContent();
};

export default Messages;
