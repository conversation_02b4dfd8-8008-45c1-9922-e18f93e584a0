@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.analyzer-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: $space-20;

  .analyzer-title {
    font-size: 14px;
    font-weight: 600;
  }

  .analyzer-description {
    font-size: 12px;
    font-weight: 400;
    max-width: 175px;
    margin: 0;
  }
}

.analyzer-header {
  display: flex;
  gap: $space-10;
  align-items: center;
  justify-content: space-between;

  .button-unblock {
    padding: $space-5 $space-10;
    text-transform: capitalize;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 14px;
  }
}

.analyzer-header-inner {
  display: flex;
  align-items: center;
  gap: $space-10;
}

.analyzer {
  display: grid;
  grid-template-columns: 90px 130px;
  grid-column-gap: $space-15;
}

.image-status-filling-text {
  font-weight: 500;
  color: $black;
  text-align: left;
  text-transform: capitalize;
  font-size: 12px;
  margin-right: $space-10;
  margin-left: 2px;
}

.filling-row {
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  justify-content: space-between;

  &.filling-row-light {
    order: 1;
  }

  &.filling-row-focus {
    order: 3;
  }

  &.filling-row-feed {
    order: 2;
  }

  &.filling-row-background {
    order: 4;
  }
}

.filling-row-inner {
  display: flex;
  align-items: center;
}

.image-status-line {
  height: 7px;
  width: 7px;
  background-color: $grey-20;
  border-radius: 50%;
}

.filling-row-status-excellent {
  .full {
    background-color: #f2bd00;
  }
}

.filling-row-status-good {
  .full {
    background-color: #f68823;
  }
}

.filling-row-status-bad {
  .full {
    background-color: $red-100;
  }
}

.image-status-filling-status {
  display: grid;
  flex-flow: row nowrap;
  grid-column-gap: $space-5;
  grid-template-columns: 1fr 1fr 1fr;
}

.image-status-filling-icon {
  display: flex;
  width: 15px;
  height: 15px;
  align-items: center;
  justify-content: center;
  background-position: 50% 50%;
  background-repeat: no-repeat;
}

.status-btn-icon-excellent {
  border-bottom-color: $green-100;
}

.status-btn-icon-good {
  border-bottom-color: $blue-100;
}

.status-btn-icon-bad,
.status-btn-icon-locked {
  border-bottom-color: $red-100;
}

.icon-light {
  background-image: url('#{$assetUrl}/assets/icons/icon-status-light.svg');
}

.icon-focus {
  background-image: url('#{$assetUrl}/assets/icons/icon-status-focus.svg');
}

.icon-background {
  background-image: url('#{$assetUrl}/assets/icons/icon-status-bg.svg');
}

.icon-feed {
  background-image: url('#{$assetUrl}/assets/icons/icon-status-composition.svg');
}

.analyzer-container-main {
  .analyzer {
    grid-template-columns: 1fr;
  }

  .image-status-line {
    height: 15px;
    width: 15px;
  }

  .image-status-filling-text {
    font-size: 16px;
  }

  .image-status-filling-icon {
    width: 20px;
    height: 20px;
  }

  .analyzer-title {
    font-size: 16px;
  }

  .analyzer-description {
    font-size: 16px;
    max-width: 100%;
  }

  @include tablet {
    .analyzer {
      grid-template-columns: 90px 130px;
    }

    .image-status-line {
      height: 7px;
      width: 7px;
    }

    .image-status-filling-text {
      font-size: 12px;
    }

    .image-status-filling-icon {
      width: 15px;
      height: 15px;
    }

    .analyzer-title {
      font-size: 14px;
    }

    .analyzer-description {
      font-size: 12px;
      max-width: 175px;
    }
  }
}

.rating-icon {
  width: 50px;
  height: 50px;
  background: $white;

  &.rating-icon-5 {
    width: 74px;
    height: 68px;
  }

  &.rating-icon-main {
    width: 63px;
    height: 63px;

    &.rating-icon-5 {
      width: 84px;
      height: 77px;
    }

    @include tablet {
      width: 50px;
      height: 50px;

      &.rating-icon-5 {
        width: 74px;
        height: 68px;
      }
    }
  }
}

.analyzer-header-main {
  display: flex;
  align-items: center;
  justify-content: space-between;

  @include tablet {
    display: none;
  }
}

.analyzer-header-main-left {
  display: flex;
  align-items: center;
  gap: $space-10;
}

.analyzer-header-main-title {
  font-size: 18px;
  font-weight: 700;
}

.icon-hint-container {
  display: flex;
  align-items: center;
}

.analyzer-basic-description-container {
  .analyzer-title {
    font-size: 14px;
  }

  .analyzer-description {
    font-size: 14px;
    color: $grey-100;
  }
}

.icon-locked-container {
  box-shadow: $shadow-locked-icon;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 46px;
  cursor: pointer;
}

.icon-locked {
  display: flex;
}

.desktop {
  display: none;

  @include tablet {
    display: block;
  }
}

.mobile {
  @include tablet {
    display: none;
  }
}

.modal-overlay {
  padding: $space-20;
}

.modal-container {
  border-radius: 15px;
  max-width: 500px;
}
