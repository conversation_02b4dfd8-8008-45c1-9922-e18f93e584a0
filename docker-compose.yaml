services:
  public:
    command: ["/bin/sh", "-c", "npm run dev"]
#    command: ["/bin/sh", "-c", "npm run build && npm run start"]
    build:
      context: ./apps/public
      dockerfile: ./docker/node/Dockerfile
      target: build-local
    develop:
      watch:
        - action: sync
          x-initialSync: true
          path: ./apps/public
          target: /app
          ignore:
            - node_modules/
            - .next
        - action: sync+restart
          x-initialSync: true
          path: ./apps/public/.env
          target: /app/.env
        - action: rebuild
          path: ./apps/public/package-lock.json
        - action: rebuild
          path: ./apps/public/docker/node/
    container_name: public-ac
    volumes:
      - next_public:/app/.next
    expose:
      - 3000
    networks:
      - proxy
    labels:
      - "traefik.enable=true"
      - "traefik.docker.network=proxy"
      - "traefik.http.routers.next-ac-public.rule=Host(`allcasting.test`)"
      - "traefik.http.routers.next-ac-public.entrypoints=http,https"
      - "traefik.http.routers.next-ac-public.tls=true"
      - "traefik.http.services.next-ac-public.loadbalancer.server.port=3000"
    extra_hosts:
      - "host.docker.internal:host-gateway"

  talent:
    command: ["/bin/sh", "-c", "npm run dev"]
#    command: ["/bin/sh", "-c", "npm run build && npm run start -- -p 3100"]
    build:
      context: ./apps/talent
      dockerfile: ./docker/node/Dockerfile
      target: build-local
    develop:
      watch:
        - action: sync
          x-initialSync: true
          path: ./apps/talent
          target: /app
          ignore:
            - node_modules/
            - .next
        - action: sync+restart
          x-initialSync: true
          path: ./apps/talent/.env
          target: /app/.env
        - action: rebuild
          path: ./apps/talent/package-lock.json
        - action: rebuild
          path: ./apps/talent/docker/node/
    container_name: talent-ac
    volumes:
      - next_talent:/app/.next
    expose:
      - 3100
    networks:
      - proxy
    labels:
      - "traefik.enable=true"
      - "traefik.docker.network=proxy"
      - "traefik.http.routers.next-ac-talent.rule=Host(`talent.allcasting.test`)"
      - "traefik.http.routers.next-ac-talent.entrypoints=http,https"
      - "traefik.http.routers.next-ac-talent.tls=true"
      - "traefik.http.services.next-ac-talent.loadbalancer.server.port=3100"
    extra_hosts:
      - "host.docker.internal:host-gateway"

  pro:
    command: ["/bin/sh", "-c", "npm run dev"]
#    command: ["/bin/sh", "-c", "npm run build && npm run start -- -p 3200"]
    build:
      context: ./apps/pro
      dockerfile: ./docker/node/Dockerfile
      target: build-local
    develop:
      watch:
        - action: sync
          x-initialSync: true
          path: ./apps/pro
          target: /app
          ignore:
            - node_modules/
            - .next
        - action: sync+restart
          x-initialSync: true
          path: ./apps/pro/.env
          target: /app/.env
        - action: rebuild
          path: ./apps/pro/package-lock.json
        - action: rebuild
          path: ./apps/pro/docker/node/
    container_name: pro-ac
    volumes:
      - next_pro:/app/.next
    expose:
      - 3200
    networks:
      - proxy
    labels:
      - "traefik.enable=true"
      - "traefik.docker.network=proxy"
      - "traefik.http.routers.next-ac-pro.rule=Host(`pro.allcasting.test`)"
      - "traefik.http.routers.next-ac-pro.entrypoints=http,https"
      - "traefik.http.routers.next-ac-pro.tls=true"
      - "traefik.http.services.next-ac-pro.loadbalancer.server.port=3200"
    extra_hosts:
      - "host.docker.internal:host-gateway"

  redis:
    image: redis:7.0-alpine
    hostname: redis-host
    expose:
      - 6379
    command: redis-server --save 20 1 --loglevel warning
    volumes:
      - redis_data:/bitnami/redis/data
    networks:
      - proxy

  cdn-allcasting:
    build:
      context: ./apps/cdn # Directory where your Node.js CDN code lives
      dockerfile: ./docker/Dockerfile
      target: development
    container_name: cdn-ac
    expose:
      - 80
    networks:
      - proxy
    volumes:
      - next_public:/app/public/_next:ro
      - next_talent:/app/talent/_next:ro
      - next_pro:/app/pro/_next:ro
      - ./apps/public/public/assets:/app/public/assets/dev:ro
      - ./apps/talent/public/assets:/app/talent/assets/dev:ro
      - ./apps/pro/public/assets:/app/pro/assets/dev:ro
    labels:
      - "traefik.enable=true"
      - "traefik.docker.network=proxy"
      - "traefik.http.routers.next-ac-cdn.rule=Host(`cdn.allcasting.test`)"
      - "traefik.http.routers.next-ac-cdn.tls=true"
    extra_hosts:
      - "host.docker.internal:host-gateway"

volumes:
  redis_data:
    driver: local
  next_public:
  next_talent:
  next_pro:
  assets_public:
  assets_talent:
  assets_pro:

networks:
  proxy:
    external: true
