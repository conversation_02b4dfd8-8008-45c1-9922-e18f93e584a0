@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.card {
  display: flex;
  flex-direction: column;
  width: 100%;

  @include desktop {
    max-height: calc(100vh - 250px);
    overflow-x: auto;
    box-shadow: 0 4px 80px 0 #d4d5d880;
    border-radius: 8px;
  }
}

.header {
  text-transform: uppercase;
  background: $violet-70;
  color: $white;
  font-weight: 700;
  font-size: 14px;
  padding: $space-5;
  text-align: center;
  position: fixed;
  top: 54px;
  width: 100%;

  @include desktop {
    border-top-right-radius: 8px;
    border-top-left-radius: 8px;
    position: initial;
  }
}

.title {
  font-weight: 700;
  font-size: 14px;
  color: $grey-60;
  text-transform: uppercase;
  padding-bottom: $space-15;
  display: inline-flex;
  line-height: 1;
}

.section {
  padding: $space-20 0;
  width: 100%;

  &:not(:last-of-type) {
    border-bottom: 1px solid $grey-20;
  }

  @include desktop {
    padding: $space-20;
  }
}

.image-section {
  display: flex;
  align-items: center;
  gap: $space-15;
}

.column {
  display: flex;
  flex-direction: column;
  gap: $space-5;
}

.image {
  object-fit: cover;
  border-radius: 50%;
}

.link {
  font-weight: 600;
  color: $blue-80;
  font-size: 14px;

  &:hover {
    text-decoration: underline;
  }
}

.name {
  font-weight: 800;
  font-size: 18px;
}

.category-container {
  display: flex;
  flex-wrap: wrap;
  gap: $space-5;
}

.category-item {
  display: inline-block;
  padding: 0 $space-10;
  line-height: 23px;
  border: 1px solid $grey-80;
  border-radius: 40px;
  font-size: 14px;
  color: $grey-100;
  white-space: nowrap;
  font-weight: 400;
}

.casting-call {
  display: flex;
  align-items: center;
  gap: $space-10;
}

.bottom-spacing {
  padding-bottom: $space-20;
}

.inactive {
  color: $grey-80;
  font-size: 14px;
}

.container {
  padding: $space-30 $space-20 70px;

  @include desktop {
    padding: 0;
  }
}
