@use '@styles/mixins' as *;
@use '@styles/variables' as *;

.profile-video-box {
  display: flex;
  flex-flow: column nowrap;
}

.profile-section-empty {
  display: flex;
  padding: 24px $space-30;
  border: 1px dashed $grey-60;
  border-radius: 10px;
  flex-flow: column nowrap;
  justify-content: center;
  align-items: center;
  min-height: 221px;
  cursor: pointer;
}

.profile-video-box-icon {
  margin-bottom: $space-10;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: 50% 50%;

  &.icon-demo-reel {
    width: 77px;
    height: 75px;
    background-image: url('#{$assetUrl}/assets/icons/icon-add-demo-reel.svg');
  }

  &.icon-slate {
    width: 87px;
    height: 55px;
    background-image: url('#{$assetUrl}/assets/icons/icon-add-slate.svg');
  }

  &.icon-ugc-demo-reel {
    width: 73px;
    height: 66px;
    background-image: url('#{$assetUrl}/assets/icons/icon-add-ugc-demo-reel.svg');
  }

  &.icon-new {
    width: 87px;
    height: 55px;
    background-image: url('#{$assetUrl}/assets/icons/icon-add-video.svg');
  }
}

.profile-video-box-add-button {
  position: relative;
  font-size: 14px;
  color: $blue-100;
  font-weight: 700;
  text-decoration: none;
  cursor: pointer;

  &::after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 2px;
    background-color: $blue-100;
    opacity: 0;
  }

  &:hover {
    text-decoration: none;

    &::after {
      opacity: 1;
    }
  }
}
