import UpgradeClient from './_client';
import { cookies, headers } from 'next/headers';
import { extractResult } from '@utils/extractPromiseResult';
import { formatPlans } from '@utils/planSelectHelper';
import Api from '@services/api';
import { CookieServiceServer } from '@services/cookieServiceServer';
import { getResolvedUrl } from '@utils/apiHelper';
import { AmplitudeExperimentHandler } from '@components';
import { AmplitudeExperimentServer } from '@services/amplitudeExperimentServer';

export const metadata = {
  title: 'Subscribe Now',
};

export default async function UpgradePage(req) {
  const searchParams = await req.searchParams;
  const cookieStore = await cookies();
  const headerStore = await headers();
  const accountId = CookieServiceServer.getAccountCookie(cookieStore);
  const userProfiles = CookieServiceServer.getUserProfilesCookie(cookieStore);
  const referer = headerStore.get('referer') || '';
  const resolvedUrl = getResolvedUrl('/upgrade', searchParams);

  const apiPaths = [
    `/accounts/${accountId}/prices`,
    `/promotions?targets[]=talent&types[]=castingcalls&types[]=compcards`,
  ];

  const results = await Promise.allSettled(
    apiPaths.map((path) =>
      Api.serverside(path, cookieStore, headerStore, resolvedUrl),
    ),
  );

  const upgradePlansResult = extractResult(results[0], {});
  const promoResult = extractResult(results[1], {});

  const initialPlans = formatPlans(upgradePlansResult?.prices);
  const promos = promoResult?.data || [];
  const initialDiscount = upgradePlansResult?.discount || {};
  const discountId = initialDiscount?.id;
  const discountExperiment = initialDiscount?.ab_experiment;
  const amplitudeExperimentKey =
    AmplitudeExperimentServer.flagKey.pricesOfferDrop;
  const amplitudeExperimentUser =
    discountId && discountExperiment
      ? {
          user_id: String(accountId),
          user_properties: {
            discount_id: String(discountId),
          },
        }
      : null;

  return (
    <>
      <AmplitudeExperimentHandler
        user={amplitudeExperimentUser}
        flagKey={amplitudeExperimentKey}
      />
      <UpgradeClient
        initialDiscount={initialDiscount}
        initialPlans={initialPlans}
        promos={promos}
        referer={referer}
        hidePlanSelectPromotions={false}
        isPreview={false}
      />
    </>
  );
}
