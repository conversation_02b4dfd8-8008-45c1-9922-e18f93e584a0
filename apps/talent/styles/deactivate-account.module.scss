@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.deactivate-account-section {
  display: flex;
  justify-content: center;
  flex-direction: column;
  padding-bottom: $space-30;

  @include tablet {
    padding: $space-30 0;
    flex-direction: row;
  }
}

.deactivate-account-container {
  display: flex;
  flex-direction: column;
  text-align: center;
  gap: $space-30;

  @include tablet {
    max-width: 600px;
  }
}

.deactivate-account-container-success {
  display: flex;
  flex-direction: column;
  text-align: center;
  gap: $space-20;
  padding: $space-20;

  @include tablet {
    gap: $space-40;
    max-width: 600px;
  }
}

.deactivate-account-title {
  font-size: 30px;
  line-height: 1.2;
  font-weight: 700;
  margin: 0;
  display: none;

  @include tablet {
    display: initial;
  }
}

.deactivate-account-title-success {
  line-height: 1.2;
  font-weight: 700;
  margin: 0;
  font-size: 18px;

  @include tablet {
    font-size: 24px;
  }
}

.deactivate-account-description {
  background-color: $grey-10;
  padding: $space-15 $space-20;
  border-bottom: 1px solid $grey-60;
  text-align: left;
  font-weight: 300;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: $space-20;

  @include tablet {
    background-color: $white;
    padding: 0;
    border-bottom: none;
    text-align: center;
  }
}

.deactivate-account-form {
  background-color: $white;
  gap: $space-25;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 $space-20;

  @include tablet {
    border-radius: 10px;
    padding: $space-40 $space-50;
    box-shadow: $shadow-card-container;
  }
}

.deactivate-account-form-label {
  font-weight: 300;
  font-size: 16px;
  line-height: 18px;
  text-align: left;
}

.link {
  display: block;
  color: $blue-100;
  font-size: 16px;
  font-weight: 400;
  margin-bottom: $space-20;
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
}

.deactivate-button {
  color: $red-60;
  font-weight: 300;
  text-align: center;
  border: none;
  background-color: transparent;
  font-size: 16px;
  cursor: pointer;

  &:disabled {
    opacity: 0.3;
    cursor: default;
  }
}

.deactivate-account-form-row {
  display: flex;
  gap: $space-20;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  flex-flow: column wrap;

  @include tablet {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
}
