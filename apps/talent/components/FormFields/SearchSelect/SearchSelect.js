'use client';
import React, { memo, useCallback, useEffect, useRef, useState } from 'react';
import styles from './SearchSelect.module.scss';
import cn from 'classnames';

const SearchSelect = ({
  name,
  options,
  placeholder,
  onChange,
  setFormFieldTouched,
  onAdd,
  hint,
  charCounter,
  maxChars,
}) => {
  const [showOptions, setShowOptions] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const inputRef = useRef(null);
  const optionsRef = useRef([]);

  const focusOption = useCallback(
    (index) => {
      const currentOption = optionsRef.current.find(
        (node) => node?.id === `${name}-option-${index}`,
      );

      currentOption?.focus();
    },
    [name],
  );

  useEffect(() => {
    const closeOptions = (e) => {
      if (!String(e.target.id).includes(name)) {
        setShowOptions(false);
      }
    };

    if (showOptions) {
      document.addEventListener('click', closeOptions);
    } else {
      document.removeEventListener('click', closeOptions);
    }

    return () => document.removeEventListener('click', closeOptions);
  }, [name, setFormFieldTouched, showOptions]);

  const toggleShowOptions = () => {
    setShowOptions(!showOptions);
  };

  const onTriggerClick = (event) => {
    event.preventDefault();
    focusInput();
    setShowOptions(!showOptions);
  };

  const onInputValueChange = (event) => {
    setInputValue(event.target.value);
    onChange(event.target.value);
  };

  const selectOption = (value) => {
    setInputValue('');
    addOption(value);
  };

  const addOption = (selectedOption) => {
    if (selectedOption) {
      onAdd(selectedOption);
      setInputValue('');
    }
  };

  const focusInput = () => {
    inputRef?.current?.focus();
  };

  const onOptionClick = (value) => {
    selectOption(value);
    setShowOptions(false);
  };

  const onInputBlur = (event) => {
    if (
      event.relatedTarget &&
      !event.relatedTarget.id.includes(`${name}-option`)
    ) {
      setShowOptions(false);
    }
  };

  const onInputKeyDown = (event) => {
    switch (event.code) {
      case 'ArrowDown':
        event.preventDefault();

        if (!showOptions) {
          setShowOptions(true);
        }

        focusOption(0);
        break;
      case 'ArrowUp':
        if (showOptions) {
          setShowOptions(false);
        }
        break;
      case 'Enter':
        addOption(inputValue);
        setShowOptions(false);
        break;
      case 'ShiftLeft':
      case 'ShiftRight':
      case 'Tab':
        break;
      default:
        if (!showOptions) {
          setShowOptions(true);
        }
        break;
    }
  };

  const onOptionKeyDown = (event, index, value) => {
    event.preventDefault();

    switch (event.code) {
      case 'ArrowUp':
        if (index !== 0) {
          focusOption(index - 1);
        } else if (index === 0) {
          focusInput();
        }
        break;
      case 'ArrowDown':
        if (index !== options.length - 1) {
          focusOption(index + 1);
        }
        break;
      case 'Enter':
      case 'Space':
        selectOption(value);
        toggleShowOptions();
        focusInput();
        break;
      case 'Tab':
        toggleShowOptions();
        focusInput();
        break;
      default:
        break;
    }
  };

  const Highlight = ({ searchValue = '', text = '' }) => {
    if (!searchValue) {
      return text;
    } else {
      const pattern = searchValue.replace(/([.?*+^$[\]\\(){}|-])/g, '\\$1');
      const regExp = new RegExp(`(${pattern})`, 'i');
      const fragments = String(text).split(regExp);

      return fragments.map((fragment, index) =>
        regExp.test(fragment) ? <b key={index}>{fragment}</b> : fragment,
      );
    }
  };

  return (
    <div className={styles['select-container']}>
      <div
        id={`${name}-container`}
        onClick={onTriggerClick}
        className={cn(styles['select-field'], {
          [styles.open]: showOptions,
        })}
      >
        <input
          ref={inputRef}
          id={name}
          className={styles.select}
          type="text"
          onBlur={onInputBlur}
          value={inputValue || ''}
          onChange={onInputValueChange}
          maxLength={maxChars}
          onKeyDown={onInputKeyDown}
        />
        {inputValue ? (
          <button
            className={styles['button-add']}
            onClick={() => addOption(inputValue)}
          >
            Add
          </button>
        ) : (
          <div
            id={`${name}-select-trigger`}
            className={cn(styles['select-trigger'], {
              [styles.open]: showOptions,
            })}
          ></div>
        )}
        <div
          id={`${name}-select-focus-border`}
          className={styles['focus-border']}
        ></div>
        <label
          id={`${name}-label`}
          className={cn(styles.label, {
            [styles['floating-label']]: inputValue || showOptions,
          })}
          htmlFor={name}
        >
          {placeholder}
        </label>
        {showOptions && (
          <div className={styles.options}>
            {options?.length ? (
              options.map((option, i) => (
                <div
                  tabIndex={i}
                  ref={(element) => (optionsRef.current[i] = element)}
                  id={`${name}-option-${i}`}
                  onClick={() => onOptionClick(option.value)}
                  key={i}
                  onKeyDown={(e) => onOptionKeyDown(e, i, option.value)}
                  className={styles.option}
                >
                  <Highlight searchValue={inputValue} text={option.title} />
                </div>
              ))
            ) : (
              <div className={styles.option}>No options</div>
            )}
          </div>
        )}
      </div>
      <div className={styles.hint}>
        {hint && <span>{hint}</span>}
        {charCounter && (
          <span className={styles.counter}>
            {inputValue.length} / {maxChars}
          </span>
        )}
      </div>
    </div>
  );
};

export default memo(SearchSelect);
