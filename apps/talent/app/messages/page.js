import React from 'react';
import { cookies, headers } from 'next/headers';
import { getResolvedUrl } from '@utils/apiHelper';
import { CookieServiceServer } from '@services/cookieServiceServer';
import Api from '@services/api';
import { MessageProvider } from '@contexts/MessageContext';
import { MainLayout, MessageCenter } from '@components';

export const metadata = {
  title: 'Messages',
};

export default async function MessagesPage(req) {
  const searchParams = await req.searchParams;
  const { chatbot } = searchParams;
  const resolvedUrl = getResolvedUrl('/messages', searchParams);
  const cookieStore = await cookies();
  const headerStore = await headers();
  const accountLevel = CookieServiceServer.getAccountLevelCookie(cookieStore);
  const profileId = CookieServiceServer.getProfileCookie(cookieStore);
  const isBotAvailable = chatbot === '1';

  const query = new URLSearchParams({
    expand: 'profile',
    profile: `${profileId}`,
    limit: '10',
  });

  const conversations = await Api.serverside(
    `/conversations?${query.toString()}`,
    cookieStore,
    headerStore,
    resolvedUrl,
  );

  const initialConversations = conversations.items;
  const canViewMessages = accountLevel?.isPaidOrDelayed;

  return (
    <MessageProvider
      canViewMessages={canViewMessages}
      profileId={profileId}
      initialConversations={initialConversations}
    >
      <MainLayout
        isDefaultHeaderVisible
        isUserMenuVisible
        isMobileMenuVisible
        isSaleHeaderVisible
      >
        <MessageCenter
          profileId={profileId}
          canViewMessages={canViewMessages}
          isBotAvailable={isBotAvailable}
        />
      </MainLayout>
    </MessageProvider>
  );
}
