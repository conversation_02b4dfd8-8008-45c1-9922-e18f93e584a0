@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.header-container {
  position: sticky;
  z-index: 3;
  top: 0;
  bottom: auto;
  box-shadow: $shadow-card-container;
  background-color: $white;
}

.header {
  display: flex;
  justify-content: center;
  align-items: center;
  align-self: auto;
  max-width: $content-max-width;
  margin-left: auto;
  margin-right: auto;
  height: 48px;

  @include desktop {
    height: 70px;
    padding: 23px $space-60;
  }
}

.logo {
  width: 93px;
  height: 21px;
  color: $violet-100;
  cursor: pointer;
}

.logo-link {
  line-height: 1;
}
