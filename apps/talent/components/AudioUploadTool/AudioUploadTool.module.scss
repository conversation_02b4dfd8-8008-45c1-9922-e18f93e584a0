@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.audio-upload-error-modal {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: $space-20;
}

.audio-upload-error {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: $space-20 $space-20 0;
  gap: $space-20;
}

.audio-upload-error-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  justify-content: center;
  align-items: stretch;
  width: 100%;
  border-top: 1px solid $grey-60;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  background-color: $white;
}

.audio-upload-error-action-button {
  background-color: transparent;
  padding: $space-20;
  border: none;
  font-weight: 300;
  font-size: 16px;
  cursor: pointer;

  &:first-of-type {
    border-right: 1px solid $grey-60;
  }

  &:hover {
    text-decoration: underline;
  }
}

.audio-upload-error-action-button-upload {
  color: $blue-100;
  font-weight: 400;
}

.audio-upload-error-title {
  color: $red-60;
  font-weight: 700;
  font-size: 18px;
}

.file-input {
  display: none;
}

.upload-error-modal-overlay {
  padding: 0 $space-20;
}

.upload-error-modal-container {
  border-radius: 10px;
}

.upload-error-modal {
  max-width: 420px;
  width: 100%;
  height: fit-content;
}
