@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.modal-container {
  position: absolute;
  bottom: 0;
  padding: 0;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  width: 100%;

  @include tablet {
    position: relative;
    max-width: 620px;
    padding: $space-20;
    border-radius: 10px;
  }
}

.footer {
  text-align: center;
  margin-top: $space-30;
  display: flex;
  flex-direction: column;
  gap: $space-10;
  align-items: center;
}

.remove-btn {
  color: $red-80;
  display: inline-block;
  cursor: pointer;

  &.disabled {
    pointer-events: none;
    opacity: 0.5;
  }
}
