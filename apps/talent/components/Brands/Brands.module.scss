@use '@styles/mixins' as *;

.catfish {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 94px;
  height: 30.36px;

  & > svg {
    height: 45.8%;
  }

  & > img {
    height: 100%;
    width: auto;
  }
}

.brands {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  opacity: 0.4;
  gap: 5px 10px;

  @include desktop {
    gap: 25px;
  }
}

.brands-text {
  font-size: 14px;
  width: 100%;

  @include desktop {
    width: initial;
    display: initial;
  }
}
