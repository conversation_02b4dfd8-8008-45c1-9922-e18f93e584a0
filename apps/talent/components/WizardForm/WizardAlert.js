import React, { useState } from 'react';
import styles from './WizardAlert.module.scss';
import IconClose from 'public/assets/icons/icon-close.svg';
import { useField } from 'formik';
import TriangleRight from 'public/assets/svg/triangle-right.svg';
import cn from 'classnames';

const WizardAlertBox = ({
  className,
  onClose,
  title,
  text,
  left = false,
  right = false,
}) => {
  return (
    <div
      className={cn(styles.box, className, {
        [styles.left]: left,
        [styles.right]: right,
      })}
    >
      <button type="button" className={styles.button} onClick={onClose}>
        <IconClose className={styles.close} />
      </button>
      <p className={styles.title}>{title}</p>
      <p className={styles.text}>{text}</p>
      <TriangleRight className={styles.triangle} />
    </div>
  );
};

const WizardAlert = ({
  className,
  field,
  title,
  text,
  left = false,
  right = false,
}) => {
  const [show, setShow] = useState(true);
  const [{ value }, { error }] = useField(field);

  const isValid = !error && value;
  const visible = show && isValid;

  if (!visible) return null;

  return (
    <WizardAlertBox
      className={className}
      onClose={() => setShow(false)}
      title={title}
      text={text}
      left={left}
      right={right}
    />
  );
};

export default WizardAlert;
