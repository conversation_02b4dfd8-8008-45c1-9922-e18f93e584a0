'use client';
import { memo, useEffect, useState } from 'react';
import styles from './SearchInput.module.scss';
import Image from 'next/image';

const SearchInput = ({ placeholder, onValueChange }) => {
  const [searchValue, setSearchValue] = useState('');

  useEffect(() => {
    onValueChange(searchValue);
  }, [searchValue]);

  const onChange = (e) => {
    setSearchValue(e.target.value);
  };

  const clearSearchValue = () => {
    setSearchValue('');
  };

  return (
    <div className={styles['search-input-container']}>
      <label className={styles['search-input-label']}>
        <input
          value={searchValue}
          className={styles['search-input']}
          type="text"
          placeholder={placeholder}
          onChange={onChange}
        />
        {searchValue && (
          <button
            className={styles['search-input-clear-button']}
            onClick={clearSearchValue}
          >
            <Image
              className={styles['clear-icon']}
              src={'/assets/icons/icon-close.svg'}
              alt="icon close"
              width={10}
              height={10}
            />
          </button>
        )}
      </label>
    </div>
  );
};

export default memo(SearchInput);
