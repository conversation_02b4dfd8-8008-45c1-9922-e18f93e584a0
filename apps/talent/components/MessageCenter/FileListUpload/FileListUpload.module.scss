@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.container {
  display: block;
  width: 100%;
}

.file-input {
  position: absolute;
  width: 0;
  height: 0;
  opacity: 0;
}

.files {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-start;

  &.attached {
    min-height: 40px;
  }

  @include desktop {
    padding: 0;
  }
}

.file {
  width: 30px;
  height: 30px;
  position: relative;
  margin-right: 7px;
  margin-bottom: 5px;
  display: flex;
  justify-content: center;
  background: $grey-20;

  .attached-label {
    display: none;
  }
}

.delete-btn {
  position: absolute;
  top: -3px;
  right: -8px;
  width: 16px;
  height: 16px;
  color: $red-60;
  opacity: 0.7;
  cursor: pointer;

  &:hover {
    opacity: 1;
  }
}
