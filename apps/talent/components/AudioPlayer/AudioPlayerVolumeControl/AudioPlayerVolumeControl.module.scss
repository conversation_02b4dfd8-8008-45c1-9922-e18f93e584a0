/* stylelint-disable */
@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.volume {
  align-items: center;
  gap: $space-5;
  display: flex;

  &.hidden {
    display: none;
  }
}

.volume-slider-container {
  border-radius: 10px;
  background-color: $white;
  align-items: center;
  display: flex;
  width: 0;
  padding: 0;
  transition: all 0.3s ease-in-out;

  &.visible {
    width: 100px;
    padding: $space-5;
    transition: all 0.3s ease-in-out;
  }

  input[type='range'],
  input[type='range']::-webkit-slider-runnable-track,
  input[type='range']::-webkit-slider-thumb {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    font: inherit;
    cursor: pointer;
  }
}

.volume-slider {
  width: 100%;
  height: 8px;
  background-color: $grey-40;
  border-radius: 4px;
  overflow: hidden;
}

.volume-slider::-webkit-slider-thumb {
  width: 0;
  background-color: transparent;
  border-radius: 0;
  box-shadow: $shadow-audio-player-progress;
}

.volume-slider::-moz-range-thumb {
  width: 0;
  background-color: transparent;
  border-radius: 0;
  box-shadow: $shadow-audio-player-progress;
  border: none;
}

.icon {
  cursor: pointer;
  transition: all 0.1s ease-in-out;

  &:hover {
    opacity: 0.8;
  }
}
