import { CookieService } from '@services/cookieService';
import { formatAccountLevel } from '@utils/formatAccountLevel';
import Api from '@services/api';

export function extractAccountIdFromToken(token) {
  return extractFromToken(token, 'Account');
}

export function extractExpiresFromAuthenticationToken(token) {
  return extractFromToken(token, 'Expires');
}

export function responseExtractProfiles(response) {
  const isTalent = responseExtractType(response) === 'talent';

  return response.links?.profiles?.items.map((el) => {
    return {
      titlePhotoUrl: el.title_photo_url,
      profileUrl: el.links?.personal_url?.path
        ? `${isTalent ? '/profile' : ''}/${el.links?.personal_url?.path}`
        : `/profile/${el.id}`,
      id: el.id,
      firstName: el.firstname,
      lastName: el.lastname,
      fullName: `${el.firstname} ${el.lastname}`,
      birthday: el.birthday,
      gender: el?.gender?.title?.toLowerCase() || '',
      href: el?.href || '',
      rating: el?.rating || 0,
      zipCode: el.links?.location?.links?.zip?.code,
      city: el.links?.location?.links?.city?.slug,
      phone: el.links?.touches?.links?.phone?.value || '',
      clientId: el.client_id,
      isEmailValid:
        !!el?.links?.touches?.links?.email?.value &&
        !el.links.touches.links.email.value.endsWith(
          '@privaterelay.appleid.com',
        ),
    };
  });
}

export function responseExtractType(response) {
  return response.links?.profiles?.items[0]?.rel || 'talent';
}

export function responseExtractProfileId(response) {
  return response.links?.profiles?.items[0]?.id || 0;
}

export const checkIsPasswordSet = (token) => {
  const identities = extractFromToken(token, 'Identities');

  return identities ? String(identities).includes('standard') : true;
};

export function responseAccountLevel(response) {
  const status = response.links?.account_level?.links?.level?.status || null;

  return formatAccountLevel(status);
}

export async function callbackAccount(
  accountId,
  cookies,
  decodedAuthenticationToken,
  volatileToken,
  path,
) {
  const headers = new Headers();

  headers.set(CookieService.cookie.authentication, decodedAuthenticationToken);
  headers.set(CookieService.cookie.volatile, volatileToken);

  return await Api.serverside(
    `/accounts/${accountId}?expand=profiles,personal_url,level,location,touches`,
    cookies,
    headers,
    path,
  );
}

export const extractFromToken = (token = '', property = null) => {
  const tokenValues = token ? token.replaceAll(';', '').split(' ') : [];
  const extractedValue = tokenValues.find((it) => it.includes(property));
  const extractedValuePair = extractedValue ? extractedValue.split('=') : null;

  return extractedValuePair?.length === 2 ? extractedValuePair[1] || 0 : 0;
};
