@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.settings-sidebar {
  width: 100%;
  background-color: $grey-10;
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: $space-20;
  gap: $space-15;
}

.settings-sidebar-title {
  font-size: 18px;
  font-weight: 900;
}

.settings-sidebar-item {
  display: flex;
  flex-direction: column;
  gap: $space-15;
}

.settings-sidebar-item-title {
  color: $grey-80;
  font-size: 12px;
  text-transform: uppercase;
  font-weight: 300;
}

.settings-sidebar-link-container {
  display: flex;
  flex-direction: column;
}

.settings-sidebar-link {
  display: flex;
  align-items: center;
  padding: 3px $space-20;
  gap: 10px;
  border: none;
  font-size: 14px;
  font-weight: 300;
  margin: 0 (-$space-20);
  cursor: pointer;
  background-color: transparent;
  color: $black;

  &.settings-sidebar-link-active {
    background-color: $white;
  }
}

.deactivate-account-button {
  border: 1px solid $black;
  background-color: transparent;
  border-radius: 38px;
  font-size: 14px;
  opacity: 0.3;
  text-transform: lowercase;
  font-weight: 300;
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 200px;
  width: 100%;
  padding: $space-5 33px $space-5 $space-5;
  cursor: pointer;
  color: $black;

  &:hover {
    opacity: 1;
  }
}
