@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.declined-image-tooltip {
  padding: $space-20;
  display: flex;
  flex-direction: column;
  gap: $space-10;

  .icon-blocked {
    align-self: center;
    width: 20px;
    height: 20px;

    @include tablet {
      width: 18px;
      height: 18px;
    }
  }
}

.tooltip-title {
  display: flex;
  align-items: center;
  gap: $space-5;
  font-size: 20px;
  font-weight: 700;
  justify-content: center;

  @include tablet {
    justify-content: flex-start;
    font-size: 16px;
    font-weight: 600;
  }
}

.tooltip-description {
  font-weight: 400;
  font-size: 16px;
  text-align: center;

  @include tablet {
    font-size: 12px;
    text-align: left;
  }
}

.button-container {
  display: flex;
  justify-content: center;
  width: 100%;
  padding-bottom: $space-20;

  .action-button {
    text-transform: capitalize;
    font-size: 14px;
    padding: 12px $space-20;
  }

  @include tablet {
    display: none;
  }
}
