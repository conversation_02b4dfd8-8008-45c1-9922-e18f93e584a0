'use client';
import React, { memo } from 'react';
import cn from 'classnames';
import IconPremium from '../../public/assets/icons/icon-premium.svg';
import styles from './PremiumFeature.module.scss';
import { Button, ElementViewed } from '../../components';
import { useRouter } from 'next/navigation';
import { Amp } from '@services/amp';
import useTrackElementActions from '@utils/useTrackElementActions';

function PremiumFeature({ isModal = false, onClose, premiumTrackingName }) {
  const router = useRouter();
  const name = premiumTrackingName || 'Unlock premium feature';
  const context = isModal
    ? Amp.element.context.ctaPopup
    : Amp.element.context.ctaBlock;

  const { onTrackClick } = useTrackElementActions({
    name,
    type: isModal ? Amp.element.type.popup : Amp.element.type.block,
    context,
    autoTrackEnabled: isModal,
  });

  const onClick = () => {
    onTrackClick();

    if (isModal) {
      onClose();
    }

    router.push('/upgrade');
  };

  return (
    <div
      className={cn(styles['premium-feature-container'], {
        [styles['modal-view']]: isModal,
      })}
    >
      <div className={styles['premium-feature']}>
        <IconPremium />
        <p>You&apos;ve discovered a</p>
        <h1 className={styles['premium-feature-title']}>Premium Feature</h1>
        <p>
          To unlock and enjoy all the benefits of allcasting, please subscribe.
        </p>
        {isModal ? (
          <Button
            className={styles.button}
            color="blue"
            label="SUBSCRIBE NOW"
            minWidth="200px"
            onClick={onClick}
          />
        ) : (
          <ElementViewed
            name={name}
            type={Amp.element.type.block}
            context={context}
          >
            <Button
              className={styles.button}
              color="blue"
              label="SUBSCRIBE NOW"
              minWidth="200px"
              onClick={onClick}
            />
          </ElementViewed>
        )}
      </div>
    </div>
  );
}

export default memo(PremiumFeature);
