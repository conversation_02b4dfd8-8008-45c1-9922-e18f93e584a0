@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.mobile-hidden {
  display: none;

  @include desktop {
    display: block;
  }
}

.link:hover {
  text-decoration: none;
}

.sale-header {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  background: $green-80 url('#{$assetUrl}/assets/header/sale-background.png')
    center no-repeat;
  background-size: auto 100%;
  font-weight: 400;
  color: $white;
  line-height: 1;
  padding: $space-10 $space-20;
  cursor: pointer;
  z-index: 1001;
  position: sticky;
  top: 0;
  height: 84px;

  @include desktop {
    flex-direction: row;
    height: 44px;
    gap: $space-20;
  }
}

.sale-header-title-container {
  display: flex;
  align-items: flex-end;
  gap: 4px;
  line-height: 1;
  margin-top: $space-10;

  @include desktop {
    margin-top: 0;
  }
}

.sale-header-title {
  font-weight: 900;
  text-transform: uppercase;
  font-size: 24px;
}

.sale-header-sub-title {
  font-size: 24px;

  @include desktop {
    font-size: 16px;
    line-height: 1.2;
  }
}
