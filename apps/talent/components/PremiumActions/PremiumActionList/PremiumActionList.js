'use client';
import styles from './PremiumActionList.module.scss';
import { memo } from 'react';
import { useLiveChat } from '@contexts/LiveChatContext';
import { LIVE_CHAT_GROUP, LIVE_CHAT_VISIBILITY } from '@constants/liveChat';
import { Amp } from '@services/amp';
import { useNotifications } from '@contexts/NotificationContext';
import cn from 'classnames';
import { FeatureManager } from '@services/featureManager';
import { useFeature } from '@contexts/FeatureContext';
import { useAuth } from '@contexts/AuthContext';
import dayjs from 'dayjs';
import Api from '@services/api';
import { ErrorMessage } from '@constants/form';

const PremiumActionList = ({ onClose }) => {
  const { initiateLiveChat, show, updateVisibility } = useLiveChat();
  const { setNotification } = useNotifications();
  const { accountId, accountLevelVerify } = useAuth();
  const {
    premiumSupportEnabled,
    premiumInstagramBoostEnabled,
    premiumInstagramBoostDate,
  } = useFeature();

  const onClick = (elementName) => {
    Amp.track(Amp.events.elementClicked, {
      name: elementName,
      scope: Amp.element.scope.global,
      section: Amp.element.section.premiumActions,
      type: Amp.element.type.button,
    });

    onClose();
  };

  const onOpenLiveChat = () => {
    if (show) {
      updateVisibility(LIVE_CHAT_VISIBILITY.Maximized);
    } else {
      initiateLiveChat(
        true,
        LIVE_CHAT_GROUP.AllcastingPremium,
        LIVE_CHAT_VISIBILITY.Maximized,
      );
    }

    onClick(`${FeatureManager.featureKeys.premiumSupport} Live Chat`);
  };

  const onRequestInstagramBoost = async () => {
    if (!premiumInstagramBoostDate) {
      const body = new FormData();

      body.append('feature_name', 'promote_instagram_story');

      const featureRequestResponse = await Api.clientside(
        `/accounts/${accountId}/features/requests`,
        {
          method: 'POST',
          body,
        },
      );

      if (featureRequestResponse.status !== 'error') {
        setNotification({
          type: 'success',
          message:
            'Your claim is registered and will be processed as soon as possible, which usually takes up to 7 days',
          timeout: '20000',
        });

        await accountLevelVerify();
      } else {
        setNotification({
          type: 'info',
          message: featureRequestResponse.message || ErrorMessage.Unexpected,
        });
      }

      onClick(FeatureManager.featureKeys.premiumInstagramBoost);
    }
  };

  return (
    <div className={styles['premium-actions']}>
      {premiumSupportEnabled && (
        <>
          <a
            className={styles['premium-action']}
            href={`tel:+***********`}
            onClick={() => {
              onClick(`${FeatureManager.featureKeys.premiumSupport} Call`);
            }}
            title={'Call for toll free priority support'}
          >
            Call <span className={styles['text-light']}>(*************</span>
          </a>
          <div
            className={styles['premium-action']}
            onClick={onOpenLiveChat}
            title={'Instant chat with a support agent'}
          >
            Open Live Chat
          </div>
        </>
      )}
      {premiumInstagramBoostEnabled && (
        <div
          className={cn(styles['premium-action'], {
            [styles.disabled]: !!premiumInstagramBoostDate,
          })}
          onClick={onRequestInstagramBoost}
          title={
            'Get spotlighted on our Instagram profile. You must have a title photo and your Instagram link added to your profile.'
          }
        >
          {premiumInstagramBoostDate
            ? `Boost requested on ${dayjs(premiumInstagramBoostDate).format(
                'MM/DD/YYYY',
              )}`
            : 'Request Instagram Boost'}
        </div>
      )}
    </div>
  );
};

export default memo(PremiumActionList);
