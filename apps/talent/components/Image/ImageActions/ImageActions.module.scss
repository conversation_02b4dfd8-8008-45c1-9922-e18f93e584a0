@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.actions-image-inner-container::before {
  content: '';
  position: absolute;
  inset: 0;
  background-color: rgb($black, 0.5);
  background-image: url('#{$assetUrl}/assets/icons/icon-zoom.svg');
  background-repeat: no-repeat;
  background-position: 50% 50%;
  background-size: 70px 70px;
  opacity: 0;
  transition: opacity ease-in-out 0.3s;
}

.profile-photo-actions-left {
  display: flex;
  align-items: center;
  gap: $space-5;
}

.profile-photo-actions {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 3;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  transition: opacity ease-in-out 0.3s;
  opacity: 0;
  cursor: default;
  backdrop-filter: blur(4px);
  background-color: rgb($black, 0.2);

  img {
    cursor: pointer;
  }
}

.small {
  padding: $space-10;

  .icon {
    width: 25px;
    height: 25px;
  }

  .icon-title {
    height: 25px;
    width: 32px;
  }
}

.large {
  padding: $space-20 $space-10;
  justify-content: flex-end;
  gap: $space-10;

  .icon {
    width: 35px;
    height: 35px;
  }

  .icon-title {
    height: 35px;
    width: 42px;
  }

  .profile-photo-actions-left {
    gap: $space-10;
  }
}

.actions-image-inner-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: $grey-10;

  &.actions-image-small::before {
    background-size: 50px 50px;
  }

  &.actions-image-medium::before {
    background-size: 70px 70px;
  }

  &.actions-image-large::before {
    background-size: 120px 120px;
  }

  .image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.actions-image-container {
  position: relative;
  padding-top: 133.124%;
  margin-bottom: -$space-10;

  @include tablet {
    margin: 0;
  }

  &:hover {
    cursor: pointer;

    .actions-image-inner-container::before {
      z-index: 2;
      opacity: 1;
    }

    .actions-image-inner-container-main.actions-image-inner-container::before {
      cursor: default;
      z-index: 0;
      opacity: 0;
    }

    .profile-photo-actions {
      z-index: 3;
      opacity: 1;
    }
  }
}

.actions-image-inner-container-main.actions-image-inner-container::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 45%;
  background-image: $gradient-image-inner-container;
}

.mobile {
  @include desktop {
    display: none;
  }
}

.desktop {
  display: none;

  @include desktop {
    display: flex;
  }
}

.image-rating {
  position: absolute;
  z-index: 3;
  background-color: $white;
  padding: $space-5 $space-10;
  border-radius: 20px;
  display: none;

  @include tablet {
    display: initial;
  }

  &.center {
    bottom: 10px;
    left: 9px;
  }

  &.left {
    bottom: 20px;
    left: 20px;
  }
}

.analyzer-container {
  padding: $space-20;
  border-radius: 10px 10px 0 0;
  position: relative;
  z-index: 2;
  background-color: $white;
  display: none;
  box-shadow: $shadow-analyzer-container;

  @include tablet {
    border-radius: 0;
    display: block;
  }
}

.edit-button {
  position: absolute;
  top: 20px;
  right: 20px;
  background-color: $white;
  border-radius: 20px;
  border: none;
  padding: 7px $space-25;
  font-size: 14px;
  font-weight: 400;
  cursor: pointer;
  box-shadow: $shadow-btn-edit;

  @include tablet {
    display: none;
  }
}
