import React, { memo, useEffect, useRef, useState } from 'react';
import { Form, Formik } from 'formik';
import * as Yup from 'yup';
import styles from './RegistrationFormPro.module.scss';
import {
  Button,
  CheckboxFormik,
  SocialButtons,
  InputFormik,
  Loading,
  Modal,
  PasswordInputFormik,
  PrivacyPolicy,
  TermsOfUse,
} from '../../index';
import cn from 'classnames';
import { maskPhoneNumber } from '../../../utils/maskPhoneNumber';
import Api from '../../../services/api';
import { useRouter } from 'next/router';
import { useAnalytics } from 'use-analytics';
import {
  GTM_EVENTS,
  GTM_CATEGORIES,
  GTM_ACTIONS,
} from '../../../constants/analytics';
import { useAuth } from '../../../contexts/AuthContext';
import ApiNoCache from '../../../services/apiNoCache';
import { CookieService } from '../../../services/cookieService';
import { cacheTest } from '../../../utils/formikHelpers';
import { Amp } from '../../../services/amp';
import {
  EMAIL_REGEX,
  ErrorMessage,
  NAME_REGEX,
  PASSWORD_REGEX,
  PHONE_NUMBER_REGEX,
} from '../../../constants/form';
import Link from 'next/link';
import * as Sentry from '@sentry/nextjs';

const RegistrationFormPro = ({ isModal, onRedirecting, onClose }) => {
  const [showTerms, setShowTerms] = useState(false);
  const [showPrivacyPolicy, setShowPrivacyPolicy] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const router = useRouter();
  const errorsRef = useRef({});
  const entriesRef = useRef({});
  const isFormStartedRef = useRef(false);
  const locationRef = useRef({ country: '', location: '' });

  const { track } = useAnalytics();
  const { refreshUserProfiles, accountLevelVerify } = useAuth();

  const initialValues = {
    firstName: '',
    lastName: '',
    zip: '',
    email: '',
    password: '',
    agree: false,
    phoneNumber: '',
  };

  const validationSchema = Yup.object({
    firstName: Yup.string()
      .required(ErrorMessage.NameRequired)
      .max(35, ErrorMessage.NamePattern)
      .matches(NAME_REGEX, ErrorMessage.NamePattern),
    lastName: Yup.string()
      .required(ErrorMessage.LastNameRequired)
      .max(35, ErrorMessage.NamePattern)
      .matches(NAME_REGEX, ErrorMessage.NamePattern),
    zip: Yup.string()
      .transform((value) => value?.replaceAll(' ', ''))
      .min(5, ErrorMessage.ZipPattern)
      .max(6, ErrorMessage.ZipPattern)
      .test('zipIsValid', ErrorMessage.ZipPattern, (value) =>
        validateZipRef.current(value),
      ),
    email: Yup.string()
      .required(ErrorMessage.EmailRequired)
      .matches(EMAIL_REGEX, ErrorMessage.EmailPattern),
    password: Yup.string()
      .required(ErrorMessage.PasswordRequired)
      .matches(PASSWORD_REGEX, ErrorMessage.PasswordPattern)
      .min(8, ErrorMessage.PasswordPattern),
    agree: Yup.bool().isTrue(),
    phoneNumber: Yup.string().matches(
      PHONE_NUMBER_REGEX,
      ErrorMessage.PhonePattern,
    ),
  });

  useEffect(() => {
    const beforeUnload = () => {
      sendFormAbandonedErrorsToAmplitude();
    };

    // Triggers on tab switch / close and URL change
    window.addEventListener('beforeunload', beforeUnload);

    return () => {
      window.removeEventListener('beforeunload', beforeUnload);

      // Triggers on component unmount
      sendFormAbandonedErrorsToAmplitude();
    };
  }, []);

  const validateZip = async (value) => {
    const length = value?.length || 0;

    if (length >= 4 && length <= 6) {
      const response = await getLocation(value);
      const isZipValid = response.count > 0;
      const {
        country = null,
        city = null,
        state = null,
      } = response?.items?.[0]?.links || {};

      locationRef.current = {
        country: country?.code || '',
        location: isZipValid ? `${city?.title}, ${state?.code}` : '',
      };

      return isZipValid;
    } else {
      return true;
    }
  };

  const validateZipRef = useRef(cacheTest(validateZip, () => false));

  const onSubmit = async (values, { resetForm }) => {
    setError('');
    setLoading(true);

    errorsRef.current = {};
    entriesRef.current = { ...values };

    const type = 'agent';
    const registrationResponse = await register({ ...values, type });
    const isError = registrationResponse.status === 'error';

    setLoading(false);

    if (!isError) {
      onRedirecting(true);
      resetForm();
      setError('');

      const loginResponse = await login(
        registrationResponse.identifier,
        registrationResponse.key,
      );

      if (loginResponse.status !== 'error') {
        const randomPhoneNumber = `+1555${Math.floor(
          Math.random() * 10000000,
        )}`;

        track(GTM_EVENTS.interaction, {
          target: GTM_CATEGORIES.successReg,
          action: GTM_ACTIONS.registration,
          label: type,
          client: {
            first_name: values.firstName,
            last_name: values.lastName,
            email: values.email,
            phone: values.phoneNumber || randomPhoneNumber,
            postal_code: values.zip,
            city: locationRef.current.location.split(', ')[0],
            region: locationRef.current.location.split(', ')[1],
            country: locationRef.current.country,
          },
        });

        const authenticationToken = loginResponse.authenticationToken;
        const volatileToken = loginResponse.volatileToken;
        const tokenExpires = loginResponse.tokenExpires;
        const accountId = loginResponse.id;
        const profileId = loginResponse.links?.profiles?.items[0]?.id;

        CookieService.setAuthenticationCookie(authenticationToken);
        CookieService.setVolatileCookie(volatileToken);
        CookieService.setExpiresCookie(tokenExpires);
        CookieService.setAccountCookie(accountId);
        CookieService.setUserTypeCookie(type);
        CookieService.setProfileCookie(profileId);
        Sentry.setUser({ id: accountId, type });

        await accountLevelVerify();
        await refreshUserProfiles(accountId);

        Amp.track(Amp.events.signUp, {
          type: 'agent',
          provider: 'native',
        });

        if (values.phoneNumber) {
          sendSubmitPhoneNumberToAmplitude(
            accountId,
            values.phoneNumber.replace(/[\D]/g, ''),
            locationRef.current.country,
            isError,
            registrationResponse.message,
          );
        }

        window.location.href = `${process.env.redirectProUrl}/welcome`;
      } else {
        Amp.track(Amp.events.invalidEventOccurred, {
          name: `login failed`,
        });

        await router.push('/login');
      }
    } else {
      setError(registrationResponse.message);
      errorsRef.current = {
        registrationResponse: registrationResponse.message,
      };

      sendFormSubmittedErrorsToAmplitude(registrationResponse.message);
      if (values.phoneNumber) {
        sendSubmitPhoneNumberToAmplitude(
          null,
          values.phoneNumber.replace(/[\D]/g, ''),
          locationRef.current.country,
          isError,
          registrationResponse.message,
        );
      }
    }
  };

  const sendFormStartedEventToAmplitude = () => {
    Amp.track(Amp.events.formStarted, {
      name: 'registration',
    });
  };

  const sendFormSubmittedErrorsToAmplitude = (message) => {
    Amp.track(Amp.events.formSubmitted, {
      name: 'registration errors',
      result: Amp.element.result.fail,
      message: message,
    });
  };

  const sendSubmitPhoneNumberToAmplitude = (
    accountId,
    phone,
    country,
    isError = false,
    errorMessage = null,
  ) => {
    Amp.track(Amp.events.submitPhoneNumber, {
      scope: Amp.element.scope.global,
      phone_number_last_4_digits: phone.slice(-4),
      country_code: country,
      status: isError ? 'failure' : 'success',
      error_message: isError ? errorMessage : null,
    });
  };

  const sendFormAbandonedErrorsToAmplitude = () => {
    if (Object.keys(errorsRef.current).length) {
      Amp.track(Amp.events.formAbandoned, {
        name: 'registration errors',
        errors: errorsRef.current,
        values: entriesRef.current,
      });
    }
  };

  const toggleShowTerms = (e) => {
    e?.preventDefault();
    setShowTerms(!showTerms);
  };

  const toggleShowPrivacyPolicy = (e) => {
    e?.preventDefault();
    setShowPrivacyPolicy(!showPrivacyPolicy);
  };

  const formatPhoneNumber = (e, setFieldValue) => {
    setFieldValue('phoneNumber', maskPhoneNumber(e.target.value));
  };

  const register = async (values) => {
    const body = { ...values };

    if (body.phoneNumber) {
      body.phoneNumber = body.phoneNumber.replace(/[^A-Z0-9]+/gi, '');
    }

    if (body.zip) {
      body.zip = body.zip.replaceAll(' ', '');
    }

    return (
      await ApiNoCache.clientAPIRoute(`/register`, {
        method: 'POST',
        body: JSON.stringify(body),
      })
    ).data;
  };

  const login = async (identifier, key) => {
    return (
      await ApiNoCache.clientAPIRoute(`/login/nonce`, {
        method: 'POST',
        body: JSON.stringify({
          identifier,
          key,
        }),
      })
    ).data;
  };

  const getLocation = async (zip) => {
    return Api.clientAPIRoute(`/register/location/${zip}`);
  };

  const onChange = () => {
    if (error) {
      setError('');
    }
  };

  return (
    <>
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={onSubmit}
      >
        {({
          isValid,
          dirty,
          isSubmitting,
          errors,
          values,
          touched,
          setFieldValue,
        }) => {
          if (!isFormStartedRef.current && dirty) {
            isFormStartedRef.current = true;
            sendFormStartedEventToAmplitude();
          }

          if (values.zip.length < 4) {
            locationRef.current = { country: '', location: '' };
          }

          errorsRef.current = {};
          entriesRef.current = {};

          if (!isValid && dirty) {
            errorsRef.current = { ...errors };
            entriesRef.current = { ...values };
          }

          return (
            <Form
              onChange={onChange}
              className={styles['registration-form']}
              data-cy="director-registration-form"
            >
              <div className={styles['socials-container']}>
                <SocialButtons isModal={isModal} />
                <span className={styles['socials-or']}>or</span>
              </div>
              <div className={styles['form-row']}>
                <InputFormik name="firstName" placeholder="First Name" />
                <InputFormik name="lastName" placeholder="Last Name" />
              </div>
              <div className={styles['form-row']}>
                <InputFormik
                  name="zip"
                  placeholder="Zip Code"
                  hint="(optional)"
                />
                <InputFormik
                  name="phoneNumber"
                  placeholder="Phone number"
                  onChange={(e) => formatPhoneNumber(e, setFieldValue)}
                  hint="(optional)"
                />
              </div>
              <InputFormik name="email" placeholder="Email" />
              <PasswordInputFormik
                name="password"
                placeholder="Create Password"
                hint="Min 8 characters"
              />
              <div
                className={cn(styles['agreement-container'], {
                  [styles['agreement-error']]: touched.agree && errors.agree,
                })}
              >
                <CheckboxFormik name="agree">
                  <div className={styles['agreement-text']}>
                    <span>
                      By choosing to join, I certify I am at least 18 years old
                      and have read and agree to the AllCasting.com
                    </span>
                    <span> </span>
                    <span
                      className={styles.link}
                      onClick={toggleShowPrivacyPolicy}
                    >
                      privacy policy
                    </span>
                    <span> and </span>
                    <span className={styles.link} onClick={toggleShowTerms}>
                      terms of use
                    </span>
                    <span> . </span>
                    <span>
                      I agree to receive welcome email, newsletter, SMS &amp;
                      occasional account updates from AllCasting.com
                    </span>
                  </div>
                </CheckboxFormik>
              </div>
              {error && (
                <small className={styles['error-message']}>{error}</small>
              )}
              <div
                className={cn(styles['action-container'], {
                  [styles['loading']]: loading,
                })}
              >
                {loading ? (
                  <Loading minHeight="40px" padding="0" />
                ) : (
                  <div className={styles['btn-box']}>
                    <Button
                      className={styles.button}
                      color="green-gradient"
                      disabled={!(isValid && dirty) || isSubmitting}
                      type="submit"
                      label="Sign Up"
                      minWidth="220px"
                    />
                  </div>
                )}
              </div>
            </Form>
          );
        }}
      </Formik>
      <div className={styles['link-container-mobile']}>
        <span>Already a member?</span>
        <Link
          href={'/login'}
          className={styles['login-link']}
          onClick={onClose}
        >
          Log in now
        </Link>
      </div>
      {showTerms && (
        <Modal
          backdropClose
          onClose={toggleShowTerms}
          disableBackgroundScroll={!isModal}
        >
          <TermsOfUse />
        </Modal>
      )}
      {showPrivacyPolicy && (
        <Modal
          backdropClose
          onClose={toggleShowPrivacyPolicy}
          disableBackgroundScroll={!isModal}
        >
          <PrivacyPolicy onNavigation={toggleShowPrivacyPolicy} />
        </Modal>
      )}
    </>
  );
};

export default memo(RegistrationFormPro);
