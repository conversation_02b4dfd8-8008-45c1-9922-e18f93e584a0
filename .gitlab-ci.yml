stages:
  - checks
  - staging-build
  - staging-wait
  - build-prod
  - deploy-prod
  - test
  - review-build
  - review-deploy
  - review-cleanup

include:
  - project: devops/common-ci-lib
    ref: 'v0.8.16'
    file:
      - '/kaniko/build.yml'
      - '/argo/watcher.yml'
      - '/gitlab/argo-review-environment.yml'

run-e2e-tests-public:
  image: cypress/browsers:node16.13.2-chrome100-ff98
  stage: test
  tags:
    - entertech-runner
  when: manual
  variables:
    APP_DIR: apps/public
  before_script:
    - cd $APP_DIR
    - npm config set cache .npm --global
    - npm ci
    - npx cypress verify
  script:
    - npx cypress run --config-file cypress.stage.config.js --browser chrome --record --key $CYPRESS_RECORD_KEY
  rules:
    - if: '$CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH'
      changes:
        - apps/public/**
        - apps/public/**/*

# Config for per-directory tests (lint, prettier, build)
.default-check-job:
  stage: checks
  tags:
    - entertech-runner
  image:
    name: registry.dyninno.net/dynatech/alpine-image/runner-casting-node2011:1133667
  cache:
    key: npm-cache
    paths:
      - .npm/
  before_script:
    - cd $APP_DIR
    - npm config set cache .npm --global
    - npm ci
  allow_failure: false

# ESLint
check-eslint-pro:
  extends: .default-check-job
  script: [npm run lint]
  variables:
    APP_DIR: apps/pro
    GIT_DEPTH: 0
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" || ($CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH != $CI_DEFAULT_BRANCH && $CI_COMMIT_TAG == "")'
      changes:
        - apps/pro/**
        - apps/pro/**/*
      when: on_success
    - when: never

check-eslint-public:
  extends: .default-check-job
  script: [npm run lint]
  variables:
    APP_DIR: apps/public
    GIT_DEPTH: 0
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" || ($CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH != $CI_DEFAULT_BRANCH && $CI_COMMIT_TAG == "")'
      changes:
        - apps/public/**
        - apps/public/**/*
      when: on_success
    - when: never

check-eslint-talent:
  extends: .default-check-job
  script: [npm run lint]
  variables:
    APP_DIR: apps/talent
    GIT_DEPTH: 0
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" || ($CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH != $CI_DEFAULT_BRANCH && $CI_COMMIT_TAG == "")'
      changes:
        - apps/talent/**
        - apps/talent/**/*
      when: on_success
    - when: never

# Prettier
check-prettier-pro:
  extends: .default-check-job
  script: [npm run prettier]
  variables:
    APP_DIR: apps/pro
    GIT_DEPTH: 0
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" || ($CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH != $CI_DEFAULT_BRANCH && $CI_COMMIT_TAG == "")'
      changes:
        - apps/pro/**
        - apps/pro/**/*
      when: on_success
    - when: never

check-prettier-public:
  extends: .default-check-job
  script: [npm run prettier]
  variables:
    APP_DIR: apps/public
    GIT_DEPTH: 0
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" || ($CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH != $CI_DEFAULT_BRANCH && $CI_COMMIT_TAG == "")'
      changes:
        - apps/public/**
        - apps/public/**/*
      when: on_success
    - when: never

check-prettier-talent:
  extends: .default-check-job
  script: [npm run prettier]
  variables:
    APP_DIR: apps/talent
    GIT_DEPTH: 0
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" || ($CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH != $CI_DEFAULT_BRANCH && $CI_COMMIT_TAG == "")'
      changes:
        - apps/talent/**
        - apps/talent/**/*
      when: on_success
    - when: never

build-stage-public:
  extends: .kaniko-build-branch
  stage: staging-build
  image: gcr.io/kaniko-project/executor:v1.23.2-debug
  tags: [entertech-runner]
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      changes:
        - apps/public/**
        - apps/public/**/*
  variables:
    BUILD_CONTEXT: apps/public
    DOCKERFILE: apps/public/docker/node/Dockerfile
    IMAGE_NAME: 'staging-public-allcasting-com'
    KANIKO_ARGS: '--target=runtime-staging --skip-unused-stages --build-arg IMAGES_PROXY=$CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX --build-arg HEALTH_CHECK_TOKEN=$HEALTH_CHECK_TOKEN --build-arg NEXT_PUBLIC_VERSION=$CI_PIPELINE_ID'
    USE_DEPENDENCY_PROXY: 1

build-stage-pro:
  extends: .kaniko-build-branch
  stage: staging-build
  image: gcr.io/kaniko-project/executor:v1.23.2-debug
  tags: [entertech-runner]
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      changes:
        - apps/pro/**
        - apps/pro/**/*
  variables:
    BUILD_CONTEXT: apps/pro
    DOCKERFILE: apps/pro/docker/node/Dockerfile
    IMAGE_NAME: 'staging-pro-allcasting-com'
    KANIKO_ARGS: '--target=runtime-staging --skip-unused-stages --build-arg IMAGES_PROXY=$CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX --build-arg HEALTH_CHECK_TOKEN=$HEALTH_CHECK_TOKEN --build-arg NEXT_PUBLIC_VERSION=$CI_PIPELINE_ID'
    USE_DEPENDENCY_PROXY: 1

build-stage-talent:
  extends: .kaniko-build-branch
  stage: staging-build
  image: gcr.io/kaniko-project/executor:v1.23.2-debug
  tags: [entertech-runner]
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      changes:
        - apps/talent/**
        - apps/talent/**/*
  variables:
    BUILD_CONTEXT: apps/talent
    DOCKERFILE: apps/talent/docker/node/Dockerfile
    IMAGE_NAME: 'staging-talent-allcasting-com'
    KANIKO_ARGS: '--target=runtime-staging --skip-unused-stages --build-arg IMAGES_PROXY=$CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX --build-arg HEALTH_CHECK_TOKEN=$HEALTH_CHECK_TOKEN --build-arg NEXT_PUBLIC_VERSION=$CI_PIPELINE_ID'
    USE_DEPENDENCY_PROXY: 1

build-stage-cdn:
  extends: .kaniko-build-branch
  stage: staging-build
  image: gcr.io/kaniko-project/executor:v1.23.2-debug
  tags: [entertech-runner]
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      changes:
        - apps/cdn/**
  variables:
    BUILD_CONTEXT: apps/cdn
    DOCKERFILE: apps/cdn/docker/Dockerfile
    IMAGE_NAME: 'staging-cdn-allcasting-com'
    KANIKO_ARGS: '--build-arg IMAGES_PROXY=$CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX'
    USE_DEPENDENCY_PROXY: 1

STAGING-DEPLOY-PUBLIC:
  extends: .await-deployment
  stage: staging-wait
  image: ghcr.io/shini4i/argo-watcher:v0.9.1
  tags: [entertech-runner]
  needs:
    - job: build-stage-public
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      changes:
        - apps/public/**
        - apps/public/**/*
  variables:
    BEARER_TOKEN: $BEARER_TOKEN
    ARGO_WATCHER_URL: https://argo-watcher.stg.entertech.art
    ARGO_APP: staging-frontend-public-ac
    IMAGES: $CI_REGISTRY_IMAGE/staging-public-allcasting-com

STAGING-DEPLOY-PRO:
  extends: .await-deployment
  stage: staging-wait
  image: ghcr.io/shini4i/argo-watcher:v0.9.1
  tags: [entertech-runner]
  needs:
    - job: build-stage-pro
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      changes:
        - apps/pro/**
        - apps/pro/**/*
  variables:
    BEARER_TOKEN: $BEARER_TOKEN
    ARGO_WATCHER_URL: https://argo-watcher.stg.entertech.art
    ARGO_APP: staging-frontend-pro-ac
    IMAGES: $CI_REGISTRY_IMAGE/staging-pro-allcasting-com

STAGING-DEPLOY-TALENT:
  extends: .await-deployment
  stage: staging-wait
  image: ghcr.io/shini4i/argo-watcher:v0.9.1
  tags: [entertech-runner]
  needs:
    - job: build-stage-talent
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      changes:
        - apps/talent/**
        - apps/talent/**/*
  variables:
    BEARER_TOKEN: $BEARER_TOKEN
    ARGO_WATCHER_URL: https://argo-watcher.stg.entertech.art
    ARGO_APP: staging-frontend-talent-ac
    IMAGES: $CI_REGISTRY_IMAGE/staging-talent-allcasting-com

STAGING-DEPLOY-CDN:
  extends: .await-deployment
  stage: staging-wait
  image: ghcr.io/shini4i/argo-watcher:v0.9.1
  tags: [entertech-runner]
  needs:
    - job: build-stage-cdn
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      changes:
        - apps/cdn/**
  variables:
    BEARER_TOKEN: $BEARER_TOKEN
    ARGO_WATCHER_URL: https://argo-watcher.stg.entertech.art
    ARGO_APP: staging-frontend-cdn-ac
    IMAGES: $CI_REGISTRY_IMAGE/staging-cdn-allcasting-com

# Prod builds
build-prod-public:
  extends: .kaniko-build-tag
  stage: build-prod
  tags:
    - entertech-runner
  image:
    name: gcr.io/kaniko-project/executor:v1.23.2-debug
    entrypoint: ['']
  rules:
    - if: '$CI_COMMIT_TAG =~ /-public$/'
  variables:
    KANIKO_ARGS: '--target=runtime-prod --skip-unused-stages --build-arg IMAGES_PROXY=$CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX --build-arg SENTRY_AUTH_TOKEN=$SENTRY_AUTH_TOKEN_PUBLIC --build-arg HEALTH_CHECK_TOKEN=$HEALTH_CHECK_TOKEN --build-arg NEXT_PUBLIC_VERSION=$CI_COMMIT_TAG'
    USE_DEPENDENCY_PROXY: 1
    BUILD_CONTEXT: apps/public
    DOCKERFILE: /apps/public/docker/node/Dockerfile
    IMAGE_NAME: 'public-allcasting-com'

build-prod-pro:
  extends: .kaniko-build-tag
  stage: build-prod
  tags:
    - entertech-runner
  image:
    name: gcr.io/kaniko-project/executor:v1.23.2-debug
    entrypoint: ['']
  rules:
    - if: '$CI_COMMIT_TAG =~ /-pro$/'
  variables:
    KANIKO_ARGS: '--target=runtime-prod --skip-unused-stages --build-arg IMAGES_PROXY=$CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX --build-arg SENTRY_AUTH_TOKEN=$SENTRY_AUTH_TOKEN_PRO --build-arg HEALTH_CHECK_TOKEN=$HEALTH_CHECK_TOKEN --build-arg NEXT_PUBLIC_VERSION=$CI_COMMIT_TAG'
    USE_DEPENDENCY_PROXY: 1
    BUILD_CONTEXT: apps/pro
    DOCKERFILE: /apps/pro/docker/node/Dockerfile
    IMAGE_NAME: 'pro-allcasting-com'

build-prod-talent:
  extends: .kaniko-build-tag
  stage: build-prod
  tags:
    - entertech-runner
  image:
    name: gcr.io/kaniko-project/executor:v1.23.2-debug
    entrypoint: ['']
  rules:
    - if: '$CI_COMMIT_TAG =~ /-talent$/'
  variables:
    KANIKO_ARGS: '--target=runtime-prod --skip-unused-stages --build-arg IMAGES_PROXY=$CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX --build-arg SENTRY_AUTH_TOKEN=$SENTRY_AUTH_TOKEN_TALENT --build-arg HEALTH_CHECK_TOKEN=$HEALTH_CHECK_TOKEN --build-arg NEXT_PUBLIC_VERSION=$CI_COMMIT_TAG'
    USE_DEPENDENCY_PROXY: 1
    BUILD_CONTEXT: apps/talent
    DOCKERFILE: /apps/talent/docker/node/Dockerfile
    IMAGE_NAME: 'talent-allcasting-com'

# Prod deploys
deploy-prod-public:
  image: ghcr.io/shini4i/argo-watcher:v0.9.1
  stage: deploy-prod
  when: manual
  allow_failure: false
  tags:
    - gitlab-runner-kaniko
  extends: .await-deployment
  rules:
    - if: '$CI_COMMIT_TAG =~ /-public$/'
  variables:
    ARGO_WATCHER_URL: https://argo-watcher.core.entertech.art
    ARGO_APP: frontend-public-ac
    IMAGES: $CI_REGISTRY_IMAGE/public-allcasting-com
    IMAGE_TAG: $CI_COMMIT_TAG
    BEARER_TOKEN: $BEARER_TOKEN

deploy-prod-pro:
  extends: .await-deployment
  image: ghcr.io/shini4i/argo-watcher:v0.9.1
  stage: deploy-prod
  when: manual
  allow_failure: false
  tags:
    - gitlab-runner-kaniko
  rules:
    - if: '$CI_COMMIT_TAG =~ /-pro$/'
  variables:
    ARGO_WATCHER_URL: https://argo-watcher.core.entertech.art
    ARGO_APP: frontend-pro-ac
    IMAGES: $CI_REGISTRY_IMAGE/pro-allcasting-com
    IMAGE_TAG: $CI_COMMIT_TAG
    BEARER_TOKEN: $BEARER_TOKEN

deploy-prod-talent:
  extends: .await-deployment
  image: ghcr.io/shini4i/argo-watcher:v0.9.1
  stage: deploy-prod
  when: manual
  allow_failure: false
  tags:
    - gitlab-runner-kaniko
  rules:
    - if: '$CI_COMMIT_TAG =~ /-talent$/'
  variables:
    ARGO_WATCHER_URL: https://argo-watcher.core.entertech.art
    ARGO_APP: frontend-talent-ac
    IMAGES: $CI_REGISTRY_IMAGE/talent-allcasting-com
    IMAGE_TAG: $CI_COMMIT_TAG
    BEARER_TOKEN: $BEARER_TOKEN

# Review build
build-review-pro:
  stage: review-build
  extends: .kaniko-build-branch
  image:
    name: gcr.io/kaniko-project/executor:v1.23.2-debug
  tags:
    - entertech-runner
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: manual
  needs: []
  variables:
    GIT_DEPTH: 1
    DOCKERFILE: apps/pro/docker/node/Dockerfile
    BUILD_CONTEXT: apps/pro
    IMAGE_NAME: review-pro
    IMAGE_TAG: '$CI_COMMIT_SHORT_SHA'
    KANIKO_ARGS: '--target=runtime-review --skip-unused-stages --build-arg IMAGES_PROXY=$CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX --build-arg BRANCH_NAME=${CI_MERGE_REQUEST_IID} --build-arg HEALTH_CHECK_TOKEN=$HEALTH_CHECK_TOKEN --build-arg NEXT_PUBLIC_VERSION=$CI_COMMIT_SHORT_SHA'
    USE_DEPENDENCY_PROXY: 1

build-review-public:
  stage: review-build
  extends: .kaniko-build-branch
  image:
    name: gcr.io/kaniko-project/executor:v1.23.2-debug
  tags:
    - entertech-runner
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: manual
  needs: []
  variables:
    GIT_DEPTH: 1
    DOCKERFILE: apps/public/docker/node/Dockerfile
    BUILD_CONTEXT: apps/public
    IMAGE_NAME: review-public
    IMAGE_TAG: '$CI_COMMIT_SHORT_SHA'
    KANIKO_ARGS: '--target=runtime-review --skip-unused-stages --build-arg IMAGES_PROXY=$CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX --build-arg BRANCH_NAME=${CI_MERGE_REQUEST_IID} --build-arg HEALTH_CHECK_TOKEN=$HEALTH_CHECK_TOKEN --build-arg NEXT_PUBLIC_VERSION=$CI_COMMIT_SHORT_SHA'
    USE_DEPENDENCY_PROXY: 1

build-review-talent:
  stage: review-build
  extends: .kaniko-build-branch
  image:
    name: gcr.io/kaniko-project/executor:v1.23.2-debug
  tags:
    - entertech-runner
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: manual
  needs: []
  variables:
    GIT_DEPTH: 1
    DOCKERFILE: apps/talent/docker/node/Dockerfile
    BUILD_CONTEXT: apps/talent
    IMAGE_NAME: review-talent
    IMAGE_TAG: '$CI_COMMIT_SHORT_SHA'
    KANIKO_ARGS: '--target=runtime-review --skip-unused-stages --build-arg IMAGES_PROXY=$CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX --build-arg BRANCH_NAME=${CI_MERGE_REQUEST_IID} --build-arg HEALTH_CHECK_TOKEN=$HEALTH_CHECK_TOKEN --build-arg NEXT_PUBLIC_VERSION=$CI_COMMIT_SHORT_SHA'
    USE_DEPENDENCY_PROXY: 1

#review deploy  
deploy-review-pro:
  extends: .gitlab_argo_review_environment
  stage: review-deploy
  tags: [entertech-runner]
  needs:
    - job: build-review-pro
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
  environment:
    name: review-pro/$CI_COMMIT_REF_SLUG
    url: $ENVIRONMENT_URL
    on_stop: stop-review-pro             
    auto_stop_in: 1 week
  variables:
    ARGO_CD_URL: https://argocd.stg.entertech.art
    ARGOCD_NAMESPACE: argo-cd
    ENVIRONMENT_URL: https://pro.${CI_MERGE_REQUEST_IID}.ac.stg.entertech.art
    ARGO_WATCHER_URL: https://argo-watcher.stg.entertech.art
    IMAGES: $CI_REGISTRY_IMAGE/review-pro
    ARGO_APP: pro-ac-review-$CI_MERGE_REQUEST_IID
    MR_LABEL: "REVIEW-pro"

deploy-review-public:
  extends: .gitlab_argo_review_environment
  stage: review-deploy
  tags: [entertech-runner]
  needs:
    - job: build-review-public
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
  environment:
    name: review-public/$CI_COMMIT_REF_SLUG
    url: $ENVIRONMENT_URL
    on_stop: stop-review-public
    auto_stop_in: 1 week
  variables:
    ARGO_CD_URL: https://argocd.stg.entertech.art
    ARGOCD_NAMESPACE: argo-cd
    ENVIRONMENT_URL: https://${CI_MERGE_REQUEST_IID}.ac.stg.entertech.art
    ARGO_WATCHER_URL: https://argo-watcher.stg.entertech.art
    IMAGES: $CI_REGISTRY_IMAGE/review-public
    ARGO_APP: public-ac-review-$CI_MERGE_REQUEST_IID
    MR_LABEL: "REVIEW-public"

deploy-review-talent:
  extends: .gitlab_argo_review_environment
  stage: review-deploy
  tags: [entertech-runner]
  needs:
    - job: build-review-talent
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
  environment:
    name: review-talent/$CI_COMMIT_REF_SLUG
    url: $ENVIRONMENT_URL
    on_stop: stop-review-talent
    auto_stop_in: 1 week
  variables:
    ARGO_CD_URL: https://argocd.stg.entertech.art
    ARGOCD_NAMESPACE: argo-cd
    ENVIRONMENT_URL: https://talent.${CI_MERGE_REQUEST_IID}.ac.stg.entertech.art
    ARGO_WATCHER_URL: https://argo-watcher.stg.entertech.art
    IMAGES: $CI_REGISTRY_IMAGE/review-talent
    ARGO_APP: talent-ac-review-$CI_MERGE_REQUEST_IID
    MR_LABEL: "REVIEW-talent"


stop-review-pro:
  extends: .gitlab_argo_review_environment_stop
  stage: review-cleanup
  tags: [entertech-runner]
  variables:
    MR_LABEL: "REVIEW-pro"
  environment:
    name: review-pro/$CI_COMMIT_REF_SLUG
    action: stop
  rules:
    - if: $CI_MERGE_REQUEST_ID
      when: manual
      allow_failure: true

stop-review-public:
  extends: .gitlab_argo_review_environment_stop
  stage: review-cleanup
  tags: [entertech-runner]
  variables:
    MR_LABEL: "REVIEW-public"
  environment:
    name: review-public/$CI_COMMIT_REF_SLUG
    action: stop
  rules:
    - if: $CI_MERGE_REQUEST_ID
      when: manual
      allow_failure: true

stop-review-talent:
  extends: .gitlab_argo_review_environment_stop
  stage: review-cleanup
  tags: [entertech-runner]
  variables:
    MR_LABEL: "REVIEW-talent"
  environment:
    name: review-talent/$CI_COMMIT_REF_SLUG
    action: stop
  rules:
    - if: $CI_MERGE_REQUEST_ID
      when: manual
      allow_failure: true
