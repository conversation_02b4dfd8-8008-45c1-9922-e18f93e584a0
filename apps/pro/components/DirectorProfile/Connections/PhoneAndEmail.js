'use client';
import styles from '../ProfileConnections.module.scss';
import Image from 'next/image';
import React, { memo, useState } from 'react';
import EditIcon from '../../../public/assets/icons/icon-edit.svg';
import { useFormik } from 'formik';
import Api from '@services/api';
import * as Yup from 'yup';
import { maskPhoneNumber } from '@utils/maskPhoneNumber';
import { Checkbox, Input } from '@components';
import { useNotifications } from '@contexts/NotificationContext';
import cn from 'classnames';
import { checkTollFreeNumber } from '@utils/checkTollFreeNumber';
import { hasFormValueChanged } from '@utils/hasFormValueChanged';
import { Amp } from '@services/amp';
import { ErrorMessage, PHONE_NUMBER_REGEX, EMAIL_REGEX } from '@constants/form';
import { useAuth } from '@contexts/AuthContext';

const PhoneAndEmail = ({
  accountId,
  phone,
  email,
  allowPhoneNotifications,
  refreshTouches,
}) => {
  const { setNotification } = useNotifications();
  const [isEditing, setEditing] = useState(false);
  const [isSaving, setSaving] = useState(false);
  const { userProfiles } = useAuth();

  const edit = () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'edit contacts',
      scope: Amp.element.scope.profile,
      section: Amp.element.section.contacts,
      type: Amp.element.type.button,
    });
    setEditing(true);
  };
  const triggerSubmit = async () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'save contacts',
      scope: Amp.element.scope.profile,
      section: Amp.element.section.contacts,
      type: Amp.element.type.button,
    });
    await formik.submitForm();
  };

  const formik = useFormik({
    enableReinitialize: true,
    initialValues: {
      email: email || '',
      phone: maskPhoneNumber(phone || ''),
      allowNotifications: allowPhoneNotifications || false,
    },
    onSubmit: async (values) => {
      if (!hasFormValueChanged(values, formik.initialValues)) {
        setEditing(false);

        return;
      }

      setSaving(true);

      const body = new FormData();
      const newPhone = values.phone?.replace(/[^A-Z0-9]+/gi, '') || '';

      body.append('email', values.email);
      body.append('phone', newPhone);
      body.append('phone_opt_outed', values.allowNotifications ? '0' : '1');

      const changeContactsResponse = await Api.clientside(
        `/accounts/${accountId}/touches`,
        {
          body,
          method: 'PUT',
        },
      );

      const isError = changeContactsResponse.status !== 'ok';
      const errorMessage =
        changeContactsResponse?.message || ErrorMessage.Unexpected;

      if (values.phone) {
        const user = userProfiles[0] || {};

        Amp.track(Amp.events.submitPhoneNumber, {
          scope: Amp.element.scope.profilePage,
          phone_number_last_4_digits: newPhone.slice(-4),
          country_code: user.country,
          status: isError ? 'failure' : 'success',
          error_message: isError ? errorMessage : null,
        });
      }

      if (isError) {
        setNotification({
          type: 'error',
          message: errorMessage,
          timeout: '5000',
        });
      } else {
        await refreshTouches();
        setEditing(false);
      }
      setSaving(false);
    },
    validationSchema: Yup.object({
      email: Yup.string()
        .required(ErrorMessage.EmailRequired)
        .matches(EMAIL_REGEX, ErrorMessage.EmailPattern),
      phone: Yup.string()
        .matches(PHONE_NUMBER_REGEX, ErrorMessage.PhonePattern)
        .test('phoneIsValid', ErrorMessage.PhonePatternToll, async (value) => {
          return checkTollFreeNumber(value);
        }),
    }),
  });

  const formatPhoneNumber = (e) => {
    formik.setFieldValue('phone', maskPhoneNumber(e.target.value));
  };

  return (
    <>
      <section className={styles['profile-info-block']}>
        <div className={styles['profile-section-title']}>
          Contacts:
          {!isEditing ? (
            <span onClick={edit} className={styles['edit-link']}>
              <EditIcon />
            </span>
          ) : (
            <button
              onClick={triggerSubmit}
              className={styles['save-button']}
              disabled={isSaving}
            >
              Save
            </button>
          )}
        </div>
        {!isEditing ? (
          <>
            {email || phone ? (
              <>
                {phone && (
                  <div className={styles['profile-info-row']}>
                    <Image
                      src={'/assets/icons/icon-phone.svg'}
                      width={27}
                      height={27}
                      alt="profile phone"
                      priority
                      className={styles['profile-info-icon']}
                    />
                    <a
                      className={styles['profile-section-link']}
                      href={`tel:${phone}`}
                    >
                      {maskPhoneNumber(phone)}
                    </a>
                  </div>
                )}
                {email && (
                  <div className={styles['profile-info-row']}>
                    <Image
                      src={'/assets/icons/icon-mail.svg'}
                      width={27}
                      height={27}
                      alt="profile email"
                      priority
                      className={styles['profile-info-icon']}
                    />
                    <a
                      className={cn(
                        styles['profile-section-link'],
                        styles['text-ellipsis'],
                      )}
                      href={`mailto:${email}`}
                    >
                      {email}
                    </a>
                  </div>
                )}
              </>
            ) : (
              <div className={styles['profile-section-empty']}>
                Add contact information visible in applications but not in your
                public profile.
                <span
                  onClick={edit}
                  className={styles['profile-section-empty-add-button']}
                >
                  Add
                </span>
              </div>
            )}
          </>
        ) : (
          <form className={styles['form']} onSubmit={formik.handleSubmit}>
            <Input
              name="phone"
              placeholder="Phone number"
              onChange={formatPhoneNumber}
              onBlur={formik.handleBlur}
              value={formik.values.phone}
              isTouched={formik.touched.phone}
              error={formik.errors.phone}
              hint="Include area code"
            />
            <div className={styles['form-notifications-container']}>
              <Checkbox
                disabled={!formik.values.phone}
                name="allowNotifications"
                onChange={formik.handleChange}
                value={formik.values.allowNotifications}
                error={formik.errors.allowNotifications}
                onBlur={formik.handleBlur}
                isTouched={formik.touched.allowNotifications}
              >
                <span className={styles['form-field-label']}>
                  Allow SMS notifications
                </span>
              </Checkbox>
            </div>
            <Input
              name="email"
              placeholder="Email"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.email}
              isTouched={formik.touched.email}
              error={formik.errors.email}
            />
          </form>
        )}
      </section>
    </>
  );
};

export default memo(PhoneAndEmail);
