'use client';
import React, { memo, useState } from 'react';
import styles from './SettingsSidebar.module.scss';
import { SearchInput } from '@components';
import Image from 'next/image';
import { settingsSidebarItems } from '@constants/settingsSidebarItems';
import { usePathname, useRouter } from 'next/navigation';
import cn from 'classnames';

const SettingsSidebar = () => {
  const [sideBarItems, setSideBarItems] = useState(settingsSidebarItems);
  const router = useRouter();
  const path = usePathname();

  const navigate = (slug) => {
    router.push(`/settings/${slug}`);
  };

  const filterSideBarItems = (value) => {
    if (!value) {
      setSideBarItems(settingsSidebarItems);
    } else {
      const reg = new RegExp(value.toLowerCase(), 'ig');
      const matchedCategories = settingsSidebarItems.filter((category) =>
        reg.test(category.title.toLowerCase()),
      );

      const matchedCategoriesByItem = settingsSidebarItems
        .filter(
          (category) =>
            category.items.filter((item) => reg.test(item.title.toLowerCase()))
              .length,
        )
        .map((category) => ({
          ...category,
          items: category.items.filter((item) =>
            reg.test(item.title.toLowerCase()),
          ),
        }));

      const result = [...matchedCategories, ...matchedCategoriesByItem].filter(
        (matchedCategory, index, categories) =>
          categories.findIndex(
            (category) => category.title === matchedCategory.title,
          ) === index,
      );

      const resultSorted = settingsSidebarItems.reduce(
        (sortedCategories, item) => {
          const matchedCategory = result.find(
            (category) => category.title === item.title,
          );

          if (matchedCategory) {
            sortedCategories.push(matchedCategory);
          }

          return sortedCategories;
        },
        [],
      );

      setSideBarItems(resultSorted);
    }
  };

  return (
    <div className={styles['settings-sidebar']}>
      <SearchInput
        placeholder="Search settings"
        onValueChange={filterSideBarItems}
      />
      <span className={styles['settings-sidebar-title']}>Settings</span>
      {sideBarItems.map(({ title, items }, index) => (
        <div key={index} className={styles['settings-sidebar-item']}>
          <span className={styles['settings-sidebar-item-title']}>{title}</span>
          <div className={styles['settings-sidebar-link-container']}>
            {items.map(({ title, iconSrc, slug }, i) =>
              slug !== 'deactivate-account' ? (
                <button
                  onClick={() => navigate(slug)}
                  key={i}
                  className={cn(styles['settings-sidebar-link'], {
                    [styles['settings-sidebar-link-active']]:
                      slug === 'change-password'
                        ? path.includes(slug) || path === '/settings'
                        : path.includes(slug),
                  })}
                >
                  <Image src={iconSrc} alt="icon" width={24} height={24} />
                  <span>{title}</span>
                </button>
              ) : (
                <button
                  key={i}
                  className={styles['deactivate-account-button']}
                  onClick={() => navigate(slug)}
                >
                  <Image src={iconSrc} alt="icon" width={28} height={28} />
                  <span>{title}</span>
                </button>
              ),
            )}
          </div>
        </div>
      ))}
    </div>
  );
};

export default memo(SettingsSidebar);
