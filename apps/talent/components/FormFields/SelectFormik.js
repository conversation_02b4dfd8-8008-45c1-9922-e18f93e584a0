'use client';
import React, { memo, useEffect } from 'react';
import { useField } from 'formik';
import { Select } from '@components';

const SelectFormik = (props) => {
  const { name } = props;
  const [
    { value },
    { error, touched, initialValue },
    { setTouched, setValue },
  ] = useField(name);

  // Autofill validation trigger
  useEffect(() => {
    if (initialValue !== value && !touched) {
      setTouched(true);
    }
  }, [value, touched, setTouched, initialValue]);

  const onChange = (value) => {
    setValue(value);
  };

  return (
    <Select
      onChange={onChange}
      value={value}
      error={error}
      isTouched={touched}
      setFormFieldTouched={() => setTouched(true)}
      {...props}
    />
  );
};

export default memo(SelectFormik);
