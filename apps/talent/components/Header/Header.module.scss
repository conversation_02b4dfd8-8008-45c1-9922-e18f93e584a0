@use '@styles/variables' as *;
@use '@styles/mixins' as *;
@use '@styles/button-animations' as *;

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  align-self: auto;
  max-width: $content-max-width;
  margin-left: auto;
  margin-right: auto;

  @include desktop {
    padding: 0 $space-60;
  }

  .logo {
    width: 93px;
    height: 21px;
    color: $violet-100;
    cursor: pointer;
  }

  .logo-link {
    line-height: 1;
  }

  .navbar {
    display: flex;
    align-items: center;

    .link {
      color: $blue-100;
      font-size: 16px;
      font-weight: 400;
      cursor: pointer;
      border-bottom: 1.5px solid transparent;
      line-height: 1;

      &:hover {
        border-bottom: 1.5px solid $blue-100;
      }
    }
  }

  .divider {
    width: 3px;
    height: 3px;
    background-color: $black;
    margin-left: $space-20;
    margin-right: $space-30;
  }

  .sign-up-button {
    color: $green-60;
    margin-left: $space-20;
    padding-top: $space-10;
    padding-bottom: $space-10;
    font-size: 14px;

    &:hover:enabled {
      padding-top: 8px;
      padding-bottom: 8px;
    }
  }
}

.header-subscribe-container {
  margin-left: $space-20;
  display: flex;
  align-items: center;
  gap: $space-30;
}

.header-divider-dot {
  width: 3px;
  height: 3px;
  background-color: $black;
}
