@use '@styles/variables' as *;

.sale-header {
  display: flex;
  align-items: center;
  justify-content: center;
  background-image: $gradient-port-gore;
  box-shadow: $shadow-card-container;
  background-size: auto 100%;
  font-weight: 400;
  color: $white;
  line-height: 2;
  padding: $space-20 $space-10;
  cursor: pointer;
  position: sticky;
  min-height: 44px;
  gap: $space-20;
  flex-direction: column;
  width: 100%;

  &::before {
    content: '';
    position: absolute;
    display: block;
    inset: 0;
    background-color: $grey-100-opacity-20;
    pointer-events: none;
  }
}

.sale-header-button {
  & button {
    color: $black;
    box-shadow: none;
    min-width: 200px;
  }
}
