@use '@styles/mixins' as *;
@use '@styles/variables' as *;

.photo-error-modal-container {
  padding: 0 $space-20;
}

.photo-error-modal {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  max-width: 400px;
  width: 100%;
  border-radius: 10px;
  padding: $space-20 0;

  .red-text {
    color: $red-60;
    font-weight: 700;
    padding: $space-20 $space-20 $space-10;
    vertical-align: middle;

    @include desktop {
      padding: $space-20 $space-10 $space-10 $space-5;
    }
  }

  .info-text {
    width: 100%;
    padding: $space-30 $space-30 $space-40;

    @include desktop {
      padding: $space-20 $space-10 $space-40 $space-5;
    }
  }
}

.photo-error-modal-btn-container {
  display: flex;
  justify-content: center;
  align-items: center;
  border-top: 1px solid $grey-60;

  button {
    width: 100%;
  }
}

.confirm-button {
  color: $blue-100;
  font-weight: 600;
  padding-top: $space-15;

  &:hover {
    cursor: pointer;
  }
}
