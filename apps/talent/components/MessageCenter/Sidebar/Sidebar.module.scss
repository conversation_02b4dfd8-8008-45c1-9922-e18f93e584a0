@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.sidebar {
  display: none;
  overflow-y: scroll;
  padding: $space-20 0;

  .title {
    font-weight: 900;
    font-size: 18px;
    margin: 0 $space-20 $space-20;
  }

  &.mobile-active {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background-color: $violet-80;
    background-image: $gradient-port-gore;
    z-index: 2;

    .title {
      color: $white;
    }
  }

  @include desktop {
    background: $grey-10;
    display: block;
    width: 240px;

    &.mobile-active {
      display: block;
      position: initial;
      height: initial;
      background: $grey-10;
      width: 240px;

      .title {
        color: $black;
      }
    }
  }
}

.load-more {
  color: $blue-100;
  display: block;
  margin-top: $space-20;
  padding-left: $space-25;
  font-weight: 400;

  &:hover {
    text-decoration: underline;
  }
}

.filter-block {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 $space-20;
  margin-bottom: $space-25;

  .filter-button {
    flex: 0 0 auto;
    width: 90px;
    font-size: 12px;
    height: 24px;
    padding: 0;

    @include desktop {
      width: auto;
      flex: 1 1 50%;
    }
  }
}

.no-messages {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: $white;
  text-align: center;
  padding: 0 $space-20;
  gap: $space-10;

  @include desktop {
    display: none;
  }
}

// --- chatbot

.conversation-button {
  display: flex;
  color: $white;
  align-items: center;
  justify-content: space-between;
  background: transparent;
  border: 0;
  width: 100%;
  margin-bottom: 3px;
  padding: 4px $space-20;
  cursor: pointer;
  font-size: 16px;
  font-weight: 300;

  &.active {
    @include desktop {
      background: $white;
      color: $black;
    }
  }

  &:hover {
    background: $white;
    color: $black;
  }

  @include desktop {
    color: $black;
  }

  .badge {
    flex-shrink: 0;
    font-size: 14px;
    font-weight: 400;
    margin-left: $space-10;

    @include desktop {
      font-size: 12px;
      font-weight: 700;
    }
  }
}

.contact {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: row;
  text-align: left;
}

.frame {
  background: $white;
  border-radius: 50%;
  border: 1px solid $grey-60;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  min-width: 36px;
  margin-right: $space-15;
}

.avatar {
  border-radius: 50%;
  width: 30px;
  height: 30px;
}
