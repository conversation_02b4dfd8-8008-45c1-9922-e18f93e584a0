'use client';
import { useState } from 'react';
import { createPortal } from 'react-dom';
import styles from './PaymentForm.module.scss';

import { SpecialTerms, TermsOfUse } from '..';
import Modal from '../Modal/Modal';

const PaymentFormDescription = ({ isLifetime, price, isProfileOutsideUSA }) => {
  const [showSpecialTerms, setShowSpecialTerms] = useState(false);
  const [showTerms, setShowTerms] = useState(false);

  const toggleShowTerms = (e) => {
    e?.preventDefault();
    setShowTerms(!showTerms);
  };

  const toggleShowSpecialTerms = (e) => {
    e?.preventDefault();
    setShowSpecialTerms(!showSpecialTerms);
  };

  return (
    <>
      <p className={styles['payment-description']}>
        {isLifetime ? (
          <>This is a one-time payment.</>
        ) : (
          <>
            Your subscription will <strong>automatically renew</strong> for the
            same price (
            <strong>
              ${price.toFixed(2)}
              {isProfileOutsideUSA && <>USD</>}
            </strong>
            ) and package length until you cancel via your Account Settings
            page. By subscribing, you <strong>authorize us</strong> to charge
            your <strong>card now</strong> and upon each{' '}
            <strong>renewal</strong>.{' '}
            <span className={styles.link} onClick={toggleShowSpecialTerms}>
              Learn more
            </span>
          </>
        )}
      </p>
      {showSpecialTerms &&
        createPortal(
          <Modal backdropClose onClose={toggleShowSpecialTerms}>
            <SpecialTerms />
          </Modal>,
          document.body,
        )}
      {showTerms &&
        createPortal(
          <Modal backdropClose onClose={toggleShowTerms}>
            <TermsOfUse />
          </Modal>,
          document.body,
        )}
    </>
  );
};

export default PaymentFormDescription;
