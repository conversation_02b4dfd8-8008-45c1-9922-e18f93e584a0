'use client';
import { memo, useEffect, useState } from 'react';
import styles from './CardInput.module.scss';
import { Input } from '@components';
import cn from 'classnames';
import Image from 'next/image';
import { validateMasterCard, validateVisa } from '@utils/validatePaymentMethod';

const CardInput = ({
  name,
  onChange,
  onBlur,
  value,
  isTouched,
  error,
  placeholder,
  hint,
}) => {
  const [isVisa, setIsVisa] = useState(false);
  const [isMasterCard, setIsMasterCard] = useState(false);

  useEffect(() => {
    const formattedValue = value.replace(/\s-\s/g, '');

    setIsMasterCard(validateMasterCard(formattedValue));
    setIsVisa(validateVisa(formattedValue));
  }, [value]);

  return (
    <div className={styles['card-input-field']}>
      <div className={styles['card-input-container']}>
        <Input
          name={name}
          placeholder={placeholder}
          onChange={onChange}
          onBlur={onBlur}
          value={value}
          isTouched={isTouched}
          type="text"
          hint={hint}
          error={error}
        />
      </div>
      <div className={styles['card-icons-outer-container']}>
        <div className={styles['card-icons-container']}>
          <div
            className={cn(styles['card-icon-container'], {
              [styles['hidden']]: isMasterCard || (value && !isVisa),
            })}
          >
            <Image
              className={styles['card-field-icon']}
              src={'/assets/icons/icon-visa.svg'}
              width={28}
              height={18}
              alt=""
            />
          </div>
          <div
            className={cn(styles['card-icon-container'], {
              [styles['hidden']]: isVisa || (value && !isMasterCard),
            })}
          >
            <Image
              className={styles['card-field-icon']}
              src={'/assets/icons/icon-mastercard.svg'}
              width={28}
              height={18}
              alt=""
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default memo(CardInput);
