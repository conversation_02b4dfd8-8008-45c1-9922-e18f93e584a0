export const imageLevelPercent = {
  excellent: 67,
  good: 33,
  bad: 0,
};

export const ImageModerationStatuses = {
  New: 'new',
  Approved: 'approved',
  Declined: 'declined',
};

export const declineDescriptionList = {
  'Bad Content':
    'Your photo has been declined as it does not meet our content guidelines. Decline reason “Bad Content” includes pictures without people, body parts, documents, celebrity images or photos of other people where you, as the talent, are not displayed. Please review the guidelines and ensure that future photo submissions adhere to the specified criteria.',
  'Poor Quality':
    'Your photo has been declined as it does not meet our content guidelines. Decline reason “Poor Quality” includes blurry or fuzzy photos, as well as pictures that are too dark or where your face is barely visible. Additionally, submitting a photo of a real photo is not allowed. Please review the guidelines and ensure that future photo submissions adhere to the specified criteria.',
  'Not ok for title':
    'Your photo has been declined as it does not comply with our content guidelines. Decline reason “Not OK for Title” includes photos where you are in swimwear or lingerie, pictures with several people visible, images with sunglasses or hats covering your face, photo-collages, and action photos. The title photo is the first thing the Casting Director sees, so please ensure that it fits our guidelines before submitting.',
  Duplicate:
    'Your photo has been declined as it does not meet our content guidelines. This photo has already been uploaded previously, and we kindly request that you avoid submitting the same picture multiple times. Please ensure that future photo submissions are unique and do not violate our guidelines.',
  'Not a headshot':
    'Your photo has been declined as it does not qualify as a head-shot. Decline reason “Not a head-shot” includes side-view or full-height photos that are uploaded as head-shots. Please ensure that future photo submissions adhere to the specified criteria.',
  'Not a side-view or 3/4':
    'Your photo has been declined as it does not qualify as a side view. Decline reason “Not a side-view or 3/4" includes headshot or full-height photos that are uploaded as side view. Please ensure that future photo submissions adhere to the specified criteria.',
  'Not a full-height':
    'Your photo has been declined as it does not qualify as a full height. Decline reason “Not a full-height” includes side view or head shot photos that are uploaded as full height. Please ensure that future photo submissions adhere to the specified criteria.',
  'Incorrectly positioned':
    'Your photo has been declined due to incorrect positioning. Decline reason “Incorrectly positioned” includes photos that are uploaded upside down or on one side, particularly horizontal photos. Please ensure that all photos are correctly oriented before submission to meet our guidelines.',
  'Incorrectly cropped':
    'Your photo has been declined due to incorrect cropping.  Decline reason “Incorrectly cropped” includes photos with white or black space/margins that are not symmetric; any photos displaying websites, contact numbers, or email addresses. Please ensure that future photo submissions adhere to the correct cropping guidelines.',
  'Group photo':
    'Your photo has been declined as it does not meet our content guidelines. In the main photos section, only you should be displayed. Photos with many people, animals, or toys should be added to the Additional Photos section. Please ensure that you follow this guideline for future photo submissions.',
  'Filters applied':
    'Your photo has been declined as it does not meet our content guidelines. Decline reason “Filters Applied” which includes all types of photos with filters, such as those with added faces, kisses, masks, or any other filter effects.',
};

export const declineActionList = {
  upload: 'Upload',
  remove: 'Remove',
};
