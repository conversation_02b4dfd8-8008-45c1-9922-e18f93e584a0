import Api from '@services/api';
import { CookieService } from '@services/cookieService';

// Marketing tracking
export function xTracking(parameters) {
  const clientside = typeof document !== 'undefined';

  let referrer = '';

  if (clientside) {
    referrer = document.referrer.includes(location.hostname)
      ? null
      : document.referrer;
  }

  let {
    cid,
    utm_source,
    utm_medium,
    utm_campaign,
    utm_term,
    utm_content,
    utm_template,
    utm_skip,
  } = parameters;

  /**
   * Special parameter to skip utm tags
   * tracking override.
   */
  if (utm_skip) {
    utm_source =
      utm_medium =
      utm_campaign =
      utm_term =
      utm_content =
      utm_template =
        null;
  }

  try {
    const tracking = CookieService.getTrackingCookie()?.tracking ?? null;

    if (tracking) {
      cid = cid || tracking?.cid || null;
      utm_source = utm_source || tracking?.utm?.source || null;
      utm_medium = utm_medium || tracking?.utm?.medium || null;
      utm_campaign = utm_campaign || tracking?.utm?.campaign || null;
      utm_term = utm_term || tracking?.utm?.term || null;
      utm_content = utm_content || tracking?.utm?.content || null;
      utm_template = utm_template || tracking?.utm?.template || null;
      referrer = referrer || tracking?.referrer || null;
    }
  } catch (e) {
    if (!clientside) {
      console.error(
        `A custom error has occurred: Cannot parse the 'x-tracking' cookie.`,
        e,
      );
    }
  }

  const hasTrackingParams = [
    cid,
    utm_source,
    utm_medium,
    utm_campaign,
    utm_term,
    utm_content,
    utm_template,
    referrer,
  ].some((value) => value);

  if (!hasTrackingParams) {
    return;
  }

  const trackingData = {
    tracking: {
      cid: cid || null,
      utm: {
        source: utm_source || null,
        medium: utm_medium || null,
        campaign: utm_campaign || null,
        term: utm_term || null,
        content: utm_content || null,
        template: utm_template || null,
      },
      referrer,
    },
  };

  CookieService.setTrackingCookie(trackingData);
}

/**
 * @clientside
 */
export async function pixelTracking(url) {
  const uuid = () => {
    function s4() {
      return Math.floor((1 + Math.random()) * 0x10000)
        .toString(16)
        .substring(1);
    }

    return (
      s4() +
      s4() +
      '-' +
      s4() +
      '-' +
      s4() +
      '-' +
      s4() +
      '-' +
      s4() +
      s4() +
      s4()
    );
  };

  if (CookieService.getSessionCookie()) {
    const encodedUrl = encodeURIComponent(url);
    const _ga = CookieService.getGoogleCookie() || null;
    const _ac = CookieService.getSessionCookie() || null;

    await Api.clientside(`/pixel/view?url=${encodedUrl}&_ga=${_ga}&_ac=${_ac}`);
  } else {
    const ac = uuid();
    const expirationDate = new Date();

    expirationDate.setMonth(expirationDate.getMonth() + 1);
    CookieService.setSessionCookie(ac, expirationDate);
  }
}

export const parseQueryString = (queryString = '') => {
  return Object.fromEntries(
    new URLSearchParams(
      queryString.startsWith('?') ? queryString.substring(1) : queryString,
    ),
  );
};
