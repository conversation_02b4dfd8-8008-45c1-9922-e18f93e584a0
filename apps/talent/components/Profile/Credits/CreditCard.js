'use client';
import { memo, useState } from 'react';
import creditCardStyles from './CreditCard.module.scss';
import dayjs from 'dayjs';
import EditCredit from './EditCredit';
import cn from 'classnames';
import { Amp } from '@services/amp';
import { EditButton } from '../EditToggle';

const CreditCard = ({
  title,
  company,
  description,
  year,
  month,
  id,
  refreshCredits,
  updateProfileRating,
}) => {
  const [isCreditEditing, setCreditEditing] = useState(false);

  const openCreditEditing = () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'edit credit',
      scope: Amp.element.scope.profile,
      section: Amp.element.section.credits,
      type: Amp.element.type.button,
    });
    setCreditEditing(true);
  };

  const closeCreditEditing = () => {
    setCreditEditing(false);
  };

  const closeCreditEditingAndRefresh = async () => {
    await refreshCredits();
    setCreditEditing(false);
  };

  return (
    <>
      {!isCreditEditing ? (
        <div
          className={cn(
            creditCardStyles['profile-credit-card'],
            creditCardStyles.clickable,
          )}
          onClick={openCreditEditing}
        >
          <div className={creditCardStyles['profile-credit-card-header']}>
            <EditButton
              className={creditCardStyles['edit-button']}
              onClick={openCreditEditing}
            />
            <div>
              <div className={creditCardStyles['card-title']}>{title}</div>
              <div className={creditCardStyles['card-subtitle']}>
                by {company}
              </div>
            </div>
          </div>
          <hr className={creditCardStyles['separator']} />
          <div className={creditCardStyles['card-description']}>
            <div>{description}</div>
          </div>
          <hr className={creditCardStyles['separator']} />
          <div className={creditCardStyles['card-date']}>
            <span>{dayjs(`${year}-${month}`).format('MMMM, YYYY')}</span>
          </div>
        </div>
      ) : (
        <EditCredit
          id={id}
          title={title}
          company={company}
          description={description}
          year={year}
          month={month}
          onClose={closeCreditEditing}
          onCloseAndRefresh={closeCreditEditingAndRefresh}
          updateProfileRating={updateProfileRating}
        />
      )}
    </>
  );
};

export default memo(CreditCard);
