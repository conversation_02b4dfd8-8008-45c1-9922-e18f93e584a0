'use client';
import styles from './Testimonials.module.scss';
import Image from 'next/image';
import cn from 'classnames';
import { Carousel, StarRating } from '@components';
import { Brands } from '@components/Brands/Brands';
import { testimonials } from '@constants/testimonials';

const Testimonials = () => {
  return (
    <div className={styles.container}>
      <Carousel
        enablePagination
        loop
        playOnInit
        delay={6000}
        slidesToScroll={1}
        className="carousel-testimonials"
        paginationColor="white"
      >
        {testimonials.map(({ id, name, image, text, isBrands }) => (
          <>
            {isBrands ? (
              <div className={styles.card} key="brands">
                <div className={cn(styles.content, styles.brands)}>
                  <div className={styles['brand-title']}>Trusted by</div>
                  <Brands />
                </div>
              </div>
            ) : (
              <div className={styles.card} key={id}>
                <div className={styles.content}>
                  <div className={styles['image-container']}>
                    <Image
                      className={styles.image}
                      src={image}
                      alt="image"
                      width={48}
                      height={64}
                    />
                  </div>
                  <div className={styles.right}>
                    <StarRating initialRating={5} />
                    <span className={styles.name}>{name}</span>
                    <div className={styles.text}>{text}</div>
                  </div>
                </div>
              </div>
            )}
          </>
        ))}
      </Carousel>
    </div>
  );
};

export default Testimonials;
