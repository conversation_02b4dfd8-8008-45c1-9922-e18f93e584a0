'use client';
import { memo, useState } from 'react';
import { createPortal } from 'react-dom';
import styles from './PaymentForm.module.scss';
import { Button, Modal, PrivacyPolicy, TermsOfUse } from '@components';
import Image from 'next/image';
import cn from 'classnames';

const PaymentFormFooter = ({ isStripe, isUpgradeForm, loading }) => {
  const [showTerms, setShowTerms] = useState(false);
  const [showPrivacyPolicy, setShowPrivacyPolicy] = useState(false);

  const toggleShowTerms = (e) => {
    e?.preventDefault();
    setShowTerms(!showTerms);
  };

  const toggleShowPrivacyPolicy = (e) => {
    e?.preventDefault();
    setShowPrivacyPolicy(!showPrivacyPolicy);
  };

  const subscribeLabel =
    (isUpgradeForm && 'Upgrade & Save') || 'Complete Subscription';

  return (
    <>
      <div className={styles['payment-form-footer']}>
        <p
          className={cn(
            styles['payment-description'],
            styles['payment-description-bottom'],
          )}
        >
          By clicking{' '}
          <span className={styles['payment-button-label']}>
            {subscribeLabel}
          </span>{' '}
          I agree to the AllCasting.com{' '}
          <span className={styles.link} onClick={toggleShowPrivacyPolicy}>
            privacy policy
          </span>{' '}
          and{' '}
          <span className={styles.link} onClick={toggleShowTerms}>
            terms of use
          </span>
        </p>
        <div className={styles['payment-button-container']}>
          <Button
            label={loading ? 'Processing...' : subscribeLabel}
            loading={loading}
            color="orange"
            minWidth="220px"
            type="submit"
            disabled={loading}
          />
        </div>
        <div className={styles['payment-certificates-container']}>
          <div className={styles['payment-certificate']}>
            <Image
              src="/assets/icons/icon-payment-secure.png"
              width={44}
              height={44}
              alt="icon secure"
            />
            <span className={styles['payment-certificate-title']}>
              Site is Secure
            </span>
          </div>
          {!isStripe && (
            <div className={styles['payment-certificate']}>
              <Image
                src="/assets/icons/icon-payment-verified.png"
                width={57}
                height={44}
                alt="icon verified"
              />
              <span className={styles['payment-certificate-title']}>
                Merchant Services
              </span>
            </div>
          )}
        </div>
      </div>
      {showTerms &&
        createPortal(
          <Modal backdropClose onClose={toggleShowTerms}>
            <TermsOfUse />
          </Modal>,
          document.body,
        )}
      {showPrivacyPolicy &&
        createPortal(
          <Modal backdropClose onClose={toggleShowPrivacyPolicy}>
            <PrivacyPolicy onNavigation={toggleShowPrivacyPolicy} />
          </Modal>,
          document.body,
        )}
    </>
  );
};

export default memo(PaymentFormFooter);
