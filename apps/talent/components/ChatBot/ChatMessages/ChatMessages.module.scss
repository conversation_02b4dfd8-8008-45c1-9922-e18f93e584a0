@use '@styles/mixins' as *;
@use '@styles/variables' as *;

.message-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-bottom: $space-25;
}

.message-container {
  margin-top: auto;
  flex-direction: column;
  justify-content: flex-end;
  align-self: center;
  width: 100%;
  overflow-y: auto;
}

.answer {
  display: flex;
  flex-direction: column;
  align-self: flex-start;

  @include desktop {
    max-width: 600px;
  }
}

.answer-message {
  flex: 1;
  max-width: 100%;
  padding: $space-20;
  overflow-wrap: word-break;
  word-break: word-break;
  hyphens: auto;
  color: $black;
  background: $grey-10;
  border-radius: 10px 10px 10px 0;
  align-content: flex-start;

  a {
    color: $blue-80;
  }
}

.answer-prompts-container {
  display: block;
  padding-top: 6px;
}

.answer-prompt {
  border-radius: 1px;
  border: 1px solid $black;
  color: $black;
  cursor: pointer;
  display: inline-block;
  margin: 0 5px 5px 0;
  padding: 5px 8px 3px;
  transition: all 0.2s ease-in-out;
}

.question {
  display: flex;
  max-width: 100%;
  flex-direction: column;
  align-self: flex-end;

  @include desktop {
    max-width: 600px;
  }
}

.question-message {
  flex: 1;
  max-width: 100%;
  padding: $space-20;
  overflow-wrap: word-break;
  word-break: word-break;
  hyphens: auto;
  border-radius: 10px 10px 0;
  background: $gradient-capri;
  color: $white;
  align-content: flex-end;

  &.is-reported {
    font-style: italic;
    color: $grey-60;
  }
}

.meta {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  font-size: 12px;
  line-height: 1;
  margin-bottom: 8px;
  color: $grey-80;
  font-weight: 300;
  gap: $space-5;
  padding: 0 $space-10;
}
