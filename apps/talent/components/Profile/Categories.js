'use client';
import React, { memo, useEffect, useState } from 'react';
import Api from '@services/api';
import styles from './ProfileInfo.module.scss';
import { useNotifications } from '@contexts/NotificationContext';
import { MultiSelect } from '@components';
import Image from 'next/image';
import cn from 'classnames';
import { arePrimitiveArraysEqual } from '@utils/arePrimitiveArraysEqual';
import { Amp } from '@services/amp';
import { EditToggle } from './EditToggle';
import { ErrorMessage } from '@constants/form';

const Categories = ({
  profileId,
  categories,
  categoryOptions,
  refreshCategories,
}) => {
  const { setNotification } = useNotifications();
  const [isCategoriesContainerEditing, setCategoriesContainerEditing] =
    useState(false);
  const [selectedCategoryIds, setSelectedCategoryIds] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    setSelectedCategoryIds([...categories.map((category) => category.id)]);
  }, [categories]);

  const removeCategory = (id) => {
    setSelectedCategoryIds(
      selectedCategoryIds.filter((categoryId) => categoryId !== id),
    );
  };

  const editCategories = () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'edit interests',
      scope: Amp.element.scope.profile,
      section: Amp.element.section.interestedIn,
      type: Amp.element.type.button,
    });
    setCategoriesContainerEditing(true);
  };

  const saveCategories = async () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'save interests',
      scope: Amp.element.scope.profile,
      section: Amp.element.section.interestedIn,
      type: Amp.element.type.button,
    });
    if (
      !arePrimitiveArraysEqual(
        categories.map((category) => category.id),
        selectedCategoryIds,
      )
    ) {
      const body = new FormData();

      setLoading(true);

      selectedCategoryIds.forEach((categoryId) => {
        body.append('items[]', categoryId);
      });

      const response = await Api.clientside(
        `/profiles/${profileId}/categories`,
        {
          method: 'PUT',
          body: body,
        },
      );

      if (response.status !== 'ok') {
        setNotification({
          type: 'error',
          message: response.message || ErrorMessage.Unexpected,
          timeout: '5000',
        });
      } else {
        await refreshCategories();
      }

      setLoading(false);
    }
    setCategoriesContainerEditing(false);
  };

  return (
    <>
      {categoryOptions?.length && (
        <section>
          <div className={styles['profile-section-title']}>
            Interested in:
            <EditToggle
              isEditing={isCategoriesContainerEditing}
              onEdit={editCategories}
              onSave={saveCategories}
              disabled={loading}
            />
          </div>
          {!isCategoriesContainerEditing ? (
            <>
              {selectedCategoryIds.length ? (
                <div className={styles['profile-badge-container']}>
                  {selectedCategoryIds.map((id, index) => (
                    <span
                      key={index}
                      className={cn(
                        styles['profile-badge-outline'],
                        styles.clickable,
                      )}
                      onClick={editCategories}
                    >
                      {
                        categoryOptions.find((option) => option.value === id)
                          .title
                      }
                    </span>
                  ))}
                </div>
              ) : (
                <div
                  className={styles['profile-section-empty']}
                  onClick={editCategories}
                >
                  List all casting call categories you want to get cast in.
                  <span className={styles['profile-section-empty-add-button']}>
                    Add
                  </span>
                </div>
              )}
            </>
          ) : (
            <>
              <MultiSelect
                options={categoryOptions}
                name="category"
                placeholder="Choose categories"
                selectedOptions={selectedCategoryIds}
                onChange={(selectedOptions) => {
                  setSelectedCategoryIds(selectedOptions);
                }}
                hint="You are interested in these categories:"
              />
              <div
                className={cn(
                  styles['profile-badge-container'],
                  styles['profile-badge-container-filled'],
                )}
              >
                {selectedCategoryIds.map((id, index) => (
                  <div key={index} className={styles['profile-badge-outline']}>
                    {
                      categoryOptions.find((option) => option.value === id)
                        .title
                    }
                    <Image
                      className={styles['delete-button']}
                      onClick={() => removeCategory(id)}
                      src="/assets/icons/icon-close.svg"
                      alt="icon"
                      width={10}
                      height={10}
                    />
                  </div>
                ))}
              </div>
            </>
          )}
        </section>
      )}
    </>
  );
};

export default memo(Categories);
