'use client';
import React, { memo } from 'react';
import cn from 'classnames';
import styles from './Button.module.scss';
import Link from 'next/link';

const Button = ({
  label = '',
  kind = 'primary', // 'primary' 'secondary'
  type = 'button', // 'button' 'link' 'submit' 'reset'
  href = '#',
  color = 'blue', // blue | orange | green-gradient | red-gradient | plain-blue | plain-red | solid-blue
  disabled = false,
  minWidth = '0',
  shadow = true,
  onClick = () => {},
  className,
  ariaLabel,
  promoLabel = false,
  promoTheme = '',
  onMouseEnter = () => {},
  loading = false,
}) => {
  const Promo = ({ active, children }) => {
    return active && !disabled ? (
      <div className={styles['promo']}>
        {promoLabel && (
          <div
            className={cn(styles['promo-label'], styles[promoTheme])}
            onClick={onClick}
          >
            {promoLabel}
          </div>
        )}
        {children}
      </div>
    ) : (
      children
    );
  };

  return (
    <>
      {type === 'link' ? (
        <Link
          href={href}
          passHref
          className={cn(styles.btn, styles[color], className, {
            [styles.primary]: kind === 'primary',
            [styles.secondary]: kind === 'secondary',
            [styles.shadow]: shadow,
            [styles.disabled]: disabled,
          })}
          style={{ minWidth }}
          aria-label={ariaLabel}
          onMouseEnter={onMouseEnter}
        >
          {label}
        </Link>
      ) : (
        <Promo active={promoLabel}>
          <button
            type={type}
            className={cn(styles.btn, styles[color], className, {
              [styles.primary]: kind === 'primary',
              [styles.secondary]: kind === 'secondary',
              [styles.shadow]: shadow && !promoLabel,
              [styles.disabled]: disabled,
            })}
            disabled={disabled}
            style={{ minWidth }}
            onClick={onClick}
            onMouseEnter={onMouseEnter}
          >
            {label}
            {loading && <span className={styles.spinner} />}
          </button>
        </Promo>
      )}
    </>
  );
};

export default memo(Button);
