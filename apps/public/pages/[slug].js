import Api from '../services/api';
import { getHeaders } from '../utils/headerHelper';

// Old profiles links support
export const getServerSideProps = async ({ query, req }) => {
  if (isNaN(Number(query.slug))) {
    const profileByUsername = await Api.serverAPIRoute(
      `/profiles/profile-by-username/${query.slug}`.toLowerCase(),
      null,
      getHeaders(req, `/${query.slug}`),
    );
    const id = profileByUsername.links?.profile?.id;

    if (id) {
      return {
        redirect: {
          destination: `/profile/${query.slug}`,
          permanent: true,
        },
      };
    }
  }

  return {
    notFound: true,
  };
};

export default function Nope() {
  return <></>;
}
