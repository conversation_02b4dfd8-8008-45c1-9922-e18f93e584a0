import { memo, useEffect, useState } from 'react';
import { ProfileDesktop, ProfileMobile } from '../../components';
import { formatTalentProfile } from '../../utils/formatProfile';
import { ProfileProvider } from '../../contexts/ProfileContext';
import Api from '../../services/api';
import { useViewport } from '../../utils/useViewport';
import Seo from '../../components/Seo/Seo';
import { isMobile } from '../../utils/isMobile';
import { getHeaders } from '../../utils/headerHelper';
import { MainLayout } from '../../components/Layouts';
import { formatAccountLevel } from '../../utils/formatAccountLevel';
import { sharedServerSideProps } from '../../utils/sharedServerSideProps';
import { getIsFullTalentProfile } from '../../utils/isFullProfile';
import {
  getTalentProfileMobileMenuItems,
  getTalentProfileSideMenuItems,
} from '../../utils/getProfileMenuItems';
import { Amp } from '../../services/amp';

export const getServerSideProps = async ({ req, res, query, resolvedUrl }) => {
  const { slug, showInactive } = query;

  let id = slug;

  if (isNaN(Number(slug))) {
    const profileByUsername = await Api.serverAPIRoute(
      `/profiles/profile-by-username/${slug}`,
      null,
      getHeaders(req, resolvedUrl),
    );

    id = profileByUsername.links?.profile?.id;
  }

  if (!id) {
    return {
      notFound: true,
    };
  }

  const profile = await Api.serverAPIRoute(
    `/profiles/${id}?expand=personal_url,account_level,location`,
    null,
    getHeaders(req, resolvedUrl),
  );

  // For agents to see inactive profiles, default - not show inactive profiles
  if (!profile.active && showInactive !== 'true') {
    return {
      notFound: true,
    };
  }

  // intentionally
  if (false === profile.success) {
    return {
      notFound: true,
    };
  }

  if (profile?.rel === 'agent') {
    return {
      redirect: {
        permanent: true,
        destination: `/director/${slug}`,
      },
    };
  }

  const { firstname, lastname, birthday, gender, links } = profile || {};

  if (
    !getIsFullTalentProfile(
      firstname,
      lastname,
      birthday,
      gender,
      links?.location?.links?.zip?.code,
      true,
    )
  ) {
    return {
      notFound: true,
    };
  }

  const accountLevelStatus =
    profile?.links?.account?.links?.account_level?.links?.level?.status || null;
  const isAccountPaidOrDelayed =
    formatAccountLevel(accountLevelStatus).isPaidOrDelayed;

  const profileImages = await Api.serverAPIRoute(
    `/profiles/images/${id}`,
    null,
    getHeaders(req, resolvedUrl),
  );
  const profileEthnicities = await Api.serverAPIRoute(
    `/profiles/ethnicities/${id}`,
    null,
    getHeaders(req, resolvedUrl),
  );
  const profileSocialNetworks = isAccountPaidOrDelayed
    ? await Api.serverAPIRoute(
        `/profiles/socialities/${id}`,
        null,
        getHeaders(req, resolvedUrl),
      )
    : { items: [] };
  const profileSkills = await Api.serverAPIRoute(
    `/profiles/skills/${id}`,
    null,
    getHeaders(req, resolvedUrl),
  );
  const profileYoutubes = await Api.serverAPIRoute(
    `/profiles/youtubes/${id}`,
    null,
    getHeaders(req, resolvedUrl),
  );
  const profileCredits = await Api.serverAPIRoute(
    `/profiles/credits/${id}`,
    null,
    getHeaders(req, resolvedUrl),
  );
  const profileCategories = await Api.serverAPIRoute(
    `/profiles/categories/${id}`,
    null,
    getHeaders(req, resolvedUrl),
  );
  const profileAudios = await Api.serverAPIRoute(
    `/profiles/audios/${id}`,
    null,
    getHeaders(req, resolvedUrl),
  );
  const formattedProfile = formatTalentProfile(
    profile,
    profileImages.items,
    profileEthnicities.items?.length ? profileEthnicities.items[0] : null,
    profileSocialNetworks.items,
    profileSkills.items,
    profileCategories.items,
    profileYoutubes.items,
    profileCredits.items,
    profileAudios.items,
  );

  // temporarily allow our agents to see portfolio page for deactivated account
  if (!profile.active) {
    formattedProfile.allowIndexing = false;
  }

  const isMobileFromUserAgent = isMobile(req.headers['user-agent']);
  const sharedProps = await sharedServerSideProps(req, res);

  return {
    props: {
      ...sharedProps,
      profile: formattedProfile,
      sideMenuItems: getTalentProfileSideMenuItems(formattedProfile),
      menuItems: getTalentProfileMobileMenuItems(formattedProfile),
      defaultIsMobile: isMobileFromUserAgent,
    },
  };
};

const ProfilePage = ({
  profile,
  sideMenuItems,
  defaultIsMobile,
  menuItems,
}) => {
  const [isMobile, setIsMobile] = useState(defaultIsMobile);

  const { width } = useViewport();

  const ogImageProps = profile.closeUpImage?.proxy_url
    ? {
        ogImageUrl: profile.closeUpImage.proxy_url,
        ogImageWidth: 600,
        ogImageHeight: 800,
      }
    : {
        ogImageUrl: `${process.env.baseUrl}/assets/meta/profile.webp`,
      };

  useEffect(() => {
    if (width) {
      setIsMobile(width < 1024);
    }
  }, [width]);

  useEffect(() => {
    Amp.track(Amp.events.viewTalent, {
      profile_id: profile.id,
    });
  }, [profile]);

  return (
    <MainLayout
      isDefaultHeaderVisible
      isUserMenuVisible
      isSaleHeaderVisible
      isMobileSubscribeHeaderVisible
      isMobileMenuVisible
    >
      <Seo
        seoPage={{
          title: `${profile.name} • Talent Profile • allcasting`,
          description: `《 allcasting 》 ${profile.name} ✪ here! Best talent. ★ Start To Be a Star Today!`,
          ...ogImageProps,
        }}
        allowIndexing={profile.allowIndexing}
      />
      <ProfileProvider value={profile}>
        {!isMobile && (
          <ProfileDesktop
            sideMenuItems={sideMenuItems}
            isMobileFromUserAgent={defaultIsMobile}
          />
        )}
        {isMobile && (
          <ProfileMobile
            isMobileFromUserAgent={defaultIsMobile}
            menuItems={menuItems}
          />
        )}
      </ProfileProvider>
    </MainLayout>
  );
};

export default memo(ProfilePage);
