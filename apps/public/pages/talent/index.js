import React, { memo } from 'react';
import Talent from './[static]';
import Api from '../../services/api';
import seoPageProps from '../../utils/seoPageProps';
import { CookieService } from '../../services/cookieService';
import { formatAnnouncement } from '../../utils/formatAnnouncement';
import { getHeaders } from '../../utils/headerHelper';
import { sharedServerSideProps } from '../../utils/sharedServerSideProps';

export const getServerSideProps = async ({ query, req, res, resolvedUrl }) => {
  const id = CookieService.getProfileCookie(req, res);
  const userType = CookieService.getUserTypeCookie(req, res);
  const content = await Api.serverAPIRoute(
    `/talent/list?filter=${JSON.stringify(query)}`,
    null,
    getHeaders(req, resolvedUrl),
  );

  let user;

  if (content.data.list.items.length && userType === 'talent') {
    if (id) {
      user = await Api.serverAPIRoute(
        `/profiles/${id}`,
        null,
        getHeaders(req, resolvedUrl),
      );
    }

    if (user) {
      content.data.list.items = [
        user,
        ...content.data.list.items.filter((user) => user.id !== id),
      ];
    }
  }

  const accountLevel = CookieService.getAccountLevelCookie(req, res);

  let formattedAnnouncement = null;

  if (accountLevel?.isPaidOrDelayed) {
    const announcements = (
      await Api.serverAPIRoute(
        `/promotions?types=Announcements&targets=talent-is-paid&limit=1`,
        null,
        getHeaders(req, resolvedUrl),
      )
    ).data;
    const announcement = announcements ? announcements[0] : null;

    formattedAnnouncement = formatAnnouncement(announcement);
  }

  const promoResponse = await Api.serverAPIRoute(
    `/promotions?targets=guest`,
    null,
    getHeaders(req, resolvedUrl),
  );
  const sharedProps = await sharedServerSideProps(req, res);

  return {
    props: {
      ...sharedProps,
      defaultFilters: await Api.serverAPIRoute(
        `/talent/filters`,
        null,
        getHeaders(req, resolvedUrl),
      ),
      content,
      promos: promoResponse.data,
      announcement: formattedAnnouncement,
      page: 1,
      seoPage: {
        ...(await seoPageProps(resolvedUrl)),
        ogImageUrl: `${process.env.baseUrl}/assets/meta/talents.webp`,
      },
    },
  };
};

const TalentIndex = ({
  defaultFilters,
  content,
  promos,
  announcement,
  seoPage,
  page,
}) => {
  return (
    <Talent
      seoPage={seoPage}
      defaultFilters={defaultFilters}
      content={content}
      promos={promos}
      announcement={announcement}
      page={page}
    />
  );
};

export default memo(TalentIndex);
