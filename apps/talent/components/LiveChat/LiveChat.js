'use client';
import { useLiveChat } from '@contexts/LiveChatContext';
import { LiveChatWidget } from '@livechat/widget-react';
import { LIVE_CHAT_LICENSE } from '@constants/liveChat';

const LiveChat = () => {
  const {
    visibility,
    group,
    sessionVariables,
    customerName,
    onVisibilityChanged,
    show,
  } = useLiveChat();

  return (
    <>
      {show && (
        <LiveChatWidget
          license={LIVE_CHAT_LICENSE}
          visibility={visibility}
          onVisibilityChanged={onVisibilityChanged}
          group={group}
          sessionVariables={sessionVariables}
          customerName={customerName}
        />
      )}
    </>
  );
};

export default LiveChat;
