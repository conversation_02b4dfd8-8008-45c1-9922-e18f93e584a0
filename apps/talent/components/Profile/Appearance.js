'use client';
import React, { memo, useEffect, useState } from 'react';
import styles from './ProfileInfo.module.scss';
import { useNotifications } from '@contexts/NotificationContext';
import { useFormik } from 'formik';
import { MultiSelect, Select } from '@components';
import { formatHeight } from '@utils/formatHeight';
import { formatWeight } from '@utils/formatWeight';
import { formatSize } from '@utils/formatSize';
import { hasFormValueChanged } from '@utils/hasFormValueChanged';
import Api from '@services/api';
import cn from 'classnames';
import AppearanceValue from './FieldComponents/AppearanceValue';
import AppearanceAttribute from './FieldComponents/AppearanceAttribute';
import { Amp } from '@services/amp';
import { EditToggle } from './EditToggle';
import { ErrorMessage } from '@constants/form';

const Appearance = ({
  profileId,
  height,
  weight,
  eyeColor,
  hairColor,
  hipSize,
  dressSize,
  bust,
  cupSize,
  genderTitle,
  heightOptions,
  weightOptions,
  eyeColorOptions,
  hairColorOptions,
  hipSizeOptions,
  dressSizeOptions,
  bustOptions,
  cupSizeOptions,
  refreshProfileDetails,
  attributes,
  profileAttributes,
  saveAttribute,
  refreshProfileAttributes,
}) => {
  const [isAppearanceContainerEditing, setAppearanceContainerEditing] =
    useState(false);
  const [isAppearanceInfoNotEmpty, setIsAppearanceInfoNotEmpty] =
    useState(false);
  const { setNotification } = useNotifications();

  const formik = useFormik({
    initialValues: {
      height: height || 'n/a',
      weight: weight || 'n/a',
      eyeColor: eyeColor?.id || 'n/a',
      hairColor: hairColor?.id || 'n/a',
      hipSize: hipSize || 'n/a',
      bust: bust || 'n/a',
      cupSize: cupSize?.id || 'n/a',
      dressSize: String(dressSize ?? 'n/a'),
      bodyType: profileAttributes.bodyType?.items.map((item) => item.id) || [],
      specificCharacteristics:
        profileAttributes.specificCharacteristics?.items.map(
          (item) => item.id,
        ) || [],
      tattoos: profileAttributes.tattoos?.items.map((item) => item.id) || [],
      piercings:
        profileAttributes.piercings?.items.map((item) => item.id) || [],
    },
    onSubmit: async (values) => {
      if (hasFormValueChanged(values, formik.initialValues)) {
        const body = new FormData();

        body.append(
          'height',
          values.height !== 'n/a' ? String(values.height) : '0',
        );
        body.append(
          'weight',
          values.weight !== 'n/a' ? String(values.weight) : '0',
        );
        body.append(
          'eye-color',
          values.eyeColor !== 'n/a' ? String(values.eyeColor) : '0',
        );
        body.append(
          'hair-color',
          values.hairColor !== 'n/a' ? String(values.hairColor) : '0',
        );
        body.append(
          'hips',
          values.hipSize !== 'n/a' ? String(values.hipSize) : '0',
        );
        body.append('bust', values.bust !== 'n/a' ? String(values.bust) : '0');

        if (genderTitle === 'female') {
          body.append(
            'dress-size',
            values.dressSize === 'n/a' ? null : String(values.dressSize),
          );

          body.append(
            'cup-size',
            values.cupSize === 'n/a' ? '0' : String(values.cupSize),
          );
        }

        const profileResponse = await Api.clientside(`/profiles/${profileId}`, {
          method: 'PATCH',
          body: body,
        });

        const attributeUpdateResponseHasError = [
          await saveAttribute({
            ...attributes.bodyType,
            items: values.bodyType.map((id) => {
              return {
                id,
                name: attributes.bodyType.name,
                value: attributes.bodyType.items.find((i) => i.id === id).value,
              };
            }),
          }),
          await saveAttribute({
            ...attributes.specificCharacteristics,
            items: values.specificCharacteristics.map((id) => {
              return {
                id,
                name: attributes.specificCharacteristics.name,
                value: attributes.specificCharacteristics.items.find(
                  (i) => i.id === id,
                ).value,
              };
            }),
          }),
          await saveAttribute({
            ...attributes.tattoos,
            items: values.tattoos.map((id) => {
              return {
                id,
                name: attributes.tattoos.name,
                value: attributes.tattoos.items.find((i) => i.id === id).value,
              };
            }),
          }),
          await saveAttribute({
            ...attributes.piercings,
            items: values.piercings.map((id) => {
              return {
                id,
                name: attributes.piercings.name,
                value: attributes.piercings.items.find((i) => i.id === id)
                  .value,
              };
            }),
          }),
        ].find((value) => value.status === 'error');

        if (
          profileResponse.status !== 'ok' ||
          attributeUpdateResponseHasError
        ) {
          setNotification({
            type: 'error',
            timeout: '5000',
            message:
              profileResponse.message ||
              attributeUpdateResponseHasError.message ||
              ErrorMessage.Unexpected,
          });
        } else {
          setAppearanceContainerEditing(false);
          formik.resetForm({ values });
          await refreshProfileDetails();
          await refreshProfileAttributes();
        }
      } else {
        setAppearanceContainerEditing(false);
      }
    },
  });

  useEffect(() => {
    setIsAppearanceInfoNotEmpty(
      Object.values(formik.initialValues).some(
        (value) => value !== 'n/a' && (!Array.isArray(value) || !!value.length),
      ),
    );
  }, [formik.initialValues]);

  const editAppearance = async () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'edit appearance',
      scope: Amp.element.scope.profile,
      section: Amp.element.section.appearance,
      type: Amp.element.type.button,
    });
    setAppearanceContainerEditing(true);
  };

  const setSelectedAttributeValue = (name, selectedOptions) => {
    formik.setFieldValue(name, selectedOptions);
  };

  const setHeightValue = (e) => {
    formik.setFieldValue('height', e);
  };

  const setWeightValue = (e) => {
    formik.setFieldValue('weight', e);
  };

  const setEyeColorValue = (e) => {
    formik.setFieldValue('eyeColor', e);
  };

  const setHairColorValue = (e) => {
    formik.setFieldValue('hairColor', e);
  };

  const setHipSizeValue = (e) => {
    formik.setFieldValue('hipSize', e);
  };

  const setBustValue = (e) => {
    formik.setFieldValue('bust', e);
  };

  const setCupSizeValue = (e) => {
    formik.setFieldValue('cupSize', e);
  };

  const setDressSizeValue = (e) => {
    formik.setFieldValue('dressSize', e);
  };

  const setHeightTouched = () => {
    formik.setFieldTouched('height');
  };

  const setWeightTouched = () => {
    formik.setFieldTouched('weight');
  };

  const setEyeColorTouched = () => {
    formik.setFieldTouched('eyeColor');
  };

  const setHairColorTouched = () => {
    formik.setFieldTouched('hairColor');
  };

  const setHipSizeTouched = () => {
    formik.setFieldTouched('hipSize');
  };

  const setBustTouched = () => {
    formik.setFieldTouched('bust');
  };

  const setCupSizeTouched = () => {
    formik.setFieldTouched('cupSize');
  };

  const setDressSizeTouched = () => {
    formik.setFieldTouched('dressSize');
  };

  const triggerSubmit = async () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'save appearance',
      scope: Amp.element.scope.profile,
      section: Amp.element.section.appearance,
      type: Amp.element.type.button,
    });
    await formik.submitForm();
  };

  return (
    <>
      <section>
        <div className={styles['profile-section-title']}>
          APPEARANCE:
          <EditToggle
            isEditing={isAppearanceContainerEditing}
            onEdit={editAppearance}
            onSave={triggerSubmit}
            disabled={formik.isSubmitting || !formik.isValid}
          />
        </div>

        {!isAppearanceContainerEditing ? (
          <>
            {isAppearanceInfoNotEmpty ? (
              <div
                className={cn(
                  styles['profile-properties-container'],
                  styles.clickable,
                )}
                onClick={editAppearance}
              >
                <AppearanceValue
                  label="Height"
                  hasValue={formik.values.height !== 'n/a'}
                >
                  {formik.values.height !== 'n/a'
                    ? formatHeight(formik.values.height)
                    : 'n/a'}
                </AppearanceValue>
                <AppearanceValue
                  label="Weight"
                  hasValue={formik.values.weight !== 'n/a'}
                >
                  {formik.values.weight !== 'n/a'
                    ? formatWeight(formik.values.weight)
                    : 'n/a'}
                </AppearanceValue>
                <AppearanceValue
                  label="Eye Color"
                  hasValue={formik.values.eyeColor !== 'n/a'}
                >
                  {eyeColorOptions.find(
                    (option) => option.value === formik.values.eyeColor,
                  )?.title || 'n/a'}
                </AppearanceValue>
                <AppearanceValue
                  label="Hair Color"
                  hasValue={formik.values.hairColor !== 'n/a'}
                >
                  {hairColorOptions.find(
                    (option) => option.value === formik.values.hairColor,
                  )?.title || 'n/a'}
                </AppearanceValue>
                <AppearanceValue
                  label="Hip Size"
                  hasValue={formik.values.hipSize !== 'n/a'}
                >
                  {formik.values.hipSize !== 'n/a'
                    ? formatSize(formik.values.hipSize)
                    : 'n/a'}
                </AppearanceValue>
                {genderTitle === 'female' && (
                  <AppearanceValue
                    label="Dress Size"
                    hasValue={formik.values.dressSize !== 'n/a'}
                  >
                    {formik.values.dressSize ?? 'n/a'}
                  </AppearanceValue>
                )}
                <AppearanceValue
                  label="Chest/Bust"
                  hasValue={formik.values.bust !== 'n/a'}
                >
                  {formik.values.bust !== 'n/a'
                    ? formatSize(formik.values.bust)
                    : 'n/a'}
                </AppearanceValue>
                {genderTitle === 'female' && (
                  <AppearanceValue
                    label="Cup size"
                    hasValue={formik.values.cupSize !== 'n/a'}
                  >
                    {cupSizeOptions.find(
                      (option) => option.value === formik.values.cupSize,
                    )?.title || 'n/a'}
                  </AppearanceValue>
                )}
                {genderTitle === 'male' && (
                  <div
                    className={cn(styles['profile-property'], styles['wide'])}
                  ></div>
                )}
                <AppearanceAttribute
                  label="Body Type"
                  values={profileAttributes.bodyType?.items}
                />
                <AppearanceAttribute
                  label="Specific Characteristics"
                  values={profileAttributes.specificCharacteristics?.items}
                />
                <AppearanceAttribute
                  label="Tattoos"
                  values={profileAttributes.tattoos?.items}
                />
                <AppearanceAttribute
                  label="Piercings"
                  values={profileAttributes.piercings?.items}
                />
              </div>
            ) : (
              <div
                className={styles['profile-section-empty']}
                onClick={editAppearance}
              >
                Provide all your appearance details to help casting
                professionals choose you.
                <span className={styles['profile-section-empty-add-button']}>
                  Add
                </span>
              </div>
            )}
          </>
        ) : (
          <div
            className={cn(
              styles['profile-properties-container'],
              styles['profile-properties-container-form'],
            )}
          >
            <div className={styles['profile-property']}>
              <div className={styles['profile-property-value']}>
                <Select
                  name="height"
                  onChange={setHeightValue}
                  value={formik.values.height}
                  isTouched={formik.touched.height}
                  error={formik.errors.height}
                  placeholder="Height"
                  options={heightOptions}
                  setFormFieldTouched={setHeightTouched}
                />
              </div>
            </div>
            <div className={styles['profile-property']}>
              <div className={styles['profile-property-value']}>
                <Select
                  name="weight"
                  onChange={setWeightValue}
                  value={formik.values.weight}
                  isTouched={formik.touched.weight}
                  error={formik.errors.weight}
                  placeholder="Weight"
                  options={weightOptions}
                  setFormFieldTouched={setWeightTouched}
                />
              </div>
            </div>
            <div className={styles['profile-property']}>
              <div className={styles['profile-property-value']}>
                <Select
                  name="eyeColor"
                  onChange={setEyeColorValue}
                  value={formik.values.eyeColor}
                  isTouched={formik.touched.eyeColor}
                  error={formik.errors.eyeColor}
                  placeholder="Eye Color"
                  options={eyeColorOptions}
                  setFormFieldTouched={setEyeColorTouched}
                />
              </div>
            </div>
            <div className={styles['profile-property']}>
              <div className={styles['profile-property-value']}>
                <Select
                  name="hairColor"
                  onChange={setHairColorValue}
                  value={formik.values.hairColor}
                  isTouched={formik.touched.hairColor}
                  error={formik.errors.hairColor}
                  placeholder="Hair Color"
                  options={hairColorOptions}
                  setFormFieldTouched={setHairColorTouched}
                />
              </div>
            </div>
            <div className={styles['profile-property']}>
              <div className={styles['profile-property-value']}>
                <Select
                  name="hipSize"
                  onChange={setHipSizeValue}
                  value={formik.values.hipSize}
                  isTouched={formik.touched.hipSize}
                  error={formik.errors.hipSize}
                  placeholder="Hip Size"
                  options={hipSizeOptions}
                  setFormFieldTouched={setHipSizeTouched}
                />
              </div>
            </div>
            {genderTitle === 'female' && (
              <div className={styles['profile-property']}>
                <div className={styles['profile-property-value']}>
                  <Select
                    name="dressSize"
                    onChange={setDressSizeValue}
                    value={formik.values.dressSize}
                    isTouched={formik.touched.dressSize}
                    error={formik.errors.dressSize}
                    placeholder="Dress Size"
                    options={dressSizeOptions}
                    setFormFieldTouched={setDressSizeTouched}
                  />
                </div>
              </div>
            )}
            <div className={styles['profile-property']}>
              <div className={styles['profile-property-value']}>
                <Select
                  name="bust"
                  onChange={setBustValue}
                  value={formik.values.bust}
                  isTouched={formik.touched.bust}
                  error={formik.errors.bust}
                  placeholder="Chest/Bust"
                  options={bustOptions}
                  setFormFieldTouched={setBustTouched}
                />
              </div>
            </div>
            {genderTitle === 'female' && (
              <div className={styles['profile-property']}>
                <div className={styles['profile-property-value']}>
                  <Select
                    name="cupSize"
                    onChange={setCupSizeValue}
                    value={formik.values.cupSize}
                    isTouched={formik.touched.cupSize}
                    error={formik.errors.cupSize}
                    placeholder="Cup Size"
                    options={cupSizeOptions}
                    setFormFieldTouched={setCupSizeTouched}
                  />
                </div>
              </div>
            )}
            {genderTitle === 'male' && (
              <div
                className={cn(styles['profile-property'], styles['wide'])}
              ></div>
            )}
            <div className={cn(styles['profile-property'], styles['wide'])}>
              <div className={styles['profile-property-value']}>
                <MultiSelect
                  options={attributes.bodyType.items
                    .map((item) => ({
                      value: item.id,
                      title: item.value,
                    }))
                    .sort((a, b) => a.value - b.value)}
                  name="bodyType"
                  placeholder="Body Type"
                  selectedOptions={formik.values.bodyType}
                  onChange={(selectedOptions) => {
                    setSelectedAttributeValue('bodyType', selectedOptions);
                  }}
                />
              </div>
            </div>
            <div className={cn(styles['profile-property'], styles['wide'])}>
              <div className={styles['profile-property-value']}>
                <MultiSelect
                  options={attributes.specificCharacteristics.items
                    .map((item) => ({
                      value: item.id,
                      title: item.value,
                    }))
                    .sort((a, b) => a.value - b.value)}
                  name="specificCharacteristics"
                  placeholder="Specific Characteristics"
                  selectedOptions={formik.values.specificCharacteristics}
                  onChange={(selectedOptions) => {
                    setSelectedAttributeValue(
                      'specificCharacteristics',
                      selectedOptions,
                    );
                  }}
                />
              </div>
            </div>
            <div className={cn(styles['profile-property'], styles['wide'])}>
              <div className={styles['profile-property-value']}>
                <MultiSelect
                  options={attributes.tattoos.items
                    .map((item) => ({
                      value: item.id,
                      title: item.value,
                    }))
                    .sort((a, b) => a.value - b.value)}
                  name="tattoos"
                  placeholder="Tattoos"
                  selectedOptions={formik.values.tattoos}
                  onChange={(selectedOptions) => {
                    setSelectedAttributeValue('tattoos', selectedOptions);
                  }}
                />
              </div>
            </div>
            <div className={cn(styles['profile-property'], styles['wide'])}>
              <div className={styles['profile-property-value']}>
                <MultiSelect
                  options={attributes.piercings.items
                    .map((item) => ({
                      value: item.id,
                      title: item.value,
                    }))
                    .sort((a, b) => a.value - b.value)}
                  name="piercings"
                  placeholder="Piercings"
                  selectedOptions={formik.values.piercings}
                  onChange={(selectedOptions) => {
                    setSelectedAttributeValue('piercings', selectedOptions);
                  }}
                />
              </div>
            </div>
          </div>
        )}
      </section>
    </>
  );
};

export default memo(Appearance);
