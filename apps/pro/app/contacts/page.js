import ContactsClient from './_client';
import { cookies, headers } from 'next/headers';
import { CookieServiceServer } from '@services/cookieServiceServer';
import { notFound, redirect } from 'next/navigation';
import { CONTACTS_LIMIT } from '@constants/pagination';
import Api from '@services/api';
import { extractResult } from '@utils/extractPromiseResult';
import { getResolvedUrl } from '@utils/apiHelper';
import { formatContacts, formatGroups } from '@utils/contactHelpers';

export const metadata = {
  title: 'Contacts',
};

export default async function ContactsPage(req) {
  const cookieStore = await cookies();
  const headerStore = await headers();
  const searchParams = await req.searchParams;
  const resolvedUrl = getResolvedUrl('/contacts', searchParams);
  const profileId = CookieServiceServer.getProfileCookie(cookieStore);
  const page = searchParams.page ? Number(searchParams.page) : 1;

  if (page <= 0 || isNaN(page)) {
    notFound();
  }

  if (searchParams.page === '1') {
    redirect('/contacts');
  }

  const paths = [
    `/profiles/${profileId}/contact-groups`,
    `/profiles/${profileId}/contacts?expand=contact&page=${page}&limit=${CONTACTS_LIMIT}`,
    '/clientstatus/info',
  ];

  const results = await Promise.allSettled(
    paths.map((path) =>
      Api.serverside(path, cookieStore, headerStore, resolvedUrl),
    ),
  );

  const [groups, contacts, clientStatus] = results.map((result) =>
    extractResult(result, { items: [], count: 0 }),
  );

  const contactsTotal = contacts.count || 0;
  const maximumPage = Math.ceil(contactsTotal / CONTACTS_LIMIT);

  if (maximumPage && page > maximumPage) {
    redirect('/contacts');
  }

  return (
    <ContactsClient
      groups={formatGroups(groups.items || [])}
      contacts={formatContacts(contacts.items || [])}
      canContactTalent={
        clientStatus.info?.allowed_to?.hasOwnProperty('contact_talent') || false
      }
      contactsTotal={contactsTotal}
      page={page}
    />
  );
}
