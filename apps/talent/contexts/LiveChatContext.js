'use client';
import React, { createContext, useContext, useState } from 'react';
import { DEFAULT_SETTINGS, LIVE_CHAT_VISIBILITY } from '@constants/liveChat';
import { CookieService } from '@services/cookieService';
import dayjs from 'dayjs';
import * as Sentry from '@sentry/nextjs';
import { deepEqual } from '@utils/deepEqual';
import { useAuth } from './AuthContext';
import Api from '../services/api';

const LiveChatContext = createContext({});

export const LiveChatProvider = ({ children }) => {
  const [settings, setSettings] = useState(DEFAULT_SETTINGS);

  const { isAuthenticated } = useAuth();

  const initiateLiveChat = async (
    show,
    group,
    visibility = LIVE_CHAT_VISIBILITY.Minimized,
  ) => {
    if (isAuthenticated) {
      const customerInfo = await getCustomerInfo();

      if (customerInfo) {
        setSettings((previousSettings) => {
          const newSettings = {
            show,
            visibility,
            group,
            sessionVariables: customerInfo.sessionVariables,
            customerName: customerInfo.customerName,
          };

          return deepEqual(previousSettings, newSettings)
            ? previousSettings
            : newSettings;
        });
      } else {
        setSettings((previousSettings) => {
          return deepEqual(previousSettings, DEFAULT_SETTINGS)
            ? previousSettings
            : DEFAULT_SETTINGS;
        });
      }
    }
  };

  const onVisibilityChanged = (value) => {
    if (value.visibility !== settings.visibility) {
      setSettings({ ...settings, visibility: value.visibility });
    }
  };

  const updateVisibility = (visibility) => {
    if (visibility !== settings.visibility) {
      setSettings({ ...settings, visibility });
    }
  };

  const getCustomerInfo = async () => {
    try {
      const accountId = CookieService.getAccountCookie();
      const paths = [
        `/accounts/${accountId}/levels/last?expand=level_features,feature_requests`,
        `/accounts/${accountId}/profiles`,
      ];
      const sessionVariables = {};

      const requests = await Promise.all(
        paths.map((path) => Api.clientside(path)),
      );

      const [levels, profiles] = requests;
      const { id, client_id, firstname, lastname } = profiles?.items?.[0] || {};

      const subscriptionStartDate = levels.start;
      const subscriptionEndDate = levels.end;
      const subscriptionPlan =
        levels.links?.level?.recurring_interval_description;

      sessionVariables.AccountLevel = levels.links?.level?.status || 'unknown';

      if (subscriptionStartDate) {
        sessionVariables.SubscriptionStartDate = dayjs(
          subscriptionStartDate,
        ).format('MM/DD/YYYY');
      }

      if (subscriptionEndDate) {
        sessionVariables.SubscriptionEndDate =
          dayjs(subscriptionEndDate).format('MM/DD/YYYY');
      }

      if (subscriptionPlan) {
        sessionVariables.SubscriptionPlan = subscriptionPlan;
      }

      if (id) {
        sessionVariables.ProfileID = id;
      }

      if (client_id) {
        sessionVariables.ClientID = client_id;
      }

      if (firstname || lastname) {
        sessionVariables.Name = `${firstname} ${lastname}`;
      }

      return { sessionVariables, customerName: firstname || 'Talent' };
    } catch (error) {
      Sentry.captureException(error);

      return null;
    }
  };

  return (
    <LiveChatContext.Provider
      value={{
        ...settings,
        updateVisibility,
        initiateLiveChat,
        onVisibilityChanged,
      }}
    >
      {children}
    </LiveChatContext.Provider>
  );
};

export const useLiveChat = () => useContext(LiveChatContext);
