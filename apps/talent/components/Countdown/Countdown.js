'use client';
import { memo, useEffect, useState } from 'react';
import styles from './Countdown.module.scss';
import cn from 'classnames';
import { formatTime } from '@utils/formatTime';
import { useInterval } from '@utils/useInterval';

const Countdown = ({
  expirationTime,
  horizontalLayout = false,
  onCountdownEnd,
}) => {
  const [countdownSettings, setCountdownSettings] = useState(null);
  const [currentTimeLeft, setCurrentTimeLeft] = useState(
    expirationTime - Math.floor(Date.now() / 1000),
  );

  useInterval(() => {
    if (currentTimeLeft === 0) {
      onCountdownEnd();
    }

    setCurrentTimeLeft(currentTimeLeft - 1);
  }, 1000);

  useEffect(() => {
    setCountdownSettings(formatTime(currentTimeLeft));
  }, [currentTimeLeft]);

  return (
    <>
      {countdownSettings && (
        <div
          className={cn(styles['countdown'], {
            [styles['countdown-horizontal']]: horizontalLayout,
          })}
        >
          <div className={styles['time-container']}>
            <span className={styles['time-digit']}>
              {countdownSettings.days}
            </span>
            <span className={styles['time-title']}>days</span>
          </div>
          <span className={styles['time-separator']}>:</span>
          <div className={styles['time-container']}>
            <span className={styles['time-digit']}>
              {countdownSettings.hours}
            </span>
            <span className={styles['time-title']}>hours</span>
          </div>
          <span className={styles['time-separator']}>:</span>
          <div className={styles['time-container']}>
            <span className={styles['time-digit']}>
              {countdownSettings.minutes}
            </span>
            <span className={styles['time-title']}>
              {horizontalLayout ? 'min' : 'minutes'}
            </span>
          </div>
          <span className={styles['time-separator']}>:</span>
          <div className={styles['time-container']}>
            <span className={styles['time-digit']}>
              {countdownSettings.seconds}
            </span>
            <span className={styles['time-title']}>
              {horizontalLayout ? 'sec' : 'seconds'}
            </span>
          </div>
        </div>
      )}
    </>
  );
};

export default memo(Countdown);
