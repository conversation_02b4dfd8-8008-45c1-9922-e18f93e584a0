'use client';
import React, { useRef } from 'react';
import styles from './WizardForm.module.scss';
import {
  BirthdayFormik,
  Button,
  ChipSelectFormik,
  InputFormik,
  Loading,
} from '@components';
import { Form, Formik } from 'formik';
import * as Yup from 'yup';
import { maskPhoneNumber } from '@utils/maskPhoneNumber';
import dayjs from 'dayjs';
import Api from '@services/api';
import { useAuth } from '@contexts/AuthContext';
import { useNotifications } from '@contexts/NotificationContext';
import { useRouter } from 'next/navigation';
import { checkTollFreeNumber } from '@utils/checkTollFreeNumber';
import { cacheTest } from '@utils/formikHelpers';
import { Amp } from '@services/amp';
import {
  ErrorMessage,
  NAME_REGEX,
  EMAIL_REGEX,
  PHONE_NUMBER_REGEX,
} from '@constants/form';
import WizardAlert from './WizardAlert';

const WizardForm = ({
  genderOptions,
  ethnicitiesOptions,
  isEmailValid = true,
  isPasswordSet,
  profile,
}) => {
  const {
    firstName,
    lastName,
    gender,
    ethnicity,
    phone,
    birthdayExtended,
    email,
    zipCode,
    location,
  } = profile;

  const { accountId, profileId, userProfiles, refreshUserProfiles } = useAuth();
  const { setNotification } = useNotifications();
  const router = useRouter();

  const isFormStartedRef = useRef(false);
  const locationRef = useRef({
    country: location?.country || '',
    location: location?.location || '',
    zip: zipCode || '',
  });

  const validateZip = async (value) => {
    if (locationRef.current.zip === value && value) return true;

    const length = value?.length || 0;

    if (length >= 4 && length <= 6) {
      const response = await getLocation(value);
      const isZipValid = response.count > 0;
      const { country, city, state } = response?.items?.[0]?.links || {};

      locationRef.current = {
        country: country?.code || '',
        location: isZipValid ? `${city?.title}, ${state?.code}` : '',
        zip: value,
      };

      return isZipValid;
    }

    return true;
  };

  const validateZipRef = useRef(cacheTest(validateZip));

  const initialFirstName = NAME_REGEX.test(firstName) ? firstName : '';
  const initialLastName = NAME_REGEX.test(lastName) ? lastName : '';

  const initialValues = {
    firstName: initialFirstName,
    lastName: initialLastName,
    gender: gender?.id || '',
    ethnicity: ethnicity?.id || '',
    birthday: {
      day: birthdayExtended?.day || '',
      month: birthdayExtended?.month || '',
      year: birthdayExtended?.year || '',
    },
    phone: phone ? maskPhoneNumber(phone) : '',
    zip: zipCode || '',
    email: !isEmailValid ? '' : (email ?? ''),
  };

  const validationSchema = Yup.object({
    firstName: Yup.string()
      .required(ErrorMessage.NameRequired)
      .max(35, ErrorMessage.NamePattern)
      .matches(NAME_REGEX, ErrorMessage.NamePattern),
    lastName: Yup.string()
      .required(ErrorMessage.LastNameRequired)
      .max(35, ErrorMessage.NamePattern)
      .matches(NAME_REGEX, ErrorMessage.NamePattern),
    gender: Yup.string().required(ErrorMessage.GenderRequired),
    ethnicity: Yup.string().required(ErrorMessage.EthnicityRequired),
    birthday: Yup.object({
      month: Yup.string().required(ErrorMessage.MonthRequired),
      day: Yup.string().required(ErrorMessage.DayRequired),
      year: Yup.string().required(ErrorMessage.YearRequired),
      date: Yup.string().test('birthdayValid', (_, context) => {
        const {
          createError,
          parent: { month, day, year },
        } = context;

        const isFullDate = !!(month && day && year);

        if (!isFullDate) {
          return true;
        }

        const validAge = dayjs().subtract(18, 'year').unix();
        const date = dayjs(`${year}-${month}-${day}`).unix();

        if (validAge <= date) {
          return createError({
            message: ErrorMessage.AgeMinWizard,
          });
        }

        return true;
      }),
    }),
    phone: Yup.string()
      .matches(PHONE_NUMBER_REGEX, ErrorMessage.PhonePattern)
      .test('test', ErrorMessage.PhonePatternToll, async (value) => {
        return checkTollFreeNumber(value);
      }),
    email: Yup.string()
      .required(ErrorMessage.EmailPattern)
      .matches(EMAIL_REGEX, ErrorMessage.EmailPattern),
    zip: Yup.string()
      .transform((value) => value?.replaceAll(' ', ''))
      .required(ErrorMessage.ZipRequired)
      .min(5, ErrorMessage.ZipPattern)
      .max(6, ErrorMessage.ZipPattern)
      .test('test', ErrorMessage.ZipPattern, validateZipRef.current),
  });

  const onSubmit = async (values, { setStatus }) => {
    const locationBody = new FormData();
    const touchesBody = new FormData();
    const profileBody = new FormData();
    const ethnicitiesBody = new FormData();
    const newPhone = values.phone?.replace(/[^A-Z0-9]+/gi, '');

    locationBody.append('zip', values.zip?.replaceAll(' ', ''));

    touchesBody.append('email', values.email);
    touchesBody.append('phone', newPhone);
    touchesBody.append('phone_opt_outed', '0');

    profileBody.append('firstname', values.firstName);
    profileBody.append('lastname', values.lastName);
    profileBody.append(
      'birthday',
      `${values.birthday.year}-${values.birthday.month}-${values.birthday.day}`,
    );
    profileBody.append('gender', values.gender);

    ethnicitiesBody.append('items[]', values.ethnicity);

    const [locationRes, touchesRes, profileRes, ethnicitiesRes] =
      await Promise.all([
        Api.clientside(`/accounts/${accountId}/location`, {
          body: locationBody,
          method: 'PUT',
        }),
        Api.clientside(`/accounts/${accountId}/touches`, {
          body: touchesBody,
          method: 'PUT',
        }),
        Api.clientside(`/profiles/${profileId}`, {
          body: profileBody,
          method: 'PATCH',
        }),
        Api.clientside(`/profiles/${profileId}/ethnicities`, {
          body: ethnicitiesBody,
          method: 'PUT',
        }),
      ]);

    const responses = [locationRes, touchesRes, profileRes, ethnicitiesRes];
    const isError = responses.some((response) => response.status !== 'ok');
    const errorMessage =
      responses.find((response) => !!response.message)?.message ||
      ErrorMessage.Unexpected;

    await refreshUserProfiles();

    if (values.phone) {
      const user = userProfiles[0] || {};
      const touchesIsError = touchesRes.status !== 'ok';
      const touchesErrorMessage =
        touchesRes?.message || ErrorMessage.Unexpected;

      Amp.track(Amp.events.submitPhoneNumber, {
        scope: Amp.element.scope.wizard,
        phone_number_last_4_digits: newPhone.slice(-4),
        country_code: user.country,
        status: touchesIsError ? 'failure' : 'success',
        error_message: touchesIsError ? touchesErrorMessage : null,
      });
    }

    Amp.track(Amp.events.formSubmitted, {
      name: 'wizard',
      scope: Amp.element.scope.wizard,
      result: isError ? Amp.element.result.fail : Amp.element.result.success,
      message: isError ? errorMessage : null,
    });

    if (isError) {
      setNotification({
        type: 'error',
        message: errorMessage,
      });
    } else {
      Amp.track(Amp.events.completeWizardStepOne, {
        type: 'talent',
      });

      setStatus('success');
      router.push('/wizard?step=2');
    }
  };

  const getLocation = async (zip) => {
    return await Api.clientside(`/locations?query=${zip}`);
  };

  const sendFormStartedEventToAmplitude = () => {
    Amp.track(Amp.events.formStarted, {
      name: 'wizard',
      scope: Amp.element.scope.wizard,
    });
  };

  return (
    <div className={styles.container}>
      <section className={styles.heading}>
        {firstName ? (
          <h2 className={styles.title}>Hi, {firstName}!</h2>
        ) : (
          <h2 className={styles.title}>Welcome, Future Superstar!</h2>
        )}
        <p className={styles.subtitle}>Complete Your Profile</p>
      </section>
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={onSubmit}
      >
        {({ values, handleSubmit, isValid, isSubmitting, dirty, status }) => {
          const { zip } = values;
          const showName = !firstName || !lastName || !isPasswordSet;
          const showZip = !zipCode || !isPasswordSet;
          const showEmail = !isEmailValid;

          if (!isFormStartedRef.current && dirty) {
            isFormStartedRef.current = true;
            sendFormStartedEventToAmplitude();
          }

          if (zip.length < 4) {
            locationRef.current = { country: '', location: '', zip };
          }

          return (
            <Form className={styles.fields}>
              {showName && (
                <InputFormik
                  name="firstName"
                  placeholder="First Name"
                  hint="Maximum 35 characters"
                />
              )}
              {showName && (
                <InputFormik
                  name="lastName"
                  placeholder="Last Name"
                  hint="Maximum 35 characters"
                />
              )}
              {showZip && <InputFormik name="zip" placeholder="Zip Code" />}
              {showEmail && (
                <InputFormik name="email" placeholder="Email Address" />
              )}
              {showZip !== showEmail && <div />}
              <BirthdayFormik
                name="birthday"
                label="Date Of Birth"
                hideFloatingPlaceholder
              />
              <div className={styles.gender}>
                <ChipSelectFormik
                  name="gender"
                  label="Gender"
                  options={genderOptions}
                />
                <WizardAlert
                  className={styles.alert}
                  field="gender"
                  title="Nice work! 🚀"
                  text="Just a few more clicks to finish your profile."
                  left
                />
              </div>
              <ChipSelectFormik
                className={styles.ethnicity}
                name="ethnicity"
                label="Ethnicity"
                options={ethnicitiesOptions}
              />
              <InputFormik
                name="phone"
                type="tel"
                label="Phone Number (optional)"
                placeholder="e.g., (*************"
                onSetValue={maskPhoneNumber}
                hint="Private · For updates & support only"
              />
              <div className={styles.actions}>
                {isSubmitting || status === 'success' ? (
                  <Loading />
                ) : (
                  <Button
                    className={styles.button}
                    ariaLabel="Continue"
                    label="Continue"
                    onClick={handleSubmit}
                    disabled={!isValid || isSubmitting}
                    loading={isSubmitting}
                    minWidth="220px"
                  />
                )}
              </div>
            </Form>
          );
        }}
      </Formik>
    </div>
  );
};

export default WizardForm;
