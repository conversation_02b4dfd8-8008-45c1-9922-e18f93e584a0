import Api from '../../../services/api';
import { formatFilters } from '../../../utils/formatFilters';

export default async function handler(req, res) {
  const formattedFilters = await formatFilters(false, req, req);

  const data = {
    list: await Api.serverGateway(
      `/calls/search${formattedFilters.filterParameters}&limit=24`,
      req.headers,
    ),
    filter: formattedFilters.filter,
  };

  res.status(200).json(data);
}
